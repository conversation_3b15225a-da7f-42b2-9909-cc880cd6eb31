#!/bin/bash

# Fetch the latest changes from the dev branch
git fetch origin dev:dev

# Extract the previous version from version.properties in the dev branch
PREVIOUS_VERSION=$(git show origin/dev:version.properties | grep -E '^version=' | cut -d'=' -f2 | tr -d '[:space:]')

# Extract the current version from the local version.properties
CURRENT_VERSION=$(grep -E '^version=' version.properties | cut -d'=' -f2 | tr -d '[:space:]')

# Debugging: Print the versions to help with troubleshooting
echo "Previous version: $PREVIOUS_VERSION"
echo "Current version: $CURRENT_VERSION"

# Check if the version has been bumped
if [[ "$PREVIOUS_VERSION" == "$CURRENT_VERSION" ]]; then
  echo "Error: The version in version.properties has not been bumped. Current version is still $CURRENT_VERSION."
  exit 1
fi

# Check if there's an entry for the current version in CHANGELOG.md
if ! grep -q "$CURRENT_VERSION" CHANGELOG.md; then
  echo "Error: No entry for version $CURRENT_VERSION found in CHANGELOG.md"
  exit 1
fi

echo "Version and CHANGELOG checks passed."
exit 0
