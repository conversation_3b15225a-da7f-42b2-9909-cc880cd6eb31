{"info": {"_postman_id": "ca6f2dd7-cf9e-4825-acbf-62eb91f78b44", "name": "PTT", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "auth", "item": [{"name": "login", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"token\", jsonData.authenticationResult[\"AccessToken\"]);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"nithin\",\n    \"password\": \"Simple2022$\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/login", "host": ["{{host}}"], "path": ["api", "login"]}}, "response": []}, {"name": "change-password", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"currentPassword\": \"Simple2020$\",\n    \"password\": \"Simple2022$\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/change-password", "host": ["{{host}}"], "path": ["api", "change-password"]}}, "response": []}, {"name": "forgot-password", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"jer<PERSON><PERSON>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/forgot-password", "host": ["{{host}}"], "path": ["api", "forgot-password"]}}, "response": []}, {"name": "confirm-forgot-password", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"jerrish\",\n    \"password\": \"#Edstem2021$\",\n    \"confirmation_code\": \"103153\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/confirm-forgot-password", "host": ["{{host}}"], "path": ["api", "confirm-forgot-password"]}}, "response": []}, {"name": "create-client", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"Nithin\",\r\n    \"email\": \"\",\r\n    \"name\": \"\",\r\n    \"clientId\": \"CR7\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/create-client", "host": ["{{host}}"], "path": ["api", "create-client"]}}, "response": []}, {"name": "respond-to-auth-challenge", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"Nithin\",\r\n    \"password\": \"Simple2022$\",\r\n    \"session\":\"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/respond-to-auth-challenge", "host": ["{{host}}"], "path": ["api", "respond-to-auth-challenge"]}}, "response": []}, {"name": "user-profile-update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "<PERSON><PERSON>", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "profilePic", "type": "file", "src": "/C:/Users/<USER>/Downloads/sample_640×426.jpeg"}]}, "url": {"raw": "{{host}}/api/profile-update", "host": ["{{host}}"], "path": ["api", "profile-update"]}}, "response": []}, {"name": "user-profile-download", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/profile-download", "host": ["{{host}}"], "path": ["api", "profile-download"]}}, "response": []}, {"name": "user-profile-details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/profile-details", "host": ["{{host}}"], "path": ["api", "profile-details"]}}, "response": []}, {"name": "logout", "request": {"auth": {"type": "bearer"}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/logout", "host": ["{{host}}"], "path": ["api", "logout"]}}, "response": []}, {"name": "refresh", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"refreshToken\":\"\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/refresh", "host": ["{{host}}"], "path": ["api", "refresh"]}}, "response": []}, {"name": "respond-to-software-token-mfa-challenge", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"mfaCode\":\"\",\r\n\"userName\":\"\",\r\n\"session\":\"\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/respond-to-software-token-mfa-challenge", "host": ["{{host}}"], "path": ["api", "respond-to-software-token-mfa-challenge"]}}, "response": []}, {"name": "verify-software-token-mfa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"userCode\":\"\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/verify-software-token-mfa", "host": ["{{host}}"], "path": ["api", "verify-software-token-mfa"]}}, "response": []}, {"name": "set-software-token-mfa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{host}}/api/set-software-token-mfa", "host": ["{{host}}"], "path": ["api", "set-software-token-mfa"]}}, "response": []}, {"name": "set-sms-mfa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"PhoneNumber\":\"\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/set-sms-mfa", "host": ["{{host}}"], "path": ["api", "set-sms-mfa"]}}, "response": []}, {"name": "respond-to-sms-mfa-challenge", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"smsMfaCode\":\"\",\r\n\"userName\":\"\",\r\n\"session\":\"\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/respond-to-sms-mfa-challenge", "host": ["{{host}}"], "path": ["api", "respond-to-sms-mfa-challenge"]}}, "response": []}, {"name": "get-user-details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/user-details", "host": ["{{host}}"], "path": ["api", "user-details"]}}, "response": []}, {"name": "get-user-list", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/list-user", "host": ["{{host}}"], "path": ["api", "list-user"]}}, "response": []}, {"name": "update-user-list", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userId\":\"ee4006bd-23ab-499a-9adc-cf7c6f195c48\",\r\n    \"confirmationStatus\":\"\",\r\n    \"role\":\"\",\r\n    \"booking\":true,\r\n    \"transaction\":true,\r\n    \"reports\":[\"weekly fortnightly reporting\",\"trust balance\"]\r\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/user-update", "host": ["{{host}}"], "path": ["api", "user-update"]}}, "response": []}]}, {"name": "client", "item": [{"name": "basic-info-upsert", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"friendlyName\": \"CR7\",\r\n    \"fullName\": \"ABC\",\r\n    \"typeOfTrustAccount\": \"61ef4e0da0d0ef7c56884319\",\r\n    \"clientId\": \"1\",\r\n    \"existingClient\": true,\r\n    \"goLiveDate\": \"2021-10-02\",\r\n    \"reuseOldBooking\": false,\r\n    \"additionChecks\": [\r\n        {\r\n            \"checkName\": \"updated\",\r\n            \"shortName\": \"string1\",\r\n            \"relatedElement\": \"string1\",\r\n            \"description\": \"string1\"\r\n        }\r\n    ],\r\n    \"address\": {\r\n        \"line1\": \"updated\",\r\n        \"line2\": \"string\",\r\n        \"line3\": \"string\",\r\n        \"town\": \"string\",\r\n        \"country\": \"string\",\r\n        \"postcode\": \"string\"\r\n    },\r\n    \"limits\":  [{\r\n        \"totalAnnualRevenue\": 12,\r\n        \"maximumNoOfClaims\": 1,\r\n        \"currency\": \"EUR\"\r\n    },{\r\n        \"totalAnnualRevenue\": 100,\r\n        \"maximumNoOfClaims\": 2,\r\n        \"currency\": \"USD\"\r\n    }],\r\n    \"frequency\": [\r\n        \"61ee2f1dcf10b991afdaf54a\"\r\n    ],\r\n    \"email\": \"<EMAIL>\",\r\n    \"pointOfContact\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/client/basic-info", "host": ["{{host}}"], "path": ["api", "client", "basic-info"]}}, "response": []}, {"name": "bank-info-upsert", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"1\",\r\n    \"data\": [\r\n        {\r\n            \"bankName\": \"updated\",\r\n            \"accountNumber\": \"string\",\r\n            \"sortCode\": \"string\",\r\n            \"currency\": \"string\",\r\n            \"iban\": \"47548\"\r\n        },\r\n        {\r\n            \"bankName\": \"updated\",\r\n            \"accountNumber\": \"string\",\r\n            \"sortCode\": \"string\",\r\n            \"currency\": \"string\",\r\n            \"iban\": \"5755\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/client/bank-account", "host": ["{{host}}"], "path": ["api", "client", "bank-account"]}}, "response": []}, {"name": "insurance-atol-info-upsert", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"1\",\r\n    \"insurance\": [\r\n        {\r\n            \"policyNumber\": \"222\",\r\n            \"provider\": \"ABCD\",\r\n            \"files\": [\r\n                \"be761366-9579-4e0d-890b-8ab1e6b82dfh\"\r\n            ],\r\n            \"supplierListFile\": \"be761366-9579-4e0d-890b-8ab1e6b82dhj\",\r\n            \"expiryDate\": \"2025-02-02\"\r\n        },\r\n        {\r\n            \"policyNumber\": \"224\",\r\n            \"provider\": \"ABC\",\r\n            \"files\": [\r\n                \"be761366-9579-4e0d-890b-8ab1e6b82d3a\",\r\n                \"be761366-9579-4e0d-890b-8ab1e6b82dgh\"\r\n            ],\r\n            \"expiryDate\": \"2024-02-07\"\r\n        }\r\n    ],\r\n    \"atol\": [\r\n        {\r\n            \"license\": \"222\",\r\n            \"startDate\": \"2020-02-02\",\r\n            \"files\": [\r\n                \"be761366-9579-4e0d-890b-8ab1e6b82dfh\",\r\n                \"be761366-9579-4e0d-890b-8ab1e6b82dhj\"\r\n            ],\r\n            \"expiryDate\": \"2025-02-02\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/client/insurance-and-atol", "host": ["{{host}}"], "path": ["api", "client", "insurance-and-atol"]}}, "response": []}, {"name": "anomalies-upsert", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"clientId\": \"1\",\n    \"anomalies\": [\n        {\n            \"anomalyId\": \"11ef4bff0a719ab106fdc14e\"\n        },\n        {\n            \"anomalyId\": \"11ef4bff0a719ab106fdc14e\",\n            \"customFieldValue\": 34\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/client/anomalies", "host": ["{{host}}"], "path": ["api", "client", "anomalies"]}}, "response": []}, {"name": "banking-columns-upsert", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"1\",\r\n    \"columns\": [\"61efc57260cc72bf21f1c441\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/client/banking-columns", "host": ["{{host}}"], "path": ["api", "client", "banking-columns"]}}, "response": []}, {"name": "claim-columns-upsert", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{  \r\n    \"clientId\": \"1\",\r\n    \"columns\": [\"61efcfb8466aef6133536960\"],\r\n    \"status\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/client/claim-columns", "host": ["{{host}}"], "path": ["api", "client", "claim-columns"]}}, "response": []}, {"name": "get-client-details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/client/64d9eb84313960553aff0096", "host": ["{{host}}"], "path": ["api", "client", "64d9eb84313960553aff0096"]}}, "response": []}, {"name": "clients-search-list", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"query\":null\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/client/search", "host": ["{{host}}"], "path": ["api", "client", "search"]}}, "response": []}, {"name": "clients-search-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"query\":null\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/client/search/export", "host": ["{{host}}"], "path": ["api", "client", "search", "export"]}}, "response": []}, {"name": "list-clients", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/client/list?claimFromTBR=", "host": ["{{host}}"], "path": ["api", "client", "list"], "query": [{"key": "claimFromTBR", "value": ""}]}}, "response": []}, {"name": "file-download", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/client/file/atol/be761366-9579-4e0d-890b-8ab1e6b82d3a", "host": ["{{host}}"], "path": ["api", "client", "file", "atol", "be761366-9579-4e0d-890b-8ab1e6b82d3a"]}}, "response": []}, {"name": "file-upload", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "Document1.pdf"}, {"key": "clientId", "value": "1", "type": "text"}, {"key": "type", "value": "insurance", "type": "text"}]}, "url": {"raw": "{{host}}/api/client/file", "host": ["{{host}}"], "path": ["api", "client", "file"]}}, "response": []}, {"name": "client-list-users", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{host}}/api/client/CR7/users", "host": ["{{host}}"], "path": ["api", "client", "CR7", "users"]}}, "response": []}, {"name": "update-client-users", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\"users\":[\"ab1c92a5-cd4a-4fd9-9bea-af53fae9cf39\"]}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/client/1/users", "host": ["{{host}}"], "path": ["api", "client", "1", "users"]}}, "response": []}, {"name": "escrow-amount", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/client/2146/escrow_amount?fileType=claim&id=12ce598bbff9a1862ca1a242", "host": ["{{host}}"], "path": ["api", "client", "2146", "escrow_amount"], "query": [{"key": "fileType", "value": "claim"}, {"key": "id", "value": "12ce598bbff9a1862ca1a242"}]}}, "response": []}, {"name": "escrow-multiplier", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/client/2146/escrow_multiplier?date=2022-09-05", "host": ["{{host}}"], "path": ["api", "client", "2146", "escrow_multiplier"], "query": [{"key": "date", "value": "2022-09-05"}]}}, "response": []}, {"name": "update-escrow-multiplier", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"date\":\"2022-09-01\",\r\n    \"multiplier\":5\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/client/2146/escrow_multiplier", "host": ["{{host}}"], "path": ["api", "client", "2146", "escrow_multiplier"]}}, "response": []}, {"name": "list-atol-standard-clients", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/client/atol-clients", "host": ["{{host}}"], "path": ["api", "client", "atol-clients"]}}, "response": []}, {"name": "authorized-signatories-update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "clientId", "value": "64d9eb84313960553aff0096", "type": "text"}, {"key": "name", "value": "<PERSON><PERSON><PERSON>", "type": "text"}, {"key": "status", "value": "Active", "type": "text"}, {"key": "signaturePic", "type": "file", "src": "/C:/Users/<USER>/Downloads/01-signature-pic.png", "disabled": true}]}, "url": {"raw": "{{host}}/api/client/auth-signatories", "host": ["{{host}}"], "path": ["api", "client", "auth-signatories"]}}, "response": []}, {"name": "max-cap-count-upsert", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "{{host}}/api/client/max-cap-count/639042b2e3dbae558a1c1714", "host": ["{{host}}"], "path": ["api", "client", "max-cap-count", "639042b2e3dbae558a1c1714"]}}, "response": []}, {"name": "upload-manuals", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "clientId", "value": "636cb9b6d412435f13cb3019", "type": "text"}, {"key": "file", "type": "file", "src": "/D:/Downloads/SHEARINGS  (1).pdf"}]}, "url": {"raw": "{{host}}/api/client/upload/manuals", "host": ["{{host}}"], "path": ["api", "client", "upload", "manuals"]}}, "response": []}, {"name": "download-manuals", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/client/download/manuals/64162de8-e763-4b1e-8197-68f137b6cfa3", "host": ["{{host}}"], "path": ["api", "client", "download", "manuals", "64162de8-e763-4b1e-8197-68f137b6cfa3"]}}, "response": []}, {"name": "get-manuals", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/client/manuals/66f3b1bc7a0edcd2147ee181", "host": ["{{host}}"], "path": ["api", "client", "manuals", "66f3b1bc7a0edcd2147ee181"]}}, "response": []}, {"name": "create-manuals-log", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userId\": \"5259e295-0f04-424c-ad42-dbe415660265\",\r\n    \"clientId\": \"66f3b1bc7a0edcd2147ee181\",\r\n    \"clientName\": \"exampleClient\",\r\n    \"activity\": \"User attempted to screenshot in\",\r\n    \"createdAt\": \"2024-11-14T10:00:00Z\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/client/create/manuals-log", "host": ["{{host}}"], "path": ["api", "client", "create", "manuals-log"]}}, "response": []}]}, {"name": "banking", "item": [{"name": "banking-search", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"query\": null,\r\n  \"client\": 2,\r\n  \"assignedTo\": \"abc\",\r\n  \"status\": \"complete\",\r\n  \"date\": \"2021-12-13\",\r\n  \"page\": 6,\r\n  \"size\": 5\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/banking/search", "host": ["{{host}}"], "path": ["api", "banking", "search"]}}, "response": []}, {"name": "banking-details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/banking/details/123", "host": ["{{host}}"], "path": ["api", "banking", "details", "123"]}}, "response": []}, {"name": "banking-payments", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/banking/payments/625f9719c4b57cdb24ef5e4d?query=&page=&size=18&sortKey=currencyCode&sortOrder=1", "host": ["{{host}}"], "path": ["api", "banking", "payments", "625f9719c4b57cdb24ef5e4d"], "query": [{"key": "query", "value": ""}, {"key": "page", "value": ""}, {"key": "size", "value": "18"}, {"key": "sortKey", "value": "currencyCode"}, {"key": "sortOrder", "value": "1"}]}}, "response": []}, {"name": "banking-upload-presigned-url", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);\r", "postman.setEnvironmentVariable(\"bankingPresignedUrl\", jsonData.presignedUrl);\r", "postman.setEnvironmentVariable(\"bankingFileId\", jsonData.fileId);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/banking/upload/presigned-url?clientId=62a810a2f5043cdcdf1f00ca&fileName=********-XYZ-Banking_2", "host": ["{{host}}"], "path": ["api", "banking", "upload", "presigned-url"], "query": [{"key": "clientId", "value": "62a810a2f5043cdcdf1f00ca"}, {"key": "fileName", "value": "********-XYZ-Banking_2"}]}}, "response": []}, {"name": "banking-upload-presigned-url", "request": {"method": "PUT", "header": [], "body": {"mode": "file", "file": {"src": "********-XYZ-Banking_1.xlsx"}}, "url": {"raw": "{{bankingPresignedUrl}}", "host": ["{{bankingPresignedUrl}}"]}}, "response": []}, {"name": "banking-upload", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"62a810a2f5043cdcdf1f00ca\",\r\n    \"fileName\": \"********-XYZ-Banking_2\",\r\n    \"fileId\": \"{{bankingFileId}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/banking/upload", "host": ["{{host}}"], "path": ["api", "banking", "upload"]}}, "response": []}, {"name": "banking-update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"bankingId\": \"6204c4e01eed04813999f21f\",\r\n  \"notes\": \"string1\",\r\n  \"status\": \"string\",\r\n  \"assignedTo\": \"6204d9d61eed04813999f227\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/banking/update", "host": ["{{host}}"], "path": ["api", "banking", "update"]}}, "response": []}, {"name": "banking-anomalies", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/banking/anomalies/625f965bc4b57cdb24ef5e4b?query=&sortKey=status&sortOrder=1&page=&size=", "host": ["{{host}}"], "path": ["api", "banking", "anomalies", "625f965bc4b57cdb24ef5e4b"], "query": [{"key": "query", "value": ""}, {"key": "sortKey", "value": "status"}, {"key": "sortOrder", "value": "1"}, {"key": "page", "value": ""}, {"key": "size", "value": ""}]}}, "response": []}, {"name": "banking-payments-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/banking/payments/625f9719c4b57cdb24ef5e4d/export?query=&sortKey=&sortOrder=", "host": ["{{host}}"], "path": ["api", "banking", "payments", "625f9719c4b57cdb24ef5e4d", "export"], "query": [{"key": "query", "value": ""}, {"key": "sortKey", "value": ""}, {"key": "sortOrder", "value": ""}]}}, "response": []}, {"name": "banking-anomalies-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/banking/anomalies/625f965bc4b57cdb24ef5e4b/export?query=&sortKey=status&sortOrder=1", "host": ["{{host}}"], "path": ["api", "banking", "anomalies", "625f965bc4b57cdb24ef5e4b", "export"], "query": [{"key": "query", "value": ""}, {"key": "sortKey", "value": "status"}, {"key": "sortOrder", "value": "1"}]}}, "response": []}, {"name": "banking-search-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"query\": null,\r\n  \"client\": 2,\r\n  \"assignedTo\": 12,\r\n  \"status\": \"complete\",\r\n  \"date\": \"2021-12-13\",\r\n  \"page\": 6,\r\n  \"size\": 5\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/banking/search/export", "host": ["{{host}}"], "path": ["api", "banking", "search", "export"]}}, "response": []}, {"name": "banking-transaction-update-check", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"amount\":2500,\r\n    \"status\":\"Cancelled\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/banking/transaction/623c1a34a93d253120cb3fc0", "host": ["{{host}}"], "path": ["api", "banking", "transaction", "623c1a34a93d253120cb3fc0"]}}, "response": []}, {"name": "banking-get-transaction", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/banking/get-transaction/623c1a34a93d253120cb3fc0", "host": ["{{host}}"], "path": ["api", "banking", "get-transaction", "623c1a34a93d253120cb3fc0"]}}, "response": []}, {"name": "banking-get-transaction-suppliername", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/banking/get-transaction?supplierName=f0be81e0-72a5-4234-92ad-85f5a9292&clientId=62fb53eb8af7efee9e56e08d", "host": ["{{host}}"], "path": ["api", "banking", "get-transaction"], "query": [{"key": "supplierName", "value": "f0be81e0-72a5-4234-92ad-85f5a9292"}, {"key": "clientId", "value": "62fb53eb8af7efee9e56e08d"}]}}, "response": []}]}, {"name": "booking", "item": [{"name": "booking-claim-checks", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"2\",\r\n    \"transactionId\": \"61f3614d14dfe6ab93037133\",\r\n    \"checks\": [\r\n        {\r\n            \"_id\": \"61f3614d14dfe6ab9303719f\",\r\n            \"description\": \"string\",\r\n            \"selected\": true\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/booking/claim-checks", "host": ["{{host}}"], "path": ["api", "booking", "claim-checks"]}}, "response": []}, {"name": "booking-search", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"clientId\": \"5\",\n    \"bookingReference\": \"string\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/booking/search", "host": ["{{host}}"], "path": ["api", "booking", "search"]}}, "response": []}, {"name": "booking-payments", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"clientId\": \"4\",\n    \"bookingReference\": \"string\",\n    \"query\":\"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/booking/payments", "host": ["{{host}}"], "path": ["api", "booking", "payments"]}}, "response": []}, {"name": "booking-claims", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"1\",\r\n    \"bookingReference\": \"string\",\r\n    \"query\":\"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/booking/claims", "host": ["{{host}}"], "path": ["api", "booking", "claims"]}}, "response": []}, {"name": "booking-claim-checks", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"2\",\r\n    \"transactionId\": \"61f3614d14dfe6ab93037177\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/booking/claim-checks", "host": ["{{host}}"], "path": ["api", "booking", "claim-checks"]}}, "response": []}, {"name": "booking-anomalies", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"2\",\r\n    \"bookingReference\": \"string\",\r\n    \"query\":\"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/booking/anomalies", "host": ["{{host}}"], "path": ["api", "booking", "anomalies"]}}, "response": []}, {"name": "booking-payments-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"4\",\r\n    \"bookingReference\": \"string\",\r\n    \"query\":\"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/booking/payments/export", "host": ["{{host}}"], "path": ["api", "booking", "payments", "export"]}}, "response": []}, {"name": "booking-claims-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"1\",\r\n    \"bookingReference\": \"string\",\r\n    \"query\":\"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/booking/claims/export", "host": ["{{host}}"], "path": ["api", "booking", "claims", "export"]}}, "response": []}, {"name": "booking-anomalies-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"2\",\r\n    \"bookingReference\": \"string\",\r\n    \"query\":\"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/booking/anomalies/export", "host": ["{{host}}"], "path": ["api", "booking", "anomalies", "export"]}}, "response": []}, {"name": "booking-update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"Test\",\r\n    \"bookingRef\": \"977957\",\r\n    \"leadPax\": \"<PERSON>\",\r\n    \"departureDate\":\"2220/12/14\",\r\n    \"bookingDate\":\"2220-12-14\",\r\n    \"returnDate\":\"2220-12-14\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/booking/update", "host": ["{{host}}"], "path": ["api", "booking", "update"]}}, "response": []}]}, {"name": "lookup", "item": [{"name": "lookup-anomalies", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/lookup/anomalies/639042b2e3dbae558a1c1714", "host": ["{{host}}"], "path": ["api", "lookup", "anomalies", "639042b2e3dbae558a1c1714"]}}, "response": []}, {"name": "lookup-trust-types", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/lookup/trust-types", "host": ["{{host}}"], "path": ["api", "lookup", "trust-types"]}}, "response": []}, {"name": "lookup-frequencies", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/lookup/frequencies?clientId=1", "host": ["{{host}}"], "path": ["api", "lookup", "frequencies"], "query": [{"key": "clientId", "value": "1"}]}}, "response": []}, {"name": "lookup-banking-columns", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/lookup/banking-columns", "host": ["{{host}}"], "path": ["api", "lookup", "banking-columns"]}}, "response": []}, {"name": "lookup-claim-columns", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/lookup/claim-columns", "host": ["{{host}}"], "path": ["api", "lookup", "claim-columns"]}}, "response": []}, {"name": "lookup-default-checks", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/lookup/default-checks", "host": ["{{host}}"], "path": ["api", "lookup", "default-checks"]}}, "response": []}, {"name": "lookup-currencies", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/lookup/currency-list?clientId=CR7", "host": ["{{host}}"], "path": ["api", "lookup", "currency-list"], "query": [{"key": "clientId", "value": "CR7"}]}}, "response": []}, {"name": "lookup-list-admin", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/lookup/list-admin", "host": ["{{host}}"], "path": ["api", "lookup", "list-admin"]}}, "response": []}, {"name": "lookup-list-client", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/lookup/list-client", "host": ["{{host}}"], "path": ["api", "lookup", "list-client"]}}, "response": []}, {"name": "claim-column-mapping-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/lookup/claim-column/export", "host": ["{{host}}"], "path": ["api", "lookup", "claim-column", "export"]}}, "response": []}, {"name": "banking-column-mapping-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/lookup/banking-column/export", "host": ["{{host}}"], "path": ["api", "lookup", "banking-column", "export"]}}, "response": []}, {"name": "lookup-claim-elements", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/lookup/claim-elements", "host": ["{{host}}"], "path": ["api", "lookup", "claim-elements"]}}, "response": []}]}, {"name": "claim", "item": [{"name": "claim-upload", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"62a810a2f5043cdcdf1f00ca\",\r\n    \"fileName\": \"********-XYZ-Claim_2\",\r\n    \"fileId\": \"{{claimFileId}}\",\r\n    \"claimFromTBR\" : \"false\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/claim/upload", "host": ["{{host}}"], "path": ["api", "claim", "upload"]}}, "response": []}, {"name": "claim-transaction", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/transaction/625f96b0c4b57cdb24ef5e4c?query=&page=2&size=&sortKey=element&sortOrder=-1", "host": ["{{host}}"], "path": ["api", "claim", "transaction", "625f96b0c4b57cdb24ef5e4c"], "query": [{"key": "query", "value": ""}, {"key": "page", "value": "2"}, {"key": "size", "value": ""}, {"key": "sortKey", "value": "element"}, {"key": "sortOrder", "value": "-1"}]}}, "response": []}, {"name": "claim-details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/details/62028d78054d195872ee8082", "host": ["{{host}}"], "path": ["api", "claim", "details", "62028d78054d195872ee8082"]}}, "response": []}, {"name": "claim-search-latest", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\"query\": null,\r\n\"client\": 1,\r\n\"status\": null,\r\n\"assignedTo\": \"abc\",\r\n\"fromDate\": \"2021-12-03\",\r\n\"toDate\": \"2022-02-04\",\r\n\"page\": 1,\r\n\"size\": 4\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/claim/search/latest", "host": ["{{host}}"], "path": ["api", "claim", "search", "latest"]}}, "response": []}, {"name": "claim-search-summary", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\"query\": null,\r\n\"client\": 2,\r\n\"assignedTo\": \"abc\",\r\n\"fromDate\": \"2021-12-03\",\r\n\"toDate\": \"2022-02-04\",\r\n\"page\": 1,\r\n\"size\": 4\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/claim/search/summary", "host": ["{{host}}"], "path": ["api", "claim", "search", "summary"]}}, "response": []}, {"name": "claim-summary", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/summary/625f96b0c4b57cdb24ef5e4c?query=&sortKey=maxAmount&sortOrder=", "host": ["{{host}}"], "path": ["api", "claim", "summary", "625f96b0c4b57cdb24ef5e4c"], "query": [{"key": "query", "value": ""}, {"key": "sortKey", "value": "maxAmount"}, {"key": "sortOrder", "value": ""}]}}, "response": []}, {"name": "claim-update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"claimId\": \"62023b691eed04813999f201\",\r\n  \"notes\": \"string2\",\r\n  \"assignedTo\": \"6204d9d61eed04813999f227\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/claim/update", "host": ["{{host}}"], "path": ["api", "claim", "update"]}}, "response": []}, {"name": "claim_check_type", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/quick_check/62037d1ac57a15c58fa5f8bf", "host": ["{{host}}"], "path": ["api", "claim", "quick_check", "62037d1ac57a15c58fa5f8bf"]}}, "response": []}, {"name": "claim-transaction-update-check", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"check\" : \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/claim/transaction/620b75d59b53aa92cd5dbb1a", "host": ["{{host}}"], "path": ["api", "claim", "transaction", "620b75d59b53aa92cd5dbb1a"]}}, "response": []}, {"name": "claim-automated-transaction", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/automated-transaction/620c7c619924a87c46042436", "host": ["{{host}}"], "path": ["api", "claim", "automated-transaction", "620c7c619924a87c46042436"]}}, "response": []}, {"name": "claim-anomalies", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/anomalies/625f96b0c4b57cdb24ef5e4c?query=&sortKey=createdAt&sortOrder=-1&page=&size=", "host": ["{{host}}"], "path": ["api", "claim", "anomalies", "625f96b0c4b57cdb24ef5e4c"], "query": [{"key": "query", "value": ""}, {"key": "sortKey", "value": "createdAt"}, {"key": "sortOrder", "value": "-1"}, {"key": "page", "value": ""}, {"key": "size", "value": ""}]}}, "response": []}, {"name": "claim-get-transaction", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/get-transaction/61fb95ede377dce36a47edc5", "host": ["{{host}}"], "path": ["api", "claim", "get-transaction", "61fb95ede377dce36a47edc5"]}}, "response": []}, {"name": "claim-summary-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/summary/621c38cae22a9303b9b091b9/export?query=&sortKey=maxAmount&sortOrder=", "host": ["{{host}}"], "path": ["api", "claim", "summary", "621c38cae22a9303b9b091b9", "export"], "query": [{"key": "query", "value": ""}, {"key": "sortKey", "value": "maxAmount"}, {"key": "sortOrder", "value": ""}]}}, "response": []}, {"name": "claim-search-latest-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\"query\": null,\r\n\"status\": null,\r\n\"assignedTo\": 1,\r\n\"fromDate\": \"2021-12-03\",\r\n\"toDate\": \"2022-02-04\",\r\n\"page\": 1,\r\n\"size\": 4\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/claim/search/latest/export", "host": ["{{host}}"], "path": ["api", "claim", "search", "latest", "export"]}}, "response": []}, {"name": "claim-search-summary-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\"query\": null,\r\n\"fromDate\": \"2021-12-03\",\r\n\"toDate\": \"2022-02-04\",\r\n\"page\": 1,\r\n\"size\": 4\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/claim/search/summary/export", "host": ["{{host}}"], "path": ["api", "claim", "search", "summary", "export"]}}, "response": []}, {"name": "claim-transaction-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/transaction/625f96b0c4b57cdb24ef5e4c/export?query=&sortKey=element&sortOrder=-1", "host": ["{{host}}"], "path": ["api", "claim", "transaction", "625f96b0c4b57cdb24ef5e4c", "export"], "query": [{"key": "query", "value": ""}, {"key": "sortKey", "value": "element"}, {"key": "sortOrder", "value": "-1"}]}}, "response": []}, {"name": "claim_check_type-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/quick-check/621c38cae22a9303b9b091b9/export?query=", "host": ["{{host}}"], "path": ["api", "claim", "quick-check", "621c38cae22a9303b9b091b9", "export"], "query": [{"key": "query", "value": ""}]}}, "response": []}, {"name": "claim-automated-transaction-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/automated-transaction/621c38cae22a9303b9b091b9/export?query=", "host": ["{{host}}"], "path": ["api", "claim", "automated-transaction", "621c38cae22a9303b9b091b9", "export"], "query": [{"key": "query", "value": ""}]}}, "response": []}, {"name": "claim-anomalies-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/anomalies/625f96b0c4b57cdb24ef5e4c/export?query=&sortKey=createdAt&sortOrder=-1", "host": ["{{host}}"], "path": ["api", "claim", "anomalies", "625f96b0c4b57cdb24ef5e4c", "export"], "query": [{"key": "query", "value": ""}, {"key": "sortKey", "value": "createdAt"}, {"key": "sortOrder", "value": "-1"}]}}, "response": []}, {"name": "claim-upload-presigned-url", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);\r", "postman.setEnvironmentVariable(\"claimPresignedUrl\", jsonData.presignedUrl);\r", "postman.setEnvironmentVariable(\"claimFileId\", jsonData.fileId);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/claim/upload/presigned-url?clientId=&fileName=&claimFromTBR=", "host": ["{{host}}"], "path": ["api", "claim", "upload", "presigned-url"], "query": [{"key": "clientId", "value": ""}, {"key": "fileName", "value": ""}, {"key": "claimFromTBR", "value": ""}]}}, "response": []}, {"name": "claim-upload-presigned-url", "request": {"method": "PUT", "header": [], "body": {"mode": "file", "file": {}}, "url": {"raw": "{{claimPresignedUrl}}", "host": ["{{claimPresignedUrl}}"]}}, "response": []}, {"name": "claim-testing", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\"query\": \"\",\r\n\"client\": \"\",\r\n\"fromDate\": \"\",\r\n\"toDate\": \"\",\r\n\"date\" : \"\",\r\n\"page\": 1,\r\n\"size\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/claim/claim-testing", "host": ["{{host}}"], "path": ["api", "claim", "claim-testing"]}}, "response": []}, {"name": "claim-testing-update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"revisedClaim\": \"12344\",\r\n  \"reasons\" : \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/claim/claim-testing/62ce598bbff9a1862ca1a241", "host": ["{{host}}"], "path": ["api", "claim", "claim-testing", "62ce598bbff9a1862ca1a241"]}}, "response": []}, {"name": "claim-testing-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\"query\":\"\",\r\n\"fromDate\": \"\",\r\n\"toDate\": \"2020-10-27\",\r\n\"page\": 1,\r\n\"size\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/claim/claim-testing/export", "host": ["{{host}}"], "path": ["api", "claim", "claim-testing", "export"]}}, "response": []}, {"name": "claim-search-summary-escrow", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\"fromDate\": \"2022-03-05\",\r\n\"toDate\": \"2022-11-04\",\r\n\"page\":1,\r\n\"size\":10\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/claim/search/summary_escrow", "host": ["{{host}}"], "path": ["api", "claim", "search", "summary_escrow"]}}, "response": []}, {"name": "update-notes", "request": {"auth": {"type": "bearer"}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"notes\":\"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/claim/edit_notes/623c1a14c1886d1dd0e6f186/fd6d25e2-632c-4e7b-92ea-71ce9cca5f0e_2023-05-03T11:34:33.949211", "host": ["{{host}}"], "path": ["api", "claim", "edit_notes", "623c1a14c1886d1dd0e6f186", "fd6d25e2-632c-4e7b-92ea-71ce9cca5f0e_2023-05-03T11:34:33.949211"]}}, "response": []}]}, {"name": "anomalies", "item": [{"name": "anomalies-banking", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"1\",\r\n    \"query\": \"str\",\r\n    \"fromDate\": \"\",\r\n    \"toDate\": \"\",\r\n    \"page\": \"\",\r\n    \"size\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/anomaly/banking", "host": ["{{host}}"], "path": ["api", "anomaly", "banking"]}}, "response": []}, {"name": "anomalies-claim", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"1\",\r\n    \"query\": \"\",\r\n    \"fromDate\": \"\",\r\n    \"toDate\": \"\",\r\n    \"page\": \"\",\r\n    \"size\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/anomaly/claims", "host": ["{{host}}"], "path": ["api", "anomaly", "claims"]}}, "response": []}, {"name": "anomaly-status-update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"anomalyId\": \"620a7ff59514d15459f4fdd8\",\r\n  \"status\": \"done\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/anomaly/claim/update-status", "host": ["{{host}}"], "path": ["api", "anomaly", "claim", "update-status"]}}, "response": []}, {"name": "anomalies-banking-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"1\",\r\n    \"query\": \"str\",\r\n    \"fromDate\": \"\",\r\n    \"toDate\": \"\",\r\n    \"page\": \"\",\r\n    \"size\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/anomaly/banking/export", "host": ["{{host}}"], "path": ["api", "anomaly", "banking", "export"]}}, "response": []}, {"name": "anomalies-claim-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"1\",\r\n    \"query\": \"\",\r\n    \"fromDate\": \"\",\r\n    \"toDate\": \"\",\r\n    \"page\": \"\",\r\n    \"size\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/anomaly/claims/export", "host": ["{{host}}"], "path": ["api", "anomaly", "claims", "export"]}}, "response": []}]}, {"name": "reports", "item": [{"name": "trust-balance-report", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/trust-balance?client=a11&page=&size=&currency=GBP", "host": ["{{host}}"], "path": ["api", "reports", "trust-balance"], "query": [{"key": "client", "value": "a11"}, {"key": "page", "value": ""}, {"key": "size", "value": ""}, {"key": "currency", "value": "GBP"}]}}, "response": []}, {"name": "trust-balance-report-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/trust-balance/export?client=AA123&page=1&size=10", "host": ["{{host}}"], "path": ["api", "reports", "trust-balance", "export"], "query": [{"key": "client", "value": "AA123"}, {"key": "page", "value": "1"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "client-files-report", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/client-files?client=TU123&fromDate=2022-02-24&toDate=2022-02-24&page=1&size=3", "host": ["{{host}}"], "path": ["api", "reports", "client-files"], "query": [{"key": "client", "value": "TU123"}, {"key": "fromDate", "value": "2022-02-24"}, {"key": "toDate", "value": "2022-02-24"}, {"key": "page", "value": "1"}, {"key": "size", "value": "3"}]}}, "response": []}, {"name": "client-files-report-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/client-files/export?client=TU123&fromDate=2022-02-24&toDate=2022-02-24&page=1&size=3", "host": ["{{host}}"], "path": ["api", "reports", "client-files", "export"], "query": [{"key": "client", "value": "TU123"}, {"key": "fromDate", "value": "2022-02-24"}, {"key": "toDate", "value": "2022-02-24"}, {"key": "page", "value": "1"}, {"key": "size", "value": "3"}]}}, "response": []}, {"name": "weekly-fortnightly-report", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/weekly-fortnightly?client=64dc9e37d32e81bfb57eb1c1&currency=GBP&fromDate=2023-08-26&toDate=2023-09-01", "host": ["{{host}}"], "path": ["api", "reports", "weekly-fortnightly"], "query": [{"key": "client", "value": "64dc9e37d32e81bfb57eb1c1"}, {"key": "currency", "value": "GBP"}, {"key": "fromDate", "value": "2023-08-26"}, {"key": "toDate", "value": "2023-09-01"}]}}, "response": []}, {"name": "weekly-fortnightly-report-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/weekly-fortnightly/export?client=64bfbf515fbed2a12be3f567&currency=GBP&fromDate=2023-04-01&toDate=2023-04-30", "host": ["{{host}}"], "path": ["api", "reports", "weekly-fortnightly", "export"], "query": [{"key": "client", "value": "64bfbf515fbed2a12be3f567"}, {"key": "currency", "value": "GBP"}, {"key": "fromDate", "value": "2023-04-01"}, {"key": "toDate", "value": "2023-04-30"}]}}, "response": []}, {"name": "bank-reconciliation-statement-report", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/bank-reconciliation?client=639042afe3dbae558a1c15da&currency=GBP&fromDate=2023-07-01&toDate=2023-07-31&accountingStatement=4&bankName=HSBC Business", "host": ["{{host}}"], "path": ["api", "reports", "bank-reconciliation"], "query": [{"key": "client", "value": "639042afe3dbae558a1c15da"}, {"key": "currency", "value": "GBP"}, {"key": "fromDate", "value": "2023-07-01"}, {"key": "toDate", "value": "2023-07-31"}, {"key": "accountingStatement", "value": "4"}, {"key": "bankName", "value": "HSBC Business"}]}}, "response": []}, {"name": "bank-reconciliation-statement-report-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/bank-reconciliation/export?client=64a502981ea263b803b821cb&currency=GBP&fromDate=2021-02-22&toDate=2023-03-22&accountingStatement=3", "host": ["{{host}}"], "path": ["api", "reports", "bank-reconciliation", "export"], "query": [{"key": "client", "value": "64a502981ea263b803b821cb"}, {"key": "currency", "value": "GBP"}, {"key": "fromDate", "value": "2021-02-22"}, {"key": "toDate", "value": "2023-03-22"}, {"key": "accountingStatement", "value": "3"}, {"key": "", "value": "", "disabled": true}]}}, "response": []}, {"name": "movements-of-funds", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/movement-of-funds?toDate=2022-02-24&fromDate=2022-01-24&clientId=ED123", "host": ["{{host}}"], "path": ["api", "reports", "movement-of-funds"], "query": [{"key": "toDate", "value": "2022-02-24"}, {"key": "fromDate", "value": "2022-01-24"}, {"key": "clientId", "value": "ED123"}]}}, "response": []}, {"name": "movements-of-funds-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/movement-of-funds/export?toDate=2022-02-24&fromDate=2022-01-24&clientId=ED123", "host": ["{{host}}"], "path": ["api", "reports", "movement-of-funds", "export"], "query": [{"key": "toDate", "value": "2022-02-24"}, {"key": "fromDate", "value": "2022-01-24"}, {"key": "clientId", "value": "ED123"}]}}, "response": []}, {"name": "banking-claim-summary-report", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/banking-claim-summary?client=CR7&fromDate=2022-01-01&toDate=2023-03-05&page=1&size=100", "host": ["{{host}}"], "path": ["api", "reports", "banking-claim-summary"], "query": [{"key": "client", "value": "CR7"}, {"key": "fromDate", "value": "2022-01-01"}, {"key": "toDate", "value": "2023-03-05"}, {"key": "page", "value": "1"}, {"key": "size", "value": "100"}]}}, "response": []}, {"name": "banking-claim-summary-report-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/banking-claim-summary/export?client=CR7&fromDate=2022-01-01&toDate=2022-05-01", "host": ["{{host}}"], "path": ["api", "reports", "banking-claim-summary", "export"], "query": [{"key": "client", "value": "CR7"}, {"key": "fromDate", "value": "2022-01-01"}, {"key": "toDate", "value": "2022-05-01"}]}}, "response": []}, {"name": "compliance-computation-report", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/compliance-computation?client=2146&currency=INR&fromDate=2020-10-27&toDate=2020-10-30", "host": ["{{host}}"], "path": ["api", "reports", "compliance-computation"], "query": [{"key": "client", "value": "2146"}, {"key": "currency", "value": "INR"}, {"key": "fromDate", "value": "2020-10-27"}, {"key": "toDate", "value": "2020-10-30"}]}}, "response": []}, {"name": "compliance-computation-report-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/compliance-computation/export?client=2146&currency=GBP&fromDate=2020-10-27&toDate=2020-10-30", "host": ["{{host}}"], "path": ["api", "reports", "compliance-computation", "export"], "query": [{"key": "client", "value": "2146"}, {"key": "currency", "value": "GBP"}, {"key": "fromDate", "value": "2020-10-27"}, {"key": "toDate", "value": "2020-10-30"}]}}, "response": []}, {"name": "report-files", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"63354791283b199c4c81746c\",\r\n    \"currency\": \"\",\r\n    \"fileType\": \"xlsx\",\r\n    \"name\": \"TrustBalance\"\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/reports/report-files", "host": ["{{host}}"], "path": ["api", "reports", "report-files"]}}, "response": []}, {"name": "atol-renewal-tracker-list", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/atol-renewal-tracker-list?client=633e6670102c602adb09c933", "host": ["{{host}}"], "path": ["api", "reports", "atol-renewal-tracker-list"], "query": [{"key": "client", "value": "633e6670102c602adb09c933"}]}}, "response": []}, {"name": "atol-renewal-tracker-list-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/atol-renewal-tracker-list/export?client=633e6670102c602adb09c933", "host": ["{{host}}"], "path": ["api", "reports", "atol-renewal-tracker-list", "export"], "query": [{"key": "client", "value": "633e6670102c602adb09c933"}]}}, "response": []}, {"name": "insurance-renewal-tracker-list", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/insurance-renewal-tracker-list?client=62419f08888c2b5ed06235d9", "host": ["{{host}}"], "path": ["api", "reports", "insurance-renewal-tracker-list"], "query": [{"key": "client", "value": "62419f08888c2b5ed06235d9"}]}}, "response": []}, {"name": "insurance-renewal-tracker-list-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/insurance-renewal-tracker-list/export?client=62419f08888c2b5ed06235d9", "host": ["{{host}}"], "path": ["api", "reports", "insurance-renewal-tracker-list", "export"], "query": [{"key": "client", "value": "62419f08888c2b5ed06235d9"}]}}, "response": []}, {"name": "daily-agent-exceptions-report", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/daily-agent-exceptions?client=62fb53eb8af7efee9e56e08d&page=1&size=10&currency=GBP", "host": ["{{host}}"], "path": ["api", "reports", "daily-agent-exceptions"], "query": [{"key": "client", "value": "62fb53eb8af7efee9e56e08d"}, {"key": "page", "value": "1"}, {"key": "size", "value": "10"}, {"key": "currency", "value": "GBP"}]}}, "response": []}, {"name": "daily-agent-exceptions-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/daily-agent-exceptions/export?client=62fb53eb8af7efee9e56e08d&page=1&size=10&currency=GBP", "host": ["{{host}}"], "path": ["api", "reports", "daily-agent-exceptions", "export"], "query": [{"key": "client", "value": "62fb53eb8af7efee9e56e08d"}, {"key": "page", "value": "1"}, {"key": "size", "value": "10"}, {"key": "currency", "value": "GBP"}]}}, "response": []}, {"name": "tbr-sftp-upload", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/trust-balance/sftp-upload?type=xlsx&client=62ce5983bff9a1862ca13424&fileId=0474d39f-ed92-4d37-8f2e-82a66fed8ec9.xlsx", "host": ["{{host}}"], "path": ["api", "reports", "trust-balance", "sftp-upload"], "query": [{"key": "type", "value": "xlsx"}, {"key": "client", "value": "62ce5983bff9a1862ca13424"}, {"key": "fileId", "value": "0474d39f-ed92-4d37-8f2e-82a66fed8ec9.xlsx"}]}}, "response": []}, {"name": "banking-summary-export", "request": {"method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/banking-summary/export?client=64d9eb84313960553aff0096&currency=GBP&fromDate=2023-05-01&toDate=2023-05-31&bankName=HSBC Business", "host": ["{{host}}"], "path": ["api", "reports", "banking-summary", "export"], "query": [{"key": "client", "value": "64d9eb84313960553aff0096"}, {"key": "currency", "value": "GBP"}, {"key": "fromDate", "value": "2023-05-01"}, {"key": "toDate", "value": "2023-05-31"}, {"key": "bankName", "value": "HSBC Business"}]}}, "response": []}, {"name": "HSBC-bank-reconciliation-statement-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/reports/hsbc-bank-reconciliation/export?client=65e9566ed324a4c7c6d807a5&currency=GBP&fromDate=2024-07-01&toDate=2024-07-31&accountingStatement=1&bankName=HSBC Business", "host": ["{{host}}"], "path": ["api", "reports", "hsbc-bank-reconciliation", "export"], "query": [{"key": "client", "value": "65e9566ed324a4c7c6d807a5"}, {"key": "currency", "value": "GBP"}, {"key": "fromDate", "value": "2024-07-01"}, {"key": "toDate", "value": "2024-07-31"}, {"key": "accountingStatement", "value": "1"}, {"key": "bankName", "value": "HSBC Business"}]}}, "response": []}]}, {"name": "dashboard", "item": [{"name": "ytd-client", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/clients?year=2022", "host": ["{{host}}"], "path": ["api", "dashboard", "clients"], "query": [{"key": "year", "value": "2022"}]}}, "response": []}, {"name": "ytd-booking", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/bookings?clientId=CR7&currency=GBP&year=2022", "host": ["{{host}}"], "path": ["api", "dashboard", "bookings"], "query": [{"key": "clientId", "value": "CR7"}, {"key": "currency", "value": "GBP"}, {"key": "year", "value": "2022"}]}}, "response": []}, {"name": "ytd-payments", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/payments?year=2022&clientId=CR7&currency=GBP", "host": ["{{host}}"], "path": ["api", "dashboard", "payments"], "query": [{"key": "year", "value": "2022"}, {"key": "clientId", "value": "CR7"}, {"key": "currency", "value": "GBP"}]}}, "response": []}, {"name": "dashboard_details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/details?currency=EUR", "host": ["{{host}}"], "path": ["api", "dashboard", "details"], "query": [{"key": "currency", "value": "EUR"}]}}, "response": []}, {"name": "ytd-claims", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/claims?year=2022&clientId=CR7&currency=GBP", "host": ["{{host}}"], "path": ["api", "dashboard", "claims"], "query": [{"key": "year", "value": "2022"}, {"key": "clientId", "value": "CR7"}, {"key": "currency", "value": "GBP"}]}}, "response": []}, {"name": "clients-category", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/clients-category", "host": ["{{host}}"], "path": ["api", "dashboard", "clients-category"]}}, "response": []}, {"name": "risk-exposure-refresh", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/risk-exposure/refresh", "host": ["{{host}}"], "path": ["api", "dashboard", "risk-exposure", "refresh"]}}, "response": []}, {"name": "risk-exposure", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/risk-exposure?currency=NOK&fromDate=2022-12-12&client=62fb53f02ce2345143250f96", "host": ["{{host}}"], "path": ["api", "dashboard", "risk-exposure"], "query": [{"key": "currency", "value": "NOK"}, {"key": "fromDate", "value": "2022-12-12"}, {"key": "client", "value": "62fb53f02ce2345143250f96"}]}}, "response": []}, {"name": "risk-exposure-status", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/risk-exposure/status", "host": ["{{host}}"], "path": ["api", "dashboard", "risk-exposure", "status"]}}, "response": []}, {"name": "status-exposure-refresh", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/exposure-status/refresh", "host": ["{{host}}"], "path": ["api", "dashboard", "exposure-status", "refresh"], "query": [{"key": "clientId", "value": "", "disabled": true}, {"key": "currency", "value": "", "disabled": true}]}}, "response": []}, {"name": "movements-of-funds", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/movement-of-funds?currency=USD&clientId=62419f08888c2b5ed06235d9&fromDate=&toDate=", "host": ["{{host}}"], "path": ["api", "dashboard", "movement-of-funds"], "query": [{"key": "currency", "value": "USD"}, {"key": "clientId", "value": "62419f08888c2b5ed06235d9"}, {"key": "fromDate", "value": ""}, {"key": "toDate", "value": ""}]}}, "response": []}, {"name": "exposure-status", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/exposure-status?client=63a3e8ee12e0defb392be622&currency=GBP", "host": ["{{host}}"], "path": ["api", "dashboard", "exposure-status"], "query": [{"key": "client", "value": "63a3e8ee12e0defb392be622"}, {"key": "currency", "value": "GBP"}]}}, "response": []}, {"name": "exposure-status-status", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "upload-transactions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/uploaded-transactions?fromDate=2023-01-01&toDate=2023-01-12&client=", "host": ["{{host}}"], "path": ["api", "dashboard", "uploaded-transactions"], "query": [{"key": "fromDate", "value": "2023-01-01"}, {"key": "toDate", "value": "2023-01-12"}, {"key": "client", "value": ""}]}}, "response": []}, {"name": "expiry", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/dashboard/expiry?client=62ce5983bff9a1862ca13424&currency=GBP&fromDate=2022-02-01&toDate=2022-02-10", "host": ["{{host}}"], "path": ["api", "dashboard", "expiry"], "query": [{"key": "client", "value": "62ce5983bff9a1862ca13424"}, {"key": "currency", "value": "GBP"}, {"key": "fromDate", "value": "2022-02-01"}, {"key": "toDate", "value": "2022-02-10"}]}}, "response": []}]}, {"name": "issue-log", "item": [{"name": "create-issue-log", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"clientId\":\"62fb53eb8af7efee9e56e08d\",\r\n\"opened\": \"string\",\r\n  \"shortDescription\": \"string\",\r\n  \"priority\": \"string\",\r\n  \"resolutionNotes\": \"string\",\r\n  \"status\": \"string\",\r\n  \"dateResolved\": \"2022-1-1\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/issue-log", "host": ["{{host}}"], "path": ["api", "issue-log"]}}, "response": []}, {"name": "list-issue-log", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/issue-log?page=1&size=10&query=&status=&sortOrder=1&sortKey=dateResolved&priority=&client=&from_date=&to_date=", "host": ["{{host}}"], "path": ["api", "issue-log"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}, {"key": "query", "value": ""}, {"key": "status", "value": ""}, {"key": "sortOrder", "value": "1"}, {"key": "sortKey", "value": "dateResolved"}, {"key": "priority", "value": ""}, {"key": "client", "value": ""}, {"key": "from_date", "value": ""}, {"key": "to_date", "value": ""}]}}, "response": []}, {"name": "update-issue-log", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\":\"62fb53eb8af7efee9e56e08d\",\r\n    \"opened\": \"\",\r\n    \"shortDescription\": \"\",\r\n    \"priority\": \"\",\r\n    \"resolutionNotes\": \"\",\r\n    \"status\": \"\",\r\n    \"dateResolved\": \"\",\r\n    \"deleted\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/issue-log/62fb53eb8af7efee9e56e08d", "host": ["{{host}}"], "path": ["api", "issue-log", "62fb53eb8af7efee9e56e08d"]}}, "response": []}, {"name": "list-issue-log-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/issue-log/export?page=1&size=10&query=&status=&sortOrder=1&sortKey=&priority=", "host": ["{{host}}"], "path": ["api", "issue-log", "export"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}, {"key": "query", "value": ""}, {"key": "status", "value": ""}, {"key": "sortOrder", "value": "1"}, {"key": "sortKey", "value": ""}, {"key": "priority", "value": ""}]}}, "response": []}]}, {"name": "open-banking", "item": [{"name": "auth", "item": [{"name": "get-auth-link", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/open-banking/auth-link?bankId=uk-cs-mock&redirectURI=https%3A%2F%2Fconsole.truelayer.com%2Fredirect-page", "host": ["{{host}}"], "path": ["api", "open-banking", "auth-link"], "query": [{"key": "bankId", "value": "uk-cs-mock"}, {"key": "redirectURI", "value": "https%3A%2F%2Fconsole.truelayer.com%2Fredirect-page"}, {"key": "currency", "value": "GBP", "disabled": true}]}}, "response": []}, {"name": "exchange-authorization-code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"bankId\": \"uk-cs-mock\",\n    \"authorizationCode\": \"DA848BCC6BC4E8A33A5239BD92D680B007BC2CE2CD1AC81D4E62D80FFA894190\",\n    \"redirectURI\": \"https://console.truelayer.com/redirect-page\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/open-banking/exchange-authorization-code", "host": ["{{host}}"], "path": ["api", "open-banking", "exchange-authorization-code"]}}, "response": []}]}, {"name": "summary-reports", "item": [{"name": "banking-summary", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"bank_accounts\": [{\"account_no\": \"********\", \"bank_name\": \"Santander\", \"bank_id\": \"uk-ob-santander\"}],\r\n    \"from_date\": \"2022-01-23\",\r\n    \"to_date\":\"2022-02-23\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/data/banking-summary", "host": ["{{host}}"], "path": ["api", "data", "banking-summary"]}}, "response": []}, {"name": "weekly-fornightly-summary", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"bank_accounts\": [{\"account_no\": \"********\", \"bank_name\": \"Santander\", \"bank_id\": \"uk-ob-santander\"}],\r\n    \"from_date\": \"2022-01-23\",\r\n    \"to_date\":\"2022-02-23\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/data/weekly-fortnightly-summary", "host": ["{{host}}"], "path": ["api", "data", "weekly-fortnightly-summary"]}}, "response": []}]}, {"name": "get-transactions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/open-banking/transactions?fromDate=2018-01-01&toDate=2018-03-31&clientId=63f4700b12d17cd83b51d51e&currency=GBP", "host": ["{{host}}"], "path": ["api", "open-banking", "transactions"], "query": [{"key": "fromDate", "value": "2018-01-01"}, {"key": "toDate", "value": "2018-03-31"}, {"key": "clientId", "value": "63f4700b12d17cd83b51d51e"}, {"key": "currency", "value": "GBP"}]}}, "response": []}]}, {"name": "banking-and-claim-summary", "item": [{"name": "list-files", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/banking-and-claims/list?isToday=true&size=20", "host": ["{{host}}"], "path": ["api", "banking-and-claims", "list"], "query": [{"key": "isToday", "value": "true"}, {"key": "clientId", "value": "636cb7ecb0d93d64885a4d8d", "disabled": true}, {"key": "page", "value": "1", "disabled": true}, {"key": "size", "value": "20"}]}}, "response": []}, {"name": "file-download", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/client/file/banking-and-claim-summary/268ab530-dbd1-4564-a6f2-eae097064b72_2023-09-05T07:09:42.562478", "host": ["{{host}}"], "path": ["api", "client", "file", "banking-and-claim-summary", "268ab530-dbd1-4564-a6f2-eae097064b72_2023-09-05T07:09:42.562478"]}}, "response": []}, {"name": "file-upload", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "clientId", "value": "63904aac052fef8add254e4f", "type": "text"}, {"key": "file", "type": "file", "src": "/C:/Users/<USER>/Downloads/PTT Files/Banking and Claim Summary Files/Banking and Claim Summary - Turquoise Holiday.xlsx"}]}, "url": {"raw": "{{host}}/api/banking-and-claims/upload", "host": ["{{host}}"], "path": ["api", "banking-and-claims", "upload"]}}, "response": []}, {"name": "list-files-body", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"isToday\": \"\",\r\n  \"clientId\": \"\",\r\n  \"page\": 1,\r\n  \"size\": 5\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/banking-and-claims/list", "host": ["{{host}}"], "path": ["api", "banking-and-claims", "list"], "query": [{"key": "isToday", "value": "true", "disabled": true}, {"key": "clientId", "value": "636cb7ecb0d93d64885a4d8d", "disabled": true}, {"key": "page", "value": "1", "disabled": true}, {"key": "size", "value": "10", "disabled": true}]}}, "response": []}]}, {"name": "email", "item": [{"name": "weekly-report-template", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/email/template/weekly?clientId=636cb7ecb0d93d64885a4d8d&fundIn=1000&fundOut=2000", "host": ["{{host}}"], "path": ["api", "email", "template", "weekly"], "query": [{"key": "clientId", "value": "636cb7ecb0d93d64885a4d8d"}, {"key": "fundIn", "value": "1000"}, {"key": "fundOut", "value": "2000"}]}}, "response": []}, {"name": "email-recipient-update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"to\": [\"<EMAIL>\"],\r\n    \"cc\": [\"srival<PERSON>@pttrustees.com\", \"<EMAIL>\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/email/recipients/update/64d9eb84313960553aff0096", "host": ["{{host}}"], "path": ["api", "email", "recipients", "update", "64d9eb84313960553aff0096"]}}, "response": []}, {"name": "monthly-report-template", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/email/template/monthly?clientId=636cb7ecb0d93d64885a4d8d&fromDate=2023-08-01&toDate=2023-08-31&openingBalance=1000&closingBalance=2000&bankingAmount=7000&claimAmount=1200", "host": ["{{host}}"], "path": ["api", "email", "template", "monthly"], "query": [{"key": "clientId", "value": "636cb7ecb0d93d64885a4d8d"}, {"key": "fromDate", "value": "2023-08-01"}, {"key": "toDate", "value": "2023-08-31"}, {"key": "openingBalance", "value": "1000"}, {"key": "closingBalance", "value": "2000"}, {"key": "bankingAmount", "value": "7000"}, {"key": "claimAmount", "value": "1200"}]}}, "response": []}, {"name": "send-email", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\": \"64d9eb84313960553aff0096\",\r\n    \"emailContent\": \"<!DOCTYPE html>\\n<html>\\n<head>\\n    <title>nithin</title>\\n</head>\\n<body>\\n    <p>nithin,</p>\\n    <p>As required by Clause 5.1, we confirm the following values in respect of juhaira for the Compliance Reporting Day of 19th September 2023:</p>\\n</body>\\n<head>\\n    <style>\\n        table, th, td {\\n           border-collapse: collapse;\\n           border: 1px solid black;\\n        }\\n     </style>\\n</head>\\n<body>\\n   <table style=\\\"width:50%; text-align: left;\\\">\\n      <tr>\\n         <th style=\\\"text-align: left;\\\">juhaira</th>\\n         <th></th>\\n      </tr>\\n      <tr>\\n        <th style=\\\"text-align: left;\\\">Statement of trust Account</th>\\n        <td></td>\\n\\n      </tr>\\n      <tr>\\n        <th style=\\\"text-align: left;\\\">For the Compliance Reporting day of 2023/09/19</th>\\n         <th style=\\\"text-align: left;\\\">Trust Account</th>\\n\\n      </tr>\\n      <tr style=\\\"width:50%; text-align: center;\\\">\\n        <td></td>\\n        <td>£</td>\\n\\n      </tr>\\n      <tr>\\n        <td>Total amount that has been paid into the Trustee Amount since<br>the Prior Compliance Reporting Day</td>\\n         <td style=\\\"text-align: right;\\\">1000</td>\\n\\n      </tr>\\n      <tr>\\n        <td>Total amount that has been paid out by the Trustee in response to<br>payment Request since the Prior Compliance Reporing Day</td>\\n         <td style=\\\"color: red; text-align: right;\\\">2000</td>\\n\\n      </tr>\\n\\n\\n   </table>\\n   <p>Kind regards,</p>\\n</body>\\n</html>\",\r\n    \"isMonthly\": false,\r\n    \"toDate\": \"2012-02-02\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/email/send", "host": ["{{host}}"], "path": ["api", "email", "send"]}}, "response": []}, {"name": "verify-email", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"emailAddress\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/email/verify", "host": ["{{host}}"], "path": ["api", "email", "verify"]}}, "response": []}, {"name": "email-recipients-monthly", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/email/recipients/monthly?clientId=64d9eb84313960553aff0096", "host": ["{{host}}"], "path": ["api", "email", "recipients", "monthly"], "query": [{"key": "clientId", "value": "64d9eb84313960553aff0096"}]}}, "response": []}, {"name": "list-email-recipients", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/email/recipients?clientId=64d9eb84313960553aff0096", "host": ["{{host}}"], "path": ["api", "email", "recipients"], "query": [{"key": "clientId", "value": "64d9eb84313960553aff0096"}]}}, "response": []}, {"name": "email-recipients-weekly", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/email/recipients/weekly?clientId=64d9eb84313960553aff0096", "host": ["{{host}}"], "path": ["api", "email", "recipients", "weekly"], "query": [{"key": "clientId", "value": "64d9eb84313960553aff0096"}]}}, "response": []}]}, {"name": "internal-audit", "item": [{"name": "banking-report", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{host}}/api/internal-audit/reports/banking?clientId=63609e95fdc7b2ec8793be57&fromDate=2020-01-01&toDate=2023-12-30", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "banking"], "query": [{"key": "clientId", "value": "63609e95fdc7b2ec8793be57"}, {"key": "fromDate", "value": "2020-01-01"}, {"key": "toDate", "value": "2023-12-30"}]}}, "response": []}, {"name": "export-banking-report", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/banking/export?clientId=632af99d7dc025fdb915f639&fromDate=2020-01-01&toDate=2022-12-30", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "banking", "export"], "query": [{"key": "clientId", "value": "632af99d7dc025fdb915f639"}, {"key": "fromDate", "value": "2020-01-01"}, {"key": "toDate", "value": "2022-12-30"}]}}, "response": []}, {"name": "claim_analysis", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/claim-analysis", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "claim-analysis"], "query": [{"key": "fromDate", "value": "2020-12-12", "disabled": true}, {"key": "toDate", "value": "2025-12-12", "disabled": true}, {"key": "client", "value": null, "disabled": true}]}}, "response": []}, {"name": "claim-report", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/claim?clientId=632af99d7dc025fdb915f639&fromDate=2020-01-01&toDate=2022-12-30", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "claim"], "query": [{"key": "clientId", "value": "632af99d7dc025fdb915f639"}, {"key": "fromDate", "value": "2020-01-01"}, {"key": "toDate", "value": "2022-12-30"}]}}, "response": []}, {"name": "export-claim-report", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/claim/export?clientId=632af99d7dc025fdb915f639&fromDate=2020-01-01&toDate=2022-12-30", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "claim", "export"], "query": [{"key": "clientId", "value": "632af99d7dc025fdb915f639"}, {"key": "fromDate", "value": "2020-01-01"}, {"key": "toDate", "value": "2022-12-30"}]}}, "response": []}, {"name": "risk-exposure-future-departure", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/risk-exposure-future-departure?fromDate=2022-12-19&toDate=2022-12-28&currency=GBP&resolution=10", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "risk-exposure-future-departure"], "query": [{"key": "fromDate", "value": "2022-12-19"}, {"key": "toDate", "value": "2022-12-28"}, {"key": "currency", "value": "GBP"}, {"key": "resolution", "value": "10"}]}}, "response": []}, {"name": "log-download", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/log-download", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "log-download"]}}, "response": []}, {"name": "banking-download", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/banking-download?clientId=62d914a7165ccb6ff35708e1&fromDate=2023-01-01&toDate=2023-06-30", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "banking-download"], "query": [{"key": "clientId", "value": "62d914a7165ccb6ff35708e1"}, {"key": "fromDate", "value": "2023-01-01"}, {"key": "toDate", "value": "2023-06-30"}]}}, "response": []}, {"name": "claim-download", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/claim-download?clientId=62d914a7165ccb6ff35708e1&fromDate=2023-01-01&toDate=2023-06-30", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "claim-download"], "query": [{"key": "clientId", "value": "62d914a7165ccb6ff35708e1"}, {"key": "fromDate", "value": "2023-01-01"}, {"key": "toDate", "value": "2023-06-30"}]}}, "response": []}, {"name": "client-performance", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/client-performance?fromDate=2021-03-05&toDate=2024-04-05&client=62419f08888c2b5ed06235d9", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "client-performance"], "query": [{"key": "fromDate", "value": "2021-03-05"}, {"key": "toDate", "value": "2024-04-05"}, {"key": "client", "value": "62419f08888c2b5ed06235d9"}]}}, "response": []}, {"name": "MA-log-download", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/MA-log-download", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "MA-log-download"]}}, "response": []}, {"name": "peer-report-download", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/peer-report-download", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "peer-report-download"]}}, "response": []}, {"name": "client-cash-flow", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/client-cash-flow?clientId=62ce5983bff9a1862ca13424&year=2020&currency=GBP&returnDate=2020-03-01", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "client-cash-flow"], "query": [{"key": "clientId", "value": "62ce5983bff9a1862ca13424"}, {"key": "year", "value": "2020"}, {"key": "currency", "value": "GBP"}, {"key": "returnDate", "value": "2020-03-01"}]}}, "response": []}, {"name": "export-client-cash-flow", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/client-cash-flow/export?clientId=62ce5983bff9a1862ca13424&year=2020&currency=GBP&returnDate=2020-03-01", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "client-cash-flow", "export"], "query": [{"key": "clientId", "value": "62ce5983bff9a1862ca13424"}, {"key": "year", "value": "2020"}, {"key": "currency", "value": "GBP"}, {"key": "returnDate", "value": "2020-03-01"}]}}, "response": []}, {"name": "list-enabled-clients", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/internal-audit/reports/list-enabled-clients", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "list-enabled-clients"]}}, "response": []}, {"name": "banking_update", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\":\"632af99d7dc025fdb915f639\",\r\n    \"status\":\"Submitted\",\r\n    \"notes\": \"Something\",\r\n    \"resolutionNotes\": \"\", \r\n    \"risk\": \"Low\",\r\n    \"testingstatus\":\"false\",\r\n    \"deleted\":\"false\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/internal-audit/reports/banking/62ce5998bff9a1862ca22b6f", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "banking", "62ce5998bff9a1862ca22b6f"]}}, "response": []}, {"name": "claims_update", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"clientId\":\"632af99d7dc025fdb915f639\",\r\n    \"status\":\"Submitted\",\r\n    \"notes\": \"Something\",\r\n    \"resolutionNotes\": \"\",\r\n    \"risk\": \"Low\", \r\n    \"deleted\":\"false\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/internal-audit/reports/claims/655ca10952344e1119800801", "host": ["{{host}}"], "path": ["api", "internal-audit", "reports", "claims", "655ca10952344e1119800801"]}}, "response": []}]}, {"name": "stripe", "item": [{"name": "stripe-transactions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/stripe/transactions?fromDate=2024-03-20&toDate=2024-03-21", "host": ["{{host}}"], "path": ["api", "stripe", "transactions"], "query": [{"key": "fromDate", "value": "2024-03-20"}, {"key": "toDate", "value": "2024-03-21"}]}}, "response": []}, {"name": "stripe-statement-download", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/stripe/statement/export?fromDate=2024-07-01&toDate=2024-07-31&client=65e9566ed324a4c7c6d807a5&currency=GBP", "host": ["{{host}}"], "path": ["api", "stripe", "statement", "export"], "query": [{"key": "fromDate", "value": "2024-07-01"}, {"key": "toDate", "value": "2024-07-31"}, {"key": "client", "value": "65e9566ed324a4c7c6d807a5"}, {"key": "currency", "value": "GBP"}]}}, "response": []}, {"name": "stripe-reconciliation-export", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/stripe/reconciliation/export?fromDate=2024-07-01&toDate=2024-07-31&client=65e9566ed324a4c7c6d807a5&currency=GBP", "host": ["{{host}}"], "path": ["api", "stripe", "reconciliation", "export"], "query": [{"key": "fromDate", "value": "2024-07-01"}, {"key": "toDate", "value": "2024-07-31"}, {"key": "client", "value": "65e9566ed324a4c7c6d807a5"}, {"key": "currency", "value": "GBP"}]}}, "response": []}, {"name": "stripe-reconciliation-summary", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/api/stripe/reconciliation/summary?fromDate=2024-07-01&toDate=2024-07-31&client=65e9566ed324a4c7c6d807a5&currency=GBP", "host": ["{{host}}"], "path": ["api", "stripe", "reconciliation", "summary"], "query": [{"key": "fromDate", "value": "2024-07-01"}, {"key": "toDate", "value": "2024-07-31"}, {"key": "client", "value": "65e9566ed324a4c7c6d807a5"}, {"key": "currency", "value": "GBP"}]}}, "response": []}]}]}