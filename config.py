import os
import tempfile
from flaskr.helpers.boto3_handler import get_secret


class Config(object):
    DEBUG = False
    TESTING = False
    AWS_REGION = "eu-west-2"
    MONGO_URI = os.environ.get("DATABASE_URI") or get_secret(AWS_REGION)
    USER_POOL_ID = os.environ.get("USER_POOL_ID")
    APP_CLIENT_ID = os.environ.get("APP_CLIENT_ID")
    POWERBI_CLIENT_ID = os.environ.get("POWERBI_CLIENT_ID")
    POWERBI_CLIENT_SECRET = os.environ.get("POWERBI_CLIENT_SECRET")
    POWERBI_TENANT_ID = os.environ.get("POWERBI_TENANT_ID")
    POWERBI_WORKSPACE_ID = os.environ.get("POWERBI_WORKSPACE_ID")
    POWERBI_REPORT_ID = os.environ.get("POWERBI_REPORT_ID")
    POWERBI_ERV_REPORT_ID = os.environ.get("POWERBI_ERV_REPORT_ID")
    BANKING_FILE_BUCKET = os.environ.get("BANKING_FILE_BUCKET")
    INSURANCE_FILE_BUCKET = os.environ.get("INSURANCE_FILE_BUCKET")
    BOND_FILE_BUCKET = os.environ.get("BOND_FILE_BUCKET")
    ATOL_FILE_BUCKET = os.environ.get("ATOL_FILE_BUCKET")
    CLAIM_FILE_BUCKET = os.environ.get("CLAIM_FILE_BUCKET")
    SUPPLIER_LIST_FILE_BUCKET = os.environ.get("SUPPLIER_LIST_FILE_BUCKET")
    FILE_TEMPLATE_BUCKET = os.environ.get("FILE_TEMPLATE_BUCKET")
    USER_PROFILE_BUCKET = os.environ.get("USER_PROFILE_BUCKET")
    REPORTS_BUCKET = os.environ.get("REPORTS_BUCKET")
    SFTP_BUCKET = os.environ.get("SFTP_BUCKET")
    PTT_BUCKET = os.environ.get("PTT_BUCKET")
    BANKING_AND_CLAIM_SUMMARY_BUCKET = os.environ.get("BANKING_AND_CLAIM_SUMMARY_BUCKET")
    ERV_DASHBOARD_GRAPHS_SNAPSHOT_BUCKET = os.environ.get("ERV_DASHBOARD_GRAPHS_SNAPSHOT_BUCKET")
    AUTHORIZED_SIGNATORIES_BUCKET = os.environ.get("AUTHORIZED_SIGNATORIES_BUCKET")
    MANUALS_BUCKET = os.environ.get("MANUALS_BUCKET")
    TEMP_DIR = tempfile.gettempdir()
    ESCROW_MULTIPLIER = 0.7
    FILE_DATE_AGE_IN_MONTHS = 12
    MAJOR_TRAVEL = os.environ.get("MAJOR_TRAVEL")
    ANGLIA_TOURS = os.environ.get("ANGLIA_TOURS")
    WST_TRAVEL = os.environ.get("WST_TRAVEL")
    WE_LOVE_HOLIDAYS = os.environ.get("WE_LOVE_HOLIDAYS")
    SWOOP = os.environ.get("SWOOP")
    SWOOP_TRAVEL = os.environ.get("SWOOP_TRAVEL")
    NAS = os.environ.get("NAS")
    TS = os.environ.get("TS")
    ITGP = os.environ.get("ITGP")
    INTE = os.environ.get("INTE")
    TURQ = os.environ.get("TURQ")
    GTL = os.environ.get("GTL")
    HOBE = os.environ.get("HOBE")
    CALEDONIAN = os.environ.get("CALEDONIAN")
    FLYPOP = os.environ.get("FLYPOP")
    BARRHEAD = os.environ.get("BARRHEAD")
    HAYS = os.environ.get("HAYS")
    SUNSHINE = os.environ.get("SUNSHINE")
    BROADWAY = os.environ.get("BROADWAY")
    IGLU_ESCROW = os.environ.get("IGLU_ESCROW")
    OPEN_BANKING_URI = "http://ptt-open-banking:5000"
    IA_BUCKET = os.environ.get("IA_BUCKET")
    TRAVEL_REPUBLIC = os.environ.get("TRAVEL_REPUBLIC")
    TCT = os.environ.get("TCT")
    EST = os.environ.get("EST")
    TDC = os.environ.get("TDC")
    NST = os.environ.get("NST")
    PGL = os.environ.get("PGL")
    WLH_NEW = os.environ.get("WLH_NEW")
    PENNYWOOD = os.environ.get("PENNYWOOD")
    BLUESTYLE = os.environ.get("BLUESTYLE")


class LocalConfig(Config):
    ENVIRONMENT = "local"
    OPEN_BANKING_URI = "http://127.0.0.1:5001"


class TestConfig(Config):
    TESTING = True
    MONGO_URI = "mongodb://localhost:60101/ptt-test"
    ENVIRONMENT = "test"


class DevelopmentConfig(Config):
    ENVIRONMENT = "dev"


class StageConfig(Config):
    ENVIRONMENT = "stage"


class PreProdConfig(Config):
    ENVIRONMENT = "pre-prod"


class ProductionConfig(Config):
    ENVIRONMENT = "prod"
