[tool.poetry]
name = "ptt-backend-service"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.8"
Flask = "^2.0.2"
boto3 = "^1.20.26"
marshmallow = "^3.14.1"
gunicorn = "^20.1.0"
Flask-Cors = "^3.0.10"
Flask-PyMongo = "^2.3.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
sheet2dict = "^0.1.1"
XlsxWriter = "^3.0.3"
dnspython = "^2.2.1"
celery = "^5.2.7"
stripe = "^8.8.0"
kafka = "^1.3.5"
confluent-kafka = "^2.6.0"
msal = "^1.32.3"

[tool.poetry.dev-dependencies]
black = {version = "^21.12b0", allow-prereleases = true}
flake8 = "^4.0.1"
pre-commit = "^2.16.0"
pytest = "^6.2.5"
mock = "^4.0.3"
PyHamcrest = "^2.0.3"
mongomock = "^4.0.0"
pytest-cov = "^3.0.0"
freezegun = "^1.2.1"
moto = "^3.1.6"
pytest-env = "^0.6.2"

[tool.poetry.group.dev.dependencies]
click = "8.0.4"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"