from datetime import datetime
from unittest.mock import MagicMock, patch
from celery_app.tasks.reports import trust_balance_report_task
from flaskr.services.reporting_service import ReportingService
from freezegun import freeze_time
import pytest


@patch.object(
    ReportingService,
    "trust_balance_report",
    lambda *args, **kwargs: {
        "content": [
            {
                "_id": "632191816f7b7b5e188306f1",
                "balance": 40000.0,
                "bookingDate": "2022-11-06",
                "bookingRef": "ab1",
                "clientId": "632191816f7b7b5e188306a5",
                "cId": "CR7",
                "clientName": "Nithin C George",
                "currency": "GBP",
                "departureDate": "2022-09-02",
                "deposits": 40000.0,
                "friendlyName": "Nithin",
                "leadPax": "Lady <PERSON>",
                "refundsFromDepositFile": 0,
                "returnDate": "2022-10-06",
                "status": "Live",
                "totalBanked": 40000.0,
                "totalBookingValue": 3000,
                "totalClaimed": 0,
                "type": "ab",
            },
            {
                "_id": "632191816f7b7b5e188306f2",
                "balance": 40000.0,
                "bookingDate": "2022-02-10",
                "bookingRef": "ab2",
                "clientId": "632191816f7b7b5e188306a5",
                "cId": "CR7",
                "clientName": "Nithin C George",
                "currency": "GBP",
                "departureDate": "2022-09-03",
                "deposits": 40000.0,
                "friendlyName": "Nithin",
                "leadPax": "Mr Charles Diamond",
                "refundsFromDepositFile": 0,
                "returnDate": "2021-11-07",
                "status": "Live",
                "totalBanked": 40000.0,
                "totalClaimed": 0,
                "totalBookingValue": 1000,
                "type": "ab",
            },
        ],
        "empty": False,
        "first": True,
        "last": True,
        "numberOfElements": 2,
        "pageNumber": 1,
        "totalElements": 2,
        "totalPages": 1,
    },
)
@patch("celery_app.tasks.reports.upload_file", MagicMock())
@freeze_time("2022-09-22")
def test_trust_balance_report_task(flask_client, patch_db):
    # Given
    patch_db.report_files.insert_many(
        [
            {
                "name": "TrustBalance",
                "client_id": "632191816f7b7b5e188306a5",
                "currency": "GBP",
                "file_id": "test_file_id_xlsx",
                "file_type": "xlsx",
                "status": "Generating New Report",
                "generated_at": datetime(2022, 9, 22),
                "created_at": datetime(2022, 9, 22),
                "updated_at": datetime(2022, 9, 22),
            },
            {
                "name": "TrustBalance",
                "client_id": "632191816f7b7b5e188306a5",
                "currency": "GBP",
                "file_id": "test_file_id_csv",
                "file_type": "csv",
                "status": "Generating New Report",
                "generated_at": datetime(2022, 9, 22),
                "created_at": datetime(2022, 9, 22),
                "updated_at": datetime(2022, 9, 22),
            },
        ]
    )
    data = {
        "client": "632191816f7b7b5e188306a5",
        "name": "TrustBalance",
        "currency": "GBP",
        "file_id_csv": "test_file_id_csv",
        "file_id_xlsx": "test_file_id_xlsx",
    }

    # When
    trust_balance_report_task.apply(kwargs={"data": data}).get()

    # Then
    data = list(patch_db.report_files.find(projection={"_id": 0}))
    assert data == [
        {
            "name": "TrustBalance",
            "client_id": "632191816f7b7b5e188306a5",
            "currency": "GBP",
            "file_id": "test_file_id_xlsx",
            "file_type": "xlsx",
            "status": "Generated New Report",
            "generated_at": datetime(2022, 9, 22),
            "created_at": datetime(2022, 9, 22),
            "updated_at": datetime(2022, 9, 22),
        },
        {
            "name": "TrustBalance",
            "client_id": "632191816f7b7b5e188306a5",
            "currency": "GBP",
            "file_id": "test_file_id_csv",
            "file_type": "csv",
            "status": "Generated New Report",
            "generated_at": datetime(2022, 9, 22),
            "created_at": datetime(2022, 9, 22),
            "updated_at": datetime(2022, 9, 22),
        },
    ]
