import pytest
from mock import patch, MagicMock
from flask import current_app
from flaskr.services.powerbi_service import powerbi_service
from flaskr import create_app


@pytest.fixture
def test_app():
    app = create_app()
    app.config.update(
        {
            "TESTING": True,
            "POWERBI_CLIENT_ID": "test-client-id",
            "POWERBI_CLIENT_SECRET": "test-client-secret",
            "POWERBI_TENANT_ID": "test-tenant-id",
            "POWERBI_WORKSPACE_ID": "test-workspace-id",
        }
    )
    return app


def test_property_getters(test_app):
    with test_app.app_context():
        # Reset the service properties to ensure they're fetched from config
        powerbi_service._client_id = None
        powerbi_service._client_secret = None
        powerbi_service._tenant_id = None
        powerbi_service._authority = None
        powerbi_service._workspace_id = None

        # Test the property getters
        assert powerbi_service.client_id == "test-client-id"
        assert powerbi_service.client_secret == "test-client-secret"
        assert powerbi_service.tenant_id == "test-tenant-id"
        assert powerbi_service.authority == "https://login.microsoftonline.com/test-tenant-id"
        assert powerbi_service.workspace_id == "test-workspace-id"

        # Test that properties are cached
        current_app.config["POWERBI_CLIENT_ID"] = "changed-id"
        assert powerbi_service.client_id == "test-client-id"


@patch("flaskr.services.powerbi_service.msal.ConfidentialClientApplication")
def test_get_access_token_success(mock_msal_app, test_app):
    with test_app.app_context():
        # Setup mock
        mock_app_instance = MagicMock()
        mock_app_instance.acquire_token_for_client.return_value = {
            "access_token": "test-access-token",
            "expires_in": 3600,
        }
        mock_msal_app.return_value = mock_app_instance

        # Call the method
        token = powerbi_service.get_access_token()

        # Assertions
        assert token == "test-access-token"
        mock_msal_app.assert_called_once_with(
            powerbi_service.client_id,
            authority=powerbi_service.authority,
            client_credential=powerbi_service.client_secret,
        )
        mock_app_instance.acquire_token_for_client.assert_called_once_with(scopes=powerbi_service.scope)


@patch("flaskr.services.powerbi_service.msal.ConfidentialClientApplication")
def test_get_access_token_failure(mock_msal_app, test_app):
    with test_app.app_context():
        # Setup mock
        mock_app_instance = MagicMock()
        mock_app_instance.acquire_token_for_client.return_value = {
            "error": "invalid_client",
            "error_description": "Client authentication failed",
        }
        mock_msal_app.return_value = mock_app_instance

        # Call the method and check exception
        with pytest.raises(Exception) as excinfo:
            powerbi_service.get_access_token()

        # Assertions
        assert "Failed to get access token" in str(excinfo.value)
        assert "invalid_client" in str(excinfo.value)
        assert "Client authentication failed" in str(excinfo.value)


@patch("flaskr.services.powerbi_service.requests.post")
@patch("flaskr.services.powerbi_service.requests.get")
def test_get_report_embed_info_token_error(mock_get, mock_post, test_app):
    with test_app.app_context():
        # Mock the access token
        powerbi_service.get_access_token = MagicMock(return_value="mock-access-token")

        # Mock the report details response - success
        mock_details_response = MagicMock()
        mock_details_response.status_code = 200
        mock_details_response.json.return_value = {
            "id": "report-123",
            "embedUrl": "https://app.powerbi.com/reportEmbed",
        }
        mock_get.return_value = mock_details_response

        # Mock the token response - error
        mock_token_response = MagicMock()
        mock_token_response.status_code = 403
        mock_token_response.text = "Forbidden"
        mock_post.return_value = mock_token_response

        # When/Then
        with pytest.raises(Exception) as excinfo:
            powerbi_service.get_report_embed_info("report-123", "workspace-456", "user-789")

        # Check for the actual error message format from the service
        assert "Failed to get embed token" in str(excinfo.value)
        assert "Status: 403" in str(excinfo.value)
        assert "Forbidden" in str(excinfo.value)


@patch("flaskr.services.powerbi_service.requests.get")
def test_get_report_embed_info_json_error(mock_get, test_app):
    with test_app.app_context():
        # Mock the access token
        powerbi_service.get_access_token = MagicMock(return_value="mock-access-token")

        # Mock the report details response with an error that returns JSON
        mock_details_response = MagicMock()
        mock_details_response.status_code = 400
        mock_details_response.json.return_value = {"error": {"code": "InvalidRequest", "message": "Bad request"}}
        mock_details_response.json.side_effect = None  # Ensure json() doesn't raise an exception
        mock_get.return_value = mock_details_response

        # When/Then
        with pytest.raises(Exception) as excinfo:
            powerbi_service.get_report_embed_info("report-123", "workspace-456", "user-789")

        # Check for the actual error message format from the service
        assert "Failed to get report details" in str(excinfo.value)
        assert "Status: 400" in str(excinfo.value)
        assert "InvalidRequest" in str(excinfo.value) or "Bad request" in str(excinfo.value)


# Test ERV dashboard configuration methods
@patch("flaskr.services.powerbi_service.get_db")
def test_get_erv_dashboard_config_from_database(mock_get_db, test_app):
    with test_app.app_context():
        # Mock database response
        mock_db = MagicMock()
        mock_collection = MagicMock()
        mock_collection.find_one.return_value = {
            "report_id": "erv-report-123",
            "name": "ERV Dashboard"
        }
        mock_db.erv_powerbi_config = mock_collection
        mock_get_db.return_value = mock_db

        # When
        config = powerbi_service.get_erv_powerbi_dashboard_config()

        # Then
        assert config["report_id"] == "erv-report-123"
        assert config["name"] == "ERV Dashboard"
        mock_collection.find_one.assert_called_once_with()


@patch("flaskr.services.powerbi_service.get_db")
def test_get_erv_dashboard_config_fallback_to_env(mock_get_db, test_app):
    with test_app.app_context():
        # Mock database response - no config found
        mock_db = MagicMock()
        mock_collection = MagicMock()
        mock_collection.find_one.return_value = None
        mock_db.erv_powerbi_config = mock_collection
        mock_get_db.return_value = mock_db

        # Set environment config
        test_app.config["POWERBI_ERV_REPORT_ID"] = "default-erv-report"

        # When
        config = powerbi_service.get_erv_powerbi_dashboard_config()

        # Then
        assert config["report_id"] == "default-erv-report"
        assert config["name"] == "ERV Dashboard"


@patch("flaskr.services.powerbi_service.get_db")
def test_get_erv_dashboard_config_no_config_found(mock_get_db, test_app):
    with test_app.app_context():
        # Mock database response - no config found
        mock_db = MagicMock()
        mock_collection = MagicMock()
        mock_collection.find_one.return_value = None
        mock_db.erv_powerbi_config = mock_collection
        mock_get_db.return_value = mock_db

        # No environment config set
        test_app.config["POWERBI_ERV_REPORT_ID"] = None

        # When
        config = powerbi_service.get_erv_powerbi_dashboard_config()

        # Then
        assert config is None


@patch("flaskr.services.powerbi_service.get_db")
def test_update_erv_dashboard_config_success(mock_get_db, test_app):
    with test_app.app_context():
        # Mock database
        mock_db = MagicMock()
        mock_collection = MagicMock()
        mock_collection.update_one.return_value = MagicMock()
        mock_db.erv_powerbi_config = mock_collection
        mock_get_db.return_value = mock_db

        # When
        result = powerbi_service.update_erv_dashboard_config("new-erv-report", "New ERV Dashboard")

        # Then
        assert result is True
        mock_collection.update_one.assert_called_once()
        call_args = mock_collection.update_one.call_args
        assert call_args[0][0] == {}  # Empty filter to update first document
        assert "$set" in call_args[0][1]
        assert call_args[0][1]["$set"]["report_id"] == "new-erv-report"
        assert call_args[0][1]["$set"]["name"] == "New ERV Dashboard"
        assert "updated_at" in call_args[0][1]["$set"]
