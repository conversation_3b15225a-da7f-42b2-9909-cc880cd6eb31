from datetime import datetime
from mongomock import ObjectId
from werkzeug.exceptions import Forbidden
import pytest
from unittest.mock import patch
from flaskr.services.dashboard_service import dashboard_service
from freezegun import freeze_time


@pytest.mark.parametrize(
    "input_client_basic_info, output",
    [
        (
            [
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e50"),
                    "client_id": "1",
                    "full_name": "abc",
                    "friendly_name": "cde",
                    "go_live_date": datetime.fromisoformat("2022-03-02"),
                    "point_of_contact": "abc",
                    "email": "<EMAIL>",
                    "type_of_trust_account": ObjectId("61ef4e0da0d0ef7c56884319"),
                    "created_at": datetime.fromisoformat("2022-03-02"),
                }
            ],
            [
                {"currentYearCount": 0, "month": "January", "previousYearCount": 0},
                {"currentYearCount": 0, "month": "February", "previousYearCount": 0},
                {"currentYearCount": 1, "month": "March", "previousYearCount": 0},
                {"currentYearCount": 0, "month": "April", "previousYearCount": 0},
                {"currentYearCount": 0, "month": "May", "previousYearCount": 0},
                {"currentYearCount": 0, "month": "June", "previousYearCount": 0},
                {"currentYearCount": 0, "month": "July", "previousYearCount": 0},
                {"currentYearCount": 0, "month": "August", "previousYearCount": 0},
                {"currentYearCount": 0, "month": "September", "previousYearCount": 0},
                {"currentYearCount": 0, "month": "October", "previousYearCount": 0},
                {"currentYearCount": 0, "month": "November", "previousYearCount": 0},
                {"currentYearCount": 0, "month": "December", "previousYearCount": 0},
            ],
        ),
    ],
)
def test_ytd_clients(
    patch_db,
    input_client_basic_info,
    output,
):
    # Given
    current_year = "2022"
    previous_year = "2021"

    patch_db.client_basic_info.insert_many(input_client_basic_info)

    # When
    result = dashboard_service.ytd_clients(current_year, previous_year)

    # Then
    assert result == output


@pytest.mark.skip("To be completed later")
def test_dashboard_details_service(flask_client, patch_db):
    # Given

    patch_db.client_basic_info.insert_many(
        [
            {"client_id": "1", "is_disabled": True, "created_at": datetime(2022, 11, 28, 14, 46, 0)},
            {"client_id": "2", "is_disabled": False, "created_at": datetime(2022, 11, 28, 14, 46, 0)},
        ]
    )
    patch_db.anomaly_banking.insert_many(
        [
            {
                "client_id": "1",
                "transaction_id": "621bf4f427afc94e603a636b",
                "file_id": "921bf4f427afc94e603a636a",
                "status": "Unresolved",
                "deleted": False,
            },
            {
                "client_id": "2",
                "transaction_id": "621bf4f427afc94e603a636b",
                "file_id": "921bf4f427afc94e603a636a",
                "status": "Unresolved",
                "deleted": False,
            },
            {
                "client_id": "2",
                "transaction_id": "621bf4f427afc94e603a636b",
                "file_id": "921bf4f427afc94e603a636a",
                "status": "Unresolved",
                "deleted": True,
            },
        ]
    )
    patch_db.anomaly_claims.insert_many(
        [
            {
                "client_id": "3",
                "transaction_id": "521bf4f427afc94e603a636b",
                "file_id": "421bf4f427afc94e603a636a",
                "status": "Unresolved",
                "deleted": False,
            },
            {
                "client_id": "1",
                "transaction_id": "521bf4f427afc94e603a636b",
                "file_id": "421bf4f427afc94e603a636a",
                "status": "Unresolved",
                "deleted": False,
            },
            {
                "client_id": "1",
                "transaction_id": "521bf4f427afc94e603a636b",
                "file_id": "421bf4f427afc94e603a636a",
                "status": "Unresolved",
                "deleted": True,
            },
        ]
    )
    patch_db.banking_metadata.insert_many(
        [
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f53"),
                "banking_files": [{"file_date": "2022-10-05", "deposit": {"GBP": 3000}}],
                "status": "Cancelled",
            },
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f54"),
                "banking_files": [
                    {"file_date": "2022-10-05", "deposit": {"GBP": 10000}},
                    {"file_date": "2022-10-05", "deposit": {"GBP": 3000}},
                ],
            },
        ]
    )
    patch_db.claims_metadata.insert_many(
        [
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f21"),
                "claim_files": [{"file_date": "2022-10-05", "claim_total": {"GBP": 4000}}],
                "status": "Cancelled",
            },
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f22"),
                "claim_files": [
                    {"file_date": "2022-10-05", "claim_total": {"GBP": 10000}},
                    {"file_date": "2022-10-05", "claim_total": {"GBP": 4000}},
                ],
            },
        ]
    )

    # When
    result = dashboard_service.dashboard_details("GBP", "2022-09-05", "2022-12-05")

    # Then
    assert result == {
        "totalClaims": 4000,
        "totalClients": 1,
        "totalPayments": 3000,
        "unhandledAnomalies": 4,
        "clientsCount": 1,
    }


def test_clients_category(flask_client, patch_db):
    # Given
    patch_db.client_basic_info.insert_many(
        [
            {
                "client_id": "1",
                "email": "<EMAIL>",
                "existing_client": True,
                "friendly_name": "updated",
                "full_name": "ABC",
                "go_live_date": datetime.fromisoformat("2021-10-02"),
                "point_of_contact": "string",
                "reuse_old_booking": False,
                "type_of_trust_account": ObjectId("61ef4e0da0d0ef7c56884319"),
                "status": "published",
                "is_disabled": False,
            },
            {
                "client_id": "2",
                "email": "<EMAIL>",
                "existing_client": True,
                "friendly_name": "updated",
                "full_name": "ABC",
                "go_live_date": datetime.fromisoformat("2021-10-02"),
                "point_of_contact": "string",
                "reuse_old_booking": False,
                "type_of_trust_account": ObjectId("61ef4e0da0d0ef7c56884319"),
                "status": "draft",
                "is_disabled": True,
            },
        ]
    )
    patch_db.lookup_trust_type.insert_many(
        [
            {"name": "ATOL Standard", "_id": ObjectId("61ef4e0da0d0ef7c56884319")},
            {"name": "Tripartite MA", "_id": ObjectId("61ef4e0da0d0ef7c56884312")},
            {"name": "Tripartite Tour Op", "_id": ObjectId("61ef4e0da0d0ef7c56884320")},
            {"name": "Non-Flight PTR 2018", "_id": ObjectId("61ef4e0da0d0ef7c56884330")},
            {"name": "Non PTR 2018", "_id": ObjectId("61ef4e0da0d0ef7c56884354")},
            {"name": "ATOL Gold", "_id": ObjectId("61ef4e0da0d0ef7c56884389")},
            {"name": "ATOL Escrow", "_id": ObjectId("61ef4e0da0d0ef7c56884390")},
        ]
    )
    # When
    result = dashboard_service.ytd_clients_category()

    # Then
    assert result == [
        {"typeOfTrust": "ATOL Standard", "clients": 1},
    ]


@freeze_time("Nov 30th, 2022")
def test_risk_exposure_success(flask_client, patch_db):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("62fb53e11a873172a59b2a1f"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    patch_db.dashboard_risk_exposure.insert_many(
        [
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1f"),
                "claim_amount": 100000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
                "claim_amount": 200000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb50ca7054170ec1d9039a"),
                "claim_amount": 300000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1f"),
                "claim_amount": 900000,
                "created_at": datetime(2022, 11, 29, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 14, 46, 0),
                "date": datetime(2022, 11, 29, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
                "claim_amount": 700000,
                "created_at": datetime(2022, 11, 29, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 15, 46, 0),
                "date": datetime(2022, 11, 29, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb50ca7054170ec1d9039a"),
                "claim_amount": 500000,
                "created_at": datetime(2022, 11, 30, 14, 46, 0),
                "updated_at": datetime(2022, 11, 30, 14, 46, 0),
                "date": datetime(2022, 11, 30, 00, 00, 00),
                "currency_code": "GBP",
            },
        ]
    )

    # When
    response = dashboard_service.risk_exposure(user_id, {"currency": "GBP"}, 10)

    # Then
    assert response == [
        {"claimAmount": 0.0, "date": "2022-11-21T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-22T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-23T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-24T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-25T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-26T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-27T00:00:00"},
        {"claimAmount": 300000.0, "date": "2022-11-28T00:00:00"},
        {"claimAmount": 1600000.0, "date": "2022-11-29T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-30T00:00:00"},
    ]


@freeze_time("Nov 30th, 2022")
def test_risk_exposure_success_date_equal(flask_client, patch_db):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("62fb53e11a873172a59b2a1f"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    patch_db.dashboard_risk_exposure.insert_many(
        [
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1f"),
                "claim_amount": 100000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
                "claim_amount": 200000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb50ca7054170ec1d9039a"),
                "claim_amount": 300000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1f"),
                "claim_amount": 900000,
                "created_at": datetime(2022, 11, 29, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 14, 46, 0),
                "date": datetime(2022, 11, 29, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
                "claim_amount": 700000,
                "created_at": datetime(2022, 11, 29, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 15, 46, 0),
                "date": datetime(2022, 11, 29, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb50ca7054170ec1d9039a"),
                "claim_amount": 500000,
                "created_at": datetime(2022, 11, 30, 14, 46, 0),
                "updated_at": datetime(2022, 11, 30, 14, 46, 0),
                "date": datetime(2022, 11, 30, 00, 00, 00),
                "currency_code": "GBP",
            },
        ]
    )

    # When
    response = dashboard_service.risk_exposure(
        user_id, {"currency": "GBP", "toDate": "2022-11-28", "fromDate": "2022-11-28"}, 10
    )

    # Then
    assert response == [{"claimAmount": 300000.0, "date": "2022-11-28T00:00:00"}]


@freeze_time("Nov 30th, 2022")
def test_risk_exposure_success_date_dif_eqls_1(flask_client, patch_db):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("62fb53e11a873172a59b2a1f"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    patch_db.dashboard_risk_exposure.insert_many(
        [
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1f"),
                "claim_amount": 100000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
                "claim_amount": 200000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb50ca7054170ec1d9039a"),
                "claim_amount": 300000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1f"),
                "claim_amount": 900000,
                "created_at": datetime(2022, 11, 29, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 14, 46, 0),
                "date": datetime(2022, 11, 29, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
                "claim_amount": 700000,
                "created_at": datetime(2022, 11, 29, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 15, 46, 0),
                "date": datetime(2022, 11, 29, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb50ca7054170ec1d9039a"),
                "claim_amount": 500000,
                "created_at": datetime(2022, 11, 30, 14, 46, 0),
                "updated_at": datetime(2022, 11, 30, 14, 46, 0),
                "date": datetime(2022, 11, 30, 00, 00, 00),
                "currency_code": "GBP",
            },
        ]
    )

    # When
    response = dashboard_service.risk_exposure(user_id, {"currency": "GBP", "fromDate": "2022-11-29"}, 1)

    # Then
    assert response == [
        {"claimAmount": 1600000.0, "date": "2022-11-29T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-30T00:00:00"},
    ]


@freeze_time("Nov 30th, 2022")
def test_risk_exposure_success_with_client(flask_client, patch_db):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("62fb53e11a873172a59b2a1f"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    patch_db.dashboard_risk_exposure.insert_many(
        [
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1f"),
                "claim_amount": 100000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
                "claim_amount": 200000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb50ca7054170ec1d9039a"),
                "claim_amount": 300000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1f"),
                "claim_amount": 900000,
                "created_at": datetime(2022, 11, 29, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 14, 46, 0),
                "date": datetime(2022, 11, 29, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
                "claim_amount": 700000,
                "created_at": datetime(2022, 11, 29, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 15, 46, 0),
                "date": datetime(2022, 11, 29, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb50ca7054170ec1d9039a"),
                "claim_amount": 500000,
                "created_at": datetime(2022, 11, 30, 14, 46, 0),
                "updated_at": datetime(2022, 11, 30, 14, 46, 0),
                "date": datetime(2022, 11, 30, 00, 00, 00),
                "currency_code": "GBP",
            },
        ]
    )

    # When
    response = dashboard_service.risk_exposure(
        user_id, {"currency": "GBP", "fromDate": "2022-11-25", "client": "62fb53e11a873172a59b2a1f"}, 10
    )

    # Then
    assert response == [
        {"claimAmount": 0.0, "date": "2022-11-25T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-26T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-27T00:00:00"},
        {"claimAmount": 100000.0, "date": "2022-11-28T00:00:00"},
        {"claimAmount": 900000.0, "date": "2022-11-29T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-30T00:00:00"},
    ]


@freeze_time("Nov 30th, 2022")
def test_risk_exposure_success_date_diff_10(flask_client, patch_db):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("62fb53e11a873172a59b2a1f"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    patch_db.dashboard_risk_exposure.insert_many(
        [
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1f"),
                "claim_amount": 100000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
                "claim_amount": 200000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb50ca7054170ec1d9039a"),
                "claim_amount": 300000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1f"),
                "claim_amount": 900000,
                "created_at": datetime(2022, 11, 29, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 14, 46, 0),
                "date": datetime(2022, 11, 29, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
                "claim_amount": 700000,
                "created_at": datetime(2022, 11, 29, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 15, 46, 0),
                "date": datetime(2022, 11, 29, 00, 00, 00),
                "currency_code": "GBP",
            },
            {
                "client_id": ObjectId("62fb50ca7054170ec1d9039a"),
                "claim_amount": 500000,
                "created_at": datetime(2022, 11, 30, 14, 46, 0),
                "updated_at": datetime(2022, 11, 30, 14, 46, 0),
                "date": datetime(2022, 11, 30, 00, 00, 00),
                "currency_code": "GBP",
            },
        ]
    )

    # When
    response = dashboard_service.risk_exposure(
        user_id, {"currency": "GBP", "fromDate": "2022-11-20", "toDate": "2022-11-30"}, 10
    )

    # Then
    assert response == [
        {"claimAmount": 0.0, "date": "2022-11-20T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-21T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-22T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-23T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-24T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-25T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-26T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-27T00:00:00"},
        {"claimAmount": 300000.0, "date": "2022-11-28T00:00:00"},
        {"claimAmount": 0.0, "date": "2022-11-30T00:00:00"},
    ]


def test_risk_exposure_status(flask_client, patch_db):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("62fb53e11a873172a59b2a1f"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    patch_db.dashboard_risk_exposure.insert_many(
        [
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1f"),
                "claim_amount": 100000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
            },
            {
                "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
                "claim_amount": 200000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
            },
            {
                "client_id": ObjectId("62fb50ca7054170ec1d9039a"),
                "claim_amount": 300000,
                "created_at": datetime(2022, 11, 28, 14, 46, 0),
                "updated_at": datetime(2022, 11, 28, 14, 46, 0),
                "date": datetime(2022, 11, 28, 00, 00, 00),
            },
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1f"),
                "claim_amount": 900000,
                "created_at": datetime(2022, 11, 29, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 14, 46, 0),
                "date": datetime(2022, 11, 29, 00, 00, 00),
            },
            {
                "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
                "claim_amount": 700000,
                "created_at": datetime(2022, 11, 29, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 15, 46, 0),
                "date": datetime(2022, 11, 29, 00, 00, 00),
            },
            {
                "client_id": ObjectId("62fb50ca7054170ec1d9039a"),
                "claim_amount": 500000,
                "created_at": datetime(2022, 11, 30, 14, 46, 0),
                "updated_at": datetime(2022, 11, 30, 14, 46, 0),
                "date": datetime(2022, 11, 30, 00, 00, 00),
            },
        ]
    )

    # When
    response = dashboard_service.risk_exposure_status(user_id)

    # Then
    assert response == {"updatedAt": datetime(2022, 11, 29, 15, 46)}


def test_movement_of_funds_dashboard_service(flask_client, patch_db):
    # Given
    data = {"currency": "GBP", "clientId": ObjectId("6256647a600d53e857f9ae74"), "fromDate": "2022-03-30"}
    patch_db.user.insert_one({"user_id": "1", "clients": [ObjectId("6256647a600d53e857f9ae74")]})
    patch_db.banking_metadata.insert_many(
        [
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f53"),
                "client_id": ObjectId("6256647a600d53e857f9ae74"),
                "banking_files": [{"deposit": {"GBP": 3000}, "file_date": "2022-03-30"}],
                "status": "Cancelled",
            },
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f54"),
                "client_id": ObjectId("6256647a600d53e857f9ae74"),
                "banking_files": [{"deposit": {"GBP": 1000}, "file_date": "2022-03-30"}],
            },
        ]
    )
    patch_db.claims_metadata.insert_many(
        [
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f21"),
                "client_id": ObjectId("6256647a600d53e857f9ae74"),
                "claim_files": [{"claim_total": {"GBP": 4000}, "file_date": "2022-03-30"}],
                "status": "Cancelled",
            },
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f22"),
                "client_id": ObjectId("6256647a600d53e857f9ae74"),
                "claim_files": [{"claim_total": {"GBP": 10000}, "file_date": "2022-03-30"}],
            },
        ]
    )
    # When
    result = dashboard_service.movement_of_funds(data, "1")
    # Then
    assert result == {"totalPayments": 1000, "totalClaims": 10000, "balance": -9000}


def test_movement_of_funds_dashboard_service_all_clients(flask_client, patch_db):
    # Given
    data = {"currency": "GBP", "fromDate": "2022-03-30"}
    patch_db.user.insert_one(
        {"user_id": "1", "clients": [ObjectId("6256647a600d53e857f9ae74"), ObjectId("6256647a600d53e857f9ae78")]}
    )
    patch_db.banking_metadata.insert_many(
        [
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f53"),
                "client_id": ObjectId("6256647a600d53e857f9ae74"),
                "banking_files": [{"deposit": {"GBP": 3000}, "file_date": "2022-03-30"}],
                "status": "Cancelled",
            },
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f54"),
                "client_id": ObjectId("6256647a600d53e857f9ae74"),
                "banking_files": [{"deposit": {"GBP": 10000}, "file_date": "2022-03-30"}],
                "status": "Submitted",
            },
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f50"),
                "client_id": ObjectId("6256647a600d53e857f9ae78"),
                "banking_files": [{"deposit": {"GBP": 50000}, "file_date": "2022-03-30"}],
                "status": "Submitted",
            },
        ]
    )
    patch_db.claims_metadata.insert_many(
        [
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f21"),
                "client_id": ObjectId("6256647a600d53e857f9ae74"),
                "claim_files": [{"claim_total": {"GBP": 4000}, "file_date": "2022-03-30"}],
                "status": "Cancelled",
            },
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f22"),
                "client_id": ObjectId("6256647a600d53e857f9ae74"),
                "claim_files": [{"claim_total": {"GBP": 10000}, "file_date": "2022-03-30"}],
                "status": "Submitted",
            },
            {
                "_id": ObjectId("62ac4fc96b88d731b5670f23"),
                "client_id": ObjectId("6256647a600d53e857f9ae78"),
                "claim_files": [{"claim_total": {"GBP": 30000}, "file_date": "2022-03-30"}],
                "status": "Submitted",
            },
        ]
    )
    # When
    result = dashboard_service.movement_of_funds(data, "1")
    # Then
    assert result == {"totalPayments": 60000, "totalClaims": 40000, "balance": 20000}


def test_exposure_status(flask_client, patch_db):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("63a3e8ee12e0defb392be622"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    patch_db.dashboard_exposure_status.insert_one(
        {
            "_id": ObjectId("63a53579935af3a178a1f49f"),
            "client_id": ObjectId("63a3e8ee12e0defb392be622"),
            "currency_code": "GBP",
            "exposure_status": {
                "open": {"count": 1, "amount": 4562},
                "flown": {"count": 26645, "amount": 39887186.42},
                "vouchered": {"count": 1, "amount": 3518},
                "rebooked": {"count": 0, "amount": 0},
                "refunded": {"count": 0, "amount": 0},
                "expired": {
                    "visa": {"count": 28186, "amount": 40616624.06},
                    "master_card": {"count": 28186, "amount": 40616624.06},
                },
                "charge_back": {"count": 0, "amount": 0},
            },
        }
    )

    # When
    response = dashboard_service.exposure_status(user_id, {"currency": "GBP"})

    # Then
    assert response == [
        {"amount": 4562, "count": 1, "name": "open"},
        {"amount": 39887186.42, "count": 26645, "name": "flown"},
        {"amount": 3518, "count": 1, "name": "vouchered"},
        {"amount": 0, "count": 0, "name": "rebooked"},
        {"amount": 0, "count": 0, "name": "refunded"},
        {"amount": 81233248.12, "count": 56372, "name": "expired"},
        {"amount": 0, "count": 0, "name": "chargeBack"},
    ]


def test_exposure_status_status(flask_client, patch_db):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("63a3e8ee12e0defb392be622"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    patch_db.dashboard_exposure_status.insert_one(
        {
            "_id": ObjectId("63a53579935af3a178a1f49f"),
            "client_id": ObjectId("63a3e8ee12e0defb392be622"),
            "currency_code": "GBP",
            "exposure_status": {
                "open": {"count": 1, "amount": 4562},
                "flown": {"count": 26645, "amount": 39887186.42},
                "vouchered": {"count": 1, "amount": 3518},
                "rebooked": {"count": 0, "amount": 0},
                "refunded": {"count": 0, "amount": 0},
                "expired": {
                    "visa": {"count": 28186, "amount": 40616624.06},
                    "master_card": {"count": 28186, "amount": 40616624.06},
                },
                "charge_back": {"count": 0, "amount": 0},
            },
            "updated_at": datetime(2022, 12, 22, 0, 0),
        }
    )

    # When
    response = dashboard_service.exposure_status_status(user_id)

    # Then
    assert response == {"updatedAt": datetime(2022, 12, 22, 0, 0)}


def test_upload_transactions(flask_client, patch_db, monkeypatch):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    data = {"fromDate": "2023-01-01", "toDate": "2023-01-12"}
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("63a3e8ee12e0defb392be622"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    patch_db.banking_file_details.insert_many(
        [
            {
                "_id": ObjectId("63a53579935af3a178a1f49f"),
                "amount": 200,
                "lead_pax": "Howard",
                "pax_count": 2,
                "booking_date": "2019-07-20",
                "departure_date": "2020-04-23",
                "return_date": "2020-04-30",
                "currency_code": "GBP",
                "client_id": ObjectId("63a3e8ee12e0defb392be622"),
                "file_id": "32487-ptt-uat-july-21",
                "deleted": False,
                "created_at": datetime(2023, 1, 2, 14, 46, 0),
                "updated_at": datetime(2022, 1, 2, 15, 46, 0),
                "banking_id": ObjectId("62da91d9ab74518c805144db"),
            },
            {
                "_id": ObjectId("63a53579935af3a178a1f426"),
                "amount": 200,
                "lead_pax": "Howard",
                "pax_count": 2,
                "booking_date": "2019-07-20",
                "departure_date": "2020-04-23",
                "return_date": "2020-04-30",
                "currency_code": "GBP",
                "client_id": ObjectId("63a3e8ee12e0defb392be622"),
                "file_id": "32487-ptt-uat-july-21",
                "deleted": False,
                "created_at": datetime(2023, 1, 2, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 15, 46, 0),
                "banking_id": ObjectId("62da91d9ab74518c805144db"),
            },
            {
                "_id": ObjectId("63a53579935af3a178a1f497"),
                "amount": 200,
                "lead_pax": "Howard",
                "pax_count": 2,
                "booking_date": "2019-07-20",
                "departure_date": "2020-04-23",
                "return_date": "2020-04-30",
                "currency_code": "GBP",
                "client_id": ObjectId("63a3e8ee12e0defb392be622"),
                "file_id": "32487-ptt-uat-july-21",
                "deleted": False,
                "created_at": datetime(2023, 1, 5, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 15, 46, 0),
                "banking_id": ObjectId("62da91d9ab74518c805144db"),
            },
            {
                "_id": ObjectId("63a53579935af3a178a1f498"),
                "amount": 200,
                "lead_pax": "Howard",
                "pax_count": 2,
                "booking_date": "2019-07-20",
                "departure_date": "2020-04-23",
                "return_date": "2020-04-30",
                "currency_code": "GBP",
                "client_id": ObjectId("63a3e8ee12e0defb392be622"),
                "file_id": "32487-ptt-uat-july-21",
                "deleted": False,
                "created_at": datetime(2023, 1, 6, 14, 46, 0),
                "updated_at": datetime(2022, 11, 29, 15, 46, 0),
                "banking_id": ObjectId("62da91d9ab74518c805144db"),
            },
        ]
    )
    monkeypatch.setattr(
        patch_db.banking_file_details,
        "aggregate",
        lambda *args, **kwargs: iter(
            [{"date": "2023-01-02", "count": 2}, {"date": "2023-01-05", "count": 1}, {"date": "2023-01-06", "count": 1}]
        ),
    )
    # When
    response = dashboard_service.uploaded_transactions(user_id, data)

    # Then
    assert response == [
        {"count": 0, "date": "2023-01-01"},
        {"count": 2, "date": "2023-01-02"},
        {"count": 0, "date": "2023-01-03"},
        {"count": 0, "date": "2023-01-04"},
        {"count": 1, "date": "2023-01-05"},
        {"count": 1, "date": "2023-01-06"},
        {"count": 0, "date": "2023-01-07"},
        {"count": 0, "date": "2023-01-08"},
        {"count": 0, "date": "2023-01-09"},
        {"count": 0, "date": "2023-01-10"},
        {"count": 0, "date": "2023-01-11"},
        {"count": 0, "date": "2023-01-12"},
    ]


def test_expiry_details(flask_client, patch_db, monkeypatch):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    data = {"currency": "GBP", "fromDate": "2022-01-20", "toDate": "2022-01-25"}
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("63a3e8ee12e0defb392be622"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )

    monkeypatch.setattr(
        patch_db.trust_fund_v2,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "date": "2022-01-20",
                "visa120": 100,
                "visa540": 50,
                "masterCard120": 0,
                "masterCard540": 0,
            },
            {
                "date": "2022-01-23",
                "visa120": 0,
                "visa540": 0,
                "masterCard120": 230,
                "masterCard540": 120,
            },
            {
                "date": "2022-01-24",
                "visa120": 0,
                "visa540": 0,
                "masterCard120": 0,
                "masterCard540": 0,
            },
            {
                "date": "2022-01-25",
                "visa120": 7890,
                "visa540": 230,
                "masterCard120": 0,
                "masterCard540": 0,
            },
        ],
    )
    # When
    response = dashboard_service.expiry_details(user_id, data)

    # Then
    assert response == [
        {
            "date": "2022-01-20",
            "visa120": 100,
            "visa540": 50,
            "masterCard120": 0,
            "masterCard540": 0,
        },
        {
            "date": "2022-01-21",
            "visa120": 0,
            "visa540": 0,
            "masterCard120": 0,
            "masterCard540": 0,
        },
        {
            "date": "2022-01-22",
            "visa120": 0,
            "visa540": 0,
            "masterCard120": 0,
            "masterCard540": 0,
        },
        {
            "date": "2022-01-23",
            "visa120": 0,
            "visa540": 0,
            "masterCard120": 230,
            "masterCard540": 120,
        },
        {
            "date": "2022-01-24",
            "visa120": 0,
            "visa540": 0,
            "masterCard120": 0,
            "masterCard540": 0,
        },
        {
            "date": "2022-01-25",
            "visa120": 7890,
            "visa540": 230,
            "masterCard120": 0,
            "masterCard540": 0,
        },
    ]


def test_ytd_bookings_abort(flask_client, patch_db):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("63a3e8ee12e0defb392be622"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    # When
    with pytest.raises(Forbidden):
        dashboard_service.ytd_bookings("2023", "2022", "63a3e8ee12e0defb392b9622", "GBP", user_id)


def test_ytd_payments_abort(flask_client, patch_db):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("63a3e8ee12e0defb392be622"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    # When
    with pytest.raises(Forbidden):
        dashboard_service.ytd_payments("2023", "2022", "63a3e8ee12e0defb392b9622", "GBP", user_id)


def test_ytd_claims_abort(flask_client, patch_db):
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("63a3e8ee12e0defb392be622"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    # When
    with pytest.raises(Forbidden):
        dashboard_service.ytd_claims("2023", "2022", "63a3e8ee12e0defb392b9622", "GBP", user_id)
