from datetime import datetime
from flask import current_app
import pytest
from freezegun import freeze_time
from hamcrest import assert_that, calling, raises
from mock import MagicMock, patch
from mongomock import ObjectId
from flaskr.models import get_db
from flaskr.models.claims.metadata import ClaimsMetadataSchema
from flaskr.services.auth_service import AuthService
from flaskr.services.booking_service import booking_service
from flaskr.services.exceptions import ServiceException


def test_list_payments(flask_client, patch_db):
    # Given
    client_id = ObjectId("62160dcda883a1dc69096743")
    booking_ref = "123"
    query = ""
    banking = [
        {
            "_id": ObjectId("62564a4e600d53e857f9ae53"),
            "client_id": ObjectId("62160dcda883a1dc69096743"),
            "booking_ref": "123",
            "amount": 9000,
            "banking_id": "62564a4e600d53e857f9ae52",
            "booking_date": "2022-01-31",
            "currency_code": "EUR",
            "customer_type": "abcd",
            "payment_type": "MI",
            "departure_date": "2021-06-08",
            "payment_date": "2021-01-18",
            "return_date": "2021-08-12",
            "deleted": False,
        },
        {
            "_id": ObjectId("62564a4e600d53e857f9ae55"),
            "client_id": ObjectId("62160dcda883a1dc69096743"),
            "booking_ref": "123",
            "amount": 9000,
            "banking_id": "62564a4e600d53e857f9ae52",
            "booking_date": "2022-01-31",
            "currency_code": "EUR",
            "customer_type": "abcd",
            "payment_type": "Credit",
            "departure_date": "2021-06-08",
            "payment_date": "2021-01-18",
            "return_date": "2021-08-12",
            "deleted": False,
        },
        {
            "_id": ObjectId("62564a4e600d53e857f9ae54"),
            "client_id": ObjectId("62160dcda883a1dc69096743"),
            "booking_ref": "123",
            "amount": -1000,
            "banking_id": "62564a4e600d53e857f9ae52",
            "booking_date": "2022-01-31",
            "currency_code": "EUR",
            "customer_type": "abcd",
            "payment_type": "VI",
            "departure_date": "2021-06-08",
            "payment_date": "2021-01-18",
            "return_date": "2021-08-12",
            "deleted": False,
        },
    ]

    patch_db.banking_file_details.insert_many(banking)
    # When
    result = booking_service.list_payments(client_id, booking_ref, query)

    # Then
    assert result == {
        "deposits": 18000.0,
        "count": 3,
        "payments": [
            {
                "_id": "62564a4e600d53e857f9ae53",
                "amount": 9000,
                "bankingId": "62564a4e600d53e857f9ae52",
                "bookingDate": "2022-01-31",
                "currencyCode": "EUR",
                "customerType": "abcd",
                "paymentType": "MI",
                "departureDate": "2021-06-08",
                "paymentDate": "2021-01-18",
                "returnDate": "2021-08-12",
            },
            {
                "_id": "62564a4e600d53e857f9ae55",
                "amount": 9000,
                "bankingId": "62564a4e600d53e857f9ae52",
                "bookingDate": "2022-01-31",
                "currencyCode": "EUR",
                "customerType": "abcd",
                "paymentType": "Credit",
                "departureDate": "2021-06-08",
                "paymentDate": "2021-01-18",
                "returnDate": "2021-08-12",
            },
            {
                "_id": "62564a4e600d53e857f9ae54",
                "amount": -1000,
                "bankingId": "62564a4e600d53e857f9ae52",
                "bookingDate": "2022-01-31",
                "currencyCode": "EUR",
                "customerType": "abcd",
                "paymentType": "VI",
                "departureDate": "2021-06-08",
                "paymentDate": "2021-01-18",
                "returnDate": "2021-08-12",
            },
        ],
        "refunds": -1000,
        "total": 17000,
    }


def test_booking_search(flask_client, patch_db):
    # Given
    booking_ref = "123"
    client_basic_info = get_db().client_basic_info.insert_one(
        {
            "client_id": "1",
            "full_name": "ABC",
            "friendly_name": "edstem",
        }
    )

    trust_fund = get_db().trust_fund_v2.insert_one(
        {
            "client_id": client_basic_info.inserted_id,
            "booking_ref": "123",
            "balance": 1000,
            "total_in_trust": 500,
            "total_claimed": 500,
            "lead_pax": "abc",
            "booking_date": "2022-01-31",
            "departure_date": "2021-08-08",
            "return_date": "2021-08-12",
            "pax_count": 5,
            "bonding": "string",
            "total_booking_value": 151,
            "total_due_to_supplier": 200,
            "total_paid_by_customer": 30000,
            "date_customer_paid": "2022-02-12",
            "supplier": "asd",
            "created_at": datetime.fromisoformat("2020-05-13"),
            "updated_at": datetime.fromisoformat("2020-06-24"),
        }
    )

    output = {
        "_id": str(trust_fund.inserted_id),
        "balance": 1000.0,
        "bonding": "string",
        "bookingDate": "2022-01-31",
        "clientName": "ABC",
        "friendlyName": "edstem",
        "createdAt": "2020-05-13T00:00:00",
        "departureDate": "2021-08-08",
        "leadPax": "abc",
        "nights": 4,
        "paxCount": 5.0,
        "returnDate": "2021-08-12",
        "totalBookingValue": 151.0,
        "totalClaimed": 500.0,
        "totalInTrust": 500.0,
        "totalPaidByCustomer": 30000.0,
        "totalDueToSupplier": 200.0,
        "supplier": "asd",
        "dateCustomerPaid": "2022-02-12",
        "updatedAt": "2020-06-24T00:00:00",
    }

    # When
    result = booking_service.booking_search(client_basic_info.inserted_id, booking_ref)

    # Then
    assert result == output


def test_booking_client_not_found(flask_client, patch_db):
    # Given

    client_id = ObjectId("62160dcda883a1dc69096743")
    booking_ref = "123"

    # Then
    assert_that(
        calling(booking_service.booking_search).with_args(client_id, booking_ref),
        raises(ServiceException, "Client not found"),
    )


def test_booking_trust_not_found(flask_client, patch_db):
    # Given
    client_basic_info = get_db().client_basic_info.insert_one({"client_id": "1", "full_name": "ABC"})

    client_id = client_basic_info.inserted_id
    booking_ref = ""

    # Then
    assert_that(
        calling(booking_service.booking_search).with_args(client_id, booking_ref),
        raises(ServiceException, "Booking info not found"),
    )


@patch.object(
    AuthService,
    "ptt_users_list",
    MagicMock(
        return_value=[
            {
                "userId": "cf0142a5-7475-4910-9321-40c323d7a876",
                "email": "<EMAIL>",
                "name": "test",
            }
        ]
    ),
)
def test_booking_claim_checks(flask_client, patch_db):
    # Given
    lookup_trust = get_db().lookup_trust_type.insert_one({"name": "ATOL Standard"})
    client_basic_info = get_db().client_basic_info.insert_one(
        {"c_id": "1", "type_of_trust_account": lookup_trust.inserted_id}
    )
    get_db().client_basic_info.insert_one({"c_id": "2", "type_of_trust_account": lookup_trust.inserted_id})

    client_id = client_basic_info.inserted_id
    transaction_id = "6202181f1eed04813999f1e7"

    booking_check = get_db().booking_claim_checks.insert_one(
        {
            "client_id": client_id,
            "transaction_id": ObjectId("6202181f1eed04813999f1e7"),
            "name": "Claim Letter",
            "description": "Agree total amount with claim letter and ensure compliance with requirements of the Trust Deed",
            "notes": "string",
            "selected": True,
            "modified_by": "cf0142a5-7475-4910-9321-40c323d7a876",
            "updated_at": datetime.fromisoformat("2022-02-28"),
        }
    )

    get_db().client_check_info.insert_one(
        {
            "client_id": client_id,
            "transaction_id": ObjectId("6202181f1eed04813999f1e7"),
            "name": "Claim Letter",
            "description": "Agree total amount with claim letter and ensure compliance with requirements of the Trust Deed",
            "notes": "string",
            "selected": True,
            "modified_by": "cf0142a5-7475-4910-9321-40c323d7a876",
        }
    )

    get_db().lookup_default_checks.insert_many(
        [
            {
                "applicable_trust": "All",
                "description": "Agree total amount with claim letter and ensure compliance with requirements of the Trust Deed",
                "name": "Claim Letter",
                "short_name": "LTTR",
            },
            {
                "applicable_trust": "All",
                "description": "Check bookings on client booking system and ensure all data agree with information as per claim report",
                "name": "Element Totals Match Client Booking System",
                "short_name": "BKNG",
            },
            {
                "applicable_trust": "All",
                "description": "Check proof of payment by customer",
                "name": "Payment Confirmation",
                "short_name": "PAYM",
            },
            {
                "applicable_trust": "ATOL Standard",
                "description": "Check proof of payment to IATA",
                "name": "Proof of payment to IATA",
                "short_name": "IATA",
            },
            {
                "applicable_trust": "Non-Flight PTR 2018",
                "description": "Ensure ASPP approved by the insurer and is in date",
                "name": "ASPP",
                "short_name": "ASPP",
            },
            {
                "applicable_trust": "Tripartite MA",
                "description": "Claim date should be less or equal to xx days/weeks from departure date",
                "name": "Claim date <= xx days/weeks from dep. date",
                "short_name": "CD<=DEP-xx days/weeks",
            },
            {
                "applicable_trust": "Tripartite Tour Op",
                "description": "Ensure that Deposit amount claimed is no more than £xx per pax",
                "name": "Deposit",
                "short_name": "DEP TO",
            },
        ]
    )

    output = [
        {
            "_id": str(booking_check.inserted_id),
            "transactionId": "6202181f1eed04813999f1e7",
            "clientId": "1",
            "description": "Agree total amount with claim letter and ensure compliance with requirements of the Trust Deed",
            "notes": "string",
            "name": "Claim Letter",
            "selected": True,
            "modifiedBy": "test",
            "updatedAt": datetime.fromisoformat("2022-02-28").isoformat(),
        }
    ]

    # When
    result = booking_service.booking_claim_checks(client_id, transaction_id)

    # Then
    assert result == output


@patch.object(
    AuthService,
    "ptt_users_list",
    MagicMock(
        return_value=[
            {
                "userId": "cf0142a5-7475-4910-9321-40c323d7a876",
                "email": "<EMAIL>",
                "name": "test",
            }
        ]
    ),
)
def test_booking_claim_checks_without_booking_claim_checks(flask_client, patch_db):
    # Given
    lookup_trust = get_db().lookup_trust_type.insert_one({"name": "ATOL Standard"})
    client_basic_info = get_db().client_basic_info.insert_one(
        {"c_id": "1", "type_of_trust_account": lookup_trust.inserted_id}
    )
    basic_info = get_db().client_basic_info.insert_one({"c_id": "2", "type_of_trust_account": lookup_trust.inserted_id})
    client_id = basic_info.inserted_id
    transaction_id = "6202181f1eed04813999f1e7"
    get_db().client_check_info.insert_one(
        {
            "client_id": client_basic_info.inserted_id,
            "transaction_id": ObjectId("6202181f1eed04813999f1e7"),
            "name": "Claim Letter",
            "description": "Agree total amount with claim letter and ensure compliance with requirements of the Trust Deed",
            "notes": "test_notes",
            "selected": True,
        }
    )

    get_db().lookup_default_checks.insert_one(
        {
            "applicable_trust": "All",
            "description": "Agree total amount with claim letter and ensure compliance with requirements of the Trust Deed",
            "name": "Claim Letter",
            "short_name": "LTTR",
        }
    )
    get_db().lookup_default_checks.insert_one(
        {
            "applicable_trust": "ATOL Standard",
            "description": "Check proof of payment to IATA",
            "name": "Proof of payment to IATA",
            "short_name": "IATA",
        }
    )

    get_db().lookup_default_checks.insert_one(
        {
            "applicable_trust": "All",
            "description": "Check proof of payment by customer",
            "name": "Payment Confirmation",
            "short_name": "PAYM",
        },
    )

    get_db().client_check_info.insert_one(
        {
            "check_name": "client_check",
            "description": "Agree total amount with claim letter and ensure compliance with requirements of the Trust Deed",
            "notes": "string1",
            "short_name": "string1",
            "related_element": "string1",
            "client_id": basic_info.inserted_id,
        }
    )

    # When
    booking_service.booking_claim_checks(client_id, transaction_id)
    booking_claim_checks = list(get_db().booking_claim_checks.find({"client_id": basic_info.inserted_id}))
    assert booking_claim_checks[0]["name"] == "Claim Letter"
    assert booking_claim_checks[1]["name"] == "Proof of payment to IATA"
    assert booking_claim_checks[2]["name"] == "Payment Confirmation"
    assert booking_claim_checks[3]["name"] == "client_check"


def test_booking_anomalies(flask_client, patch_db):
    # Given
    client_basic_info = get_db().client_basic_info.insert_one({"client_id": "1"})
    client_id = client_basic_info.inserted_id
    booking_ref = "977957"
    query = ""

    get_db().anomaly_banking.insert_many(
        [
            {
                "client_id": client_id,
                "booking_ref": "977957",
                "anomaly_type": "Negative funds in Trust",
                "status": "Unresolved",
                "anomaly_category": "banking",
                "deleted": False,
            },
            {
                "client_id": client_id,
                "booking_ref": "977957",
                "anomaly_type": "Negative funds in Trust",
                "status": "Unresolved",
                "anomaly_category": "banking",
                "deleted": True,
            },
        ]
    )
    get_db().anomaly_claims.insert_many(
        [
            {
                "client_id": client_id,
                "booking_ref": "977957",
                "anomaly_type": "Multi-currency booking",
                "status": "Unresolved",
                "anomaly_category": "claims",
                "deleted": False,
            },
            {
                "client_id": client_id,
                "booking_ref": "977957",
                "anomaly_type": "Multi-currency booking",
                "status": "Unresolved",
                "anomaly_category": "claims",
                "deleted": True,
            },
        ]
    )

    output = [
        {"anomalyCategory": "banking", "anomalyType": "Negative funds in Trust", "count": 1, "status": "Unresolved"},
        {"anomalyCategory": "claims", "anomalyType": "Multi-currency booking", "count": 1, "status": "Unresolved"},
    ]

    # When
    result = booking_service.list_anomalies(client_id, booking_ref, query)

    # Then
    assert result["anomalies"] == output


@freeze_time("Jan 1st 2022")
def test_update_booking_claim_checks_success(flask_client, patch_db):
    # Given
    check_id_1 = patch_db.booking_claim_checks.insert_one(
        {"client_id": "1", "notes": "string1", "selected": True}
    ).inserted_id
    check_id_2 = patch_db.booking_claim_checks.insert_one(
        {"client_id": "1", "notes": "string1", "selected": True}
    ).inserted_id

    data = [
        {"_id": check_id_1, "notes": "string3", "selected": True},
        {"_id": check_id_2, "notes": "string1", "selected": True},
    ]

    expected_output_1 = {
        "client_id": "1",
        "notes": "string3",
        "selected": True,
        "modified_by": "test_user_id",
        "updated_at": datetime(2022, 1, 1),
    }
    expected_output_2 = {
        "client_id": "1",
        "notes": "string1",
        "selected": True,
    }

    # When
    booking_service.update_booking_claim_checks(data, "test_user_id")

    # Then
    check_1 = patch_db.booking_claim_checks.find_one({"_id": check_id_1}, projection={"_id": 0})
    assert check_1 == expected_output_1
    check_2 = patch_db.booking_claim_checks.find_one({"_id": check_id_2}, projection={"_id": 0})
    assert check_2 == expected_output_2


def test_booking_claims(flask_client, patch_db):
    # Given
    client_basic_info = get_db().client_basic_info.insert_one({"client_id": "1"})
    client_id = client_basic_info.inserted_id
    booking_ref = "123"
    query = ""
    client_details = ClaimsMetadataSchema().load(
        {
            "clientId": client_id,
            "clientName": "ABC",
            "createdAt": "2020-05-19T18:30:00.000+00:00",
            "notes": "string",
            "status": "string",
            "updatedAt": "2020-08-24T18:30:00.000+00:00",
            "claimFiles": [
                {
                    "fileId": "111258412241665569",
                    "fileDate": "2022-02-24",
                    "claimTotal": {"GBP": 9000, "USD": 3500, "INR": 7000},
                    "fileName": "string",
                    "items": {"GBP": 2, "USD": 5, "INR": 1},
                    "status": "string",
                    "submittedDate": "2021-08-11T18:30:00.000+00:00",
                }
            ],
        }
    )

    metadata = get_db().claims_metadata.insert_one(client_details)

    claims = patch_db.claims_metadata.find_one({"client_id": client_id})
    file_date = claims["claim_files"][-1]["file_date"]

    claims = get_db().claims_file_details.insert_one(
        {
            "client_id": client_id,
            "booking_ref": "123",
            "booking_date": "2022-07-12",
            "element": "Performance",
            "amount": 9000,
            "currency_code": "GBP",
            "claims_id": metadata.inserted_id,
            "deleted": False,
            "type": "Direct",
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "total_due_to_supplier": 12,
        }
    )

    output = [
        {
            "_id": str(claims.inserted_id),
            "bookingDate": "2022-07-12",
            "amount": 9000.0,
            "claimsId": str(metadata.inserted_id),
            "currencyCode": "GBP",
            "claimDate": file_date,
            "element": "Performance",
            "supplierRef": "A45EX",
            "supplierNames": "Hoseason",
            "totalDueToSupplier": 12.0,
            "type": "Direct",
        }
    ]

    # When
    result = booking_service.booking_claims(client_id, booking_ref, query)

    # Then
    assert result["claims"] == output
    assert result["count"] == len(output)


def test_booking_update(flask_client, patch_db):
    # Given
    client_basic_info = get_db().client_basic_info.insert_one({"client_id": "1"})
    data = {
        "client_id": client_basic_info.inserted_id,
        "booking_ref": "123",
        "lead_pax": "Emma",
        "booking_date": "2020-12-14",
        "departure_date": "2020-12-21",
        "return_date": "2020-02-12",
        "total_due_to_supplier": 2000,
        "total_paid_by_customer": 30000,
        "date_customer_paid": "2022-12-22",
        "total_booking_value": 151,
        "supplier": "wsd",
    }

    get_db().trust_fund_v2.insert_one(
        {
            "client_id": client_basic_info.inserted_id,
            "booking_ref": "123",
            "balance": 1000,
            "total_in_trust": 500,
            "total_claimed": 500,
            "lead_pax": "abc",
            "booking_date": "2022-01-31",
            "departure_date": "2021-08-08",
            "return_date": "2021-08-12",
            "total_due_to_supplier": 1000,
            "total_paid_by_customer": 20000,
            "date_customer_paid": "2021-12-21",
            "supplier": "qwe",
            "pax_count": 5,
            "bonding": "string",
            "total_booking_value": 120,
            "created_at": datetime.fromisoformat("2020-05-13"),
            "updated_at": datetime.fromisoformat("2020-06-24"),
        }
    )
    # When

    booking_service.booking_update(data)

    # Then
    booking = patch_db.trust_fund_v2.find_one({"client_id": data["client_id"]})
    assert booking["lead_pax"] == "Emma"
    assert booking["booking_date"] == "2020-12-14"
    assert booking["departure_date"] == "2020-12-21"
    assert booking["return_date"] == "2020-02-12"
    assert booking["total_due_to_supplier"] == 2000
    assert booking["total_paid_by_customer"] == 30000
    assert booking["date_customer_paid"] == "2022-12-22"
    assert booking["supplier"] == "wsd"
    assert booking["total_booking_value"] == 151


def test_booking_update_departure_date_and_return_date(flask_client, patch_db):
    # Given
    client_basic_info = get_db().client_basic_info.insert_one({"client_id": "1"})
    data = {
        "client_id": client_basic_info.inserted_id,
        "booking_ref": "123",
        "lead_pax": "Emma",
        "departure_date": "2021-12-21",
        "return_date": "2021-12-25",
        "booking_date": "2021-12-21",
    }

    get_db().trust_fund_v2.insert_one(
        {
            "client_id": client_basic_info.inserted_id,
            "booking_ref": "123",
            "balance": 1000,
            "total_in_trust": 500,
            "total_claimed": 500,
            "lead_pax": "abc",
            "booking_date": "2022-01-31",
            "pax_count": 5,
            "bonding": "string",
            "total_booking_value": 151,
            "created_at": datetime.fromisoformat("2020-05-13"),
            "updated_at": datetime.fromisoformat("2020-06-24"),
        }
    )
    # When

    booking_service.booking_update(data)

    # Then
    booking = patch_db.trust_fund_v2.find_one({"client_id": data["client_id"]})
    assert booking["lead_pax"] == "Emma"
    assert booking["departure_date"] == "2021-12-21"
    assert booking["return_date"] == "2021-12-25"


def test_booking_update_booking_not_found(flask_client, patch_db):
    # Given
    client_basic_info = get_db().client_basic_info.insert_one({"client_id": "1"})
    client_id = client_basic_info.inserted_id
    request = {"client_id": client_id, "booking_ref": "123", "lead_pax": "Emma"}

    # Then
    assert_that(
        calling(booking_service.booking_update).with_args(request),
        raises(ServiceException, "booking not found"),
    )


def test_booking_update_Invalid_date(flask_client, patch_db):
    # Given
    client_basic_info = get_db().client_basic_info.insert_one({"client_id": "1"})
    client_id = client_basic_info.inserted_id
    data = {
        "client_id": client_id,
        "booking_ref": "123",
        "lead_pax": "Emma",
        "departure_date": "2021-12-21",
        "return_date": "2021-1225",
        "booking_date": "2021-12-25",
    }
    assert_that(
        calling(booking_service.booking_update).with_args(data),
        raises(ServiceException, "Invalid Date Formats"),
    )


@pytest.mark.parametrize(
    "input_booking_date,existing_booking_date",
    [
        ("2022-04-03", "2022-03-01"),
        ("2022-03-01", "2022-04-03"),
        ("2022-04-01", "2022-03-31"),
        ("2022-03-31", "2022-04-01"),
    ],
)
def test_booking_update_major_travel_service_exception(
    flask_client, patch_db, input_booking_date, existing_booking_date
):
    # Given
    data = {
        "client_id": "621c4699b29069e5c622ca88",
        "booking_ref": "123",
        "booking_date": input_booking_date,
    }
    get_db().trust_fund_v2.insert_one(
        {
            "client_id": ObjectId("621c4699b29069e5c622ca88"),
            "booking_ref": "123",
            "balance": 1000,
            "total_in_trust": 500,
            "total_claimed": 500,
            "lead_pax": "abc",
            "booking_date": existing_booking_date,
            "pax_count": 5,
            "bonding": "string",
            "total_booking_value": 151,
            "created_at": datetime.fromisoformat("2020-05-13"),
            "updated_at": datetime.fromisoformat("2020-06-24"),
        }
    )
    current_app.config["MAJOR_TRAVEL"] = "621c4699b29069e5c622ca88"
    assert_that(
        calling(booking_service.booking_update).with_args(data),
        raises(ServiceException, "Booking is not editable for the given booking date"),
    )
