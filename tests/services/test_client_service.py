from datetime import datetime
from mock import MagicMock, patch
from mongomock import ObjectId

import pytest
from hamcrest import assert_that, calling, raises
import re
from werkzeug.datastructures import FileStorage
from flaskr.services.auth_service import AuthService

from tests.data.client_address_info import ClientAddressRequestBuilder, ClientAddressRequestInvalidData
from tests.data.client_banking_column import ClientBankingColumnRequestBuilder, ClientBankingColumnRequestInvalidData
from tests.data.client_basic_info import ClientBasicInfoRequestBuilder, ClientBasicInfoRequestInvalidData
from tests.data.client_check_info import ClientCheckInfoRequestBuilder, ClientCheckInfoRequestInvalidData
from tests.data.client_bank_info import ClientBankInfoRequestBuilder, ClientBankInfoRequestInvalidData
from tests.data.client_atol_info import ClientAtolInfoRequestBuilder, ClientAtolInfoRequestInvalidData
from tests.data.client_claims_column import ClientClaimColumnRequestBuilder, ClientClaimColumnRequestInvalidData
from tests.data.client_frequency_info import ClientClaimFrequencyRequestBuilder, ClientClaimFrequencyRequestInvalidData
from tests.data.client_insurance_info import ClientInsuranceInfoRequestBuilder, ClientInsuranceInfoRequestInvalidData
from tests.data.client_anomalies import ClientAnomalyRequestBuilder, ClientAnomalyRequestInvalidData
from tests.data.client_limt_info import ClientLimitRequestBuilder, ClientLimitRequestInvalidData
from marshmallow import ValidationError
from flaskr.services.client_service import client_service, ClientService
from flaskr.services.exceptions import ServiceException
from werkzeug.exceptions import NotFound
from freezegun import freeze_time


@pytest.mark.parametrize(
    "additionChecks",
    [
        [],
        [ClientCheckInfoRequestBuilder.a_client_check_info_request().build()],
        [
            ClientCheckInfoRequestBuilder.a_client_check_info_request().build(),
            ClientCheckInfoRequestBuilder.a_client_check_info_request().build(),
        ],
    ],
)
def test_upsert_basic_info_create(flask_client, patch_db, additionChecks):
    # Given
    data = ClientBasicInfoRequestBuilder.a_client_basic_info_request().build()
    data["additionChecks"] = additionChecks
    data["address"] = ClientAddressRequestBuilder.a_client_address_request().build()
    data["limits"] = [
        {"maximumNoOfClaims": 1, "totalAnnualRevenue": 1000, "currency": "GBP", "preferred": True},
        {"maximumNoOfClaims": 1, "totalAnnualRevenue": 1000, "currency": "USD", "preferred": True},
        {"maximumNoOfClaims": 1, "totalAnnualRevenue": 1000, "currency": "INR"},
    ]

    data["frequency"] = ["61ee2f1dcf10b991afdaf548"]
    data["clientId"] = "81ee2f1dcf10b991afdaf548"
    data["cId"] = "101"
    patch_db.user.insert_many(
        [
            {"user_id": "1198a22d-f283-4bc5-9375-2a3069b5ca77", "role": "ptt-admin"},
            {"user_id": "1198a22d-f283-4bc5-9375-2a3069b5ca77", "role": "ptt-user"},
        ]
    )
    # When
    result = client_service.upsert_basic_info(data)

    # Then
    assert result == data["clientId"]
    basic_info = patch_db.client_basic_info.find_one({"_id": ObjectId(data["clientId"])})
    check_info_list = patch_db.client_check_info.find({"client_id": ObjectId(data["clientId"])})
    address_info = patch_db.client_address.find_one({"client_id": ObjectId(data["clientId"])})
    limits = patch_db.client_limit.find({"client_id": ObjectId(data["clientId"])})
    claim_frequency_list = patch_db.client_claim_frequency.find({"client_id": ObjectId(data["clientId"])})
    ptt_admin = patch_db.user.find_one({"role": "ptt-admin", "clients": {"$in": [ObjectId(data["clientId"])]}})
    ptt_user = patch_db.user.find_one({"role": "ptt-user", "clients": {"$in": [ObjectId(data["clientId"])]}})
    assert_client_basic_info(basic_info, {**data, "status": "draft"})
    assert_client_address(address_info, {**data["address"]})
    for index, check_info in enumerate(check_info_list):
        assert_client_check_info(check_info, {"clientId": ObjectId(data["clientId"]), **data["additionChecks"][index]})
    for index, limit in enumerate(limits):
        assert_client_limit(limit, {"clientId": ObjectId(data["clientId"]), **data["limits"][index]})
    for index, frequency_id in enumerate(data["frequency"]):
        assert_client_claim_frequency(
            claim_frequency_list[index], {"clientId": ObjectId(data["clientId"]), "frequencyId": frequency_id}
        )
    assert ptt_admin and ptt_user is not None


def test_upsert_basic_info_create_whitespace(flask_client, patch_db):
    # Given
    data = ClientBasicInfoRequestBuilder.a_client_basic_info_request().build()
    data["additionChecks"] = [ClientCheckInfoRequestBuilder.a_client_check_info_request().build()]
    data["address"] = ClientAddressRequestBuilder.a_client_address_request().build()
    data["limit"] = ClientLimitRequestBuilder.a_client_limit_request().build()
    data["frequency"] = ["61ee2f1dcf10b991afdaf548"]
    data["clientId"] = "81ee2f1dcf10b991afdaf548"
    data["cId"] = "1 0 1"
    # When
    result = client_service.upsert_basic_info(data)

    # Then
    assert result == "".join(data.get("clientId").split())
    basic_info = patch_db.client_basic_info.find_one({})
    assert basic_info["c_id"] == "101"


def test_upsert_basic_info_update(flask_client, patch_db):
    # Given
    patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("621bf4f49bed97189d98ddcd"),
            "c_id": "1",
            "create": True,
            "email": "<EMAIL>",
            "existing_client": True,
            "friendly_name": "ABC",
            "full_name": "ABC",
            "point_of_contact": "string",
            "reuse_old_booking": False,
            "type_of_trust_account": "61ef4bff0a719ab106fdc146",
            "additionChecks": [],
            "address": {},
            "limits": {},
            "frequency": [],
        },
    )
    data = {
        "clientId": "621bf4f49bed97189d98ddcd",
        "cId": "1",
        "email": "<EMAIL>",
        "existingClient": True,
        "friendlyName": "updated",
        "fullName": "updated",
        "goLiveDate": "2021-10-02",
        "pointOfContact": "string",
        "reuseOldBooking": False,
        "typeOfTrustAccount": "61ef4bff0a719ab106fdc146",
        "additionChecks": [],
        "address": {},
        "limits": {},
        "frequency": [],
    }

    # When
    result = client_service.upsert_basic_info(data)

    # Then
    assert result == data["clientId"]
    basic_info = patch_db.client_basic_info.find_one({"c_id": data["cId"]})
    assert basic_info["friendly_name"] == "updated"


def test_upsert_basic_info_disable_client(flask_client, patch_db):
    # Given
    patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("621bf4f49bed97189d98ddcd"),
            "c_id": "1",
            "create": True,
            "email": "<EMAIL>",
            "existing_client": True,
            "friendly_name": "ABC",
            "full_name": "ABC",
            "point_of_contact": "string",
            "reuse_old_booking": False,
            "type_of_trust_account": "61ef4e0da0d0ef7c56884319",
            "additionChecks": [],
            "address": {},
            "limits": {},
            "frequency": [],
            "is_disabled": True,
        },
    )
    # When
    result = client_service.upsert_basic_info({"clientId": "621bf4f49bed97189d98ddcd", "isDisabled": True})

    # Then
    basic_info = patch_db.client_basic_info.find_one({"_id": ObjectId("621bf4f49bed97189d98ddcd"), "c_id": "1"})
    assert basic_info["is_disabled"] is True
    assert result == "621bf4f49bed97189d98ddcd"


def test_upsert_basic_info_update_service_exception(flask_client, patch_db):
    # Given
    patch_db.client_basic_info.insert_one({"_id": ObjectId("621bf4f49bed97189d98ddcd"), "c_id": "2"})
    data = {
        "clientId": "621bf4f49bed97189d98ddc4",
        "cId": "2",
        "email": "<EMAIL>",
        "existingClient": True,
        "friendlyName": "updated",
        "fullName": "updated",
        "goLiveDate": "2021-10-02",
        "pointOfContact": "string",
        "reuseOldBooking": False,
        "typeOfTrustAccount": "61ef4e0da0d0ef7c56884319",
        "additionChecks": [],
        "address": {},
        "limits": {},
        "frequency": [],
    }

    # Then
    assert_that(
        calling(client_service.upsert_basic_info).with_args(data),
        raises(ServiceException, "Client not found"),
    )


@pytest.mark.parametrize(
    "invalid_data, message",
    [
        (
            ClientBasicInfoRequestInvalidData.FRIENDLY_NAME_MISSING,
            re.escape("{'friendlyName': ['Field may not be null.']}"),
        ),
        (ClientBasicInfoRequestInvalidData.FULL_NAME_MISSING, re.escape("{'fullName': ['Field may not be null.']}")),
        (
            ClientBasicInfoRequestInvalidData.GO_LIVE_DATE_MISSING,
            re.escape("{'goLiveDate': ['Field may not be null.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "additionChecks": [ClientCheckInfoRequestInvalidData.CHECK_NAME_MISSING],
            },
            re.escape("{'checkName': ['Field may not be null.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "additionChecks": [ClientCheckInfoRequestInvalidData.SHORT_NAME_MISSING],
            },
            re.escape("{'shortName': ['Field may not be null.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "additionChecks": [ClientCheckInfoRequestInvalidData.RELATED_ELEMENT_MISSING],
            },
            re.escape("{'relatedElement': ['Field may not be null.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "additionChecks": [
                    ClientCheckInfoRequestBuilder.a_client_check_info_request().build(),
                    ClientCheckInfoRequestInvalidData.DESCRIPTION_MISSING,
                ],
            },
            re.escape("{'description': ['Field may not be null.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "frequency": [
                    ClientClaimFrequencyRequestBuilder.a_client_claim_frequency_request().build(),
                    ClientClaimFrequencyRequestInvalidData.FREQUENCY_MISSING,
                ],
            },
            re.escape("{'frequencyId': ['invalid ObjectId']}"),
        ),
        (
            ClientBasicInfoRequestInvalidData.FRIENDLY_NAME_EMPTY,
            re.escape("{'friendlyName': ['Invalid value.']}"),
        ),
        (ClientBasicInfoRequestInvalidData.FULL_NAME_EMPTY, re.escape("{'fullName': ['Invalid value.']}")),
        (ClientBasicInfoRequestInvalidData.GO_LIVE_DATE_EMPTY, re.escape("{'goLiveDate': ['Not a valid datetime.']}")),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "additionChecks": [ClientCheckInfoRequestInvalidData.CHECK_NAME_EMPTY],
            },
            re.escape("{'checkName': ['Invalid value.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "additionChecks": [ClientCheckInfoRequestInvalidData.SHORT_NAME_EMPTY],
            },
            re.escape("{'shortName': ['Invalid value.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "additionChecks": [ClientCheckInfoRequestInvalidData.RELATED_ELEMENT_EMPTY],
            },
            re.escape("{'relatedElement': ['Invalid value.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "additionChecks": [
                    ClientCheckInfoRequestBuilder.a_client_check_info_request().build(),
                    ClientCheckInfoRequestInvalidData.DESCRIPTION_EMPTY,
                ],
            },
            re.escape("{'description': ['Invalid value.']}"),
        ),
        (
            ClientBasicInfoRequestInvalidData.FRIENDLY_NAME_INVALID,
            re.escape("{'friendlyName': ['Not a valid string.']}"),
        ),
        (ClientBasicInfoRequestInvalidData.FULL_NAME_INVALID, re.escape("{'fullName': ['Not a valid string.']}")),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "limits": [
                    {
                        **ClientLimitRequestBuilder.a_client_limit_request().build(),
                        "maximumNoOfClaims": ClientLimitRequestInvalidData.MAXIMUM_NO_OF_CLAIMS_INVALID,
                    }
                ],
            },
            re.escape("{'maximumNoOfClaims': ['Not a valid number.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "limits": [
                    {
                        **ClientLimitRequestBuilder.a_client_limit_request().build(),
                        "totalAnnualRevenue": ClientLimitRequestInvalidData.TOTAL_ANNUAL_REVENUE_INVALID,
                    }
                ],
            },
            re.escape("{'totalAnnualRevenue': ['Not a valid number.']}"),
        ),
        (
            ClientBasicInfoRequestInvalidData.TYPE_OF_TRUST_ACCOUNT_INVALID,
            re.escape("{'typeOfTrustAccount': ['invalid ObjectId']}"),
        ),
        (
            ClientBasicInfoRequestInvalidData.EXISTING_CLIENT_INVALID,
            re.escape("{'existingClient': ['Not a valid boolean.']}"),
        ),
        (
            ClientBasicInfoRequestInvalidData.GO_LIVE_DATE_INVALID,
            re.escape("{'goLiveDate': ['Not a valid datetime.']}"),
        ),
        (
            ClientBasicInfoRequestInvalidData.REUSE_OLD_BOOKING_INVALID,
            re.escape("{'reuseOldBooking': ['Not a valid boolean.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "frequency": [ClientClaimFrequencyRequestInvalidData.FREQUENCY_INVALID],
            },
            re.escape("{'frequencyId': ['invalid ObjectId']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "address": {
                    **ClientAddressRequestBuilder.a_client_address_request().build(),
                    "line1": ClientAddressRequestInvalidData.LINE1_INVALID,
                },
            },
            re.escape("{'line1': ['Not a valid string.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "address": {
                    **ClientAddressRequestBuilder.a_client_address_request().build(),
                    "line2": ClientAddressRequestInvalidData.LINE2_INVALID,
                },
            },
            re.escape("{'line2': ['Not a valid string.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "address": {
                    **ClientAddressRequestBuilder.a_client_address_request().build(),
                    "line3": ClientAddressRequestInvalidData.LINE3_INVALID,
                },
            },
            re.escape("{'line3': ['Not a valid string.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "address": {
                    **ClientAddressRequestBuilder.a_client_address_request().build(),
                    "town": ClientAddressRequestInvalidData.TOWN_INVALID,
                },
            },
            re.escape("{'town': ['Not a valid string.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "address": {
                    **ClientAddressRequestBuilder.a_client_address_request().build(),
                    "country": ClientAddressRequestInvalidData.COUNTRY_INVALID,
                },
            },
            re.escape("{'country': ['Not a valid string.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "address": {
                    **ClientAddressRequestBuilder.a_client_address_request().build(),
                    "postcode": ClientAddressRequestInvalidData.POSTCODE_INVALID,
                },
            },
            re.escape("{'postcode': ['Not a valid string.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "additionChecks": [ClientCheckInfoRequestInvalidData.CHECK_NAME_INVALID],
            },
            re.escape("{'checkName': ['Not a valid string.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "additionChecks": [ClientCheckInfoRequestInvalidData.SHORT_NAME_INVALID],
            },
            re.escape("{'shortName': ['Not a valid string.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "additionChecks": [ClientCheckInfoRequestInvalidData.RELATED_ELEMENT_INVALID],
            },
            re.escape("{'relatedElement': ['Not a valid string.']}"),
        ),
        (
            {
                **ClientBasicInfoRequestBuilder.a_client_basic_info_request().build(),
                "additionChecks": [
                    ClientCheckInfoRequestBuilder.a_client_check_info_request().build(),
                    ClientCheckInfoRequestInvalidData.DESCRIPTION_INVALID,
                ],
            },
            re.escape("{'description': ['Not a valid string.']}"),
        ),
        (
            ClientBasicInfoRequestInvalidData.SFTP_LOCATION_INVALID,
            re.escape("{'sftpLocation': ['Not a valid list.']}"),
        ),
        (
            ClientBasicInfoRequestInvalidData.SFTP_LOCATION_INVALID_FIELD_TYPE,
            re.escape("{'sftpLocation': {0: ['Not a valid string.']}}"),
        ),
    ],
)
def test_upsert_basic_info_validation_error(flask_client, patch_db, invalid_data, message):
    # Given
    data = invalid_data

    # Then
    assert_that(
        calling(client_service.upsert_basic_info).with_args(data),
        raises(ValidationError, message),
    )


@pytest.mark.parametrize(
    "invalid_data,message",
    [
        (ClientBasicInfoRequestInvalidData.CLIENT_ID_MISSING, re.escape("ClientId Missing")),
        (ClientBasicInfoRequestInvalidData.CLIENT_ID_EMPTY, re.escape("ClientId Missing")),
    ],
)
def test_upsert_basic_info_service_exception(flask_client, patch_db, invalid_data, message):
    # Given
    data = invalid_data

    # Then
    assert_that(
        calling(client_service.upsert_basic_info).with_args(data),
        raises(ServiceException, message),
    )


def test_upsert_bank_info_create(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"_id": ObjectId("621bf4f49bed97189d98ddcd"), "client_id": "1"})
    client_id = basic_info.inserted_id
    data = ClientBankInfoRequestBuilder.a_client_bank_info_request().build()
    data1 = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_updated_request().build()
    data_list = [data, data1]
    # When
    client_service.upsert_bank_info({"data": data_list, "clientId": client_id})

    # Then
    bank_info_list = patch_db.client_bank_acct_details.find({"client_id": client_id})
    for index, bank_info in enumerate(bank_info_list):
        assert_client_bank_info(bank_info, {**data_list[index], "clientId": client_id})


@pytest.mark.parametrize(
    "invalid_data, message",
    [
        (
            ClientBankInfoRequestInvalidData.BANK_NAME_INVALID,
            re.escape("{'bankName': ['Not a valid string.']}"),
        ),
        (
            ClientBankInfoRequestInvalidData.ACCOUNT_NUMBER_INVALID,
            re.escape("{'accountNumber': ['Not a valid string.']}"),
        ),
        (
            ClientBankInfoRequestInvalidData.SORT_CODE_INVALID,
            re.escape("{'sortCode': ['Not a valid string.']}"),
        ),
        (
            ClientBankInfoRequestInvalidData.CURRENCY_INVALID,
            re.escape("{'currency': ['Not a valid string.']}"),
        ),
        (
            ClientBankInfoRequestInvalidData.IBAN_INVALID,
            re.escape("{'iban': ['Not a valid string.']}"),
        ),
        (
            ClientBankInfoRequestInvalidData.ACCOUNT_TYPE_INVALID,
            re.escape("{'accountType': ['Not a valid string.']}"),
        ),
        (
            ClientBankInfoRequestInvalidData.EXCLUDE_FROM_REPORT_INVALID,
            re.escape("{'excludeFromReport': ['Not a valid boolean.']}"),
        ),
    ],
)
def test_upsert_bank_info_validation_error(flask_client, patch_db, invalid_data, message):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"_id": ObjectId("621bf4f49bed97189d98ddcd"), "client_id": "1"})
    client_id = basic_info.inserted_id
    data1 = ClientBankInfoRequestBuilder.a_client_bank_info_request().build()
    data_list = [invalid_data, data1]

    # Then
    assert_that(
        calling(client_service.upsert_bank_info).with_args({"data": data_list, "clientId": client_id}),
        raises(ValidationError, message),
    )
    bank_info_list = list(patch_db.client_bank_acct_details.find({"client_id": client_id}))
    assert len(bank_info_list) == 0


@pytest.mark.parametrize(
    "data",
    [
        (ClientAnomalyRequestBuilder.a_client_anomaly_request().build()),
        (ClientAnomalyRequestBuilder.a_client_anomaly_request().build()),
    ],
)
def test_client_anomaly_success(flask_client, patch_db, data):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"_id": ObjectId("621bf4f49bed97189d98ddcd"), "client_id": "1"})
    client_id = basic_info.inserted_id
    data_list = data

    # When
    client_service.upsert_anomalies({**data_list, "clientId": client_id})

    # Then
    anomaly_list = patch_db.client_anomaly.find({"client_id": client_id})
    for index, item in enumerate(anomaly_list):
        assert_client_anomalies(
            anomaly_list[index],
            {"clientId": client_id, "anomalyId": item["anomaly_id"], "customFieldValue": item["custom_field_value"]},
        )


@pytest.mark.parametrize(
    "invalid_data, message",
    [
        (
            ClientAnomalyRequestInvalidData.ANOMALY_ID_INVALID,
            re.escape("{'anomalyId': ['invalid ObjectId']}"),
        ),
        (
            {
                "anomalyId": "11ef4bff0a719ab106fdc14e",
                "customFieldValue": {**ClientAnomalyRequestInvalidData.CUSTOM_FIELD_VALUE_INVALID},
            },
            re.escape("{'customFieldValue': ['Not a valid number.']}"),
        ),
    ],
)
def test_client_anomaly_validation_error(flask_client, patch_db, invalid_data, message):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"_id": ObjectId("621bf4f49bed97189d98ddcd"), "client_id": "1"})
    client_id = basic_info.inserted_id
    data = ClientAnomalyRequestBuilder.a_client_anomaly_request().build()
    data_list = [invalid_data, data]

    # Then
    assert_that(
        calling(client_service.upsert_anomalies).with_args({"anomalies": data_list, "clientId": client_id}),
        raises(ValidationError, message),
    )
    anomaly_list = list(patch_db.client_anomaly.find({"client_id": client_id}))
    assert len(anomaly_list) == 0


def test_upsert_atol_info_create(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"_id": ObjectId("621bf4f49bed97189d98ddcd"), "client_id": "1"})
    client_id = basic_info.inserted_id
    data = ClientAtolInfoRequestBuilder.a_client_atol_info_request().build()
    data1 = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_updated_request().build()
    data_list = [data, data1]

    # When
    client_service.upsert_insurance_atol({"data": data_list, "clientId": client_id})

    # Then
    atol_info_list = patch_db.client_atol_info.find({"client_id": client_id})

    for index, atol_info in enumerate(atol_info_list):
        assert_client_atol_info(atol_info, {**data_list[index], "clientId": client_id})


@pytest.mark.parametrize(
    "invalid_data, message",
    [
        (
            ClientAtolInfoRequestInvalidData.LICENSE_INVALID,
            re.escape("{'license': ['Not a valid string.']}"),
        ),
        (
            ClientAtolInfoRequestInvalidData.START_DATE_INVALID,
            re.escape("{'startDate': ['Not a valid datetime.']}"),
        ),
        (
            ClientAtolInfoRequestInvalidData.EXPIRY_DATE_INVALID,
            re.escape("{'expiryDate': ['Not a valid datetime.']}"),
        ),
    ],
)
def test_upsert_atol_info_validation_error(flask_client, patch_db, invalid_data, message):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"_id": ObjectId("621bf4f49bed97189d98ddcd"), "client_id": "1"})
    client_id = basic_info.inserted_id
    data = ClientAtolInfoRequestBuilder.a_client_atol_info_request().build()
    data_list = [invalid_data, data]

    # Then
    assert_that(
        calling(client_service.upsert_insurance_atol).with_args({"atol": data_list, "clientId": client_id}),
        raises(ValidationError, message),
    )

    atol_info_list = list(patch_db.client_atol_info.find({"client_id": client_id}))
    assert len(atol_info_list) == 0


def test_upsert_insurance_info_create(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    data = ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().build()
    data1 = ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_updated_request().build()
    data_list = [data, data1]

    # When
    client_service.upsert_insurance_atol({"data": data_list, "clientId": client_id})

    # Then
    insurance_info_list = patch_db.client_insurance_info.find({"client_id": client_id})
    for index, insurance_info in enumerate(insurance_info_list):
        assert_insurance_info(insurance_info, {**data_list[index], "clientId": client_id})


@pytest.mark.parametrize(
    "invalid_data, message",
    [
        (
            ClientInsuranceInfoRequestInvalidData.PROVIDER_INVALID,
            re.escape("{'provider': ['Not a valid string.']}"),
        ),
        (
            ClientInsuranceInfoRequestInvalidData.EXPIRY_DATE_INVALID,
            re.escape("{'expiryDate': ['Not a valid datetime.']}"),
        ),
        (
            ClientInsuranceInfoRequestInvalidData.POLICY_NUMBER_INVALID,
            re.escape("{'policyNumber': ['Not a valid string.']}"),
        ),
    ],
)
def test_upsert_insurance_info_validation_error(flask_client, patch_db, invalid_data, message):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"_id": ObjectId("621bf4f49bed97189d98ddcd"), "client_id": "1"})
    client_id = basic_info.inserted_id
    data = ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().build()
    data_list = [invalid_data, data]
    # client_id = ObjectId("621bf4f49bed97189d98ddcd")

    # Then
    assert_that(
        calling(client_service.upsert_insurance_atol).with_args({"insurance": data_list, "clientId": client_id}),
        raises(ValidationError, message),
    )

    insurance_info_list = list(patch_db.client_insurance_info.find({"client_id": client_id}))
    assert len(insurance_info_list) == 0


@patch.object(
    ClientService,
    "_ClientService__total_annual_revenue_and_claim_limit_calculation",
    MagicMock(
        return_value={
            "isAnnualRevenueLimitExceeded": True,
            "isClaimsExceeded": False,
            "TotalAnnualRevenue": 0,
            "totalNoOfClaims": 0,
        }
    ),
)
@pytest.mark.parametrize(
    "client_limit, client_limit_output",
    [
        (
            None,
            [],
        ),
        (
            {
                "_id": ObjectId("6285aeb1504c11ba490ffd32"),
                "client_id": ObjectId("621bf4f49bed97189d98ddcd"),
                "currency": "EUR",
                "max_no_of_claims": 1,
                "total_annual_revenue": 1000,
                "from_date": datetime(2022, 4, 13, 3, 58, 6, 805000),
                "to_date": datetime(2022, 5, 13, 3, 58, 6, 805000),
            },
            [
                {
                    "clientId": "621bf4f49bed97189d98ddcd",
                    "currency": "EUR",
                    "id": "6285aeb1504c11ba490ffd32",
                    "maximumNoOfClaims": 1.0,
                    "isClaimsExceeded": False,
                    "isAnnualRevenueLimitExceeded": True,
                    "fromDate": "2022-04-13",
                    "toDate": "2022-05-13",
                    "totalAnnualRevenue": 1000.0,
                }
            ],
        ),
        (
            {
                "_id": ObjectId("6285aeb1504c11ba490ffd32"),
                "client_id": ObjectId("621bf4f49bed97189d98ddcd"),
                "currency": "EUR",
                "max_no_of_claims": 1.0,
                "total_annual_revenue": 12.0,
                "from_date": datetime(2022, 5, 14, 3, 58, 6, 805000),
                "to_date": datetime(2022, 6, 13, 3, 58, 6, 805000),
                "name": "",
            },
            [
                {
                    "clientId": "621bf4f49bed97189d98ddcd",
                    "id": "6285aeb1504c11ba490ffd32",
                    "currency": "EUR",
                    "maximumNoOfClaims": 1.0,
                    "totalAnnualRevenue": 12.0,
                    "isClaimsExceeded": False,
                    "isAnnualRevenueLimitExceeded": True,
                    "fromDate": "2022-05-14",
                    "toDate": "2022-06-13",
                }
            ],
        ),
        (
            {
                "_id": ObjectId("6285aeb1504c11ba490ffd32"),
                "client_id": ObjectId("621bf4f49bed97189d98ddcd"),
                "currency": "EUR",
                "max_no_of_claims": 2.0,
                "total_annual_revenue": 12.0,
                "from_date": datetime(2022, 6, 14, 3, 58, 6, 805000),
                "to_date": datetime(2022, 7, 13, 3, 58, 6, 805000),
            },
            [
                {
                    "clientId": "621bf4f49bed97189d98ddcd",
                    "currency": "EUR",
                    "id": "6285aeb1504c11ba490ffd32",
                    "maximumNoOfClaims": 2.0,
                    "totalAnnualRevenue": 12.0,
                    "isClaimsExceeded": False,
                    "isAnnualRevenueLimitExceeded": True,
                    "fromDate": "2022-06-14",
                    "toDate": "2022-07-13",
                }
            ],
        ),
    ],
)
def test_fetch_client_details(flask_client, patch_db, client_limit, client_limit_output):
    # Given
    basic_info_details = patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("621bf4f49bed97189d98ddcd"),
            "c_id": "1",
            "email": "<EMAIL>",
            "existing_client": True,
            "friendly_name": "updated",
            "full_name": "ABC",
            "go_live_date": datetime.fromisoformat("2021-10-02"),
            "point_of_contact": "string",
            "reuse_old_booking": False,
            "type_of_trust_account": ObjectId("61ef4e0da0d0ef7c56884319"),
            "status": "string",
        }
    )
    client_id = basic_info_details.inserted_id
    patch_db.lookup_trust_type.insert_one({"_id": ObjectId("61ef4e0da0d0ef7c56884319"), "name": "Tripartite Tour Op"})
    check_info_details = patch_db.client_check_info.insert_one(
        {
            "check_name": "updated",
            "description": "string1",
            "related_element": "string1",
            "client_id": client_id,
            "short_name": "string1",
        }
    )
    address_info_details = patch_db.client_address.insert_one(
        {
            "client_id": client_id,
            "country": "string",
            "line1": "updated",
            "line2": "string",
            "line3": "string",
            "postcode": "string",
            "town": "string",
        }
    )
    patch_db.client_limit.insert_one(client_limit) if client_limit else None
    patch_db.client_claim_frequency.insert_one({"client_id": client_id, "frequency_id": "61ee2f1dcf10b991afdaf54a"})
    bank_info_details = patch_db.client_bank_acct_details.insert_one(
        {
            "sort_code": "string",
            "account_no": "string",
            "iban": "47548",
            "client_id": client_id,
            "bank_name": "updated",
            "currency": "string",
            "account_type": "Deposit",
            "exclude_from_report": True,
        }
    )
    insurance_details = patch_db.client_insurance_info.insert_one(
        {
            "policy_no": "222",
            "client_id": client_id,
            "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
            "provider": "ABCD",
            "expiry_date": datetime.fromisoformat("2025-02-02"),
            "supplier_list_file": {"file_id": "e9003c3a-f45b-4fc5-ab29-e5c4cd3a1320"},
        }
    )
    bond_details = patch_db.client_bond_info.insert_one(
        {
            "client_id": client_id,
            "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
            "expiry_date": datetime.fromisoformat("2022-12-11"),
            "bond_name": "Somebond",
            "note": "This is some note",
            "start_date": datetime.fromisoformat("2022-06-11"),
        }
    )
    atol_details = patch_db.client_atol_info.insert_one(
        {
            "license": "222",
            "client_id": client_id,
            "files": [
                {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"},
                {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj"},
            ],
            "start_date": datetime.fromisoformat("2020-02-02"),
            "expiry_date": datetime.fromisoformat("2025-02-02"),
        }
    )
    signatories_details = patch_db.client_authorized_signatories_info.insert_one(
        {
            "name": "John",
            "status": "Active",
            "client_id": client_id,
            "signature_file": {"file_id": "e9003c3a-f45b-4fc5-ab29-e5c4cd3a1320"},
        }
    )
    patch_db.client_banking_column.insert_one({"column": "61efc57260cc72bf21f1c441", "client_id": client_id})
    patch_db.client_claim_column.insert_one({"client_id": client_id, "column": "61efcfb8466aef6133536960"})
    patch_db.client_anomaly.insert_many(
        [
            {"anomaly_id": "11ef4bff0a719ab106fdc14e", "client_id": client_id},
            {
                "anomaly_id": "11ef4bff0a719ab106fdc14a",
                "client_id": client_id,
                "custom_field_value": 20.0,
                "elements": ["Deposit", "ASPP"],
                "name": "",
            },
        ]
    )
    patch_db.client_files.insert_many(
        [
            {"client_id": client_id, "file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh", "file_name": "test.pdf"},
            {"client_id": client_id, "file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj", "file_name": "test.pdf"},
            {"client_id": client_id, "file_id": "e9003c3a-f45b-4fc5-ab29-e5c4cd3a1320", "file_name": "test.xlsx"},
        ]
    )

    # When
    result = client_service.get_client.__wrapped__(client_service, client_id)

    # Then
    trust_type_name = patch_db.lookup_trust_type.find_one({"_id": ObjectId("61ef4e0da0d0ef7c56884319")})
    assert result == {
        "cId": "1",
        "additionChecks": [
            {
                "_id": str(check_info_details.inserted_id),
                "checkName": "updated",
                "clientId": "621bf4f49bed97189d98ddcd",
                "description": "string1",
                "relatedElement": "string1",
                "shortName": "string1",
            }
        ],
        "address": {
            "clientId": "621bf4f49bed97189d98ddcd",
            "country": "string",
            "id": str(address_info_details.inserted_id),
            "line1": "updated",
            "line2": "string",
            "line3": "string",
            "postcode": "string",
            "town": "string",
        },
        "anomalies": [
            {"anomalyId": "11ef4bff0a719ab106fdc14e", "fromDate": None, "toDate": None},
            {
                "anomalyId": "11ef4bff0a719ab106fdc14a",
                "customFieldValue": 20.0,
                "elements": ["Deposit", "ASPP"],
                "fromDate": None,
                "toDate": None,
            },
        ],
        "atol": [
            {
                "_id": str(atol_details.inserted_id),
                "clientId": "621bf4f49bed97189d98ddcd",
                "expiryDate": "2025-02-02",
                "files": [
                    {"fileId": "be761366-9579-4e0d-890b-8ab1e6b82dfh", "fileName": "test.pdf"},
                    {"fileId": "be761366-9579-4e0d-890b-8ab1e6b82dhj", "fileName": "test.pdf"},
                ],
                "license": "222",
                "startDate": "2020-02-02",
            }
        ],
        "bankDetails": [
            {
                "_id": str(bank_info_details.inserted_id),
                "accountNumber": "string",
                "bankName": "updated",
                "clientId": "621bf4f49bed97189d98ddcd",
                "currency": "string",
                "iban": "47548",
                "sortCode": "string",
                "accountType": "Deposit",
                "excludeFromReport": True,
            },
        ],
        "bankingColumns": ["61efc57260cc72bf21f1c441"],
        "claimColumns": ["61efcfb8466aef6133536960"],
        "clientId": "621bf4f49bed97189d98ddcd",
        "email": "<EMAIL>",
        "existingClient": True,
        "frequency": ["61ee2f1dcf10b991afdaf54a"],
        "friendlyName": "updated",
        "fullName": "ABC",
        "goLiveDate": "2021-10-02",
        "insurance": [
            {
                "_id": str(insurance_details.inserted_id),
                "clientId": "621bf4f49bed97189d98ddcd",
                "expiryDate": "2025-02-02",
                "files": [{"fileId": "be761366-9579-4e0d-890b-8ab1e6b82dfh", "fileName": "test.pdf"}],
                "policyNumber": "222",
                "provider": "ABCD",
                "supplierListFile": {"fileId": "e9003c3a-f45b-4fc5-ab29-e5c4cd3a1320", "fileName": "test.xlsx"},
            }
        ],
        "bond": [
            {
                "_id": str(bond_details.inserted_id),
                "bondName": "Somebond",
                "clientId": "621bf4f49bed97189d98ddcd",
                "expiryDate": "2022-12-11",
                "files": [{"fileId": "be761366-9579-4e0d-890b-8ab1e6b82dfh", "fileName": "test.pdf"}],
                "note": "This is some note",
                "startDate": "2022-06-11",
            }
        ],
        "limits": client_limit_output,
        "pointOfContact": "string",
        "sftpLocation": [],
        "to": [],
        "cc": [],
        "authorizedSignatories": [
            {
                "_id": str(signatories_details.inserted_id),
                "name": "John",
                "status": "Active",
                "clientId": "621bf4f49bed97189d98ddcd",
                "signatureFile": {"file_id": "e9003c3a-f45b-4fc5-ab29-e5c4cd3a1320"},
            }
        ],
        "reuseOldBooking": False,
        "status": "string",
        "typeOfTrustAccount": "61ef4e0da0d0ef7c56884319",
        "trustAccountName": trust_type_name["name"],
        "claimFromTBR": False,
        "isDisabled": False,
    }


def test_get_client_validation_error(flask_client, patch_db):
    # Given
    data = ObjectId("621bf4f49bed97189d98ddcd")
    # Then
    assert_that(
        calling(client_service.get_client.__wrapped__).with_args(client_service, data), raises(ServiceException)
    )


@pytest.mark.skip("To be completed later")
@pytest.mark.parametrize(
    "input_client_basic_info, input_client_bank_acct_details,input_client_address,input_client_insurance_info,input_client_atol_info,input_lookup_trust_type, output",
    [
        (
            [
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e50"),
                    "client_id": "1",
                    "full_name": "abc",
                    "friendly_name": "cde",
                    "go_live_date": datetime.fromisoformat("2022-03-02"),
                    "point_of_contact": "abc",
                    "email": "<EMAIL>",
                    "type_of_trust_account": ObjectId("61ef4e0da0d0ef7c56884319"),
                },
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e51"),
                    "client_id": "2",
                    "full_name": "abc",
                    "friendly_name": "abc",
                    "go_live_date": datetime.fromisoformat("2022-03-02"),
                    "point_of_contact": "abc",
                    "email": "<EMAIL>",
                    "type_of_trust_account": ObjectId("61ef4e0da0d0ef7c56884319"),
                },
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e53"),
                    "client_id": "3",
                    "full_name": "qwe",
                    "friendly_name": "ABC",
                    "go_live_date": datetime.fromisoformat("2022-03-02"),
                    "point_of_contact": "abc",
                    "email": "<EMAIL>",
                    "type_of_trust_account": ObjectId("61ef4e0da0d0ef7c56884319"),
                },
            ],
            [
                {"_id": ObjectId("620257e8697b3b6f30f37e50"), "client_id": "1", "currency": "string"},
                {"_id": ObjectId("620257e8697b3b6f30f37e51"), "client_id": "2", "currency": "string"},
                {"_id": ObjectId("620257e8697b3b6f30f37e53"), "client_id": "3", "currency": "string"},
            ],
            [
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e50"),
                    "postcode": "string",
                    "country": "string",
                    "line3": "string",
                    "client_id": "1",
                    "line2": "string",
                    "town": "string",
                    "line1": "string",
                },
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e51"),
                    "postcode": "string",
                    "country": "string",
                    "line3": "string",
                    "client_id": "2",
                    "line2": "string",
                    "town": "string",
                    "line1": "string",
                },
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e53"),
                    "postcode": "string",
                    "country": "string",
                    "line3": "string",
                    "client_id": "3",
                    "line2": "string",
                    "town": "string",
                    "line1": "string",
                },
            ],
            [
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e50"),
                    "client_id": "1",
                    "expiry_date": datetime.fromisoformat("2024-10-02"),
                },
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e51"),
                    "client_id": "1",
                    "expiry_date": datetime.fromisoformat("2021-10-02"),
                },
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e53"),
                    "client_id": "3",
                    "expiry_date": datetime.fromisoformat("2019-10-02"),
                },
            ],
            [
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e50"),
                    "client_id": "1",
                    "expiry_date": datetime.fromisoformat("2024-10-02"),
                },
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e51"),
                    "client_id": "1",
                    "expiry_date": datetime.fromisoformat("2021-10-02"),
                },
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e53"),
                    "client_id": "3",
                    "expiry_date": datetime.fromisoformat("2019-10-02"),
                },
            ],
            [
                {"_id": ObjectId("61ef4e0da0d0ef7c56884319"), "name": "ATOL Standard"},
                {"_id": ObjectId("61ef4e0da0d0ef7c56884329"), "name": "ATOL Standard"},
                {"_id": ObjectId("61ef4e0da0d0ef7c56884359"), "name": "ATOL Standard"},
            ],
            [
                {
                    "clientId": "620257e8697b3b6f30f37e51",
                    "country": ["string"],
                    "currency": ["string"],
                    "friendlyName": "abc",
                    "goLiveDate": datetime(2022, 3, 2, 0, 0),
                    "insuranceExpiryDate": ["2021-10-02"],
                    "atolExpiryDate": ["2021-10-02"],
                    "line1": ["string"],
                    "line2": ["string"],
                    "line3": ["string"],
                    "pocEmail": "<EMAIL>",
                    "pocName": "abc",
                    "postCode": ["string"],
                    "showInsuranceExpiry": True,
                    "showAtolExpiry": True,
                    "town": ["string"],
                    "typeOfTrustAccount": "61ef4e0da0d0ef7c56884319",
                    "typeOfTrustAccountName": "ATOL Standard",
                },
                {
                    "clientId": "620257e8697b3b6f30f37e53",
                    "country": ["string"],
                    "currency": ["string"],
                    "friendlyName": "ABC",
                    "goLiveDate": datetime(2022, 3, 2, 0, 0),
                    "insuranceExpiryDate": ["2019-10-02"],
                    "atolExpiryDate": ["2019-10-02"],
                    "line1": ["string"],
                    "line2": ["string"],
                    "line3": ["string"],
                    "pocEmail": "<EMAIL>",
                    "pocName": "abc",
                    "postCode": ["string"],
                    "showInsuranceExpiry": True,
                    "showAtolExpiry": True,
                    "town": ["string"],
                    "typeOfTrustAccount": "61ef4e0da0d0ef7c56884319",
                    "typeOfTrustAccountName": "ATOL Standard",
                },
                {
                    "clientId": "620257e8697b3b6f30f37e50",
                    "country": ["string"],
                    "currency": ["string"],
                    "friendlyName": "cde",
                    "goLiveDate": datetime(2022, 3, 2, 0, 0),
                    "insuranceExpiryDate": ["2024-10-02"],
                    "atolExpiryDate": ["2024-10-02"],
                    "line1": ["string"],
                    "line2": ["string"],
                    "line3": ["string"],
                    "pocEmail": "<EMAIL>",
                    "pocName": "abc",
                    "postCode": ["string"],
                    "showInsuranceExpiry": True,
                    "showAtolExpiry": True,
                    "town": ["string"],
                    "typeOfTrustAccount": "61ef4e0da0d0ef7c56884319",
                    "typeOfTrustAccountName": "ATOL Standard",
                },
            ],
        ),
    ],
)
def test_client_search_list(
    patch_db,
    input_client_basic_info,
    input_client_bank_acct_details,
    input_client_address,
    input_client_insurance_info,
    input_client_atol_info,
    input_lookup_trust_type,
    output,
    monkeypatch,
):
    # Given
    query = "1"

    patch_db.client_basic_info.insert_many(input_client_basic_info)
    patch_db.client_bank_acct_details.insert_many(input_client_bank_acct_details)
    patch_db.client_address.insert_many(input_client_address)
    patch_db.client_insurance_info.insert_many(input_client_insurance_info)
    patch_db.client_atol_info.insert_many(input_client_atol_info)
    patch_db.lookup_trust_type.insert_many(input_lookup_trust_type)
    patch_db.user.insert_one({"user_id": "1", "clients": ["1"]})

    monkeypatch.setattr(
        patch_db.client_basic_info,
        "aggregate",
        lambda x, **kwargs: [
            {
                "clientId": "620257e8697b3b6f30f37e50",
                "friendlyName": "cde",
                "currency": ["string"],
                "typeOfTrustAccount": ObjectId("61ef4e0da0d0ef7c56884319"),
                "insuranceExpiryDate": [datetime(2024, 10, 2, 0, 0)],
                "atolExpiryDate": [datetime(2024, 10, 2, 0, 0)],
                "goLiveDate": datetime(2022, 3, 2, 0, 0),
                "country": ["string"],
                "line1": ["string"],
                "line2": ["string"],
                "line3": ["string"],
                "postCode": ["string"],
                "town": ["string"],
                "pocName": "abc",
                "pocEmail": "<EMAIL>",
            },
            {
                "clientId": "620257e8697b3b6f30f37e51",
                "friendlyName": "abc",
                "currency": ["string"],
                "typeOfTrustAccount": ObjectId("61ef4e0da0d0ef7c56884319"),
                "insuranceExpiryDate": [datetime(2021, 10, 2, 0, 0)],
                "atolExpiryDate": [datetime(2021, 10, 2, 0, 0)],
                "goLiveDate": datetime(2022, 3, 2, 0, 0),
                "country": ["string"],
                "line1": ["string"],
                "line2": ["string"],
                "line3": ["string"],
                "postCode": ["string"],
                "town": ["string"],
                "pocName": "abc",
                "pocEmail": "<EMAIL>",
            },
            {
                "clientId": "620257e8697b3b6f30f37e53",
                "friendlyName": "ABC",
                "currency": ["string"],
                "typeOfTrustAccount": ObjectId("61ef4e0da0d0ef7c56884319"),
                "insuranceExpiryDate": [datetime(2019, 10, 2, 0, 0)],
                "atolExpiryDate": [datetime(2019, 10, 2, 0, 0)],
                "goLiveDate": datetime(2022, 3, 2, 0, 0),
                "country": ["string"],
                "line1": ["string"],
                "line2": ["string"],
                "line3": ["string"],
                "postCode": ["string"],
                "town": ["string"],
                "pocName": "abc",
                "pocEmail": "<EMAIL>",
            },
        ],
    )

    # When
    result = client_service.clients_details_list("1", query)

    # Then
    assert result == output


@pytest.mark.skip("To be completed later")
@patch("flaskr.helpers.boto3_handler.upload_file", MagicMock(return_value=None))
def test_file_upload_pdf(flask_client, patch_db):
    # Given
    client_id = "1"
    filename = "Document1.pdf"
    # When
    file_id = client_service.client_file_upload(
        client_id,
        "insurance",
        FileStorage(
            filename=filename,
            content_type="application/pdf",
        ),
    )
    # Then
    file_upload = patch_db.client_files.find_one({"client_id": client_id})
    assert_client_file_upload(file_upload, {"clientId": client_id, "fileId": file_id, "fileName": "Document1.pdf"})


@patch("flaskr.helpers.boto3_handler.upload_file", MagicMock(return_value=None))
def test_file_upload_invalid_content_type(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    # Then
    assert_that(
        calling(client_service.client_file_upload).with_args(
            str(client_id),
            "insurance",
            FileStorage(
                filename="Document1.jpg",
                content_type="application/jpg",
            ),
        ),
        raises(ServiceException, "File format not supported, use pdf files"),
    )


def test_file_upload_invalid_file_type(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    # Then
    assert_that(
        calling(client_service.client_file_upload).with_args(
            str(client_id),
            "abc",
            FileStorage(
                filename="Document1.pdf",
                content_type="application/pdf",
            ),
        ),
        raises(ServiceException, "file_type error"),
    )


def test_file_upload_invalid_client(flask_client, patch_db):
    # Given
    client_id = ObjectId("621bf4f49bed97189d98ddcd")
    # Then
    assert_that(
        calling(client_service.client_file_upload).with_args(
            client_id,
            "insurance",
            FileStorage(
                filename="20220510-Document1.pdf",
                content_type="application/pdf",
            ),
        ),
        raises(ServiceException, "client not found"),
    )


@pytest.mark.skip("To be completed later")
@pytest.mark.parametrize(
    "data",
    [
        ({"file_type": "atol", "file_id": "be761366-9579-4e0d-890b-8ab1e6b82d3a", "bucket": "test-atol"}),
        ({"file_type": "insurance", "file_id": "be761366-9579-4e0d-890b-8ab1e6b82d3a", "bucket": "test-insurance"}),
    ],
)
@patch("flaskr.services.client_service.download_file", MagicMock(return_value="a file"))
@patch("flaskr.services.client_service.send_file", MagicMock(return_value="a file"))
def test_client_file_download(flask_client, patch_db, data):
    # Given
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one({"user_id": user_id, "clients": [ObjectId("62fb53e11a873172a59b2a1e")]})
    file_type = data["file_type"]
    file_id = data["file_id"]

    patch_db.client_insurance_info.insert_one(
        {"files": [{"file_id": file_id}], "client_id": ObjectId("62fb53e11a873172a59b2a1e")}
    )
    patch_db.client_atol_info.insert_one(
        {"files": [{"file_id": file_id}], "client_id": ObjectId("62fb53e11a873172a59b2a1e")}
    )
    # When

    result = client_service.client_file_download(file_type, file_id, user_id)

    # then
    assert result == "a file"


@pytest.mark.skip("To be completed later")
@pytest.mark.parametrize(
    "data,message",
    [
        ({"file_type": "", "file_id": ""}, re.escape("file_type error")),
        ({"file_type": "", "file_id": "be761366-9579-4e0d-890b-8ab1e6b82d3a"}, re.escape("file_type error")),
        ({"file_type": "report-files", "file_id": "be761366-9579-4e0d-890b-8ab1e6b82d3a"}, re.escape("access denied")),
    ],
)
def test_client_file_download_invalid(flask_client, patch_db, data, message):
    # Given
    file_type = data["file_type"]
    file_id = data["file_id"]
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one({"user_id": user_id, "clients": [ObjectId("62fb53e11a873172a59b2a1e")]})

    # then

    assert_that(
        calling(client_service.client_file_download).with_args(file_type, file_id, user_id),
        raises(ServiceException, message),
    )


@pytest.mark.parametrize(
    "data",
    [
        (ClientBankingColumnRequestBuilder.a_client_banking_column_request().build()),
        (ClientBankingColumnRequestBuilder.a_client_banking_column_request().with_updated_request().build()),
    ],
)
def test_banking_column_create(flask_client, patch_db, data):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    data = data
    # When

    client_service.upsert_banking_columns({**data, "clientId": str(client_id)})
    # Then
    banking_column_list = list(patch_db.client_banking_column.find({"client_id": client_id}))
    for index, x in enumerate(banking_column_list):
        assert_client_banking_column(banking_column_list[index], {"clientId": client_id, "column": x["column"]})


@pytest.mark.parametrize(
    "invalid_data, message",
    [
        (
            ClientBankingColumnRequestInvalidData.COLUMN_MISSING,
            re.escape("{'column': ['invalid ObjectId']}"),
        ),
        (
            ClientBankingColumnRequestInvalidData.COLUMN_EMPTY,
            re.escape("{'column': ['invalid ObjectId']}"),
        ),
        (
            ClientBankingColumnRequestInvalidData.COLUMN_INVALID,
            re.escape("{'column': ['invalid ObjectId']}"),
        ),
    ],
)
def test_banking_column_validation_error(flask_client, patch_db, invalid_data, message):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    data = ClientBankingColumnRequestBuilder.a_client_banking_column_request().build()
    data_list = [invalid_data, data]
    # Then
    assert_that(
        calling(client_service.upsert_banking_columns).with_args({"columns": data_list, "clientId": str(client_id)}),
        raises(ValidationError, message),
    )

    banking_column_list = list(patch_db.client_banking_column.find({"client_id": client_id}))
    assert len(banking_column_list) == 0


@pytest.mark.parametrize(
    "data",
    [
        (ClientClaimColumnRequestBuilder.a_client_claim_column_request().build()),
        (ClientClaimColumnRequestBuilder.a_client_claim_column_request().with_updated_request().build()),
    ],
)
def test_claim_column_create(flask_client, patch_db, data):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    data = data
    # When
    client_service.upsert_claim_columns({**data, "clientId": str(client_id), "status": "string"})

    # Then
    claim_column_list = list(patch_db.client_claim_column.find({"client_id": client_id}))
    for index, x in enumerate(claim_column_list):
        assert_client_claim_column(claim_column_list[index], {"clientId": client_id, "column": x["column"]})


@pytest.mark.parametrize(
    "data",
    [
        (ClientClaimColumnRequestBuilder.a_client_claim_column_request().build()),
        (ClientClaimColumnRequestBuilder.a_client_claim_column_request().with_updated_request().build()),
    ],
)
def test_claim_column_create_with_status_published(flask_client, patch_db, data):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    data = data

    # Then
    assert_that(
        calling(client_service.upsert_claim_columns).with_args(
            {**data, "clientId": str(client_id), "status": "published"}
        ),
        raises(ServiceException, "Mandatory Banking Columns needs to be selected"),
    )


@pytest.mark.parametrize(
    "invalid_data, message",
    [
        (
            ClientClaimColumnRequestInvalidData.COLUMN_EMPTY,
            re.escape("{'column': ['invalid ObjectId']}"),
        ),
        (
            ClientClaimColumnRequestInvalidData.COLUMN_INVALID,
            re.escape("{'column': ['invalid ObjectId']}"),
        ),
    ],
)
def test_claim_column_validation_error(flask_client, patch_db, invalid_data, message):
    # Given
    ClientClaimColumnRequestBuilder.a_client_claim_column_request().build()
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    data_list = invalid_data
    # Then
    assert_that(
        calling(client_service.upsert_claim_columns).with_args(
            {**data_list, "clientId": str(client_id), "status": "draft"}
        ),
        raises(ValidationError, message),
    )

    claim_column_list = list(patch_db.client_claim_column.find({"client_id": client_id}))
    assert len(claim_column_list) == 0


def test_upsert_claim_column_client_not_found_service_exception(flask_client, patch_db):
    # Given

    data = {"clientId": "61efcfb8466aef6133536912", "columns": ["61efcfb8466aef6133536960"], "status": "string"}

    # Then
    assert_that(
        calling(client_service.upsert_claim_columns).with_args(data),
        raises(ServiceException, "Client not found"),
    )


def test_list_clients(patch_db, monkeypatch):
    # Given
    patch_db.user.insert_one(
        {"user_id": "1", "clients": [ObjectId("62c0205c4ba4942bd9863d8f"), ObjectId("62bfe1514dd6c1e0e10bd609")]}
    )
    monkeypatch.setattr(
        patch_db.client_basic_info,
        "find",
        lambda *args, **kwargs: [
            {
                "clientId": "62c0205c4ba4942bd9863d8f",
                "clientFriendlyName": "testName",
                "clientName": "testName",
            },
            {
                "clientId": "62bfe1514dd6c1e0e10bd609",
                "clientFriendlyName": "testName",
                "clientName": "testName",
            },
        ],
    )
    # When
    result = client_service.list_clients("1")

    # Then
    assert result == [
        {
            "clientId": "62c0205c4ba4942bd9863d8f",
            "clientFriendlyName": "testName",
            "clientName": "testName",
        },
        {
            "clientId": "62bfe1514dd6c1e0e10bd609",
            "clientFriendlyName": "testName",
            "clientName": "testName",
        },
    ]


@pytest.mark.parametrize(
    "data,result",
    [
        (
            {"users": ["ee4006bd-23ab-499a-9adc-cf7c6f195c48", "ee4006bd-23ab-499a-9adc-cf7c6f195c49"]},
            [
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
                    "clients": ["1", ObjectId("633c095ae3d0dd04183f3455")],
                    "role": "ptt-client",
                },
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c49",
                    "clients": ["2", ObjectId("633c095ae3d0dd04183f3455")],
                    "role": "ptt-client",
                },
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c50",
                    "clients": ["1", "2", ObjectId("633c01126c2ee1632927949b")],
                    "role": "ptt-client",
                },
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c51",
                    "clients": ["1", "2", ObjectId("633c002c2a83960fa805e504")],
                    "role": "ptt-admin",
                },
            ],
        ),
        (
            {"users": ["ee4006bd-23ab-499a-9adc-cf7c6f195c49"]},
            [
                {"user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48", "clients": ["1"], "role": "ptt-client"},
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c49",
                    "clients": ["2", ObjectId("633c095ae3d0dd04183f3455")],
                    "role": "ptt-client",
                },
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c50",
                    "clients": ["1", "2", ObjectId("633c01126c2ee1632927949b")],
                    "role": "ptt-client",
                },
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c51",
                    "clients": ["1", "2", ObjectId("633c002c2a83960fa805e504")],
                    "role": "ptt-admin",
                },
            ],
        ),
        (
            {"users": ["ee4006bd-23ab-499a-9adc-cf7c6f195c50"]},
            [
                {"user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48", "clients": ["1"], "role": "ptt-client"},
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c49",
                    "clients": [
                        "2",
                    ],
                    "role": "ptt-client",
                },
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c50",
                    "clients": ["1", "2", ObjectId("633c01126c2ee1632927949b"), ObjectId("633c095ae3d0dd04183f3455")],
                    "role": "ptt-client",
                },
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c51",
                    "clients": ["1", "2", ObjectId("633c002c2a83960fa805e504")],
                    "role": "ptt-admin",
                },
            ],
        ),
        (
            {
                "users": [
                    "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
                    "ee4006bd-23ab-499a-9adc-cf7c6f195c49",
                    "ee4006bd-23ab-499a-9adc-cf7c6f195c50",
                ]
            },
            [
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
                    "clients": ["1", ObjectId("633c095ae3d0dd04183f3455")],
                    "role": "ptt-client",
                },
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c49",
                    "clients": ["2", ObjectId("633c095ae3d0dd04183f3455")],
                    "role": "ptt-client",
                },
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c50",
                    "clients": ["1", "2", ObjectId("633c01126c2ee1632927949b"), ObjectId("633c095ae3d0dd04183f3455")],
                    "role": "ptt-client",
                },
                {
                    "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c51",
                    "clients": ["1", "2", ObjectId("633c002c2a83960fa805e504")],
                    "role": "ptt-admin",
                },
            ],
        ),
    ],
)
def test_update_client_user(flask_client, patch_db, data, result):
    # Given
    patch_db.client_basic_info.insert_one({"_id": ObjectId("633c095ae3d0dd04183f3455"), "client_id": "3"})
    basic_info = patch_db.client_basic_info.find_one({"_id": ObjectId("633c095ae3d0dd04183f3455")})
    client_id = basic_info["_id"]

    patch_db.user.insert_many(
        [
            {"user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48", "clients": ["1"], "role": "ptt-client"},
            {"user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c49", "clients": ["2"], "role": "ptt-client"},
            {
                "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c50",
                "clients": ["1", "2", ObjectId("633c01126c2ee1632927949b")],
                "role": "ptt-client",
            },
            {
                "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c51",
                "clients": ["1", "2", ObjectId("633c002c2a83960fa805e504")],
                "role": "ptt-admin",
            },
        ]
    )

    # When
    client_service.update_client_users(str(client_id), data)

    user = list(patch_db.user.find({}, projection={"_id": 0}))

    assert user == result


@pytest.mark.parametrize(
    "total_annual_revenue,total_no_of_claims, flag",
    [
        (
            5000,
            100,
            {
                "isAnnualRevenueLimitExceeded": True,
                "isClaimsExceeded": False,
                "TotalAnnualRevenue": 6000,
                "totalNoOfClaims": 75,
            },
        ),
        (
            7000,
            50,
            {
                "isAnnualRevenueLimitExceeded": False,
                "isClaimsExceeded": True,
                "TotalAnnualRevenue": 6000,
                "totalNoOfClaims": 75,
            },
        ),
    ],
)
def test_total_annual_revenue_and_claim_calculation(
    flask_client, patch_db, monkeypatch, total_annual_revenue, total_no_of_claims, flag
):
    # Given
    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda x: [{"_id": None, "TotalAnnualRevenue": 6000}],
    )
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda x: [{"_id": None, "totalNoOfClaims": 75}],
    )

    # When
    client_service = ClientService()
    response = client_service._ClientService__total_annual_revenue_and_claim_limit_calculation(
        "633c095ae3d0dd04183f3455", "GBP", "2022-03-21", "2022-03-21", total_annual_revenue, total_no_of_claims
    )

    # Then
    assert response == flag


@freeze_time("2022-08-01")
def test_update_escrow_multiplier(flask_client, patch_db):
    # Given
    data = {
        "client_id": ObjectId("633c095ae3d0dd04183f3455"),
        "date": "2022-10-05",
        "multiplier": 3,
        "modified_by": "1234",
        "credit_multiplier": 0.1,
        "debit_multiplier": 0.9,
    }
    patch_db.client_escrow_multiplier.insert_one(data)
    request_data = {"date": "2022-10-05", "multiplier": 5.0, "creditMultiplier": 0.5, "debitMultiplier": 0.2}

    # When
    result = client_service.update_escrow_multiplier("633c095ae3d0dd04183f3455", "1234", request_data)
    output = patch_db.client_escrow_multiplier.find_one(result, projection={"_id": 0})

    # Then
    assert output == {
        "client_id": ObjectId("633c095ae3d0dd04183f3455"),
        "date": "2022-10-05",
        "multiplier": 5.0,
        "modified_by": "1234",
        "credit_multiplier": 0.5,
        "debit_multiplier": 0.2,
    }


@freeze_time("2022-08-01")
def test_update_escrow_multiplier_with_credit_multiplier_only(flask_client, patch_db):
    # Given
    data = {
        "client_id": ObjectId("633c095ae3d0dd04183f3455"),
        "date": "2022-10-05",
        "multiplier": 3,
        "modified_by": "1234",
        "credit_multiplier": 0.1,
        "debit_multiplier": 0.9,
    }
    patch_db.client_escrow_multiplier.insert_one(data)
    request_data = {"date": "2022-10-05", "multiplier": 5.0, "creditMultiplier": 0.5}

    # When
    result = client_service.update_escrow_multiplier("633c095ae3d0dd04183f3455", "1234", request_data)
    output = patch_db.client_escrow_multiplier.find_one(result, projection={"_id": 0})

    # Then
    assert output == {
        "client_id": ObjectId("633c095ae3d0dd04183f3455"),
        "date": "2022-10-05",
        "multiplier": 5.0,
        "modified_by": "1234",
        "credit_multiplier": 0.5,
        "debit_multiplier": 0.9,
    }


@freeze_time("2022-08-01")
def test_update_escrow_multiplier_without_debit_credit_multipliers(flask_client, patch_db):
    # Given
    data = {
        "client_id": ObjectId("633c095ae3d0dd04183f3455"),
        "date": "2022-10-05",
        "multiplier": 3,
        "modified_by": "1234",
        "credit_multiplier": 0.1,
        "debit_multiplier": 0.9,
    }
    patch_db.client_escrow_multiplier.insert_one(data)
    request_data = {"date": "2022-10-05", "multiplier": 5.0}

    # When
    result = client_service.update_escrow_multiplier("633c095ae3d0dd04183f3455", "1234", request_data)
    output = patch_db.client_escrow_multiplier.find_one(result, projection={"_id": 0})

    # Then
    assert output == {
        "client_id": ObjectId("633c095ae3d0dd04183f3455"),
        "date": "2022-10-05",
        "multiplier": 5.0,
        "modified_by": "1234",
        "credit_multiplier": 0.1,
        "debit_multiplier": 0.9,
    }


def test_update_escrow_multiplier_past_date(flask_client, patch_db):
    # Given
    data = {
        "client_id": ObjectId("633c095ae3d0dd04183f3455"),
        "date": "2022-01-01",
        "multiplier": 0.7,
        "modified_by": "1234",
        "credit_multiplier": 0.1,
        "debit_multiplier": 0.9,
    }
    patch_db.client_escrow_multiplier.insert_one(data)
    request_data = {"date": "2022-01-01", "multiplier": 0.8, "creditMultiplier": 0.5, "debitMultiplier": 0.2}

    # When
    result = client_service.update_escrow_multiplier("633c095ae3d0dd04183f3455", "1234", request_data)
    output = patch_db.client_escrow_multiplier.find_one(result, projection={"_id": 0})

    # Then
    assert output == {
        "client_id": ObjectId("633c095ae3d0dd04183f3455"),
        "date": "2022-01-01",
        "multiplier": 0.8,
        "modified_by": "1234",
        "credit_multiplier": 0.5,
        "debit_multiplier": 0.2,
    }


@patch.object(
    AuthService,
    "ptt_users_list",
    MagicMock(
        return_value=[
            {
                "userId": "1234",
                "email": "<EMAIL>",
                "name": "sam",
            }
        ]
    ),
)
def test_get_escrow_multiplier(flask_client, patch_db):
    # Given
    data = {
        "client_id": ObjectId("633c095ae3d0dd04183f3455"),
        "date": "2022-10-05",
        "multiplier": 3,
        "modified_by": "1234",
        "credit_multiplier": 0.1,
        "debit_multiplier": 0.3,
    }
    patch_db.client_escrow_multiplier.insert_one(data)

    # When
    result = client_service.get_escrow_multiplier("633c095ae3d0dd04183f3455", "2022-10-05")

    # Then
    assert result == {"multiplier": 3, "modified_by": "sam", "credit_multiplier": 0.1, "debit_multiplier": 0.3}


@patch.object(
    AuthService,
    "ptt_users_list",
    MagicMock(
        return_value=[
            {
                "userId": "1234",
                "email": "<EMAIL>",
                "name": "sam",
            }
        ]
    ),
)
def test_get_escrow_service_exception(flask_client, patch_db):
    # Given
    data = {
        "client_id": ObjectId("633c095ae3d0dd04183f3455"),
        "date": "2022-10-05",
        "multiplier": 3,
        "modified_by": "1234",
        "credit_multiplier": 0.1,
        "debit_multiplier": 0.3,
    }
    patch_db.client_escrow_multiplier.insert_one(data)

    # Then
    assert_that(
        calling(client_service.get_escrow_multiplier).with_args(str(data["client_id"]), "2022-03-09"),
        raises(
            NotFound,
            "404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.",
        ),
    )


@patch.object(
    AuthService,
    "ptt_users_list",
    MagicMock(
        return_value=[
            {
                "userId": "1234",
                "email": "<EMAIL>",
                "name": "sam",
            }
        ]
    ),
)
def test_escrow_amount_claims(flask_client, patch_db, monkeypatch):
    # Given
    monkeypatch.setattr(
        patch_db.claims_file_details,
        "aggregate",
        lambda *args, **kwargs: [
            {"_id": "GBP", "amount": 34000.0, "count": 1},
        ],
    )
    claims_metadata = patch_db.claims_metadata.insert_one(
        {
            "client_id": "a12",
            "claim_files": [
                {
                    "file_id": "39.1-ptt-icheck3-june",
                    "file_name": "Automated Claim Generation ********",
                    "file_date": "2021-12-16",
                    "status": "Submitted",
                    "item_count": {
                        "GBP": 1,
                    },
                    "claim_total": {
                        "GBP": 17000.0,
                    },
                    "checks": {
                        "GBP": 0,
                    },
                    "checked_amount": {
                        "GBP": 0,
                    },
                },
            ],
            "status": "Submitted",
            "frequency": "Daily",
            "notes": "abc",
        }
    )

    # When
    result = client_service.get_escrow_amount_claims.__wrapped__(
        client_service, claims_metadata.inserted_id, "a12", "claim"
    )

    # Then
    assert result == [{"currency": "GBP", "count": 1, "escrowAmount": 17000.0, "actualAmount": 34000.0}]


@patch.object(
    AuthService,
    "ptt_users_list",
    MagicMock(
        return_value=[
            {
                "userId": "1234",
                "email": "<EMAIL>",
                "name": "sam",
            }
        ]
    ),
)
def test_escrow_amount_banking(flask_client, patch_db, monkeypatch):
    # Given
    monkeypatch.setattr(
        patch_db.banking_file_details,
        "aggregate",
        lambda *args, **kwargs: [
            {"_id": "GBP", "amount": 34000.0, "count": 1},
        ],
    )
    banking_metadata = patch_db.banking_metadata.insert_one(
        {
            "client_id": "a12",
            "banking_files": [
                {
                    "file_id": "39.1-ptt-icheck3-june",
                    "file_name": "Automated Claim Generation ********",
                    "file_date": "2021-12-16",
                    "status": "Submitted",
                    "item_count": {
                        "GBP": 1,
                    },
                    "deposit": {
                        "GBP": 17000.0,
                    },
                },
            ],
            "status": "Submitted",
            "frequency": "Daily",
            "notes": "abc",
        }
    )

    # When
    result = client_service.get_escrow_amount_banking.__wrapped__(
        client_service, banking_metadata.inserted_id, "a12", "banking"
    )

    # Then
    assert result == [{"currency": "GBP", "count": 1, "escrowAmount": 17000.0, "actualAmount": 34000.0}]


def test_list_atol_standard_clients(patch_db, monkeypatch):
    # Given
    patch_db.user.insert_one(
        {"user_id": "1", "clients": [ObjectId("62c0205c4ba4942bd9863d8f"), ObjectId("62bfe1514dd6c1e0e10bd609")]}
    )
    patch_db.lookup_trust_type.insert_one({"name": "ATOL Standard"})
    monkeypatch.setattr(
        patch_db.client_basic_info,
        "find",
        lambda *args, **kwargs: [
            {
                "clientId": "62c0205c4ba4942bd9863d8f",
                "clientFriendlyName": "testName",
                "clientName": "testName",
            },
            {
                "clientId": "62bfe1514dd6c1e0e10bd609",
                "clientFriendlyName": "testName",
                "clientName": "testName",
            },
        ],
    )
    # When
    result = client_service.list_atol_standard_clients("1")

    # Then
    assert result == [
        {
            "clientId": "62c0205c4ba4942bd9863d8f",
            "clientFriendlyName": "testName",
            "clientName": "testName",
        },
        {
            "clientId": "62bfe1514dd6c1e0e10bd609",
            "clientFriendlyName": "testName",
            "clientName": "testName",
        },
    ]


def assert_client_basic_info(basic_info, data):
    assert str(basic_info["_id"]) == data["clientId"]
    assert basic_info["friendly_name"] == data["friendlyName"]
    assert basic_info["full_name"] == data["fullName"]
    assert str(basic_info["type_of_trust_account"]) == data["typeOfTrustAccount"]
    assert basic_info["go_live_date"].strftime("%Y-%m-%d") == data["goLiveDate"]
    assert basic_info["reuse_old_booking"] == data["reuseOldBooking"]
    assert basic_info["status"] == data["status"]
    assert basic_info["email"] == data["email"]
    assert basic_info["point_of_contact"] == data["pointOfContact"]
    assert basic_info["claim_from_tbr"] == data["claimFromTBR"]
    assert basic_info["is_editable"] is True
    assert basic_info["is_disabled"] is False


def assert_client_check_info(check_info, data):
    assert check_info["client_id"] == data["clientId"]
    assert check_info["check_name"] == data["checkName"]
    assert check_info["short_name"] == data["shortName"]
    assert check_info["related_element"] == data["relatedElement"]
    assert check_info["description"] == data["description"]


def assert_client_address(address_info, data):
    assert address_info["line1"] == data["line1"]
    assert address_info["line2"] == data["line2"]
    assert address_info["line3"] == data["line3"]
    assert address_info["town"] == data["town"]
    assert address_info["country"] == data["country"]
    assert address_info["postcode"] == data["postcode"]


def assert_client_limit(limits, data):
    assert limits["total_annual_revenue"] == data["totalAnnualRevenue"]
    assert limits["max_no_of_claims"] == data["maximumNoOfClaims"]
    assert limits["currency"] == data["currency"]


def assert_client_claim_frequency(frequency, data):
    assert str(frequency["frequency_id"]) == data["frequencyId"]
    assert frequency["client_id"] == data["clientId"]


def assert_client_bank_info(bank_info, data):
    assert bank_info["client_id"] == data["clientId"]
    assert bank_info["bank_name"] == data["bankName"]
    assert bank_info["account_no"] == data["accountNumber"]
    assert bank_info["sort_code"] == data["sortCode"]
    assert bank_info["currency"] == data["currency"]
    assert bank_info["iban"] == data["iban"]


def assert_client_atol_info(atol_info, data):
    assert atol_info["client_id"] == data["clientId"]
    assert atol_info["license"] == data["license"]
    assert atol_info["start_date"] == data["startDate"]
    assert atol_info["expiry_date"] == data["expiryDate"]
    assert atol_info["files"] == data["files"]


def assert_insurance_info(insurance_info, data):
    assert insurance_info["client_id"] == data["clientId"]
    assert insurance_info["policy_number"] == data["policyNumber"]
    assert insurance_info["provider"] == data["provider"]
    assert insurance_info["expiry_date"] == data["expiryDate"]
    assert insurance_info["files"] == data["files"]
    assert insurance_info["supplier_list_file"] == data["supplierListFile"]


def assert_client_file_upload(file_upload, data):
    assert file_upload["client_id"] == data["clientId"]
    assert file_upload["file_name"] == data["fileName"]
    assert file_upload["file_id"] == data["fileId"]


def assert_client_anomalies(anomalies, data):
    assert anomalies["client_id"] == data["clientId"]
    assert anomalies["anomaly_id"] == data["anomalyId"]
    assert anomalies["custom_field_value"] == data["customFieldValue"]


def assert_client_banking_column(banking_columns, data):
    assert banking_columns["column"] == data["column"]
    assert banking_columns["client_id"] == data["clientId"]


def assert_client_claim_column(claim_columns, data):
    assert claim_columns["column"] == data["column"]
    assert claim_columns["client_id"] == data["clientId"]
