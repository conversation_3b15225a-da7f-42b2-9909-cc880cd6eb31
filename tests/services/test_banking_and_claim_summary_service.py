from datetime import datetime
from bson import ObjectId
import pytest

from flaskr.services.banking_and_claim_summary_service import banking_and_claim_service


@pytest.mark.skip("To be completed later")
def test_get_banking_and_claim_summary(flask_client, patch_db):
    # Given
    client_id = ObjectId("62fb53e11a873172a59b2a1e")
    patch_db.banking_and_claim_summary.insert_one(
        {
            "client_id": client_id,
            "deleted": False,
            "client_name": "asd",
            "file_date": "2020-12-18",
            "file_id": "XYZ123",
            "file_size": "32",
            "last_downloaded_by": "asd",
            "last_downloaded_date_and_time": datetime(2022, 11, 28, 14, 46, 0),
            "last_uploaded_by": "asd",
            "last_uploaded_date_and_time": datetime(2022, 10, 21, 14, 46, 0),
        }
    )

    # When
    result = banking_and_claim_service.get_banking_and_claim_summary(client_id)

    # Then
    assert result == [
        {
            "clientId": "62fb53e11a873172a59b2a1e",
            "clientNames": "asd",
            "deleted": False,
            "fileDate": "2020-12-18",
            "fileId": "XYZ123",
            "fileSize": "32",
            "lastDownloadedBy": "asd",
            "lastDownloadedDateAndTime": "2022-11-28T14:46:00",
            "lastUploadedDateAndTime": "2022-10-21T14:46:00",
            "lastUploadedBy": "asd",
        }
    ]
