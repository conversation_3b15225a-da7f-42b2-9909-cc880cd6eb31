import pytest
from flaskr.services.auth_service import AuthService
from flaskr.services.lookup_service import lookup_service
from flaskr.services.auth_service import auth_service
from unittest.mock import MagicMock, patch
from mongomock import ObjectId


def test_trust_type(flask_client, patch_db):
    # Given
    trust_types = [
        {"name": "ATOL Standard"},
        {"name": "Tripartite MA"},
        {"name": "Tripartite Tour Op"},
        {"name": "Non-Flight PTR 2018"},
        {"name": "Non PTR 2018"},
        {"name": "ATOL Gold"},
        {"name": "ATOL Escrow"},
    ]
    patch_db.lookup_trust_type.insert_many(trust_types)

    # When
    output_list = lookup_service.get_trust_types()
    modified_list = []
    for item in output_list:
        if item.get("_id"):
            del item["_id"]
        modified_list.append(item)

    # Then
    assert modified_list == [
        {"name": "ATOL Standard"},
        {"name": "Tripartite MA"},
        {"name": "Tripartite Tour Op"},
        {"name": "Non-Flight PTR 2018"},
        {"name": "Non PTR 2018"},
        {"name": "ATOL Gold"},
        {"name": "ATOL Escrow"},
    ]


def test_anomalies(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"_id": ObjectId("621bf4f49bed97189d98ddcd"), "client_id": "1"})
    client_id = basic_info.inserted_id
    anomalies = [
        {
            "name": "Unhandled Anomaly",
            "description": "A new anomaly was returned by the data store but was unhandled in code.",
        },
        {
            "name": "Funds in Trust exceed Total Booking Value",
            "description": "Funds in Trust exceed Total Booking Value",
        },
        {"name": "Negative funds in Trust", "description": "Negative cumulative Funds in Trust"},
        {"name": "Claim Performance Before Return", "description": "Performance claim before client return"},
        {"name": "Duplicate", "description": "Duplicate claim value"},
        {"name": "Duplicate with refund", "description": "Duplicate claim with negative value"},
        {
            "name": "Claim too early for departure date",
            "description": "The claim was attempted too early from the configured limits",
        },
        {
            "name": "Claims exceed cost per pax",
            "description": "Claim value exceeds the maximum allowed for the number of passengers",
        },
        {"name": "Multi-currency booking", "description": "Multiple currencies detected for this booking"},
    ]
    patch_db.lookup_anomaly.insert_many(anomalies)

    # When
    output_list = lookup_service.get_anomalies(client_id)
    modified_list = []
    for item in output_list:
        if item.get("_id"):
            del item["_id"]
        modified_list.append(item)

    # Then
    assert modified_list == [
        {
            "name": "Unhandled Anomaly",
            "description": "A new anomaly was returned by the data store but was unhandled in code.",
        },
        {
            "name": "Funds in Trust exceed Total Booking Value",
            "description": "Funds in Trust exceed Total Booking Value",
        },
        {"name": "Negative funds in Trust", "description": "Negative cumulative Funds in Trust"},
        {
            "name": "Claim Performance Before Return",
            "description": "Performance claim before client return",
        },
        {"name": "Duplicate", "description": "Duplicate claim value"},
        {"name": "Duplicate with refund", "description": "Duplicate claim with negative value"},
        {
            "name": "Claim too early for departure date",
            "description": "The claim was attempted too early from the configured limits",
        },
        {
            "name": "Claims exceed cost per pax",
            "description": "Claim value exceeds the maximum allowed for the number of passengers",
        },
        {
            "name": "Multi-currency booking",
            "description": "Multiple currencies detected for this booking",
        },
    ]


def test_frequencies(flask_client, patch_db):
    # Given
    frequencies = [
        {"name": "Daily"},
        {"name": "Weekly"},
        {"name": "Monthly"},
        {"name": "One-off"},
        {"name": "Limited"},
    ]
    patch_db.lookup_frequency.insert_many(frequencies)

    # When
    output_list = lookup_service.get_frequencies("")
    modified_list = []
    for item in output_list:
        if item.get("_id"):
            del item["_id"]
        modified_list.append(item)

    # Then
    assert modified_list == [
        {
            "name": "Daily",
        },
        {
            "name": "Weekly",
        },
        {
            "name": "Monthly",
        },
        {
            "name": "One-off",
        },
        {
            "name": "Limited",
        },
    ]


def test_claim_columns(flask_client, patch_db):
    # Given
    claim_column = [
        {"name": "Pax", "column_name": "pax_count", "data_type": "number"},
        {"name": "PaxCount", "column_name": "pax_count", "data_type": "number", "preferred": True},
        {"name": "BookingRef", "column_name": "booking_ref", "data_type": "string"},
        {
            "name": "Booking Reference",
            "column_name": "booking_ref",
            "data_type": "string",
            "preferred": True,
            "required": True,
        },
        {"name": "Trade Agent", "column_name": "type", "data_type": "string"},
        {"name": "Booking Type", "column_name": "type", "data_type": "string", "preferred": True},
    ]
    patch_db.lookup_claim.insert_many(claim_column)

    # When
    output_list = lookup_service.get_claim_columns()
    modified_list = []
    for item in output_list:
        if item.get("_id"):
            del item["_id"]
        modified_list.append(item)

    # Then
    assert modified_list == [
        {"columnName": "pax_count", "dataType": "number", "name": "PaxCount", "preferred": True},
        {
            "columnName": "booking_ref",
            "dataType": "string",
            "name": "Booking Reference",
            "preferred": True,
            "required": True,
        },
        {"columnName": "type", "dataType": "string", "name": "Booking Type", "preferred": True},
    ]


def test_banking_columns(flask_client, patch_db):
    # Given
    banking_column = [
        {"name": "PaxCount", "column_name": "pax_count", "data_type": "number", "preferred": True},
        {"name": "PaxCount / Passenger count", "column_name": "pax_count", "data_type": "number"},
        {"name": "BookingRef", "column_name": "booking_ref", "data_type": "alphanumeric"},
        {
            "name": "Booking Reference",
            "column_name": "booking_ref",
            "data_type": "alphanumeric",
            "preferred": True,
            "required": True,
        },
        {"name": "CCTransaction Id", "column_name": "supplier_ref", "data_type": "alphanumeric"},
        {"name": "SupplierRef", "column_name": "supplier_ref", "data_type": "alphanumeric", "preferred": True},
    ]
    patch_db.lookup_banking.insert_many(banking_column)

    # When
    output_list = lookup_service.get_banking_columns()
    modified_list = []
    for item in output_list:
        if item.get("_id"):
            del item["_id"]
        modified_list.append(item)

    # Then
    assert modified_list == [
        {"columnName": "pax_count", "dataType": "number", "name": "PaxCount", "preferred": True},
        {
            "columnName": "booking_ref",
            "dataType": "alphanumeric",
            "name": "Booking Reference",
            "preferred": True,
            "required": True,
        },
        {"columnName": "supplier_ref", "dataType": "alphanumeric", "name": "SupplierRef", "preferred": True},
    ]


def test_default_checks(flask_client, patch_db):
    # Given
    default_checks = [
        {
            "name": "Claim Letter",
            "short_name": "LTTR",
            "description": "Agree total amount with claim letter and ensure compliance with requirements of the Trust Deed",
        },
        {
            "name": "Element Totals Match Client Booking System",
            "short_name": "BKNG",
            "description": "Check bookings on client booking system and ensure all data agree with information as per claim report",
        },
        {"name": "Payment Confirmation", "short_name": "PAYM", "description": "Check proof of payment by customer"},
    ]
    patch_db.lookup_default_checks.insert_many(default_checks)

    # When
    output_list = lookup_service.get_default_checks()
    modified_list = []
    for item in output_list:
        if item.get("_id"):
            del item["_id"]
        modified_list.append(item)

    # Then
    assert modified_list == [
        {
            "name": "Claim Letter",
            "shortName": "LTTR",
            "description": "Agree total amount with claim letter and ensure compliance with requirements of the Trust Deed",
        },
        {
            "name": "Element Totals Match Client Booking System",
            "shortName": "BKNG",
            "description": "Check bookings on client booking system and ensure all data agree with information as per claim report",
        },
        {
            "name": "Payment Confirmation",
            "shortName": "PAYM",
            "description": "Check proof of payment by customer",
        },
    ]


def test_currency_list(flask_client, patch_db):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = client_basic_info.inserted_id
    patch_db.client_bank_acct_details.insert_many(
        [
            {"client_id": client_id, "currency": "GBP"},
            {"client_id": client_id, "currency": "USD"},
            {"client_id": client_id, "currency": "EUR"},
            {"client_id": client_id, "currency": "GBP"},
        ]
    )
    currency_gbp = patch_db.lookup_currency.insert_one(
        {"code": "GBP", "name": "British Pound", "symbol": "£", "order": 0},
    )
    currency_eur = patch_db.lookup_currency.insert_one(
        {"code": "EUR", "name": "Euro", "symbol": "€", "order": 1},
    )
    currency_usd = patch_db.lookup_currency.insert_one(
        {"code": "USD", "name": "United States Dollar", "symbol": "$", "order": 2},
    )

    # When
    result = lookup_service.get_currency_list(client_id)

    # Then
    assert result == [
        {"_id": str(currency_gbp.inserted_id), "code": "GBP", "name": "British Pound", "symbol": "£"},
        {"_id": str(currency_eur.inserted_id), "code": "EUR", "name": "Euro", "symbol": "€"},
        {
            "_id": str(currency_usd.inserted_id),
            "code": "USD",
            "name": "United States Dollar",
            "symbol": "$",
        },
    ]


def test_currency_list_without_client_id(patch_db):
    # Given

    currency_gbp = patch_db.lookup_currency.insert_one(
        {"code": "GBP", "name": "British Pound", "symbol": "£", "order": 0},
    )
    currency_eur = patch_db.lookup_currency.insert_one(
        {"code": "EUR", "name": "Euro", "symbol": "€", "order": 1},
    )
    currency_usd = patch_db.lookup_currency.insert_one(
        {"code": "USD", "name": "United States Dollar", "symbol": "$", "order": 2},
    )

    # When
    result = lookup_service.get_currency_list("")

    # Then
    assert result == [
        {"_id": str(currency_gbp.inserted_id), "code": "GBP", "name": "British Pound", "symbol": "£"},
        {"_id": str(currency_eur.inserted_id), "code": "EUR", "name": "Euro", "symbol": "€"},
        {
            "_id": str(currency_usd.inserted_id),
            "code": "USD",
            "name": "United States Dollar",
            "symbol": "$",
        },
    ]


@patch.object(
    AuthService,
    "list_users_in_groups",
    MagicMock(
        return_value={
            "Users": [
                {
                    "Username": "nithin",
                    "Attributes": [
                        {"Name": "sub", "Value": "aa4006bd-27ab-499a-9adc-cf7c6f195c24"},
                        {"Name": "email_verified", "Value": "true"},
                        {"Name": "name", "Value": "Surya z"},
                        {"Name": "email", "Value": "<EMAIL>"},
                    ],
                    "Enabled": True,
                    "UserStatus": "CONFIRMED",
                },
            ]
        }
    ),
)
def test_list_admin(patch_db):
    # Given

    # When

    result = auth_service.ptt_users_list()
    # Then
    assert result == [
        {
            "status": "CONFIRMED",
            "userId": "aa4006bd-27ab-499a-9adc-cf7c6f195c24",
            "email": "<EMAIL>",
            "name": "Surya z",
        },
        {
            "status": "CONFIRMED",
            "userId": "aa4006bd-27ab-499a-9adc-cf7c6f195c24",
            "email": "<EMAIL>",
            "name": "Surya z",
        },
    ]


@patch.object(
    AuthService,
    "list_clients",
    MagicMock(
        return_value=[
            {
                "Username": "Test",
                "Attributes": [
                    {"Name": "sub", "Value": "c9348cfb-ac45-45fb-af6a-972619e0c3ef"},
                    {"Name": "email_verified", "Value": "true"},
                    {"Name": "name", "Value": "Test1"},
                    {"Name": "email", "Value": "<EMAIL>"},
                ],
                "Enabled": True,
                "UserStatus": "CONFIRMED",
            },
        ]
    ),
)
def test_list_clients(patch_db):
    # Given

    # When

    result = auth_service.get_list_clients()
    # Then
    assert result == [
        {
            "status": "CONFIRMED",
            "userId": "c9348cfb-ac45-45fb-af6a-972619e0c3ef",
            "email": "<EMAIL>",
            "name": "Test1",
            "username": "Test",
        }
    ]


def test_lookup_frequency(patch_db):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = client_basic_info.inserted_id

    frequency_id = patch_db.lookup_frequency.insert_one({"name": "Daily"}).inserted_id

    patch_db.client_claim_frequency.insert_one({"client_id": client_id, "frequency_id": frequency_id})

    # When

    result = lookup_service.get_frequencies(client_id)

    # Then
    assert result == [{"_id": str(frequency_id), "name": "Daily"}]


def test_claim_elements(flask_client, patch_db):
    # Given
    claim_elements = [
        {"name": "Charter Flight (Price)"},
        {"name": "Scheduled Flight (Price)"},
        {"name": "Accom (Cost)"},
        {"name": "Transfer (Cost)"},
        {"name": "Parking (Cost)"},
        {"name": "Car Hire (Cost)"},
        {"name": "Tour (Cost)"},
        {"name": "Add On Tour (Cost)"},
        {"name": "Generic (Cost)"},
        {"name": "Cruise (Cost)"},
        {"name": "TRVL INS (Cost)"},
        {"name": "Output VAT"},
        {"name": "Fees (with cost only)"},
        {"name": "TD Profit"},
        {"name": "Cancellation"},
        {"name": "Commission"},
        {"name": "VAT on Commission"},
        {"name": "EJH Deposit"},
        {"name": "EJH Balance"},
        {"name": "Performance"},
        {"name": "Accommodation"},
        {"name": "Returning Customers"},
        {"name": "ATOL"},
        {"name": "Flight Plus"},
        {"name": "Package"},
        {"name": "Refund"},
        {"name": "Refunds"},
        {"name": "APC"},
        {"name": "Travel Insurance"},
        {"name": "Insurance"},
        {"name": "INS"},
        {"name": "CPC"},
        {"name": "SAFI"},
        {"name": "SFC"},
        {"name": "Flight"},
        {"name": "Low Cost Flight"},
        {"name": "Low Cost Flights"},
        {"name": "LCF"},
        {"name": "Chartered Flight"},
        {"name": "Non-Trust"},
        {"name": "Flight Deposit"},
        {"name": "Flight Balance"},
        {"name": "Corporate Credit Card Flight"},
        {"name": "Flights paid by CC"},
        {"name": "BSP"},
        {"name": "Cancellation charge adjustment"},
        {"name": "Assumption"},
        {"name": "Refund Assumptions Reversal"},
        {"name": "Trade Assumption Bal. Reversal"},
        {"name": "Trade Assumption Dep. Reversal"},
        {"name": "Amendment fee"},
        {"name": "ASPP"},
        {"name": "Deposit"},
        {"name": "DEP"},
        {"name": "Balance"},
        {"name": "BAL"},
        {"name": "Non-Atol"},
    ]
    patch_db.lookup_claim_elements.insert_many(claim_elements)

    # When
    output_list = lookup_service.get_claim_elements()
    modified_list = []
    for item in output_list:
        if item.get("_id"):
            del item["_id"]
        modified_list.append(item)

    # Then
    assert modified_list == [
        {"name": "Charter Flight (Price)"},
        {"name": "Scheduled Flight (Price)"},
        {"name": "Accom (Cost)"},
        {"name": "Transfer (Cost)"},
        {"name": "Parking (Cost)"},
        {"name": "Car Hire (Cost)"},
        {"name": "Tour (Cost)"},
        {"name": "Add On Tour (Cost)"},
        {"name": "Generic (Cost)"},
        {"name": "Cruise (Cost)"},
        {"name": "TRVL INS (Cost)"},
        {"name": "Output VAT"},
        {"name": "Fees (with cost only)"},
        {"name": "TD Profit"},
        {"name": "Cancellation"},
        {"name": "Commission"},
        {"name": "VAT on Commission"},
        {"name": "EJH Deposit"},
        {"name": "EJH Balance"},
        {"name": "Performance"},
        {"name": "Accommodation"},
        {"name": "Returning Customers"},
        {"name": "ATOL"},
        {"name": "Flight Plus"},
        {"name": "Package"},
        {"name": "Refund"},
        {"name": "Refunds"},
        {"name": "APC"},
        {"name": "Travel Insurance"},
        {"name": "Insurance"},
        {"name": "INS"},
        {"name": "CPC"},
        {"name": "SAFI"},
        {"name": "SFC"},
        {"name": "Flight"},
        {"name": "Low Cost Flight"},
        {"name": "Low Cost Flights"},
        {"name": "LCF"},
        {"name": "Chartered Flight"},
        {"name": "Non-Trust"},
        {"name": "Flight Deposit"},
        {"name": "Flight Balance"},
        {"name": "Corporate Credit Card Flight"},
        {"name": "Flights paid by CC"},
        {"name": "BSP"},
        {"name": "Cancellation charge adjustment"},
        {"name": "Assumption"},
        {"name": "Refund Assumptions Reversal"},
        {"name": "Trade Assumption Bal. Reversal"},
        {"name": "Trade Assumption Dep. Reversal"},
        {"name": "Amendment fee"},
        {"name": "ASPP"},
        {"name": "Deposit"},
        {"name": "DEP"},
        {"name": "Balance"},
        {"name": "BAL"},
        {"name": "Non-Atol"},
    ]


@pytest.mark.skip("To be completed later")
def test_get_bank_accounts(patch_db):
    # Given
    client_id = ObjectId()
    patch_db.client_bank_acct_details.insert_many(
        [
            {"client_id": client_id, "currency": "GBP", "account_no": "********", "bank_name": "Test Bank"},
            {
                "client_id": client_id,
                "currency": "GBP",
                "account_no": "********",
                "bank_name": "Test Bank 2",
                "exclude_from_report": True,
            },
            {
                "client_id": client_id,
                "currency": "GBP",
                "account_no": "********",
                "bank_name": "Test Bank",
                "exclude_from_report": False,
            },
        ]
    )
    patch_db.lookup_banks.insert_many(
        [{"name": "Test Bank", "bank_id": "test-id"}, {"name": "Test Bank 2", "bank_id": "test-id-2"}]
    )

    # When
    result = lookup_service.get_bank_accounts(client_id, "GBP")

    # Then
    assert result == [
        {"bank_id": "test-id", "account_no": "********", "bank_name": "Test Bank"},
        {"bank_id": "test-id", "account_no": "********", "bank_name": "Test Bank"},
    ]
