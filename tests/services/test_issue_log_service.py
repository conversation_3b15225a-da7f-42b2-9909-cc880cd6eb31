from datetime import datetime
from freezegun import freeze_time
import pytest
from mongomock import ObjectId
from flaskr.services.issue_log_service import issue_log_service


@freeze_time("Jan 14 2012")
def test_create_issue_log(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"c_id": "2156"})
    data = {
        "clientId": str(basic_info.inserted_id),
        "opened": "2022-12-12",
        "shortDescription": "description",
        "priority": "High",
        "resolutionNotes": "resolution notes",
        "status": "Submitted",
        "dateResolved": "2022-12-12",
    }
    # When
    result = issue_log_service.create_issue_log(data)
    issue_log = patch_db.issue_log.find_one({"client_id": basic_info.inserted_id})

    # Then
    assert issue_log == {
        "_id": ObjectId(result["_id"]),
        "client_id": basic_info.inserted_id,
        "opened": datetime(2022, 12, 12, 0, 0),
        "short_description": "description",
        "priority": "High",
        "resolution_notes": "resolution notes",
        "status": "Submitted",
        "date_resolved": datetime(2022, 12, 12, 0, 0),
        "created_at": datetime(2012, 1, 14),
        "deleted": False,
    }


@pytest.mark.parametrize(
    "sort_order,sort_key,response",
    [
        (
            -1,
            "created_at",
            [
                {
                    "_id": "62fb53eb8af7efee9e56e089",
                    "clientId": "2",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "High",
                    "shortDescription": "description",
                    "opened": None,
                    "dateResolved": "2022-03-03",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e088",
                    "clientId": "1",
                    "status": "Authorized",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes2",
                    "priority": "Medium",
                    "shortDescription": "description2",
                    "opened": "2021-01-02",
                    "dateResolved": "2022-02-02",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e087",
                    "clientId": "1",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "Low",
                    "shortDescription": "description",
                    "opened": "2022-01-02",
                    "dateResolved": "2022-01-02",
                },
            ],
        ),
        (
            -1,
            "date_resolved",
            [
                {
                    "_id": "62fb53eb8af7efee9e56e089",
                    "clientId": "2",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "High",
                    "shortDescription": "description",
                    "opened": None,
                    "dateResolved": "2022-03-03",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e088",
                    "clientId": "1",
                    "status": "Authorized",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes2",
                    "priority": "Medium",
                    "shortDescription": "description2",
                    "opened": "2021-01-02",
                    "dateResolved": "2022-02-02",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e087",
                    "clientId": "1",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "Low",
                    "shortDescription": "description",
                    "opened": "2022-01-02",
                    "dateResolved": "2022-01-02",
                },
            ],
        ),
        (
            1,
            "date_resolved",
            [
                {
                    "_id": "62fb53eb8af7efee9e56e087",
                    "clientId": "1",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "Low",
                    "shortDescription": "description",
                    "opened": "2022-01-02",
                    "dateResolved": "2022-01-02",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e088",
                    "clientId": "1",
                    "status": "Authorized",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes2",
                    "priority": "Medium",
                    "shortDescription": "description2",
                    "opened": "2021-01-02",
                    "dateResolved": "2022-02-02",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e089",
                    "clientId": "2",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "High",
                    "shortDescription": "description",
                    "opened": None,
                    "dateResolved": "2022-03-03",
                },
            ],
        ),
        (
            1,
            "status",
            [
                {
                    "_id": "62fb53eb8af7efee9e56e088",
                    "clientId": "1",
                    "status": "Authorized",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes2",
                    "priority": "Medium",
                    "shortDescription": "description2",
                    "opened": "2021-01-02",
                    "dateResolved": "2022-02-02",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e087",
                    "clientId": "1",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "Low",
                    "shortDescription": "description",
                    "opened": "2022-01-02",
                    "dateResolved": "2022-01-02",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e089",
                    "clientId": "2",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "High",
                    "shortDescription": "description",
                    "opened": None,
                    "dateResolved": "2022-03-03",
                },
            ],
        ),
        (
            -1,
            "status",
            [
                {
                    "_id": "62fb53eb8af7efee9e56e087",
                    "clientId": "1",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "Low",
                    "shortDescription": "description",
                    "opened": "2022-01-02",
                    "dateResolved": "2022-01-02",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e089",
                    "clientId": "2",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "High",
                    "shortDescription": "description",
                    "opened": None,
                    "dateResolved": "2022-03-03",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e088",
                    "clientId": "1",
                    "status": "Authorized",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes2",
                    "priority": "Medium",
                    "shortDescription": "description2",
                    "opened": "2021-01-02",
                    "dateResolved": "2022-02-02",
                },
            ],
        ),
        (
            1,
            "priority",
            [
                {
                    "_id": "62fb53eb8af7efee9e56e089",
                    "clientId": "2",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "High",
                    "shortDescription": "description",
                    "opened": None,
                    "dateResolved": "2022-03-03",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e087",
                    "clientId": "1",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "Low",
                    "shortDescription": "description",
                    "opened": "2022-01-02",
                    "dateResolved": "2022-01-02",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e088",
                    "clientId": "1",
                    "status": "Authorized",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes2",
                    "priority": "Medium",
                    "shortDescription": "description2",
                    "opened": "2021-01-02",
                    "dateResolved": "2022-02-02",
                },
            ],
        ),
        (
            -1,
            "priority",
            [
                {
                    "_id": "62fb53eb8af7efee9e56e088",
                    "clientId": "1",
                    "status": "Authorized",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes2",
                    "priority": "Medium",
                    "shortDescription": "description2",
                    "opened": "2021-01-02",
                    "dateResolved": "2022-02-02",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e087",
                    "clientId": "1",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "Low",
                    "shortDescription": "description",
                    "opened": "2022-01-02",
                    "dateResolved": "2022-01-02",
                },
                {
                    "_id": "62fb53eb8af7efee9e56e089",
                    "clientId": "2",
                    "status": "Submitted",
                    "clientName": "G Touring Ltd",
                    "resolutionNotes": "resolution notes",
                    "priority": "High",
                    "shortDescription": "description",
                    "opened": None,
                    "dateResolved": "2022-03-03",
                },
            ],
        ),
    ],
)
def test_issue_log_list_sort(flask_client, patch_db, monkeypatch, sort_order, sort_key, response):
    patch_db.client_basic_info.insert_one(
        {
            "c_id": "1",
            "full_name": "G Touring Ltd",
            "friendly_name": "G Touring Ltd",
            "_id": ObjectId("62fb53eb8af7efee9e56e088"),
        }
    )
    patch_db.client_basic_info.insert_one(
        {
            "c_id": "2",
            "full_name": "G Touring Ltd",
            "friendly_name": "G Touring Ltd",
            "_id": ObjectId("62fb53eb8af7efee9e56e089"),
        }
    )
    patch_db.issue_log.insert_many(
        [
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "_id": ObjectId("62fb53eb8af7efee9e56e087"),
                "opened": datetime(2022, 1, 2, 0, 0),
                "short_description": "description",
                "priority": "Low",
                "resolution_notes": "resolution notes",
                "status": "Submitted",
                "date_resolved": datetime(2022, 1, 2, 0, 0),
                "created_at": datetime(2012, 1, 1),
                "deleted": False,
            },
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "opened": datetime(2021, 1, 2, 0, 0),
                "short_description": "description2",
                "priority": "Medium",
                "resolution_notes": "resolution notes2",
                "status": "Authorized",
                "date_resolved": datetime(2022, 2, 2, 0, 0),
                "created_at": datetime(2012, 1, 2),
                "deleted": False,
            },
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e089"),
                "opened": None,
                "_id": ObjectId("62fb53eb8af7efee9e56e089"),
                "short_description": "description",
                "priority": "High",
                "resolution_notes": "resolution notes",
                "status": "Submitted",
                "date_resolved": datetime(2022, 3, 3, 0, 0),
                "created_at": datetime(2012, 1, 3),
                "deleted": False,
            },
        ]
    )

    # When

    result = issue_log_service.list_issue_log(None, None, None, None, None, None, sort_key, sort_order, 1, 10)
    # Then
    assert result["content"] == response


def test_issue_log_list_with_status_filter(flask_client, patch_db, monkeypatch):
    patch_db.client_basic_info.insert_one(
        {
            "c_id": "1",
            "full_name": "G Touring Ltd",
            "friendly_name": "G Touring Ltd",
            "_id": ObjectId("62fb53eb8af7efee9e56e088"),
        }
    )
    patch_db.client_basic_info.insert_one(
        {
            "c_id": "2",
            "full_name": "G Touring Ltd",
            "friendly_name": "G Touring Ltd",
            "_id": ObjectId("62fb53eb8af7efee9e56e089"),
        }
    )
    patch_db.issue_log.insert_many(
        [
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "_id": ObjectId("62fb53eb8af7efee9e56e087"),
                "opened": datetime(2022, 1, 2, 0, 0),
                "short_description": "description",
                "priority": "Low",
                "resolution_notes": "resolution notes",
                "status": "Submitted",
                "date_resolved": datetime(2022, 1, 2, 0, 0),
                "created_at": datetime(2012, 1, 1),
                "deleted": False,
            },
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "opened": datetime(2022, 2, 2, 0, 0),
                "short_description": "description2",
                "priority": "Medium",
                "resolution_notes": "resolution notes2",
                "status": "Authorized",
                "date_resolved": datetime(2022, 2, 2, 0, 0),
                "created_at": datetime(2012, 1, 2),
                "deleted": False,
            },
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e089"),
                "opened": datetime(2022, 3, 3, 0, 0),
                "_id": ObjectId("62fb53eb8af7efee9e56e089"),
                "short_description": "description",
                "priority": "High",
                "resolution_notes": "resolution notes",
                "status": "Submitted",
                "date_resolved": datetime(2022, 3, 3, 0, 0),
                "created_at": datetime(2012, 1, 3),
                "deleted": False,
            },
        ]
    )
    # When

    result = issue_log_service.list_issue_log(
        "2022-01-01", "2023-01-01", None, None, "Submitted", None, "created_at", 1, 1, 10
    )
    # Then

    assert result == {
        "content": [
            {
                "_id": "62fb53eb8af7efee9e56e087",
                "clientId": "1",
                "clientName": "G Touring Ltd",
                "status": "Submitted",
                "resolutionNotes": "resolution notes",
                "priority": "Low",
                "shortDescription": "description",
                "opened": "2022-01-02",
                "dateResolved": "2022-01-02",
            },
            {
                "_id": "62fb53eb8af7efee9e56e089",
                "clientId": "2",
                "clientName": "G Touring Ltd",
                "status": "Submitted",
                "resolutionNotes": "resolution notes",
                "priority": "High",
                "shortDescription": "description",
                "opened": "2022-03-03",
                "dateResolved": "2022-03-03",
            },
        ],
        "empty": False,
        "first": True,
        "last": True,
        "pageNumber": 1,
        "numberOfElements": 2,
        "totalElements": 2,
        "totalPages": 1,
    }


def test_issue_log_list_with_priority_filter(flask_client, patch_db, monkeypatch):
    patch_db.client_basic_info.insert_one(
        {"c_id": "1", "full_name": "Annie", "friendly_name": "ann", "_id": ObjectId("62fb53eb8af7efee9e56e088")}
    )
    patch_db.client_basic_info.insert_one(
        {"c_id": "2", "full_name": "Xavi", "friendly_name": "xavi", "_id": ObjectId("62fb53eb8af7efee9e56e089")}
    )
    patch_db.issue_log.insert_many(
        [
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "_id": ObjectId("62fb53eb8af7efee9e56e087"),
                "opened": datetime(2022, 1, 2, 0, 0),
                "short_description": "description",
                "priority": "Low",
                "resolution_notes": "resolution notes",
                "status": "Submitted",
                "date_resolved": datetime(2022, 1, 2, 0, 0),
                "created_at": datetime(2012, 1, 1),
                "deleted": False,
            },
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "opened": datetime(2022, 1, 2, 0, 0),
                "short_description": "description2",
                "priority": "Medium",
                "resolution_notes": "resolution notes2",
                "status": "Authorized",
                "date_resolved": datetime(2022, 2, 2, 0, 0),
                "created_at": datetime(2012, 1, 2),
                "deleted": False,
            },
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e089"),
                "opened": datetime(2022, 1, 2, 0, 0),
                "_id": ObjectId("62fb53eb8af7efee9e56e089"),
                "short_description": "description",
                "priority": "High",
                "resolution_notes": "resolution notes",
                "status": "Submitted",
                "date_resolved": datetime(2022, 3, 3, 0, 0),
                "created_at": datetime(2012, 1, 3),
                "deleted": False,
            },
        ]
    )

    # When

    result = issue_log_service.list_issue_log(None, None, None, None, None, "Medium", "created_at", 1, 1, 10)
    # Then

    assert result == {
        "content": [
            {
                "_id": "62fb53eb8af7efee9e56e088",
                "clientId": "1",
                "clientName": "Annie",
                "status": "Authorized",
                "resolutionNotes": "resolution notes2",
                "priority": "Medium",
                "shortDescription": "description2",
                "opened": "2022-01-02",
                "dateResolved": "2022-02-02",
            }
        ],
        "empty": False,
        "first": True,
        "last": True,
        "pageNumber": 1,
        "numberOfElements": 1,
        "totalElements": 1,
        "totalPages": 1,
    }


def test_issue_log_list_with_client_filter(flask_client, patch_db, monkeypatch):
    patch_db.client_basic_info.insert_one(
        {"c_id": "1", "full_name": "Annie", "friendly_name": "ann", "_id": ObjectId("62fb53eb8af7efee9e56e088")}
    )
    patch_db.client_basic_info.insert_one(
        {"c_id": "2", "full_name": "Xavi", "friendly_name": "xavi", "_id": ObjectId("62fb53eb8af7efee9e56e089")}
    )
    patch_db.issue_log.insert_many(
        [
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "_id": ObjectId("62fb53eb8af7efee9e56e087"),
                "opened": datetime(2022, 1, 2, 0, 0),
                "short_description": "description",
                "priority": "Low",
                "resolution_notes": "resolution notes",
                "status": "Submitted",
                "date_resolved": datetime(2022, 1, 2, 0, 0),
                "created_at": datetime(2012, 1, 1),
                "deleted": False,
            },
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "opened": datetime(2022, 1, 2, 0, 0),
                "short_description": "description2",
                "priority": "Medium",
                "resolution_notes": "resolution notes2",
                "status": "Authorized",
                "date_resolved": datetime(2022, 2, 2, 0, 0),
                "created_at": datetime(2012, 1, 2),
                "deleted": False,
            },
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e089"),
                "opened": datetime(2022, 1, 2, 0, 0),
                "_id": ObjectId("62fb53eb8af7efee9e56e089"),
                "short_description": "description",
                "priority": "High",
                "resolution_notes": "resolution notes",
                "status": "Submitted",
                "date_resolved": datetime(2022, 3, 3, 0, 0),
                "created_at": datetime(2012, 1, 3),
                "deleted": False,
            },
        ]
    )

    # When

    result = issue_log_service.list_issue_log(
        "", "", None, "62fb53eb8af7efee9e56e088", None, None, "created_at", 1, 1, 10
    )

    # Then

    assert result == {
        "content": [
            {
                "_id": "62fb53eb8af7efee9e56e087",
                "clientId": "1",
                "status": "Submitted",
                "clientName": "Annie",
                "resolutionNotes": "resolution notes",
                "priority": "Low",
                "shortDescription": "description",
                "opened": "2022-01-02",
                "dateResolved": "2022-01-02",
            },
            {
                "_id": "62fb53eb8af7efee9e56e088",
                "clientId": "1",
                "clientName": "Annie",
                "status": "Authorized",
                "resolutionNotes": "resolution notes2",
                "priority": "Medium",
                "shortDescription": "description2",
                "opened": "2022-01-02",
                "dateResolved": "2022-02-02",
            },
        ],
        "empty": False,
        "first": True,
        "last": True,
        "pageNumber": 1,
        "numberOfElements": 2,
        "totalElements": 2,
        "totalPages": 1,
    }


def test_issue_log_list_with_date_filter(flask_client, patch_db, monkeypatch):
    patch_db.client_basic_info.insert_one(
        {"c_id": "1", "full_name": "Annie", "friendly_name": "ann", "_id": ObjectId("62fb53eb8af7efee9e56e088")}
    )
    patch_db.client_basic_info.insert_one(
        {"c_id": "2", "full_name": "Xavi", "friendly_name": "xavi", "_id": ObjectId("62fb53eb8af7efee9e56e089")}
    )
    patch_db.issue_log.insert_many(
        [
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "_id": ObjectId("62fb53eb8af7efee9e56e087"),
                "opened": datetime(2022, 10, 11, 0, 0),
                "short_description": "description",
                "priority": "Low",
                "resolution_notes": "resolution notes",
                "status": "Submitted",
                "date_resolved": datetime(2022, 1, 2, 0, 0),
                "created_at": datetime(2012, 1, 1),
                "deleted": False,
            },
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "opened": datetime(2021, 1, 2, 0, 0),
                "short_description": "description2",
                "priority": "Medium",
                "resolution_notes": "resolution notes2",
                "status": "Authorized",
                "date_resolved": datetime(2022, 2, 2, 0, 0),
                "created_at": datetime(2012, 1, 2),
                "deleted": False,
            },
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e089"),
                "opened": None,
                "_id": ObjectId("62fb53eb8af7efee9e56e089"),
                "short_description": "description",
                "priority": "High",
                "resolution_notes": "resolution notes",
                "status": "Submitted",
                "date_resolved": datetime(2022, 3, 3, 0, 0),
                "created_at": datetime(2012, 1, 3),
                "deleted": False,
            },
        ]
    )

    # When

    result = issue_log_service.list_issue_log(
        "2022-01-01", "2022-12-01", None, None, None, None, "created_at", 1, 1, 10
    )

    # Then

    assert result == {
        "content": [
            {
                "_id": "62fb53eb8af7efee9e56e087",
                "clientId": "1",
                "status": "Submitted",
                "clientName": "Annie",
                "resolutionNotes": "resolution notes",
                "priority": "Low",
                "shortDescription": "description",
                "opened": "2022-10-11",
                "dateResolved": "2022-01-02",
            },
        ],
        "empty": False,
        "first": True,
        "last": True,
        "pageNumber": 1,
        "numberOfElements": 1,
        "totalElements": 1,
        "totalPages": 1,
    }


@pytest.mark.parametrize(
    "query,response",
    [
        (
            "xavi",
            {
                "content": [
                    {
                        "_id": "62fb53eb8af7efee9e56e089",
                        "clientId": "2",
                        "clientName": "Xavi",
                        "status": "Submitted",
                        "resolutionNotes": "resolution notes",
                        "priority": "High",
                        "shortDescription": "description",
                        "opened": "2022-03-03",
                        "dateResolved": "2022-03-03",
                    }
                ],
                "empty": False,
                "first": True,
                "last": True,
                "pageNumber": 1,
                "numberOfElements": 1,
                "totalElements": 1,
                "totalPages": 1,
            },
        ),
        (
            "description",
            {
                "content": [
                    {
                        "_id": "62fb53eb8af7efee9e56e087",
                        "clientId": "1",
                        "clientName": "Annie",
                        "status": "Submitted",
                        "resolutionNotes": "resolution notes",
                        "priority": "Low",
                        "shortDescription": "description",
                        "opened": "2022-01-02",
                        "dateResolved": "2022-01-02",
                    },
                    {
                        "_id": "62fb53eb8af7efee9e56e088",
                        "clientId": "1",
                        "clientName": "Annie",
                        "status": "Authorized",
                        "resolutionNotes": "resolution notes2",
                        "priority": "Medium",
                        "shortDescription": "description2",
                        "opened": "2022-02-02",
                        "dateResolved": "2022-02-02",
                    },
                    {
                        "_id": "62fb53eb8af7efee9e56e089",
                        "clientId": "2",
                        "clientName": "Xavi",
                        "status": "Submitted",
                        "resolutionNotes": "resolution notes",
                        "priority": "High",
                        "shortDescription": "description",
                        "opened": "2022-03-03",
                        "dateResolved": "2022-03-03",
                    },
                ],
                "empty": False,
                "first": True,
                "last": True,
                "pageNumber": 1,
                "numberOfElements": 3,
                "totalElements": 3,
                "totalPages": 1,
            },
        ),
        (
            "low",
            {
                "content": [
                    {
                        "_id": "62fb53eb8af7efee9e56e087",
                        "clientId": "1",
                        "clientName": "Annie",
                        "status": "Submitted",
                        "resolutionNotes": "resolution notes",
                        "priority": "Low",
                        "shortDescription": "description",
                        "opened": "2022-01-02",
                        "dateResolved": "2022-01-02",
                    }
                ],
                "empty": False,
                "first": True,
                "last": True,
                "pageNumber": 1,
                "numberOfElements": 1,
                "totalElements": 1,
                "totalPages": 1,
            },
        ),
        (
            "Authorized",
            {
                "content": [
                    {
                        "_id": "62fb53eb8af7efee9e56e088",
                        "clientId": "1",
                        "clientName": "Annie",
                        "status": "Authorized",
                        "resolutionNotes": "resolution notes2",
                        "priority": "Medium",
                        "shortDescription": "description2",
                        "opened": "2022-02-02",
                        "dateResolved": "2022-02-02",
                    }
                ],
                "empty": False,
                "first": True,
                "last": True,
                "pageNumber": 1,
                "numberOfElements": 1,
                "totalElements": 1,
                "totalPages": 1,
            },
        ),
        (
            "resolution notes2",
            {
                "content": [
                    {
                        "_id": "62fb53eb8af7efee9e56e088",
                        "clientId": "1",
                        "clientName": "Annie",
                        "status": "Authorized",
                        "resolutionNotes": "resolution notes2",
                        "priority": "Medium",
                        "shortDescription": "description2",
                        "opened": "2022-02-02",
                        "dateResolved": "2022-02-02",
                    }
                ],
                "empty": False,
                "first": True,
                "last": True,
                "pageNumber": 1,
                "numberOfElements": 1,
                "totalElements": 1,
                "totalPages": 1,
            },
        ),
    ],
)
def test_issue_log_list_with_query_filter(flask_client, patch_db, monkeypatch, query, response):
    patch_db.client_basic_info.insert_one(
        {"c_id": "1", "full_name": "Annie", "friendly_name": "ann", "_id": ObjectId("62fb53eb8af7efee9e56e088")}
    )
    patch_db.client_basic_info.insert_one(
        {"c_id": "2", "full_name": "Xavi", "friendly_name": "xavi", "_id": ObjectId("62fb53eb8af7efee9e56e089")}
    )
    patch_db.issue_log.insert_many(
        [
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "_id": ObjectId("62fb53eb8af7efee9e56e087"),
                "opened": datetime(2022, 1, 2, 0, 0),
                "short_description": "description",
                "priority": "Low",
                "resolution_notes": "resolution notes",
                "status": "Submitted",
                "date_resolved": datetime(2022, 1, 2, 0, 0),
                "created_at": datetime(2012, 1, 1),
                "deleted": False,
            },
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "_id": ObjectId("62fb53eb8af7efee9e56e088"),
                "opened": datetime(2022, 2, 2, 0, 0),
                "short_description": "description2",
                "priority": "Medium",
                "resolution_notes": "resolution notes2",
                "status": "Authorized",
                "date_resolved": datetime(2022, 2, 2, 0, 0),
                "created_at": datetime(2012, 1, 2),
                "deleted": False,
            },
            {
                "client_id": ObjectId("62fb53eb8af7efee9e56e089"),
                "opened": datetime(2022, 3, 3, 0, 0),
                "_id": ObjectId("62fb53eb8af7efee9e56e089"),
                "short_description": "description",
                "priority": "High",
                "resolution_notes": "resolution notes",
                "status": "Submitted",
                "date_resolved": datetime(2022, 3, 3, 0, 0),
                "created_at": datetime(2012, 1, 3),
                "deleted": False,
            },
        ]
    )
    # When

    result = issue_log_service.list_issue_log(None, None, query, None, None, None, "created_at", 1, 1, 10)

    # Then
    assert result == response


@freeze_time("Jan 14 2012")
def test_update_issue_log(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"c_id": "2156"})
    issue = patch_db.issue_log.insert_one({"_id": ObjectId("621bf4f49bed97189d98ddcd")})
    issue_id = issue.inserted_id
    data = {
        "clientId": str(basic_info.inserted_id),
        "opened": "2022-12-12",
        "shortDescription": "description",
        "priority": "High",
        "resolutionNotes": "resolution notes",
        "status": "Submitted",
        "dateResolved": "2022-12-12",
        "deleted": False,
    }
    # When
    issue_log_service.update_issue_log(data, issue_id)
    issue_log = patch_db.issue_log.find_one({"client_id": ObjectId(data["clientId"])})

    # Then
    assert issue_log == {
        "_id": issue_id,
        "client_id": basic_info.inserted_id,
        "opened": datetime(2022, 12, 12, 0, 0),
        "short_description": "description",
        "priority": "High",
        "resolution_notes": "resolution notes",
        "status": "Submitted",
        "date_resolved": datetime(2022, 12, 12, 0, 0),
        "updated_at": datetime(2012, 1, 14),
        "deleted": False,
    }
