from datetime import datetime
from flaskr.services.internal_audit_service import internal_audit_service
from mongomock import ObjectId


def test_banking_report_success(flask_client, patch_db, monkeypatch):
    # Given
    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "_id": ObjectId("62ce5998bff9a1862ca22b6b"),
                "FileName": ["Bank Testing File-1"],
                "BankingReportAmount": [{"GBP": ********.********}],
                "BusinessRules": ["Movement of funds between bookings", "Movement of funds between bookings"],
            },
            {
                "_id": ObjectId("6524f3f7adde3547620b3f20"),
                "FileName": ["Bank Testing File-3"],
                "BankingReportAmount": [{"GBP": 9, "EUR": 7890.0, "DD": 567422.0}],
                "BusinessRules": [],
            },
            {
                "_id": ObjectId("652645e30191e88907783701"),
                "FileName": ["BAnk Testing File-2"],
                "BankingReportAmount": [{"NOK": 4900.0, "USD": 10000.0}],
                "BusinessRules": ["Agent Balance"],
            },
        ],
    )

    # When
    response = internal_audit_service.banking_report("632af99d7dc025fdb915f639", "2020-01-01", "2022-12-30")

    # Then
    assert response == {
        "content": [
            {
                "AmountasperBank": "",
                "BankingReportAmount": [{"amount": ********.********, "currency": "GBP"}],
                "BusinessRules": "Movement of funds between bookings",
                "Notes": "",
                "ResolutionNotes": "",
                "Risk": "",
                "Samples": "",
                "Status": "",
                "TestingStatus": "",
                "_id": "62ce5998bff9a1862ca22b6b",
                "fileName": "Bank Testing File-1",
            },
            {
                "AmountasperBank": "",
                "BankingReportAmount": [
                    {"amount": 9, "currency": "GBP"},
                    {"amount": 7890.0, "currency": "EUR"},
                    {"amount": 567422.0, "currency": "DD"},
                ],
                "BusinessRules": "",
                "Notes": "",
                "ResolutionNotes": "",
                "Risk": "",
                "Samples": "",
                "Status": "",
                "TestingStatus": "",
                "_id": "6524f3f7adde3547620b3f20",
                "fileName": "Bank Testing File-3",
            },
            {
                "AmountasperBank": "",
                "BankingReportAmount": [{"amount": 4900.0, "currency": "NOK"}, {"amount": 10000.0, "currency": "USD"}],
                "BusinessRules": "Agent Balance",
                "Notes": "",
                "ResolutionNotes": "",
                "Risk": "",
                "Samples": "",
                "Status": "",
                "TestingStatus": "",
                "_id": "652645e30191e88907783701",
                "fileName": "BAnk Testing File-2",
            },
        ]
    }


def test_banking_report_empty(flask_client, patch_db, monkeypatch):
    # Given
    monkeypatch.setattr(patch_db.banking_metadata, "aggregate", lambda *args, **kwargs: [])

    # When
    response = internal_audit_service.banking_report("632af99d7dc025fdb915f639", "2089-01-01", "2090-12-30")

    # Then
    assert response == {"content": []}


def test_claim_report_success(flask_client, patch_db, monkeypatch):
    # Given
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "_id": ObjectId("632bf1bdbc7e2d644cce7b76"),
                "FileName": ["Initial data load"],
                "BusinessRules": ["Claim Too Early For Departure Date"],
                "amount": [{"GBP": ********.*********, "USD": 34}],
                "count": [{"GBP": 11985, "USD": 32}],
                "checks": [{"GBP": 1, "USD": 1}],
                "checkedAmount": [{"GBP": 4083.22, "USD": 34567}],
                "BookingRef": "A9589",
                "element": "Commission",
                "checkCurrency": "USD",
                "transactionId": ObjectId("650bed46043b2ce136abcab8"),
            },
            {
                "_id": ObjectId("632bf1bdbc7e2d644cce7b76"),
                "FileName": ["Initial data load"],
                "BusinessRules": ["Claim Too Early For Departure Date"],
                "amount": [{"GBP": ********.*********, "USD": 34}],
                "count": [{"GBP": 11985, "USD": 32}],
                "checks": [{"GBP": 1, "USD": 1}],
                "checkedAmount": [{"GBP": 4083.22, "USD": 34567}],
                "BookingRef": "1234",
                "element": "Deposit",
                "checkCurrency": "GBP",
                "transactionId": ObjectId("650bef44043b2ce136abcab9"),
            },
        ],
    )

    trust_type = patch_db.lookup_trust_type.insert_one({"name": "ATOL Standard"})
    patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("632af99d7dc025fdb915f639"),
            "client_id": "1",
            "full_name": "abc",
            "friendly_name": "a",
            "type_of_trust_account": trust_type.inserted_id,
        }
    )
    patch_db.booking_claim_checks.insert_many(
        [
            {
                "client_id": ObjectId("632af99d7dc025fdb915f639"),
                "selected": False,
                "name": "Cancellation invoice/email",
                "transaction_id": ObjectId("650bed46043b2ce136abcab8"),
            },
            {
                "client_id": ObjectId("632af99d7dc025fdb915f639"),
                "selected": True,
                "name": "Proof of refund",
                "transaction_id": ObjectId("650bed46043b2ce136abcab8"),
            },
            {
                "client_id": ObjectId("632af99d7dc025fdb915f639"),
                "selected": True,
                "name": "Supplier invoice(s)/statement(s)",
                "transaction_id": ObjectId("650bed46043b2ce136abcab8"),
            },
            {
                "client_id": ObjectId("632af99d7dc025fdb915f639"),
                "selected": False,
                "name": "Booking Confirmation/Client invoice",
                "transaction_id": ObjectId("650bef44043b2ce136abcab9"),
            },
            {
                "client_id": ObjectId("632af99d7dc025fdb915f639"),
                "selected": True,
                "name": "Payment confirmation",
                "transaction_id": ObjectId("650bef44043b2ce136abcab9"),
            },
        ]
    )

    patch_db.claim_mandatory_checks.insert_many(
        [
            {
                "applicable_trust": "ATOL Standard",
                "element": "performance",
                "mandatory_checks": [
                    "payment confirmation ",
                    "Booking confirmation",
                    "Flight Tickets",
                    "Supplier invoice(s)/statement(s)",
                ],
            },
            {
                "applicable_trust": "ATOL Standard",
                "element": "commission",
                "mandatory_checks": [
                    "Payment confirmation",
                    "Booking Confirmation/Client invoice",
                    "Proof of refund",
                    "Cancellation invoice/email",
                ],
            },
            {
                "applicable_trust": "ATOL Standard",
                "element": "deposit",
                "mandatory_checks": ["Payment confirmation", "Booking Confirmation/Client invoice"],
            },
        ]
    )
    # When
    response = internal_audit_service.claim_report("632af99d7dc025fdb915f639", "2020-01-01", "2022-12-30")

    # Then
    assert response == {
        "content": [
            {
                "AmountasperBank": "",
                "BusinessRules": "Claim Too Early For Departure Date",
                "Notes": "",
                "ResolutionNotes": "",
                "Risk": "",
                "Status": "",
                "SubsetofFullChecks": "",
                "_id": "632bf1bdbc7e2d644cce7b76",
                "fileName": "Initial data load",
                "items": [
                    {
                        "MandatoryChecks": None,
                        "bookingRef": None,
                        "checkedAmount": 4083.22,
                        "checks": 1,
                        "currency": "GBP",
                        "element": None,
                        "originalClaim": None,
                        "percentageCheck": 0.01,
                        "percentageTotal": 0.02,
                        "revisedClaim": ********.*********,
                    },
                    {
                        "MandatoryChecks": "Cancellation invoice/email",
                        "bookingRef": "A9589",
                        "checkedAmount": 34567,
                        "checks": 1,
                        "currency": "USD",
                        "element": "Commission",
                        "originalClaim": None,
                        "percentageCheck": 3.12,
                        "percentageTotal": 101667.65,
                        "revisedClaim": 34,
                    },
                ],
            },
            {
                "AmountasperBank": "",
                "BusinessRules": "Claim Too Early For Departure Date",
                "Notes": "",
                "ResolutionNotes": "",
                "Risk": "",
                "Status": "",
                "SubsetofFullChecks": "",
                "_id": "632bf1bdbc7e2d644cce7b76",
                "fileName": "Initial data load",
                "items": [
                    {
                        "MandatoryChecks": "Booking Confirmation/Client invoice",
                        "bookingRef": "1234",
                        "checkedAmount": 4083.22,
                        "checks": 1,
                        "currency": "GBP",
                        "element": "Deposit",
                        "originalClaim": None,
                        "percentageCheck": 0.01,
                        "percentageTotal": 0.02,
                        "revisedClaim": ********.*********,
                    },
                    {
                        "MandatoryChecks": None,
                        "bookingRef": None,
                        "checkedAmount": 34567,
                        "checks": 1,
                        "currency": "USD",
                        "element": None,
                        "originalClaim": None,
                        "percentageCheck": 3.12,
                        "percentageTotal": 101667.65,
                        "revisedClaim": 34,
                    },
                ],
            },
        ]
    }


def test_claim_report_empty(flask_client, patch_db, monkeypatch):
    # Given
    monkeypatch.setattr(patch_db.claims_metadata, "aggregate", lambda *args, **kwargs: [])

    trust_type = patch_db.lookup_trust_type.insert_one({"name": "ATOL Standard"})
    patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("632af99d7dc025fdb915f639"),
            "client_id": "1",
            "full_name": "abc",
            "friendly_name": "a",
            "type_of_trust_account": trust_type.inserted_id,
        }
    )
    # When
    response = internal_audit_service.claim_report("632af99d7dc025fdb915f639", "2020-01-01", "2022-12-30")

    # Then
    assert response == {"content": []}


def test_client_performance(flask_client, patch_db, monkeypatch):
    # Given
    from_date = "2021-03-01"
    to_date = "2022-02-02"
    client_id = ObjectId("62419f08888c2b5ed06235d9")
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "_id": ObjectId("632bf1bdbc7e2d644cce7b76"),
                "status": "Submitted",
                "clientId": client_id,
                "claim_files": [
                    {
                        "status": "Cancelled by System",
                        "file_id": "e9eb95d8-2ba4-4a8a-a9cb-8e0d2e941984_2023-09-29T13:36:37.070399",
                        "file_name": "********-TTCUK-Claim.xlsx",
                        "file_date": "2022-01-01",
                        "submitted_date": {"$date": "2023-09-29T13:36:39.257Z"},
                        "checked_amount": {"GBP": 0},
                        "checks": {"GBP": 0},
                        "claim_total": {"GBP": 300000, "USD": 8000},
                        "item_count": {"GBP": 106},
                        "notes": "first",
                    }
                ],
            },
            {
                "_id": ObjectId("632bf1bdbc7e2d644cce7b76"),
                "status": "Submitted",
                "clientId": client_id,
                "claim_files": [
                    {
                        "status": "Submitted",
                        "file_id": "e9eb95d8-2ba4-4a8a-a9cb-8e0d2e941984_2023-09-29T13:36:37.070399",
                        "file_name": "********-TTCUK-Claim.xlsx",
                        "file_date": "2022-01-01",
                        "submitted_date": {"$date": "2023-09-29T13:36:39.257Z"},
                        "checked_amount": {"GBP": 0},
                        "checks": {"GBP": 0},
                        "claim_total": {"GBP": 300000, "USD": 8000},
                        "item_count": {"GBP": 106},
                        "notes": "first",
                    }
                ],
            },
        ],
    )
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda *args, **kwargs: iter([{"totalNoOfClaims": 2}]),
    )
    mock_response = [
        {
            "_id": {"date": "2021-04-01"},
            "amount": 200,
        },
        {
            "_id": {"date": "2022-02-01"},
            "amount": 200,
        },
    ]
    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda *args, **kwargs: mock_response,
    )
    monkeypatch.setattr(
        patch_db.client_limit,
        "find_one",
        lambda *args, **kwargs: {
            "clientId": client_id,
            "max_no_of_claims": 2.0,
            "total_annual_revenue": 2000,
            "from_date": datetime(2021, 3, 1, 14, 46, 0),
            "to_date": datetime(2022, 2, 2, 14, 46, 0),
        },
    )
    # When
    response = internal_audit_service.client_performance(str(client_id), from_date, to_date)

    # Then
    assert response == {
        "monthly": [
            {
                "amount": 0,
                "budgeted_amount": 166.67,
                "max_no_of_claims": 0,
                "month": "March",
                "no_of_claims": 2,
                "total_amount": 0,
                "total_claims": 2.0,
            },
            {
                "amount": 200,
                "budgeted_amount": 166.67,
                "max_no_of_claims": 0,
                "month": "April",
                "no_of_claims": 2,
                "total_amount": 200,
                "total_claims": 2.0,
            },
            {
                "amount": 0,
                "budgeted_amount": 166.67,
                "max_no_of_claims": 0,
                "month": "May",
                "no_of_claims": 2,
                "total_amount": 0,
                "total_claims": 2.0,
            },
            {
                "amount": 0,
                "budgeted_amount": 166.67,
                "max_no_of_claims": 0,
                "month": "June",
                "no_of_claims": 2,
                "total_amount": 0,
                "total_claims": 2.0,
            },
            {
                "amount": 0,
                "budgeted_amount": 166.67,
                "max_no_of_claims": 0,
                "month": "July",
                "no_of_claims": 2,
                "total_amount": 0,
                "total_claims": 2.0,
            },
            {
                "amount": 0,
                "budgeted_amount": 166.67,
                "max_no_of_claims": 0,
                "month": "August",
                "no_of_claims": 2,
                "total_amount": 0,
                "total_claims": 2.0,
            },
            {
                "amount": 0,
                "budgeted_amount": 166.67,
                "max_no_of_claims": 0,
                "month": "September",
                "no_of_claims": 2,
                "total_amount": 0,
                "total_claims": 2.0,
            },
            {
                "amount": 0,
                "budgeted_amount": 166.67,
                "max_no_of_claims": 0,
                "month": "October",
                "no_of_claims": 2,
                "total_amount": 0,
                "total_claims": 2.0,
            },
            {
                "amount": 0,
                "budgeted_amount": 166.67,
                "max_no_of_claims": 0,
                "month": "November",
                "no_of_claims": 2,
                "total_amount": 0,
                "total_claims": 2.0,
            },
            {
                "amount": 0,
                "budgeted_amount": 166.67,
                "max_no_of_claims": 0,
                "month": "December",
                "no_of_claims": 2,
                "total_amount": 0,
                "total_claims": 2.0,
            },
            {
                "amount": 0,
                "budgeted_amount": 166.67,
                "max_no_of_claims": 0,
                "month": "January",
                "no_of_claims": 2,
                "total_amount": 0,
                "total_claims": 2.0,
            },
            {
                "amount": 200,
                "budgeted_amount": 166.67,
                "max_no_of_claims": 0,
                "month": "February",
                "no_of_claims": 2,
                "total_amount": 400,
                "total_claims": 2.0,
            },
        ],
        "yearly": {"actualClaims": 2, "totalAnnualActualRevenue": 400, "totalAnnualRevenue": 2000, "totalClaims": 2.0},
    }
