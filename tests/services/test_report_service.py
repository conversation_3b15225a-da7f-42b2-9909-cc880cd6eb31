from datetime import datetime

import werkzeug.exceptions
from freezegun import freeze_time
from hamcrest import assert_that, calling, raises
from mongomock import ObjectId
import pytest
from werkzeug.datastructures import ImmutableMultiDict
from unittest.mock import MagicMock, patch
from flaskr.services.reporting_service import ReportingService, reporting_service


@pytest.mark.skip("To be completed later")
def test_trust_balance(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one({"c_id": "1", "is_disabled": None})
    client_id = client_basic_info.inserted_id
    patch_db.trust_fund_v2.insert_many(
        [
            {"client_id": client_id, "booking_ref": "1001", "balance": -360000.0, "currency_code": ["GBP"]},
            {"client_id": client_id, "booking_ref": "50863", "balance": -1440.0, "currency_code": ["GBP"]},
            {"client_id": client_id, "booking_ref": "45073", "balance": -2400.0, "currency_code": ["GBP"]},
        ]
    )
    monkeypatch.setattr(
        patch_db.trust_fund_v2,
        "aggregate",
        lambda *args, **kwargs: iter(
            [
                {
                    "_id": ObjectId("629ddb43186880847d980d3a"),
                    "clientId": client_id,
                    "balance": -360000.0,
                    "bookingDate": "2022-11-05",
                    "bookingRef": "1001",
                    "cId": "1",
                    "clientName": "ABC",
                    "friendlyName": "Edstem",
                    "currency": ["GBP"],
                    "departureDate": "2022-04-01",
                    "deposits": -360000.0,
                    "leadPax": "Lady Emma Monson",
                    "refundsFromDepositFile": 0.0,
                    "returnDate": "2021-10-16",
                    "status": "Active",
                    "totalBanked": -360000.0,
                    "totalClaimed": 0.0,
                    "type": None,
                    "totalBookingValue": 0.0,
                },
                {
                    "_id": ObjectId("625e37cec30536f930fbba99"),
                    "clientId": client_id,
                    "balance": -1440.0,
                    "bookingDate": None,
                    "bookingRef": "50863",
                    "cId": "1",
                    "clientName": "ABC",
                    "friendlyName": "Edstem",
                    "currency": ["GBP"],
                    "departureDate": None,
                    "deposits": 0.0,
                    "leadPax": None,
                    "refundsFromDepositFile": 0.0,
                    "returnDate": None,
                    "status": "Active",
                    "totalBanked": 0.0,
                    "totalClaimed": 1440.0,
                    "type": None,
                    "totalBookingValue": 0.0,
                },
                {
                    "_id": ObjectId("625e37cec30536f930fbba97"),
                    "clientId": client_id,
                    "balance": -2400.0,
                    "bookingDate": None,
                    "bookingRef": "45073",
                    "cId": "1",
                    "clientName": "ABC",
                    "friendlyName": "Edstem",
                    "currency": ["GBP"],
                    "departureDate": None,
                    "deposits": 34392.0,
                    "leadPax": None,
                    "refundsFromDepositFile": 34392.0,
                    "returnDate": None,
                    "status": "Active",
                    "totalBanked": 0.0,
                    "totalClaimed": 2400.0,
                    "type": "Agent",
                    "totalBookingValue": 0.0,
                },
            ]
        )
        if args[0][-1] != {"$count": "count"}
        else iter([{"count": 3}]),
    )
    data = {"currency": "GBP", "client": client_id, "page": 1, "size": 4}

    # When
    response = reporting_service.trust_balance_report(data)

    # Then
    response["content"] = list(response["content"])
    assert response == {
        "content": [
            {
                "_id": "629ddb43186880847d980d3a",
                "balance": -360000.0,
                "bookingDate": "2022-11-05",
                "bookingRef": "1001",
                "clientId": str(client_id),
                "cId": "1",
                "clientName": "ABC",
                "friendlyName": "Edstem",
                "currency": "GBP",
                "departureDate": "2022-04-01",
                "deposits": -360000.0,
                "leadPax": "Lady Emma Monson",
                "refundsFromDepositFile": 0.0,
                "returnDate": "2021-10-16",
                "status": "Active",
                "totalBanked": -360000.0,
                "totalBookingValue": 0.0,
                "totalClaimed": 0.0,
                "type": None,
            },
            {
                "_id": "625e37cec30536f930fbba99",
                "balance": -1440.0,
                "bookingDate": None,
                "bookingRef": "50863",
                "clientId": str(client_id),
                "cId": "1",
                "clientName": "ABC",
                "friendlyName": "Edstem",
                "currency": "GBP",
                "departureDate": None,
                "deposits": 0.0,
                "leadPax": None,
                "refundsFromDepositFile": 0.0,
                "returnDate": None,
                "status": "Active",
                "totalBanked": 0.0,
                "totalBookingValue": 0.0,
                "totalClaimed": 1440.0,
                "type": None,
            },
            {
                "_id": "625e37cec30536f930fbba97",
                "balance": -2400.0,
                "bookingDate": None,
                "bookingRef": "45073",
                "clientId": str(client_id),
                "cId": "1",
                "clientName": "ABC",
                "friendlyName": "Edstem",
                "currency": "GBP",
                "departureDate": None,
                "deposits": 34392.0,
                "leadPax": None,
                "refundsFromDepositFile": 34392.0,
                "returnDate": None,
                "status": "Active",
                "totalBanked": 0.0,
                "totalBookingValue": 0.0,
                "totalClaimed": 2400.0,
                "type": "Agent",
            },
        ],
        "empty": False,
        "first": True,
        "last": True,
        "pageNumber": 1,
        "totalElements": 3,
        "totalPages": 1,
    }


@patch.object(
    ReportingService,
    "banking_and_claim_between_dates",
    lambda a, b, c, d, e: {"bankingAmount": 41720, "claimAmount": 55000, "clientName": "abc"},
)
def test_bank_reconciliation_statement_report(flask_client, patch_db):
    # Given

    trust_type = patch_db.lookup_trust_type.insert_one({"name": "ATOL Standard"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "full_name": "abc", "friendly_name": "a", "type_of_trust_account": trust_type.inserted_id}
    )
    client_id = basic_info.inserted_id
    patch_db.opening_closing_balance.insert_one(
        {
            "client_id": client_id,
            "opening_balance": 789965,
            "closing_balance": 45677.89,
            "date": datetime.fromisoformat("2022-02-24"),
            "updated_at": datetime.fromisoformat("2022-02-24"),
            "created_at": datetime.fromisoformat("2022-02-24"),
            "currency": "GBP",
        }
    )
    patch_db.banking_metadata.insert_many(
        [
            {
                "client_id": client_id,
                "status": "Client Not Banked",
                "banking_files": [
                    {"file_date": "2022-02-24", "deposit": {"GBP": 9000, "USD": 900}},
                    {"file_date": "2022-02-24", "deposit": {"GBP": 1000, "USD": 500}},
                ],
            },
            {
                "client_id": client_id,
                "status": "Client Not Banked",
                "banking_files": [
                    {"file_date": "2022-02-24", "deposit": {"GBP": 9000, "USD": 900}},
                    {"file_date": "2022-02-24", "deposit": {"GBP": 1000, "USD": 500}},
                ],
            },
            {
                "client_id": client_id,
                "status": "Submitted",
                "banking_files": [
                    {"file_date": "2022-02-24", "deposit": {"GBP": 9000, "USD": 900}},
                    {"file_date": "2022-02-24", "deposit": {"GBP": 1000, "USD": 500}},
                ],
            },
            {
                "client_id": ObjectId(),
                "status": "Submitted",
                "banking_files": [
                    {"file_date": "2022-02-24", "deposit": {"GBP": 9000, "USD": 900}},
                    {"file_date": "2022-02-24", "deposit": {"GBP": 1000, "USD": 500}},
                ],
            },
        ]
    )
    patch_db.claims_metadata.insert_many(
        [
            {
                "client_id": client_id,
                "status": "Client Not Banked",
                "claim_files": [
                    {"file_date": "2022-02-24", "claim_amount": {"GBP": 9000, "USD": 900}},
                    {"file_date": "2022-02-24", "claim_amount": {"GBP": 500, "USD": 500}},
                ],
            },
            {
                "client_id": client_id,
                "status": "Client Not Banked",
                "claim_files": [
                    {"file_date": "2022-02-24", "claim_amount": {"GBP": 9000, "USD": 900}},
                    {"file_date": "2022-02-24", "claim_amount": {"GBP": 500, "USD": 500}},
                ],
            },
            {
                "client_id": client_id,
                "status": "Submitted",
                "claim_files": [
                    {"file_date": "2022-02-24", "claim_amount": {"GBP": 9000, "USD": 900}},
                    {"file_date": "2022-02-24", "claim_amount": {"GBP": 500, "USD": 500}},
                ],
            },
            {
                "client_id": ObjectId(),
                "status": "Submitted",
                "claim_files": [
                    {"file_date": "2022-02-24", "claim_amount": {"GBP": 9000, "USD": 900}},
                    {"file_date": "2022-02-24", "claim_amount": {"GBP": 500, "USD": 500}},
                ],
            },
        ]
    )

    # When
    result = reporting_service.bank_reconciliation_statement_report(client_id, "GBP", "2022-02-24", "2022-02-24")

    # Then
    assert result == {
        "clientName": "abc",
        "bankingAmount": 39720,
        "claimAmount": -54000,
        "closingBalance": 44677.89,
        "openingBalance": 789965,
    }


def test_movements_report_data(flask_client, patch_db, monkeypatch):
    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda x: [
            {
                "currencyCode": "GBP",
                "amount": 1000,
                "clientId": "1",
                "clientName": "abc",
            }
        ],
    )
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda x: [
            {
                "currencyCode": "GBP",
                "amount": 1000,
                "clientId": "1",
                "clientName": "abc",
            }
        ],
    )

    reporting_service = ReportingService()
    response = reporting_service._ReportingService__movements_report_data("1", "2022-11-12", "2022-12-12", "Current")
    assert response == (
        [
            {
                "receiptsCurrent": 1000.0,
                "clientId": "1",
                "clientName": "abc",
                "currencyCode": "GBP",
                "receiptsCurrentThousandth": 1.0,
            }
        ],
        [
            {
                "paymentsCurrent": 1000.0,
                "clientId": "1",
                "clientName": "abc",
                "currencyCode": "GBP",
                "paymentsCurrentThousandth": 1.0,
            }
        ],
    )


def test_client_files_report(flask_client, patch_db, monkeypatch):
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda x: [
            {
                "metadata": [{"total": 44, "page": 1}],
                "data": [
                    {
                        "_id": ObjectId("628b4919a47691370038cba1"),
                        "clientName": "joemon",
                        "friendlyName": "Edstem",
                        "clientId": "1212",
                        "fileDate": "2022-05-20",
                        "fileStatus": "Submitted",
                        "fileType": "Claim File",
                        "fileName": "********-All -claim -mapping01.xlsx",
                        "fileSubmitted": datetime(2022, 5, 23, 8, 43, 5, 903000),
                        "claims": {"_id": {"currency": "GBP"}, "sum": {"false": 110000.0}, "count": {"false": 11}},
                        # "trust": "ATOL Standard",
                        "currency": "GBP",
                    },
                    {
                        "_id": ObjectId("628b4919a47691370038cba1"),
                        "clientName": "joemon",
                        "friendlyName": "Edstem",
                        "clientId": "1212",
                        "fileDate": "2022-05-20",
                        "fileStatus": "Submitted",
                        "fileType": "Claim File",
                        "fileName": "********-All -claim -mapping01.xlsx",
                        "fileSubmitted": datetime(2022, 5, 23, 8, 43, 5, 903000),
                        "claims": {
                            "_id": {"currency": "USD"},
                            "sum": {"true": 10000.0, "false": 20000.0},
                            "count": {"true": 1, "false": 2},
                        },
                        "currency": "USD",
                    },
                    {
                        "_id": ObjectId("628b48e6a47691370038cba0"),
                        "clientName": "joemon 2",
                        "friendlyName": "Edstem",
                        "clientId": "151515",
                        "fileDate": "2022-05-20",
                        "fileStatus": "Submitted",
                        "fileType": "Banking File",
                        "fileName": "********-All -Banking -mapping01.xlsx",
                        "fileSubmitted": datetime(2022, 5, 23, 8, 42, 14, 610000),
                        "bank": {"_id": {"currency": "GBP"}, "sum": {"false": 110000.0}, "count": {"false": 11}},
                        "currency": "GBP",
                    },
                    {
                        "_id": ObjectId("628b48e6a47691370038cba0"),
                        "clientName": "joemon 2",
                        "friendlyName": "Edstem",
                        "clientId": "151515",
                        "fileDate": "2022-05-20",
                        "fileStatus": "Submitted",
                        "fileType": "Banking File",
                        "fileName": "********-All -Banking -mapping01.xlsx",
                        "fileSubmitted": datetime(2022, 5, 23, 8, 42, 14, 610000),
                        "bank": {"_id": {"currency": "USD"}, "sum": {"false": 20000.0}, "count": {"false": 3}},
                        "currency": "USD",
                    },
                ],
            }
        ],
    )

    response = reporting_service.client_files_report(
        ImmutableMultiDict(
            [
                ("client", "628b4919a47691370038cfd1"),
                ("fromDate", "2022-02-24"),
                ("toDate", "2023-02-24"),
                ("page", "1"),
                ("size", "4"),
            ]
        )
    )
    assert response == {
        "content": [
            {
                "_id": "628b4919a47691370038cba1",
                "bankingFileTotal": 0,
                "bankingItems": 0,
                "claimFileTotal": 110000.0,
                "claimItems": 11.0,
                "clientName": "joemon",
                "friendlyName": "Edstem",
                "currency": "GBP",
                "fileDate": "2022-05-20",
                "fileName": "********-All -claim -mapping01.xlsx",
                "fileStatus": "Submitted",
                "fileSubmitted": "2022-05-23T08:43:05.903000",
                "fileType": "Claim File",
                "validClaimAmount": 110000.0,
                "validClaimItems": 11.0,
                "voidClaimAmount": 0.0,
                "voidClaimItems": 0.0,
            },
            {
                "_id": "628b4919a47691370038cba1",
                "bankingFileTotal": 0,
                "bankingItems": 0,
                "claimFileTotal": 30000.0,
                "claimItems": 3.0,
                "clientName": "joemon",
                "friendlyName": "Edstem",
                "currency": "USD",
                "fileDate": "2022-05-20",
                "fileName": "********-All -claim -mapping01.xlsx",
                "fileStatus": "Submitted",
                "fileSubmitted": "2022-05-23T08:43:05.903000",
                "fileType": "Claim File",
                "validClaimAmount": 20000.0,
                "validClaimItems": 2.0,
                "voidClaimAmount": 10000.0,
                "voidClaimItems": 1.0,
            },
            {
                "_id": "628b48e6a47691370038cba0",
                "bankingFileTotal": 110000.0,
                "bankingItems": 11.0,
                "claimFileTotal": 0,
                "claimItems": 0,
                "clientName": "joemon 2",
                "friendlyName": "Edstem",
                "currency": "GBP",
                "fileDate": "2022-05-20",
                "fileName": "********-All -Banking -mapping01.xlsx",
                "fileStatus": "Submitted",
                "fileSubmitted": "2022-05-23T08:42:14.610000",
                "fileType": "Banking File",
                "validClaimAmount": 0,
                "validClaimItems": 0,
                "voidClaimAmount": 0,
                "voidClaimItems": 0,
            },
            {
                "_id": "628b48e6a47691370038cba0",
                "bankingFileTotal": 20000.0,
                "bankingItems": 3.0,
                "claimFileTotal": 0,
                "claimItems": 0,
                "clientName": "joemon 2",
                "friendlyName": "Edstem",
                "currency": "USD",
                "fileDate": "2022-05-20",
                "fileName": "********-All -Banking -mapping01.xlsx",
                "fileStatus": "Submitted",
                "fileSubmitted": "2022-05-23T08:42:14.610000",
                "fileType": "Banking File",
                "validClaimAmount": 0,
                "validClaimItems": 0,
                "voidClaimAmount": 0,
                "voidClaimItems": 0,
            },
        ],
        "empty": False,
        "first": True,
        "last": False,
        "numberOfElements": 4,
        "pageNumber": 1,
        "totalElements": 44,
        "totalPages": 11,
    }


@pytest.mark.parametrize(
    "currency, weekly_fornightly, trust_type_input, output",
    [
        ("GBP", True, {"name": "ATOL Escrow"}, {"bankingAmount": 0, "claimAmount": 7800, "clientName": "abc"}),
        (
            "GBP",
            False,
            {"name": "ATOL Standard"},
            {"bankingAmount": 6200, "claimAmount": 14000.0, "clientName": "abc"},
        ),
        ("USD", False, {"name": "ATOL Standard"}, {"bankingAmount": 0, "claimAmount": 0, "clientName": "abc"}),
    ],
)
@patch.object(
    ReportingService,
    "_ReportingService__movements_report_data",
    lambda *args: (
        [
            {
                "clientId": "2146",
                "clientName": "CR7",
                "currencyCode": "GBP",
                "receipts": 6200,
                "receiptsThousandth": 6.200,
            }
        ],
        [
            {
                "clientId": "2146",
                "clientName": "CR7",
                "currencyCode": "GBP",
                "payments": 14000.0,
                "paymentsThousandth": 14.000,
            }
        ],
    ),
)
def test_banking_and_claim_between_dates(
    flask_client, patch_db, monkeypatch, currency, weekly_fornightly, trust_type_input, output
):
    # Given
    monkeypatch.setattr(patch_db.banking_metadata, "aggregate", lambda x: [])
    trust_type = patch_db.lookup_trust_type.insert_one(trust_type_input)
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "CR7", "full_name": "abc", "friendly_name": "a", "type_of_trust_account": trust_type.inserted_id}
    )
    client_id = basic_info.inserted_id
    # When
    response = reporting_service.banking_and_claim_between_dates(
        client_id, currency, "2022-02-24", "2023-02-24", weekly_fornightly
    )
    # Then
    assert response == output


def test_banking_claim_summary_report(flask_client, patch_db, monkeypatch):
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda x: [
            {
                "metadata": [{"total": 3, "page": 1}],
                "data": [
                    {
                        "_id": {"reportDate": datetime(2022, 1, 25, 0, 0), "currency": "GBP"},
                        "totalClaim": 20775,
                        "totalBanking": 0,
                        "paymentDate": [],
                        "paymentType": [],
                        "elementType": [{"elementType": "performance", "amount": 20775}],
                    },
                    {
                        "_id": {"reportDate": datetime(2022, 4, 13, 0, 0), "currency": "USD"},
                        "totalClaim": 42000,
                        "totalBanking": 0,
                        "paymentDate": [],
                        "paymentType": [],
                        "elementType": [{"elementType": "performance", "amount": 42000}],
                    },
                    {
                        "_id": {"reportDate": datetime(2022, 4, 13, 0, 0), "currency": "GBP"},
                        "totalClaim": 84000,
                        "totalBanking": 0,
                        "paymentDate": [],
                        "paymentType": [],
                        "elementType": [
                            {"elementType": "balance", "amount": 7000},
                            {"elementType": "claim", "amount": 14000},
                            {"elementType": "deposit", "amount": 21000},
                            {"elementType": "performance", "amount": 42000},
                        ],
                    },
                ],
            }
        ],
    )
    basic_info = patch_db.client_basic_info.insert_one({"c_id": "1", "full_name": "abc", "friendly_name": "a"})
    client_id = basic_info.inserted_id
    patch_db.opening_closing_balance.insert_one(
        {
            "client_id": client_id,
            "opening_balance": 789965,
            "closing_balance": 45677.89,
            "date": datetime.fromisoformat("2022-04-13"),
            "updated_at": datetime.fromisoformat("2022-02-24"),
            "created_at": datetime.fromisoformat("2022-02-24"),
            "currency": "GBP",
        }
    )

    response = reporting_service.banking_claim_summary_report(client_id, "2022-02-24", "2023-02-24", "1", "10")
    assert response["content"] == [
        {
            "apcFee": 0.0,
            "atol": 0.0,
            "balance": 0.0,
            "bankTransfer": 0.0,
            "bsp": 0.0,
            "cancellation": 0.0,
            "card": 0.0,
            "cash": 0.0,
            "charterFlightsOrScheduledFlights": 0.0,
            "cheque": 0.0,
            "clientId": "1",
            "clientName": "abc",
            "closingBalance": 0,
            "commission": 0.0,
            "cropCard": 0.0,
            "cruise": 0.0,
            "currency": "GBP",
            "deposit": 0.0,
            "friendlyName": "a",
            "insurance": 0.0,
            "lcf": 0.0,
            "nonTrust": 0.0,
            "openingBalance": 0,
            "otherElementAmount": 0.0,
            "otherPaymentAmount": 0.0,
            "paymentDate": [],
            "paysafeAmount": 0.0,
            "performance": 20775.0,
            "refund": 0.0,
            "reportDate": "25/01/2022",
            "totalBanking": 0,
            "totalClaim": 20775.0,
        },
        {
            "apcFee": 0.0,
            "atol": 0.0,
            "balance": 0.0,
            "bankTransfer": 0.0,
            "bsp": 0.0,
            "cancellation": 0.0,
            "card": 0.0,
            "cash": 0.0,
            "charterFlightsOrScheduledFlights": 0.0,
            "cheque": 0.0,
            "clientId": "1",
            "clientName": "abc",
            "closingBalance": 0,
            "commission": 0.0,
            "cropCard": 0.0,
            "cruise": 0.0,
            "currency": "USD",
            "deposit": 0.0,
            "friendlyName": "a",
            "insurance": 0.0,
            "lcf": 0.0,
            "nonTrust": 0.0,
            "openingBalance": 0,
            "otherElementAmount": 0.0,
            "otherPaymentAmount": 0.0,
            "paymentDate": [],
            "paysafeAmount": 0.0,
            "performance": 42000.0,
            "refund": 0.0,
            "reportDate": "13/04/2022",
            "totalBanking": 0,
            "totalClaim": 42000.0,
        },
        {
            "apcFee": 0.0,
            "atol": 0.0,
            "balance": 7000.0,
            "bankTransfer": 0.0,
            "bsp": 0.0,
            "cancellation": 0.0,
            "card": 0.0,
            "cash": 0.0,
            "charterFlightsOrScheduledFlights": 0.0,
            "cheque": 0.0,
            "clientId": "1",
            "clientName": "abc",
            "closingBalance": 45677.89,
            "commission": 0.0,
            "cropCard": 0.0,
            "cruise": 0.0,
            "currency": "GBP",
            "deposit": 21000.0,
            "friendlyName": "a",
            "insurance": 0.0,
            "lcf": 0.0,
            "nonTrust": 0.0,
            "openingBalance": 789965.0,
            "otherElementAmount": 14000.0,
            "otherPaymentAmount": 0.0,
            "paymentDate": [],
            "paysafeAmount": 0.0,
            "performance": 42000.0,
            "refund": 0.0,
            "reportDate": "13/04/2022",
            "totalBanking": 0,
            "totalClaim": 84000.0,
        },
    ]


@patch.object(
    ReportingService,
    "banking_and_claim_between_dates",
    lambda a, b, c, d, e, f: {"bankingAmount": 40000, "claimAmount": 0, "clientName": "abc"},
)
def test_compliance_computation_report(flask_client, patch_db, monkeypatch):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "2146", "friendly_name": "abc"})
    client_id = basic_info.inserted_id
    patch_db.opening_closing_balance.insert_many(
        [
            {
                "client_id": client_id,
                "date": datetime.fromisoformat("2020-10-27"),
                "closing_balance": 1000,
                "opening_balance": 2000,
                "currency": "GBP",
            },
            {
                "client_id": client_id,
                "date": datetime.fromisoformat("2020-10-30"),
                "closing_balance": 8000,
                "opening_balance": 4000,
                "currency": "GBP",
            },
        ]
    )
    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "_id": "GBP",
                "actual_amount_total": ********.*********,
                "original_amount_total": 0,
                "file_date": ["2020-10-29", "2020-10-28", "2020-10-27", "2020-10-30"],
                "file_name": [
                    "Initial data load",
                    "20201028DailyPayments.csv",
                    "20201029DailyPayments.csv",
                    "20201030DailyPayments.csv",
                ],
            }
        ],
    )
    data = {
        "client_id": client_id,
        "multiplier": 4,
        "date": "2020-10-01",
        "modified_by": "string",
        "credit_multiplier": "2",
        "debit_multiplier": "3",
    }
    patch_db.client_escrow_multiplier.insert_one(data)

    # When
    response = reporting_service.compliance_computation_report(client_id, "GBP", "2020-10-27", "2020-10-30")

    # Then
    assert response == {
        "amount": 80000,
        "bankingAmount": 40000,
        "claimAmount": 0,
        "closingBalanceCurrent": 80000,
        "closingBalancePrevious": 40000,
        "clientName": "abc",
    }


def test_compliance_computation_report_wlh(flask_client, patch_db, monkeypatch):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "2175", "friendly_name": "we love"})
    client_id = basic_info.inserted_id
    patch_db.opening_closing_balance.insert_many(
        [
            {
                "client_id": client_id,
                "date": datetime.fromisoformat("2020-10-27"),
                "closing_balance": 1000,
                "opening_balance": 2000,
                "currency": "GBP",
            },
            {
                "client_id": client_id,
                "date": datetime.fromisoformat("2020-10-30"),
                "closing_balance": 8000,
                "opening_balance": 4000,
                "currency": "GBP",
            },
        ]
    )
    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda *args, **kwargs: iter(
            [
                {
                    "_id": ObjectId("62bfebe14dd6c1e0e13d41cc"),
                    "file_datetime": datetime(2022, 5, 4, 0, 0),
                    "balance_total": 7000,
                    "original_balance_total": 10000,
                    "file_date": "2022-05-04",
                    "file_name": "Automated Import of file ********-WLH-Banking-april30.xlsx",
                },
                {
                    "_id": ObjectId("62bfebe04dd6c1e0e13d3f90"),
                    "file_datetime": datetime(2022, 4, 4, 0, 0),
                    "balance_total": 7500,
                    "original_balance_total": 10000,
                    "file_date": "2022-04-04",
                    "file_name": "Automated Import of file ********-WLH-Banking-march31.xlsx",
                },
                {
                    "_id": ObjectId("62bfebdf4dd6c1e0e13d3cde"),
                    "file_datetime": datetime(2022, 2, 3, 0, 0),
                    "balance_total": 6500,
                    "original_balance_total": 10000,
                    "file_date": "2022-02-03",
                    "file_name": "Automated Import of file ********-WLH-Banking-feb28.xlsx",
                },
            ]
        ),
    )
    data = {
        "client_id": client_id,
        "multiplier": 4,
        "date": "2020-10-01",
        "modified_by": "string",
        "credit_multiplier": "2",
        "debit_multiplier": "3",
    }
    patch_db.client_escrow_multiplier.insert_one(data)

    # When
    response = reporting_service.compliance_computation_report_wlh(client_id, "GBP", "2020-10-28", "2020-10-30")

    # Then
    assert response == {
        "amount": 7000,
        "bankingAmount": 1000,
        "claimAmount": 0,
        "closingBalanceCurrent": 7500,
        "closingBalancePrevious": 6500,
        "originalAmount": 10000,
        "header": [
            "Filename: Automated Import of file ********-WLH-Banking-march31.xlsx File Date: 2022-04-04 Prev. MALT: 0.00  GAR PCT: 0.7",
            "Filename: Automated Import of file ********-WLH-Banking-april30.xlsx File Date: 2022-05-04 Curr. MALT: 0.00  GAR PCT: 0.7 %DCC: None %DDD: None",
        ],
        "clientName": "we love",
    }


def test_report_files(flask_client, patch_db, monkeypatch):
    # Given
    monkeypatch.setattr(
        patch_db.report_files,
        "find_one_or_404",
        lambda *args, **kwargs: patch_db.report_files.find_one(*args, **kwargs),
    )
    data = {"name": "TrustBalance", "clientId": "633d61dcedd6fd5f4aa97e11", "currency": "GBP"}
    report_file = patch_db.report_files.insert_one(
        {
            **data,
            "client_id": ObjectId("633d61dcedd6fd5f4aa97e11"),
            "file_id": "test_file_id",
            "file_type": "csv",
            "status": "Generated New Report",
            "generated_at": datetime(2022, 9, 22),
            "created_at": datetime(2022, 9, 22),
            "updated_at": datetime(2022, 9, 22),
        }
    )

    # When
    response = reporting_service.report_files(data)

    # Then
    assert response == {
        "_id": str(report_file.inserted_id),
        "name": "TrustBalance",
        "clientId": data["clientId"],
        "currency": "GBP",
        "fileId": "test_file_id",
        "fileType": "csv",
        "status": "Generated New Report",
        "generatedAt": "2022-09-22T00:00:00",
        "createdAt": "2022-09-22T00:00:00",
        "updatedAt": "2022-09-22T00:00:00",
    }


def test_atol_renewal_tracker_report(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "2146", "friendly_name": "abc"})
    client_id = basic_info.inserted_id
    patch_db.client_atol_info.insert_many(
        [
            {
                "client_id": client_id,
                "start_date": datetime(2022, 5, 22),
                "license": "222",
                "expiry_date": datetime(2022, 12, 22),
            },
            {
                "client_id": client_id,
                "start_date": datetime(2022, 5, 10),
                "license": "2251",
                "expiry_date": datetime(2022, 12, 18),
            },
        ]
    )

    # When
    response = reporting_service.atol_renewal_tracker_list(client_id)

    # Then
    assert response == [
        {
            "clientName": "abc",
            "expiryDate": "2022-12-22",
            "license": "222",
            "startDate": "2022-05-22",
        },
        {
            "clientName": "abc",
            "expiryDate": "2022-12-18",
            "license": "2251",
            "startDate": "2022-05-10",
        },
    ]


def test_insurance_renewal_tracker_list(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1", "friendly_name": "CR7"})
    client_id = basic_info.inserted_id
    patch_db.client_insurance_info.insert_many(
        [
            {
                "client_id": client_id,
                "policy_no": "222",
                "supplier_list_file": {"file_id": "98141fda-fb8f-49ed-9d57-28aa0372257f"},
                "provider": "ABCD",
                "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
                "expiry_date": datetime(2025, 2, 2),
            },
            {
                "client_id": client_id,
                "policy_no": "224",
                "provider": "ABC",
                "files": [
                    {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82d3a"},
                    {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dgh"},
                ],
                "expiry_date": datetime(2025, 2, 2),
            },
        ]
    )

    patch_db.client_files.insert_many(
        [
            {
                "file_id": "98141fda-fb8f-49ed-9d57-28aa0372257f",
                "client_id": client_id,
                "file_name": "PTTAYATA-ClientAPIs-240122-1005.pdf",
                "supplier_list": [
                    {"supplier_name": "supplier1", "cap_amount": 1000},
                    {"supplier_name": "supplier2", "cap_amount": 2000},
                ],
            }
        ]
    )

    # When
    result = reporting_service.insurance_renewal_tracker_list(client_id)
    # Then
    assert result == [
        {
            "clientName": "CR7",
            "policyNumber": "222",
            "provider": "ABCD",
            "expiryDate": "2025-02-02",
            "supplierName": "supplier1",
            "capAmount": 1000,
            "totalMaxCap": 3000,
        },
        {
            "clientName": None,
            "policyNumber": None,
            "provider": None,
            "expiryDate": None,
            "supplierName": "supplier2",
            "capAmount": 2000,
            "totalMaxCap": None,
        },
        {
            "clientName": "CR7",
            "policyNumber": "224",
            "provider": "ABC",
            "expiryDate": "2025-02-02",
            "supplierName": None,
            "capAmount": None,
            "totalMaxCap": None,
        },
    ]


@freeze_time("Nov 25th, 2022")
def test_daily_agent_exceptions_report(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"c_id": "1", "is_disabled": None, "lead_time": 1, "friendly_name": "john"}
    )
    client_id = client_basic_info.inserted_id
    monkeypatch.setattr(
        patch_db.trust_fund_v2,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "metadata": [{"total": 3, "page": 1}],
                "data": [
                    {
                        "_id": ObjectId("62564a18aa1a4bcb7f1aa86c"),
                        "bookingRef": "14000",
                        "departureDate": "2022-11-27",
                        "returnDate": "2022-12-27",
                        "bookingDate": "2019-12-31",
                        "totalBanked": 7000,
                        "totalClaimed": 5000,
                        "refund": 1000,
                        "balance": 1000,
                        "bonding": "ATOL",
                        "leadTime": 2,
                        "paxCount": 2,
                    },
                    {
                        "_id": ObjectId("638036aeaf7161bae150c49a"),
                        "bookingRef": "140000",
                        "departureDate": "2022-11-28",
                        "returnDate": "2022-12-27",
                        "bookingDate": "2019-12-31",
                        "totalBanked": 8000,
                        "totalClaimed": 3000,
                        "refund": 0,
                        "balance": 3000,
                        "bonding": "ATOL",
                        "leadTime": 3,
                    },
                    {
                        "_id": ObjectId("638036d5af7161bae150c49d"),
                        "bookingRef": "140001",
                        "departureDate": "2022-11-29",
                        "returnDate": "2022-12-27",
                        "bookingDate": "2019-12-31",
                        "totalBanked": 7000,
                        "totalClaimed": 0,
                        "refund": 1000,
                        "balance": 6000,
                        "bonding": "ATOL",
                        "leadTime": 4,
                    },
                ],
            }
        ]
        if args[0][-1] != {"$count": "count"}
        else iter([{"count": 3}]),
    )
    data = {"currency": "GBP", "client": client_id, "page": 1, "size": 4}

    # When
    response = reporting_service.daily_agent_exceptions_report(data)

    # Then
    assert response == {
        "content": [
            {
                "_id": "62564a18aa1a4bcb7f1aa86c",
                "bookingRef": "14000",
                "totalBookingValue": None,
                "leadPax": None,
                "numberOfPax": 2,
                "bookingDate": "2019-12-31",
                "dateOfTravel": "2022-11-27",
                "dateOfReturn": "2022-12-27",
                "deposits": 8000,
                "leadTime": 2,
                "totalInTrust": 7000,
                "totalClaimAmount": 5000,
                "totalAgentAmountInTrust": 1000,
                "bondType": "ATOL",
                "clientName": "john",
                "clientId": str(client_id),
            },
            {
                "_id": "638036aeaf7161bae150c49a",
                "bookingRef": "140000",
                "totalBookingValue": None,
                "leadPax": None,
                "numberOfPax": None,
                "bookingDate": "2019-12-31",
                "dateOfTravel": "2022-11-28",
                "dateOfReturn": "2022-12-27",
                "deposits": 8000,
                "leadTime": 3,
                "totalInTrust": 8000,
                "totalClaimAmount": 3000,
                "totalAgentAmountInTrust": 3000,
                "bondType": "ATOL",
                "clientName": "john",
                "clientId": str(client_id),
            },
            {
                "_id": "638036d5af7161bae150c49d",
                "bookingRef": "140001",
                "totalBookingValue": None,
                "leadPax": None,
                "numberOfPax": None,
                "bookingDate": "2019-12-31",
                "dateOfTravel": "2022-11-29",
                "dateOfReturn": "2022-12-27",
                "deposits": 8000,
                "leadTime": 4,
                "totalInTrust": 7000,
                "totalClaimAmount": 0,
                "totalAgentAmountInTrust": 6000,
                "bondType": "ATOL",
                "clientName": "john",
                "clientId": str(client_id),
            },
        ],
        "empty": False,
        "first": True,
        "last": True,
        "numberOfElements": 3,
        "pageNumber": 1,
        "totalElements": 3,
        "totalPages": 1,
    }


@freeze_time("Nov 25th, 2022")
def test_daily_agent_exceptions_report_409_exception(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one({"c_id": "1", "is_disabled": None})
    client_id = client_basic_info.inserted_id
    data = {"currency": "GBP", "client": client_id, "page": 1, "size": 4}

    # Then
    assert_that(
        calling(reporting_service.daily_agent_exceptions_report).with_args(data),
        raises(werkzeug.exceptions.HTTPException),
    )


@patch("flaskr.services.reporting_service.download_file", MagicMock(return_value="a file"))
@patch("flaskr.services.reporting_service.upload_file", MagicMock(return_value=None))
def test_tbr_sftp_upload(flask_client, patch_db):
    # Given
    data = {"type": "xlsx", "client": "62ce5983bff9a1862ca13424", "fileId": "123456"}
    patch_db.client_basic_info.insert_one(
        {"_id": ObjectId("62ce5983bff9a1862ca13424"), "sftp_location_reports": "/abc/", "full_name": "abc"}
    )

    # When
    result = reporting_service.tbr_sftp_upload(data)

    # Then
    assert result is None


@patch("flaskr.services.reporting_service.download_file", MagicMock(return_value="a file"))
@patch("flaskr.services.reporting_service.upload_file", MagicMock(return_value=None))
@pytest.mark.parametrize(
    "basic_info",
    [
        {"_id": ObjectId("62ce5983bff9a1862ca13424"), "sftp_location_reports": "", "full_name": "abc"},
        {"_id": ObjectId("62ce5983bff9a1862ca13424"), "full_name": "abc"},
        {"_id": ObjectId("62ce5983bff9a1862ca13424"), "sftp_location_reports": None, "full_name": "abc"},
    ],
)
def test_tbr_sftp_upload_exception(flask_client, basic_info, patch_db):
    # Given
    data = {"type": "xlsx", "client": "62ce5983bff9a1862ca13424", "fileId": "123456"}
    patch_db.client_basic_info.insert_one(basic_info)

    # Then
    assert_that(
        calling(reporting_service.tbr_sftp_upload).with_args(data),
        raises(werkzeug.exceptions.HTTPException),
    )
