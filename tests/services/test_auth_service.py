from hamcrest import assert_that, calling, has_items, raises
from moto import mock_cognitoidp
from mongomock import ObjectId
from flaskr.services.auth_service import AuthService
from flaskr.services.exceptions import ServiceException
from unittest.mock import MagicMock, patch


@mock_cognitoidp
def cognito_setup(flask_app, cognito_client):
    client_name = "app-client"
    user_pool_id = cognito_client.create_user_pool(PoolName="ptt-user-pool-test")["UserPool"]["Id"]
    user_pool_client_id = cognito_client.create_user_pool_client(
        UserPoolId=user_pool_id,
        ClientName=client_name,
        GenerateSecret=True,
        ExplicitAuthFlows=["ADMIN_NO_SRP_AUTH"],
    )["UserPoolClient"]["ClientId"]

    flask_app.config["USER_POOL_ID"], flask_app.config["APP_CLIENT_ID"] = (
        user_pool_id,
        user_pool_client_id,
    )

    group_name = "ptt-admin"
    cognito_client.create_group(GroupName=group_name, UserPoolId=user_pool_id)

    ptt_user_group_name = "ptt-user"
    cognito_client.create_group(GroupName=ptt_user_group_name, UserPoolId=user_pool_id)

    client_group_name = "ptt-client"
    cognito_client.create_group(GroupName=client_group_name, UserPoolId=user_pool_id)

    username = "test_user"
    temporary_password = "test_password"
    name = "Test Name"

    cognito_client.admin_create_user(
        UserPoolId=user_pool_id,
        Username=username,
        TemporaryPassword=temporary_password,
        UserAttributes=[{"Name": "name", "Value": name}],
    )

    cognito_client.admin_add_user_to_group(UserPoolId=user_pool_id, Username=username, GroupName=group_name)

    result = cognito_client.admin_initiate_auth(
        UserPoolId=user_pool_id,
        ClientId=user_pool_client_id,
        AuthFlow="ADMIN_NO_SRP_AUTH",
        AuthParameters={"USERNAME": username, "PASSWORD": temporary_password},
    )
    client_username = "client_test_user"

    cognito_client.admin_create_user(
        UserPoolId=user_pool_id,
        Username=client_username,
        TemporaryPassword=temporary_password,
        UserAttributes=[{"Name": "name", "Value": name}],
    )
    cognito_client.admin_add_user_to_group(
        UserPoolId=user_pool_id, Username=client_username, GroupName=client_group_name
    )

    # This sets a new password and logs the user in (creates tokens)
    new_password = "password"
    result = cognito_client.respond_to_auth_challenge(
        Session=result["Session"],
        ClientId=user_pool_client_id,
        ChallengeName="NEW_PASSWORD_REQUIRED",
        ChallengeResponses={"USERNAME": username, "NEW_PASSWORD": new_password},
    )
    return result["AuthenticationResult"]["AccessToken"]


@mock_cognitoidp
def test_create_client_new_user(flask_app, cognito_client, patch_db):
    # Given
    cognito_setup(flask_app, cognito_client)
    auth_service = AuthService()

    # When
    response = auth_service.create_client(
        None, "test", "<EMAIL>", ObjectId("62160dcda883a1dc69096743"), "Name"
    )

    # Then
    user_attributes = response["User"]["Attributes"]
    user_id = next(filter(lambda x: x["Name"] == "sub", user_attributes))["Value"]
    user = patch_db.user.find_one({"user_id": user_id})
    assert response["User"]["Username"] == "test"
    assert_that(
        user_attributes,
        has_items(
            {"Name": "email", "Value": "<EMAIL>"},
            {"Name": "email_verified", "Value": "true"},
            {"Name": "name", "Value": "Name"},
            {"Name": "picture", "Value": "profile"},
        ),
    )
    assert {
        "user_id": user_id,
        "role": "ptt-client",
        "confirmation_status": "Force change password",
        "clients": [ObjectId("62160dcda883a1dc69096743")],
    }.items() <= user.items()


@mock_cognitoidp
def test_create_client_new_user_failure(flask_app, cognito_client, patch_db):
    # Given
    cognito_setup(flask_app, cognito_client)
    auth_service = AuthService()
    # when
    auth_service.create_client(None, "test", "<EMAIL>", ObjectId("62160dcda883a1dc69096743"), "Name")

    # Then
    assert_that(
        calling(auth_service.create_client).with_args(None, "test", "<EMAIL>", "1", "Name"),
        raises(ServiceException),
    )


@mock_cognitoidp
def test_create_client_existing_user(flask_app, cognito_client, patch_db):
    # Given
    user_id = "79901007-4d95-4f3f-9144-68e59e090d0b"
    patch_db.user.insert_one(
        {
            "user_id": user_id,
            "role": "ptt-client",
            "confirmation_status": "Force change password",
            "clients": [ObjectId("62160dcda883a1dc69096743")],
        }
    )
    cognito_setup(flask_app, cognito_client)
    auth_service = AuthService()

    # When
    response = auth_service.create_client(user_id, None, None, ObjectId("62160dcda883a1dc69096744"), None)

    # Then
    user = patch_db.user.find_one({"user_id": user_id})
    assert user["clients"] == [ObjectId("62160dcda883a1dc69096743"), ObjectId("62160dcda883a1dc69096744")]
    assert response == {"msg": "Client added for existing user successfully"}


@mock_cognitoidp
def test_create_client_existing_user_failure(flask_app, cognito_client, patch_db):
    # Given
    user_id = "79901007-4d95-4f3f-9144-68e59e090d0b"
    cognito_setup(flask_app, cognito_client)
    auth_service = AuthService()

    # Then
    assert_that(
        calling(auth_service.create_client).with_args(user_id, None, None, ObjectId("62160dcda883a1dc69096744"), None),
        raises(ServiceException),
    )


@mock_cognitoidp
def test_list_user_groups(flask_app, cognito_client, patch_db):
    # Given
    cognito_setup(flask_app, cognito_client)
    auth_service = AuthService()
    # When
    result = auth_service._AuthService__list_user_groups("test_user")
    # Then
    assert result["Groups"][0]["GroupName"] == "ptt-admin"


@mock_cognitoidp
def test_list_users_in_groups(flask_app, cognito_client, patch_db):
    # Given
    cognito_setup(flask_app, cognito_client)
    auth_service = AuthService()
    # When
    result = auth_service.list_users_in_groups("ptt-admin")
    # Then
    assert len(result["Users"]) != 0


@mock_cognitoidp
def test_list_clients(flask_app, cognito_client, patch_db):
    # Given
    cognito_setup(flask_app, cognito_client)
    auth_service = AuthService()
    # When
    result = auth_service.list_clients()
    # Then
    assert len(result) != 0


@mock_cognitoidp
def test_login(flask_app, cognito_client, patch_db):
    # Given
    cognito_setup(flask_app, cognito_client)
    auth_service = AuthService()
    # When
    result = auth_service.login("test_user", "password")
    # Then
    assert result["authenticationResult"]["AccessToken"] is not None
    assert len(result["userGroups"][0]) != 1


@mock_cognitoidp
def test_confirm_forgot_password(flask_app, cognito_client, patch_db):
    # Given
    cognito_setup(flask_app, cognito_client)
    auth_service = AuthService()

    # When
    result = auth_service.confirm_forgot_password("test_user", "Test#1234", "45673")
    assert result["ResponseMetadata"]["HTTPStatusCode"] == 200


@mock_cognitoidp
def test_forgot_password(flask_app, cognito_client, patch_db):
    # Given
    cognito_setup(flask_app, cognito_client)
    auth_service = AuthService()

    # When
    result = auth_service.forgot_password("test_user")
    assert result["ResponseMetadata"]["HTTPStatusCode"] == 200


def test_get_user_details(flask_client, patch_db):
    # Given
    user_id = "79901007-4d95-4f3f-9144-68e59e090d0b"
    patch_db.user.insert_one(
        {
            "_id": ObjectId("63ce4c6ab1296d09a5fd9a94"),
            "user_id": user_id,
            "confirmation_status": "Confirmed",
            "role": "ptt-user",
            "clients": [ObjectId("623c1a14c1886d1dd0e6f186"), ObjectId("62fb53f02ce2345143250f96")],
            "reports": ["movement of funds", "trust balance report"],
            "booking": True,
            "transaction": False,
        }
    )

    auth_service = AuthService()
    # When
    result = auth_service.get_user_details(user_id)

    # Then
    assert result == {
        "_id": "63ce4c6ab1296d09a5fd9a94",
        "clients": ["623c1a14c1886d1dd0e6f186", "62fb53f02ce2345143250f96"],
        "confirmationStatus": "Confirmed",
        "role": "ptt-user",
        "userId": "79901007-4d95-4f3f-9144-68e59e090d0b",
        "reports": ["movement of funds", "trust balance report"],
        "booking": True,
        "transaction": False,
    }


@patch.object(
    AuthService,
    "get_list_clients",
    MagicMock(
        return_value=[
            {
                "userId": "1",
                "email": "<EMAIL>",
                "status": "FORCE_CHANGE_PASSWORD",
                "name": "sam",
                "username": "sam01",
            }
        ]
    ),
)
def test_list_user_details(flask_client, patch_db):
    # Given
    patch_db.user.insert_many([
        {
            "user_id": "1",
            "confirmation_status": "Confirmed",
            "role": "ptt-client",
            "clients": [ObjectId("623c1a14c1886d1dd0e6f186"), ObjectId("62fb53f02ce2345143250f96")],
            "reports": ["movement of funds", "trust balance report"],
            "booking": True,
            "transaction": False,
        },
        {
            "user_id": "2",
            "confirmation_status": "Confirmed",
            "role": "ptt-admin",
            "clients": [ObjectId("623c1a14c1886d1dd0e6f186"), ObjectId("62fb53f02ce2345143250f96")],
        }]
    )

    auth_service = AuthService()
    # When
    result = auth_service.list_user_details()

    # Then
    assert result == [
        {
            "userId": "1",
            "confirmationStatus": "Confirmed",
            "role": "ptt-client",
            "clients": ["623c1a14c1886d1dd0e6f186", "62fb53f02ce2345143250f96"],
            "reports": ["movement of funds", "trust balance report"],
            "booking": True,
            "transaction": False,
            "userDetails": {
                "email": "<EMAIL>",
                "name": "sam",
                "status": "FORCE_CHANGE_PASSWORD",
                "userId": "1",
                "username": "sam01",
            },
        }
    ]


def test_update_user_details(flask_client, patch_db):
    # Given
    patch_db.user.insert_one(
        {
            "user_id": "1",
            "confirmation_status": "",
            "role": "",
            "clients": [ObjectId("623c1a14c1886d1dd0e6f186"), ObjectId("62fb53f02ce2345143250f96")],
            "reports": ["weekly fortnitly"],
            "booking": True,
            "transaction": True,
        }
    )

    data = {
        "confirmationStatus": "",
        "role": "",
        "booking": False,
        "transaction": False,
        "reports": ["weekly fortnitly", "trust balance"],
    }

    # When
    auth_service = AuthService()
    auth_service.update_user_details(data, "1")

    # Then
    user_details = patch_db.user.find_one({"user_id": "1"})
    assert user_details["booking"] == data["booking"]
    assert user_details["transaction"] == data["transaction"]
    assert user_details["reports"] == ["weekly fortnitly", "trust balance"]
