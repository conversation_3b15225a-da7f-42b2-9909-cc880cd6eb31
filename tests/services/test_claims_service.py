import re
from flask import current_app
from marshmallow import ValidationError
from hamcrest import assert_that, calling, raises
from mongomock import ObjectId
import pytest
from flaskr.models.claims.metadata import ClaimsMetadataSchema
from flaskr.services.auth_service import AuthService
from flaskr.services.exceptions import ServiceException
from datetime import datetime
from flaskr.services.claims_service import ClaimService, claim_service
from freezegun import freeze_time
from unittest.mock import MagicMock, Mock, patch
import werkzeug.exceptions


@pytest.mark.parametrize(
    "input_data, output_data",
    [
        (
            [
                "Performance",
                "Balance",
                "Cruise",
                "ASPP",
                "claim",
                "Deposit",
                "flights",
            ],
            "ASPP, Balance, claim, Cruise, Deposit, flights, Performance",
        ),
        (
            [
                "AsPp",
                "Cruise",
                "Flight",
                "Insurance",
                "Performance",
                "commission",
            ],
            "AsPp, commission, Cruise, Flight, Insurance, Performance",
        ),
    ],
)
def test_claims_search_success(flask_client, patch_db, monkeypatch, input_data, output_data):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = client_basic_info.inserted_id
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "metadata": [{"total": 1, "page": 1}],
                "data": [
                    {
                        "_id": ObjectId("62aafc5336feb4b25b99f9f5"),
                        "clientId": client_id,
                        "cId": "123",
                        "claims": {"_id": None, "items": [{"amount": 0, "count": 2}], "elementList": input_data},
                        "clientName": "Abhiram",
                        "friendlyName": "abhi",
                        "submittedDate": [datetime(2022, 4, 13, 4, 20, 51, 224000)],
                        "fileDate": [datetime(2022, 4, 13, 4, 20, 51, 224000)],
                        "status": "In Progress",
                        "frequency": "Daily",
                        "count": [{"GBP": 17, "USD": 5, "INR": 1}],
                        "amount": [{"GBP": 17000.0, "USD": 5000, "INR": 2000}],
                        "notes": "In Progress",
                        "trust": "ATOL Escrow",
                    }
                ],
            }
        ],
    )
    patch_db.lookup_currency.insert_many(
        [{"code": "GBP", "symbol": "£"}, {"code": "USD", "symbol": "$"}, {"code": "INR", "symbol": "₹"}]
    )
    data = {"query": "", "client": client_id, "assignedTo": "", "status": "", "date": "", "page": 1, "size": 4}
    # When

    response = claim_service.claim_search_summary("123", data)

    # Then
    assert response == {
        "content": [
            {
                "_id": "62aafc5336feb4b25b99f9f5",
                "clientId": str(client_id),
                "cId": "123",
                "clientName": "Abhiram",
                "friendlyName": "abhi",
                "element": output_data,
                "fileDate": datetime(2022, 4, 13, 4, 20, 51, 224000),
                "frequency": "Daily",
                "items": [
                    {"amount": 17000.0, "count": 17, "currency": "GBP", "symbol": "£"},
                    {"amount": 5000, "count": 5, "currency": "USD", "symbol": "$"},
                    {"amount": 2000, "count": 1, "currency": "INR", "symbol": "₹"},
                ],
                "notes": "In Progress",
                "status": "In Progress",
                "submittedDate": "2022-04-13T04:20:51.224000",
                "trustAccount": "ATOL Escrow",
            }
        ],
        "empty": False,
        "first": True,
        "last": True,
        "numberOfElements": 1,
        "pageNumber": 1,
        "totalElements": 1,
        "totalPages": 1,
    }


def test_claims_search1_latest_success(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = client_basic_info.inserted_id
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "metadata": [{"total": 1, "page": 1}],
                "data": [
                    {
                        "_id": ObjectId("62aafc5336feb4b25b99f9f5"),
                        "clientId": client_id,
                        "clientName": "Abhiram",
                        "friendlyName": "abhi",
                        "submittedDate": [datetime(2022, 4, 13, 4, 20, 51, 224000)],
                        "claimDate": ["2021-04-10"],
                        "assignedTo": "6204d9d61eed04813999f227",
                        "status": "Submitted",
                        "count": [{"GBP": 17, "USD": 5, "INR": 1}],
                        "amount": [{"GBP": 17000.0, "USD": 5000, "INR": 2000}],
                        "checks": [{"GBP": 2, "USD": 3, "INR": 1}],
                        "checkedAmount": [{"GBP": 10000, "USD": 4000, "INR": 1000}],
                        "notes": "Submitted",
                        "anomalyCount": 0,
                        "trust": "ATOL Escrow",
                    }
                ],
            }
        ],
    )
    patch_db.lookup_currency.insert_many(
        [{"code": "GBP", "symbol": "£"}, {"code": "USD", "symbol": "$"}, {"code": "INR", "symbol": "₹"}]
    )
    data = {
        "query": "",
        "client": client_id,
        "assignedTo": "6204d9d61eed04813999f227",
        "status": "",
        "date": "",
        "page": 1,
        "size": 4,
    }
    # When

    response = claim_service.claim_search_latest.__wrapped__(claim_service, "123", data)

    # Then
    assert response == {
        "content": [
            {
                "_id": "62aafc5336feb4b25b99f9f5",
                "anomalies": 0,
                "assignedTo": "6204d9d61eed04813999f227",
                "claimDate": "2021-04-10",
                "clientId": client_id,
                "clientName": "Abhiram",
                "friendlyName": "abhi",
                "items": [
                    {
                        "amount": 17000.0,
                        "checkedAmount": 10000,
                        "checks": 2,
                        "count": 17,
                        "currency": "GBP",
                        "percentageCheck": 11.764705882352942,
                        "percentageTotal": 58.8235294117647,
                        "symbol": "£",
                    },
                    {
                        "amount": 5000,
                        "checkedAmount": 4000,
                        "checks": 3,
                        "count": 5,
                        "currency": "USD",
                        "percentageCheck": 60.0,
                        "percentageTotal": 80.0,
                        "symbol": "$",
                    },
                    {
                        "amount": 2000,
                        "checkedAmount": 1000,
                        "checks": 1,
                        "count": 1,
                        "currency": "INR",
                        "percentageCheck": 100.0,
                        "percentageTotal": 50.0,
                        "symbol": "₹",
                    },
                ],
                "notes": "Submitted",
                "status": "Submitted",
                "submittedDate": "2022-04-13T04:20:51.224000",
                "trustAccount": "ATOL Escrow",
            }
        ],
        "empty": False,
        "first": True,
        "last": True,
        "numberOfElements": 1,
        "pageNumber": 1,
        "totalElements": 1,
        "totalPages": 1,
    }


@patch(
    "flaskr.services.claims_service.generate_presigned_url",
    MagicMock(return_value="https://test-url"),
)
@freeze_time("May 5th 2022")
@pytest.mark.parametrize(
    "file_name",
    [
        "********-TFN-Claims.xlsx",
        "********-TFN-Claims.xls",
        "********-TFN-Claims.csv",
    ],
)
def test_claim_create_presigned_url(flask_client, patch_db, file_name):
    # Given
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "is_disabled": None, "claim_from_tbr": False, "company_alias": "TFN"}
    )
    client_id = basic_info.inserted_id
    user_id = "1"

    # When
    response = claim_service.claim_create_presigned_url(str(client_id), file_name, False, user_id)
    # Then
    assert "fileId" in response
    assert response["presignedUrl"] == "https://test-url"


# def test_claim_create_presigned_url_invalid_file_date(flask_client, patch_db):
#     # Given
#     basic_info = patch_db.client_basic_info.insert_one(
#         {"client_id": "1", "is_disabled": None, "claim_from_tbr": False, "company_alias": "TFN"}
#     )
#     client_id = basic_info.inserted_id
#     user_id = "1"
#     # When
#     assert_that(
#         calling(claim_service.claim_create_presigned_url).with_args(
#             str(client_id), "20210501-TFN-Claims.xlsx", False, user_id
#         ),
#         raises(
#             ServiceException,
#             re.escape(f"File date cannot be older than {current_app.config['FILE_DATE_AGE_IN_MONTHS']} month(s)."),
#         ),
#     )


@pytest.mark.parametrize(
    "invalid_file_name",
    [
        "20210501Document1Claim.xlsx",
        "20210501Document1-Claim.xlsx",
    ],
)
def test_claim_create_presigned_url_invalid_file_name(flask_client, patch_db, invalid_file_name):
    # Given
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "is_disabled": None, "claim_from_tbr": False, "company_alias": "TFN"}
    )
    client_id = basic_info.inserted_id
    user_id = "1"
    # When
    assert_that(
        calling(claim_service.claim_create_presigned_url).with_args(str(client_id), invalid_file_name, False, user_id),
        raises(
            ServiceException,
            re.escape("Invalid file name"),
        ),
    )


def test_claim_create_presigned_url_invalid_file_date_format(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "is_disabled": None, "claim_from_tbr": False, "company_alias": "TFN"}
    )
    client_id = basic_info.inserted_id
    user_id = "1"
    # When
    assert_that(
        calling(claim_service.claim_create_presigned_url).with_args(
            str(client_id), "20211313-TFN-Claims.xlsx", False, user_id
        ),
        raises(
            ServiceException,
            re.escape("Invalid file name, failed to extract file date."),
        ),
    )


# def test_claim_create_presigned_url_invalid_client(flask_client, patch_db):
#     # Given
#     invalid_client_id = "12257912c51e741b53957643"
#     user_id = "1"
#     # When
#     assert_that(
#         calling(claim_service.claim_create_presigned_url).with_args(
#             invalid_client_id, "20220510-TFN-Claims.xls", False, user_id
#         ),
#         raises(ServiceException, "client not found"),
#     )


def test_claim_create_presigned_url_anomaly_not_selected(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "is_disabled": None, "claim_from_tbr": True, "company_alias": "TFN"}
    )
    client_id = basic_info.inserted_id
    patch_db.lookup_anomaly.insert_one({"name": "Claim Too Early For Departure Date"})
    # When
    assert_that(
        calling(claim_service.claim_create_presigned_url).with_args(
            str(client_id), "Trust-balance-report_30082022.xlsx", True, "1"
        ),
        raises(werkzeug.exceptions.HTTPException),
    )


@patch("flaskr.services.claims_service.call_lambda", MagicMock(return_value=None))
@freeze_time("May 5th 2022")
@pytest.mark.parametrize(
    "file_name,content_type",
    [
        ("********-TFN-Claims.xlsx", "application/vnd.ms-excel"),
        ("********-TFN-Claims", "application/vnd.ms-excel"),
        ("********-TFN-Claims.csv", "text/csv"),
    ],
)
def test_claim_create(flask_client, patch_db, file_name, content_type):
    # Given
    file_id = "test-id"
    sftp = False
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "is_disabled": None, "claim_from_tbr": False, "company_alias": "TFN"}
    )
    client_id = client_basic_info.inserted_id
    user_id = "1"

    # When
    with patch("flaskr.services.claims_service.head_object", MagicMock(return_value={"ContentType": content_type})):
        claim_service.claim_create(str(client_id), file_name, file_id, False, sftp, user_id, "")

    # Then
    claims_metadata = patch_db.claims_metadata.find_one({"client_id": client_id})
    assert claims_metadata["claim_files"][0]["file_name"] == file_name


@patch("flaskr.services.claims_service.call_lambda", MagicMock(return_value=None))
@freeze_time("May 5th 2022")
def test_claim_create_claim_from_tbr_true(flask_client, patch_db):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "is_disabled": None, "claim_from_tbr": True, "company_alias": "TFN"}
    )
    client_id = client_basic_info.inserted_id
    user_id = "1"

    # When
    with patch(
        "flaskr.services.claims_service.head_object",
        MagicMock(return_value={"ContentType": "application/vnd.ms-excel"}),
    ):
        claim_service.claim_create(
            str(client_id), "Trust-balance-report_30082022.xlsx", "test-id", True, False, user_id, ""
        )

    # Then
    claims_metadata = patch_db.claims_metadata.find_one({"client_id": client_id})
    assert claims_metadata["claim_files"][0]["file_name"] == "20220505-Trust-balance-report_30082022.xlsx"


@patch("flaskr.services.claims_service.call_lambda", MagicMock(return_value=None))
@freeze_time("May 5th 2022")
def test_claim_create_claim_from_tbr_conflict(flask_client, patch_db):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "is_disabled": None, "claim_from_tbr": False, "company_alias": "TFN"}
    )
    client_id = client_basic_info.inserted_id
    user_id = "1"

    # Then
    with patch(
        "flaskr.services.claims_service.head_object",
        MagicMock(return_value={"ContentType": "application/vnd.ms-excel"}),
    ):
        assert_that(
            calling(claim_service.claim_create).with_args(
                str(client_id), "Trust-balance-report_30082022.xlsx", "test-id", True, False, user_id, ""
            ),
            raises(werkzeug.exceptions.HTTPException),
        )


@patch("flaskr.services.claims_service.call_lambda", MagicMock(return_value=None))
@freeze_time("May 5th 2022")
def test_claim_create_claim_file_with_date(flask_client, patch_db):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "is_disabled": None, "claim_from_tbr": True, "company_alias": "TFN"}
    )
    client_id = client_basic_info.inserted_id
    user_id = "1"

    # When
    with patch(
        "flaskr.services.claims_service.head_object",
        MagicMock(return_value={"ContentType": "application/vnd.ms-excel"}),
    ):
        claim_service.claim_create(
            str(client_id), "20220505-Trust-balance-report_30082022.xlsx", "test-id", True, False, user_id, ""
        )

    # Then
    claims_metadata = patch_db.claims_metadata.find_one({"client_id": client_id})
    assert claims_metadata["claim_files"][0]["file_name"] == "20220505-Trust-balance-report_30082022.xlsx"


@freeze_time("May 5th 2022")
def test_claim_create_invalid_file_date(flask_client, patch_db):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "is_disabled": None, "claim_from_tbr": False, "company_alias": "TFN"}
    )
    client_id = client_basic_info.inserted_id
    user_id = "1"
    # When
    assert_that(
        calling(claim_service.claim_create).with_args(
            str(client_id), "20210501-TFN-Claims.xlsx", "test-id", False, False, user_id, ""
        ),
        raises(
            ServiceException,
            re.escape(f"File date cannot be older than {current_app.config['FILE_DATE_AGE_IN_MONTHS']} month(s)."),
        ),
    )


@freeze_time("May 5th 2022")
@patch("flaskr.services.claims_service.delete_object", MagicMock())
@patch("flaskr.services.claims_service.head_object", MagicMock(return_value={"ContentType": "application/pdf"}))
def test_claim_create_invalid(flask_client, patch_db):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "is_disabled": None, "claim_from_tbr": False, "company_alias": "TFN"}
    )
    client_id = client_basic_info.inserted_id
    user_id = "1"
    # When
    assert_that(
        calling(claim_service.claim_create).with_args(
            str(client_id), "20220505-TFN-Claims.pdf", "test-id", False, False, user_id, ""
        ),
        raises(ServiceException, "File format not supported, use xls, xlsx or csv files"),
    )


@pytest.mark.parametrize(
    "invalid_file_name",
    [
        "20210501Document1Claim.xlsx",
        "20210501Document1-Claim.xlsx",
    ],
)
def test_claim_create_invalid_file_name(flask_client, patch_db, invalid_file_name):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "is_disabled": None, "claim_from_tbr": False, "company_alias": "TFN"}
    )
    client_id = client_basic_info.inserted_id
    user_id = "1"
    # When
    assert_that(
        calling(claim_service.claim_create).with_args(
            str(client_id), invalid_file_name, "test-id", False, False, user_id, ""
        ),
        raises(
            ServiceException,
            re.escape("Invalid file name"),
        ),
    )


def test_claim_create_invalid_client(flask_client, patch_db):
    # Given
    invalid_client_id = "12257912c51e741b53957643"
    user_id = "1"
    # When
    assert_that(
        calling(claim_service.claim_create).with_args(
            invalid_client_id, "20220510-Document1.xls", "test-id", False, False, user_id, ""
        ),
        raises(ServiceException, "client not found"),
    )


@pytest.mark.parametrize(
    "input_claim_files, output_claim_files",
    [
        (
            [
                {
                    "fileId": "111258412241665569",
                    "fileName": "string",
                    "status": "string",
                    "items": {
                        "GBP": 18,
                        "INR": 1,
                        "USD": 5,
                    },
                    "claimTotal": {"GBP": 2500.0, "INR": 7000.0, "USD": 3500.0},
                    "submittedDate": "2021-08-10T18:30:00.000+00:00",
                    "notes": "string",
                }
            ],
            [
                {
                    "submittedDate": "2021-08-10T18:30:00",
                    "fileId": "111258412241665569",
                    "fileName": "string",
                    "status": "string",
                    "items": {"GBP": 18, "USD": 5, "INR": 1},
                    "claimTotal": {"GBP": 2500.0, "USD": 3500.0, "INR": 7000.0},
                    "notes": "string",
                }
            ],
        ),
        (
            [
                {
                    "fileId": "111258412241665569",
                    "fileName": "string",
                    "status": "string",
                    "submittedDate": "2021-08-10T18:30:00.000+00:00",
                    "notes": "string",
                }
            ],
            [
                {
                    "submittedDate": "2021-08-10T18:30:00",
                    "fileId": "111258412241665569",
                    "fileName": "string",
                    "status": "string",
                    "notes": "string",
                }
            ],
        ),
    ],
)
def test_claim_details(flask_client, patch_db, input_claim_files, output_claim_files):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {
            "c_id": "1",
            "full_name": "ABC",
            "friendly_name": "edstem",
        }
    )
    client_id = client_basic_info.inserted_id
    client_details = ClaimsMetadataSchema().load(
        {
            "clientId": client_id,
            "clientName": "ABC",
            "createdAt": "2020-05-19T18:30:00.000+00:00",
            "notes": "string",
            "status": "string",
            "updatedAt": "2020-08-24T18:30:00.000+00:00",
            "claimFiles": input_claim_files,
        }
    )
    meta_data = patch_db.claims_metadata.insert_one(client_details)

    # When
    result = claim_service.get_claim_details.__wrapped__(claim_service, meta_data.inserted_id)

    # Then
    assert result == {
        "_id": str(meta_data.inserted_id),
        "clientId": str(client_id),
        "clientName": "ABC",
        "friendlyName": "edstem",
        "createdAt": "2020-05-19T18:30:00",
        "notes": "string",
        "status": "string",
        "updatedAt": "2020-08-24T18:30:00",
        "claimFiles": output_claim_files,
    }


def test_claim_id_validation_error(flask_client):
    # Given
    data = "123"

    # Then
    assert_that(
        calling(claim_service.get_claim_details.__wrapped__).with_args(claim_service, data), raises(ServiceException)
    )


@pytest.mark.parametrize(
    "input,sort_key,output",
    [
        (
            [],
            "",
            {
                "content": [],
                "pageNumber": 1,
                "numberOfElements": 0,
                "totalElements": 0,
                "totalPages": 0,
            },
        ),
        (
            [
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e50"),
                    "claims_id": ObjectId("520257e8697b3b6f30f37e50"),
                    "file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60",
                    "booking_ref": "456",
                    "booking_date": "2022-07-12",
                    "amount": 9000.0,
                    "currency_code": "euro",
                    "deleted": False,
                    "client_id": ObjectId("12257912c51e741b53957643"),
                    "status": "submitted",
                    "element": "Deposit",
                },
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e51"),
                    "claims_id": ObjectId("520257e8697b3b6f30f37e50"),
                    "file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60",
                    "booking_ref": "123",
                    "booking_date": "2022-07-12",
                    "amount": 7000.0,
                    "currency_code": "euro",
                    "deleted": False,
                    "client_id": ObjectId("12257912c51e741b53957643"),
                    "status": "submitted",
                    "element": "Claim",
                },
            ],
            "booking_ref",
            {
                "content": [
                    {
                        "status": "submitted",
                        "currencyCode": "euro",
                        "clientId": "1",
                        "amount": 7000.0,
                        "_id": "620257e8697b3b6f30f37e51",
                        "bookingRef": "123",
                        "count": 1,
                        "duplicates": 1,
                        "element": "Claim",
                    },
                    {
                        "status": "submitted",
                        "currencyCode": "euro",
                        "clientId": "1",
                        "amount": 9000.0,
                        "_id": "620257e8697b3b6f30f37e50",
                        "bookingRef": "456",
                        "count": 1,
                        "duplicates": 1,
                        "element": "Deposit",
                    },
                ],
                "pageNumber": 1,
                "numberOfElements": 2,
                "totalElements": 2,
                "totalPages": 1,
            },
        ),
        (
            [
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e50"),
                    "claims_id": ObjectId("520257e8697b3b6f30f37e50"),
                    "file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60",
                    "booking_ref": "456",
                    "booking_date": "2022-07-12",
                    "amount": 9000.0,
                    "currency_code": "GBP",
                    "deleted": False,
                    "client_id": ObjectId("12257912c51e741b53957643"),
                    "status": "submitted",
                    "element": "Deposit",
                },
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e51"),
                    "claims_id": ObjectId("520257e8697b3b6f30f37e50"),
                    "file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60",
                    "booking_ref": "123",
                    "booking_date": "2022-07-12",
                    "amount": 7000.0,
                    "currency_code": "euro",
                    "deleted": False,
                    "client_id": ObjectId("12257912c51e741b53957643"),
                    "status": "submitted",
                    "element": "Claim",
                },
            ],
            "amount",
            {
                "content": [
                    {
                        "status": "submitted",
                        "currencyCode": "euro",
                        "clientId": "1",
                        "amount": 7000.0,
                        "_id": "620257e8697b3b6f30f37e51",
                        "bookingRef": "123",
                        "count": 1,
                        "duplicates": 1,
                        "element": "Claim",
                    },
                    {
                        "status": "submitted",
                        "currencyCode": "GBP",
                        "clientId": "1",
                        "amount": 9000.0,
                        "_id": "620257e8697b3b6f30f37e50",
                        "bookingRef": "456",
                        "count": 1,
                        "duplicates": 1,
                        "element": "Deposit",
                    },
                ],
                "pageNumber": 1,
                "numberOfElements": 2,
                "totalElements": 2,
                "totalPages": 1,
            },
        ),
        (
            [
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e50"),
                    "claims_id": ObjectId("520257e8697b3b6f30f37e50"),
                    "file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60",
                    "booking_ref": "456",
                    "booking_date": "2022-07-12",
                    "amount": 9000.0,
                    "currency_code": "GBP",
                    "deleted": False,
                    "client_id": ObjectId("12257912c51e741b53957643"),
                    "status": "submitted",
                    "element": "Deposit",
                },
                {
                    "_id": ObjectId("620257e8697b3b6f30f37e51"),
                    "claims_id": ObjectId("520257e8697b3b6f30f37e50"),
                    "file_id": "38190854-2ee3-416e-9874-35953f3a7a6d",
                    "booking_ref": "123",
                    "booking_date": "2022-07-12",
                    "amount": 7000.0,
                    "currency_code": "euro",
                    "deleted": False,
                    "client_id": ObjectId("12257912c51e741b53957643"),
                    "status": "submitted",
                    "element": "Claim",
                },
            ],
            "element",
            {
                "content": [
                    {
                        "status": "submitted",
                        "currencyCode": "GBP",
                        "clientId": "1",
                        "amount": 9000.0,
                        "_id": "620257e8697b3b6f30f37e50",
                        "bookingRef": "456",
                        "count": 1,
                        "duplicates": 1,
                        "element": "Deposit",
                    },
                ],
                "pageNumber": 1,
                "numberOfElements": 1,
                "totalElements": 1,
                "totalPages": 1,
            },
        ),
    ],
)
def test_claim_transaction(patch_db, input, sort_key, output):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"c_id": "1", "_id": ObjectId("12257912c51e741b53957643")}
    )
    client_id = client_basic_info.inserted_id
    query = ()
    patch_db.claims_metadata.insert_one(
        {
            "_id": ObjectId("520257e8697b3b6f30f37e50"),
            "client_id": client_id,
            "claim_files": [
                {"file_id": "38190854-2ee3-416e-9874-35953f3a7a6d"},
                {"file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60"},
            ],
        }
    )
    if input:
        patch_db.claims_file_details.insert_many(input)

    # When
    result = claim_service.get_claim_transaction.__wrapped__(
        claim_service, ("520257e8697b3b6f30f37e50"), query, "1", "10", sort_key, 1
    )

    # Then
    assert result == output


def test_claim_transaction_id_validation_error(flask_client):
    # Given
    data = "123"

    # Then
    assert_that(
        calling(claim_service.get_claim_details.__wrapped__).with_args(claim_service, data),
        raises(ServiceException),
    )


def test_claim_summary_deleted_true(flask_client, patch_db):
    # Given
    query = ()
    patch_db.lookup_currency.insert_one({"code": "GBP", "name": "British Pound", "symbol": "£", "order": 0})
    claims_metadata = patch_db.claims_metadata.insert_one(
        {
            "client_id": "a12",
            "claim_files": [
                {
                    "file_id": "39.1-ptt-icheck3-june",
                    "file_name": "Automated Claim Generation 20211216",
                    "file_date": "2021-12-16",
                    "status": "Submitted",
                    "item_count": {
                        "GBP": 6177,
                    },
                    "claim_total": {
                        "GBP": 1272498,
                    },
                    "checks": {
                        "GBP": 0,
                    },
                    "checked_amount": {
                        "GBP": 0,
                    },
                },
                {
                    "file_id": "39-ptt-icheck3-june",
                    "file_name": "Automated Claim Generation 20211216",
                    "file_date": "2021-12-16",
                    "status": "Submitted",
                    "item_count": {
                        "GBP": 6177,
                    },
                    "claim_total": {
                        "GBP": 1272498,
                    },
                    "checks": {
                        "GBP": 0,
                    },
                    "checked_amount": {
                        "GBP": 0,
                    },
                },
            ],
            "status": "Submitted",
            "frequency": "Daily",
            "notes": "abc",
        }
    )
    patch_db.claims_file_details.insert_many(
        [
            {
                "currency_code": "GBP",
                "file_id": "39-ptt-icheck3-june",
                "customer_type": "Direct",
                "bonding": "ATOL",
                "payment_type": "Giro",
                "days_to_process": 2,
                "total_booking_value": 4739,
                "supplier_ref": "B685z",
                "supplier_names": "Hoseason",
                "payment_date": "2020-12-01",
                "booking_status": "Live",
                "client_id": "a12",
                "claims_id": claims_metadata.inserted_id,
                "amount": 4439,
                "element": "Performance",
                "deleted": True,
            },
            {
                "currency_code": "GBP",
                "file_id": "39.1-ptt-icheck3-june",
                "customer_type": "Direct",
                "bonding": "ATOL",
                "payment_type": "Giro",
                "days_to_process": 2,
                "total_booking_value": 5000,
                "supplier_ref": "B685z",
                "supplier_names": "Hoseason",
                "payment_date": "2020-12-01",
                "booking_status": "Live",
                "client_id": "a12",
                "claims_id": claims_metadata.inserted_id,
                "amount": 5000,
                "element": "Performance",
                "deleted": True,
            },
            {
                "currency_code": "GBP",
                "file_id": "39-ptt-icheck3-june",
                "customer_type": "Direct",
                "bonding": "ATOL",
                "payment_type": "Giro",
                "days_to_process": 2,
                "total_booking_value": 4739,
                "supplier_ref": "B685z",
                "supplier_names": "Hoseason",
                "payment_date": "2020-12-01",
                "booking_status": "Live",
                "client_id": "a12",
                "claims_id": claims_metadata.inserted_id,
                "amount": 4439,
                "element": "Performance",
                "deleted": False,
            },
            {
                "currency_code": "GBP",
                "file_id": "39.1-ptt-icheck3-june",
                "customer_type": "Direct",
                "bonding": "ATOL",
                "payment_type": "Giro",
                "days_to_process": 2,
                "total_booking_value": 5000,
                "supplier_ref": "B685z",
                "supplier_names": "Hoseason",
                "payment_date": "2020-12-01",
                "booking_status": "Live",
                "client_id": "a12",
                "claims_id": claims_metadata.inserted_id,
                "amount": 5000,
                "element": "Performance",
                "deleted": False,
            },
        ]
    )

    # When
    result = claim_service.claim_summary(claims_metadata.inserted_id, query, "", 1, True)

    # Then
    assert result == [
        {
            "content": [
                {
                    "count": 1,
                    "currency": "GBP",
                    "element": "Performance",
                    "maxAmount": 4439,
                    "minAmount": 4439,
                    "total": 4439,
                }
            ],
            "currency": "GBP",
            "symbol": "£",
            "totalAmount": 4439,
            "totalCount": 1,
        }
    ]
    assert len(result) == 1


def test_claim_summary_deleted_false(flask_client, patch_db):
    # Given
    query = ()
    claims_metadata = patch_db.claims_metadata.insert_one(
        {
            "client_id": "a12",
            "claim_files": [
                {
                    "file_id": "39.1-ptt-icheck3-june",
                    "file_name": "Automated Claim Generation 20211216",
                    "file_date": "2021-12-16",
                    "status": "Submitted",
                    "item_count": {
                        "GBP": 6177,
                    },
                    "claim_total": {
                        "GBP": 1272498,
                    },
                    "checks": {
                        "GBP": 0,
                    },
                    "checked_amount": {
                        "GBP": 0,
                    },
                },
                {
                    "file_id": "39-ptt-icheck3-june",
                    "file_name": "Automated Claim Generation 20211216",
                    "file_date": "2021-12-16",
                    "status": "Submitted",
                    "item_count": {
                        "GBP": 6177,
                    },
                    "claim_total": {
                        "GBP": 1272498,
                    },
                    "checks": {
                        "GBP": 0,
                    },
                    "checked_amount": {
                        "GBP": 0,
                    },
                },
            ],
            "status": "Submitted",
            "frequency": "Daily",
            "notes": "abc",
        }
    )
    claim_details_list = [
        {
            "currency_code": "GBP",
            "customer_type": "Direct",
            "file_id": "39-ptt-icheck3-june",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2,
            "total_booking_value": 3000,
            "supplier_ref": "B685z",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "client_id": "a12",
            "claims_id": claims_metadata.inserted_id,
            "amount": 3000,
            "element": "Performance",
            "deleted": False,
        },
        {
            "currency_code": "GBP",
            "customer_type": "Direct",
            "bonding": "ATOL",
            "file_id": "39-ptt-icheck3-june",
            "payment_type": "Giro",
            "days_to_process": 2,
            "total_booking_value": 5000,
            "supplier_ref": "B685z",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "client_id": "a12",
            "claims_id": claims_metadata.inserted_id,
            "amount": 5000,
            "element": "Cruise",
            "deleted": False,
        },
        {
            "currency_code": "AED",
            "customer_type": "Direct",
            "file_id": "39-ptt-icheck3-june",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2,
            "total_booking_value": 1000,
            "supplier_ref": "B685z",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "client_id": "a12",
            "claims_id": claims_metadata.inserted_id,
            "amount": 1000,
            "element": "Performance",
            "deleted": False,
        },
        {
            "currency_code": "AED",
            "customer_type": "Direct",
            "bonding": "ATOL",
            "file_id": "39-ptt-icheck3-june",
            "payment_type": "Giro",
            "days_to_process": 2,
            "total_booking_value": 2000,
            "supplier_ref": "B685z",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "client_id": "a12",
            "claims_id": claims_metadata.inserted_id,
            "amount": 2000,
            "element": "Performance",
            "deleted": False,
        },
        {
            "currency_code": "AED",
            "customer_type": "Direct",
            "bonding": "ATOL",
            "file_id": "39.1-ptt-icheck3-june",
            "payment_type": "Giro",
            "days_to_process": 2,
            "total_booking_value": 2000,
            "supplier_ref": "B685z",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "client_id": "a12",
            "claims_id": claims_metadata.inserted_id,
            "amount": 2000,
            "element": "Performance",
            "deleted": False,
        },
        {
            "currency_code": "AED",
            "customer_type": "Direct",
            "bonding": "ATOL",
            "file_id": "39-ptt-icheck3-june",
            "payment_type": "Giro",
            "days_to_process": 2,
            "total_booking_value": 2000,
            "supplier_ref": "B685z",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "client_id": "a12",
            "claims_id": claims_metadata.inserted_id,
            "amount": 2000,
            "element": "Performance",
            "deleted": True,
        },
    ]
    patch_db.claims_file_details.insert_many(claim_details_list)
    patch_db.lookup_currency.insert_many(
        [
            {"code": "GBP", "name": "British Pound", "symbol": "£", "order": 0},
            {"code": "AED", "name": "Dinar", "symbol": "ṣ", "order": 1},
        ]
    )

    # When
    result = claim_service.claim_summary(claims_metadata.inserted_id, query, "element", 1, False)

    # Then
    assert result == [
        {
            "content": [
                {
                    "count": 2,
                    "currency": "AED",
                    "element": "Performance",
                    "maxAmount": 2000,
                    "minAmount": 1000,
                    "total": 3000,
                }
            ],
            "currency": "AED",
            "symbol": "ṣ",
            "totalAmount": 3000,
            "totalCount": 2,
        },
        {
            "content": [
                {
                    "count": 1,
                    "currency": "GBP",
                    "element": "Cruise",
                    "maxAmount": 5000,
                    "minAmount": 5000,
                    "total": 5000.0,
                },
                {
                    "count": 1,
                    "currency": "GBP",
                    "element": "Performance",
                    "maxAmount": 3000,
                    "minAmount": 3000,
                    "total": 3000.0,
                },
            ],
            "currency": "GBP",
            "symbol": "£",
            "totalAmount": 8000.0,
            "totalCount": 2,
        },
    ]
    assert len(result) == 2


def test_claim_summary_invalid_id(flask_client):
    # When
    query = ()
    assert_that(
        calling(claim_service.claim_summary).with_args("abc", query, "element", 1),
        raises(ServiceException, "invalid claimId"),
    )


@freeze_time("Jan 14th, 2012")
@patch("flaskr.services.claims_service.call_lambda", MagicMock(return_value=None))
@pytest.mark.parametrize(
    "data, output_data",
    [
        ({"notes": "string", "claimId": "62023b691eed04813999f201"}, {"notes": "string"}),
        ({"status": "string1", "claimId": "62023b691eed04813999f201"}, {"status": "string1"}),
        (
            {"assignedTo": "62023bc91eed04813999f20d", "claimId": "62023b691eed04813999f201"},
            {"assigned_to": "62023bc91eed04813999f20d"},
        ),
        (
            {"notes": "string", "status": "string1", "claimId": "62023b691eed04813999f201"},
            {"notes": "string", "status": "string1"},
        ),
        (
            {
                "notes": "string",
                "status": "string1",
                "assignedTo": "62023bc91eed04813999f20d",
                "frequency": "Daily",
                "claimId": "62023b691eed04813999f201",
            },
            {
                "notes": "string",
                "status": "string1",
                "frequency": "Daily",
                "assigned_to": "62023bc91eed04813999f20d",
            },
        ),
        (
            {"frequency": "Daily", "claimId": "62023b691eed04813999f201"},
            {"frequency": "Daily"},
        ),
    ],
)
def test_update_claims_metadata_success(flask_client, patch_db, data, output_data):
    # Given
    claim_id = patch_db.claims_metadata.insert_one(
        {
            "status": "Submitted",
            "client_id": "1",
            "updated_at": datetime(2012, 1, 14),
            "claim_files": [
                {
                    "status": "Submitted",
                    "file_name": "20220224-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.now(),
                    "file_id": "628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748",
                    "file_date": "2022-01-21",
                    "item_count": 14,
                    "deposit": 45920,
                }
            ],
            "created_at": datetime(2012, 1, 14),
        }
    ).inserted_id

    data = {
        "notes": data.get("notes"),
        "status": data.get("status"),
        "assignedTo": data.get("assignedTo"),
        "frequency": data.get("frequency"),
        "claimId": str(claim_id),
    }

    # When
    claim_service.update_claims_metadata(data, "1")

    # Then
    metadata = patch_db.claims_metadata.find_one({"_id": claim_id})
    for key, value in output_data.items():
        assert (key, value) in metadata.items()


@pytest.mark.parametrize(
    "invalid_data",
    [
        {"notes": 123, "claimId": "62023b691eed04813999f201"},
        {"status": 123, "claimId": "62023b691eed04813999f201"},
        {"assignedTo": 12334, "claimId": "62023b691eed04813999f201"},
    ],
)
def test_update_claims_metadata_validation_error(flask_client, patch_db, invalid_data):
    # Given
    data = invalid_data

    # Then
    assert_that(calling(claim_service.update_claims_metadata).with_args(data, "1"), raises(ValidationError))


@pytest.mark.parametrize(
    "invalid_data",
    [
        {"notes": "string", "claimId": "62023b691eed04813999f209"},
        {"notes": "string", "claimId": "62023b691eed04813999f20"},
    ],
)
def test_update_claims_metadata_service_exception(flask_client, patch_db, invalid_data):
    # Given
    data = invalid_data

    # Then
    assert_that(calling(claim_service.update_claims_metadata).with_args(data, "1"), raises(ServiceException))


@patch.object(
    ClaimService, "_ClaimService__handle_claims_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@patch.object(ClaimService, "_ClaimService__handle_claims_transaction_checks_update", MagicMock(return_value=None))
@patch("flaskr.services.claims_service.track_opening_closing_balance_changes", MagicMock(return_value=None))
def test_update_claim_transaction_success(flask_client, patch_db):
    # Given
    trust_account = patch_db.lookup_trust_type.insert_one({"name": "ATOL Standard"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "type_of_trust_account": trust_account.inserted_id}
    )
    client_id = basic_info.inserted_id
    user_id = "1"
    patch_db.claims_metadata.insert_one(
        {
            "_id": ObjectId("628783cf556173a598ea4322"),
            "status": "Submitted",
            "client_id": client_id,
            "claim_files": [
                {
                    "file_date": "2022-05-29",
                    "file_id": "1234",
                    "checked_amount": {"GBP": 9000, "USD": 3500, "INR": 7000},
                    "checks": {"GBP": 2, "USD": 5, "INR": 1},
                }
            ],
        }
    )
    id = patch_db.claims_file_details.insert_one(
        {
            "client_id": client_id,
            "status": "string",
            "check": "",
            "amount": 1000,
            "currency_code": "GBP",
            "claims_id": ObjectId("628783cf556173a598ea4322"),
            "supplier_name": None,
        }
    ).inserted_id
    data = {
        "check": "",
        "status": "string",
        "file_id": "1234",
        "statusReason": "string1",
        "totalDueToSupplier": 1234,
        "transactionId": id,
        "supplier_name": None,
    }

    # When
    claim_service.update_claim_transaction(id, data, user_id)

    # Then
    transaction = patch_db.claims_file_details.find_one({"_id": data["transactionId"]})
    assert transaction["check"] == data["check"]
    assert transaction["status"] == data["status"]
    assert transaction["status_reason"] == data["statusReason"]
    assert transaction["total_due_to_supplier"] == data["totalDueToSupplier"]


@pytest.mark.parametrize(
    "previous_amount,previous_original_amount ,new_amount,new_original_amount",
    [
        (70, 100, 77, 110),
        (77, 110, 70, 100),
        (70, 100, 70, 100),
    ],
)
@patch.object(
    ClaimService, "_ClaimService__handle_claims_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@patch.object(ClaimService, "_ClaimService__handle_claims_transaction_checks_update", MagicMock(return_value=None))
@patch.object(ClaimService, "_ClaimService__handle_anomaly_recalculation", MagicMock(return_value=None))
@patch("flaskr.services.claims_service.track_opening_closing_balance_changes", MagicMock(return_value=None))
def test_update_claim_transaction_escrow_client_success(
    flask_client, patch_db, previous_amount, previous_original_amount, new_amount, new_original_amount
):
    # Given
    trust_account = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "type_of_trust_account": trust_account.inserted_id}
    )
    client_id = basic_info.inserted_id
    user_id = "1"
    patch_db.claims_metadata.insert_one(
        {
            "_id": ObjectId("628783cf556173a598ea4322"),
            "status": "Submitted",
            "client_id": client_id,
            "claim_files": [
                {
                    "file_date": "2022-05-29",
                    "file_id": "1234",
                    "checked_amount": {"GBP": 9000, "USD": 3500, "INR": 7000},
                    "checks": {"GBP": 2, "USD": 5, "INR": 1},
                }
            ],
        }
    )
    id = patch_db.claims_file_details.insert_one(
        {
            "client_id": client_id,
            "status": "string",
            "file_id": "1234",
            "check": "",
            "currency_code": "GBP",
            "claims_id": ObjectId("628783cf556173a598ea4322"),
            "supplier_name": None,
            "amount": previous_amount,
            "original_amount": previous_original_amount,
        }
    ).inserted_id
    data = {
        "check": "",
        "status": "string",
        "statusReason": "string1",
        "totalDueToSupplier": 1234,
        "transactionId": id,
        "supplierName": None,
        "amount": new_amount,
    }

    # When
    claim_service.update_claim_transaction(id, data, user_id)

    # Then
    transaction = patch_db.claims_file_details.find_one({"_id": data["transactionId"]})
    assert transaction["check"] == data["check"]
    assert transaction["status"] == data["status"]
    assert transaction["status_reason"] == data["statusReason"]
    assert transaction["total_due_to_supplier"] == data["totalDueToSupplier"]
    assert transaction["amount"] == new_amount
    assert transaction["original_amount"] == new_original_amount


@pytest.mark.parametrize(
    "previous_transaction,new_transaction,escrow_multiplier",
    [
        (
            {"amount": 50, "original_amount": 100, "booking_date": "2022-04-01"},
            {"amount": 100, "original_amount": 200, "booking_date": "2022-04-01"},
            0.5,
        ),
        (
            {"amount": 100, "original_amount": 200, "booking_date": "2022-04-01"},
            {"amount": 50, "original_amount": 100, "booking_date": "2022-04-01"},
            0.5,
        ),
        (
            {"amount": 100, "original_amount": 200, "booking_date": "2022-04-01"},
            {"amount": 100, "original_amount": 200, "booking_date": "2022-04-01"},
            0.5,
        ),
        (
            {"amount": 50, "original_amount": 100, "booking_date": None},
            {"amount": 100, "original_amount": 200, "booking_date": None},
            0.5,
        ),
        (
            {"amount": 70, "original_amount": 100, "booking_date": "2022-04-02"},
            {"amount": 77, "original_amount": 110, "booking_date": "2022-04-03"},
            0.7,
        ),
        (
            {"amount": 77, "original_amount": 110, "booking_date": "2022-04-02"},
            {"amount": 70, "original_amount": 100, "booking_date": "2022-04-03"},
            0.7,
        ),
        (
            {"amount": 70, "original_amount": 100, "booking_date": "2022-04-02"},
            {"amount": 70, "original_amount": 100, "booking_date": "2022-04-03"},
            0.7,
        ),
        (
            {"amount": 70, "original_amount": 100, "booking_date": None},
            {"amount": 77, "original_amount": 110, "booking_date": None},
            0.7,
        ),
        (
            {"amount": 70, "original_amount": 100, "booking_date": "2022-04-03"},
            {"amount": 77, "original_amount": 110, "booking_date": "2022-04-03"},
            None,
        ),
    ],
)
@patch.object(
    ClaimService, "_ClaimService__handle_claims_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@patch.object(ClaimService, "_ClaimService__handle_claims_transaction_checks_update", MagicMock(return_value=None))
@patch.object(ClaimService, "_ClaimService__handle_anomaly_recalculation", MagicMock(return_value=None))
@patch("flaskr.services.claims_service.track_opening_closing_balance_changes", MagicMock(return_value=None))
def test_update_claim_transaction_major_travel_escrow_client_success(
    flask_client, patch_db, previous_transaction, new_transaction, escrow_multiplier
):
    # Given
    trust_account = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "2193", "type_of_trust_account": trust_account.inserted_id}
    )
    client_id = client_basic_info.inserted_id
    user_id = "1"
    patch_db.claims_metadata.insert_one(
        {
            "_id": ObjectId("628783cf556173a598ea4322"),
            "status": "Submitted",
            "client_id": client_id,
            "claim_files": [
                {
                    "file_date": "2022-05-29",
                    "file_id": "1234",
                    "checked_amount": {"GBP": 9000, "USD": 3500, "INR": 7000},
                    "checks": {"GBP": 2, "USD": 5, "INR": 1},
                }
            ],
        }
    )
    id = patch_db.claims_file_details.insert_one(
        {
            "client_id": client_id,
            "status": "string",
            "file_id": "1234",
            "check": "",
            "currency_code": "GBP",
            "claims_id": ObjectId("628783cf556173a598ea4322"),
            "supplier_name": None,
            "amount": previous_transaction["amount"],
            "original_amount": previous_transaction["original_amount"],
            "booking_date": previous_transaction["booking_date"],
            "escrow_multiplier": escrow_multiplier,
        }
    ).inserted_id
    data = {
        "check": "",
        "status": "string",
        "statusReason": "string1",
        "totalDueToSupplier": 1234,
        "transactionId": id,
        "supplierName": None,
        "amount": new_transaction["amount"],
        "bookingDate": new_transaction["booking_date"],
    }

    # When
    claim_service.update_claim_transaction(id, data, user_id)

    # Then
    transaction = patch_db.claims_file_details.find_one({"_id": data["transactionId"]})
    assert transaction["check"] == data["check"]
    assert transaction["status"] == data["status"]
    assert transaction["status_reason"] == data["statusReason"]
    assert transaction["total_due_to_supplier"] == data["totalDueToSupplier"]
    assert transaction["amount"] == new_transaction["amount"]
    assert transaction["original_amount"] == new_transaction["original_amount"]
    assert transaction["booking_date"] == new_transaction["booking_date"]


@patch.object(
    ClaimService, "_ClaimService__handle_claims_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@pytest.mark.parametrize(
    "invalid_data",
    [
        {"check": 123, "transactionId": "620b75d59b53aa92cd5dbb1"},
        {"check": 123, "transactionId": "620b75d59b53aa92cd5dbb1a"},
        {"status": 123, "transactionId": "620b75d59b53aa92cd5dbb1"},
        {"statusReason": 123, "transactionId": "620b75d59b53aa92cd5dbb1"},
        {"totalDueToSupplier": "string", "transactionId": "620b75d59b53aa92cd5dbb1"},
    ],
)
def test_update_claim_transaction_validation_error(flask_client, patch_db, invalid_data):
    # Given

    data = invalid_data

    # Then
    assert_that(
        calling(claim_service.update_claim_transaction).with_args(data["transactionId"], data, "1"),
        raises(ValidationError),
    )


@patch.object(
    ClaimService, "_ClaimService__handle_claims_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@patch.object(ClaimService, "_ClaimService__handle_claims_transaction_checks_update", MagicMock(return_value=None))
@patch.object(ClaimService, "_ClaimService__handle_anomaly_recalculation", MagicMock(return_value=None))
@patch("flaskr.services.claims_service.track_opening_closing_balance_changes", MagicMock(return_value=None))
@pytest.mark.parametrize(
    "input_booking_date,existing_booking_date",
    [
        ("2022-04-03", "2022-03-01"),
        ("2022-03-01", "2022-04-03"),
        ("2022-04-01", "2022-03-31"),
        ("2022-03-31", "2022-04-01"),
    ],
)
def test_update_claim_transaction_service_exception_major_travel(
    flask_client, patch_db, input_booking_date, existing_booking_date
):
    # Given

    trust_account = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("621c4699b29069e5c622ca88"),
            "client_id": "2193",
            "type_of_trust_account": trust_account.inserted_id,
        }
    )
    patch_db.claims_metadata.insert_one(
        {
            "_id": ObjectId("628783cf556173a598ea4322"),
            "status": "Submitted",
            "client_id": ObjectId("621c4699b29069e5c622ca88"),
            "claim_files": [
                {
                    "file_date": "2022-05-29",
                    "file_id": "1234",
                    "checked_amount": {"GBP": 9000, "USD": 3500, "INR": 7000},
                    "checks": {"GBP": 2, "USD": 5, "INR": 1},
                }
            ],
        }
    )
    id = patch_db.claims_file_details.insert_one(
        {
            "client_id": ObjectId("621c4699b29069e5c622ca88"),
            "status": "string",
            "file_id": "1234",
            "check": "",
            "currency_code": "GBP",
            "claims_id": ObjectId("628783cf556173a598ea4322"),
            "supplier_name": None,
            "amount": 50,
            "original_amount": 100,
            "booking_date": existing_booking_date,
            "escrow_multiplier": 0.5,
        }
    ).inserted_id
    data = {
        "check": "",
        "status": "string",
        "statusReason": "string1",
        "totalDueToSupplier": 1234,
        "amount": 100,
        "transactionId": id,
        "supplierName": None,
        "bookingDate": input_booking_date,
    }
    current_app.config["MAJOR_TRAVEL"] = "621c4699b29069e5c622ca88"

    # Then
    assert_that(
        calling(claim_service.update_claim_transaction).with_args(data["transactionId"], data, "1"),
        raises(ServiceException, "Transaction is not editable for the given booking date"),
    )


@patch.object(
    ClaimService, "_ClaimService__handle_claims_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@pytest.mark.parametrize(
    "invalid_data",
    [
        {"check": "", "transactionId": "620b75d59b53aa92cd5dbb1p"},
        {"check": "quick-check", "transactionId": "620b75d59b53aa92cd5dbb1p"},
        {"check": "full-check", "transactionId": "620b75d59b53aa92c"},
    ],
)
def test_update_claim_transaction_service_exception(flask_client, patch_db, invalid_data):
    # Given

    data = invalid_data

    # Then
    assert_that(
        calling(claim_service.update_claim_transaction).with_args(data["transactionId"], data, "1"),
        raises(ServiceException),
    )


@patch.object(
    ClaimService, "_ClaimService__handle_claims_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@pytest.mark.parametrize(
    "data, output_data",
    [
        (
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1e"),
                "booking_ref": "977957",
                "lead_pax": "KV",
                "booking_date": "2020-08-21",
                "departure_date": "2022-03-26",
                "return_date": "2022-04-08",
                "supplier_ref": "A45EX",
                "supplier_names": "Hoseason",
                "total_paid": 11392,
                "status": "live",
                "status_reason": "string",
                "amount": 600,
                "total_due_to_supplier": 123,
                "deleted": False,
            },
            {
                "clientId": "62fb53e11a873172a59b2a1e",
                "amount": 600.0,
                "bookingDate": "2020-08-21",
                "bookingRef": "977957",
                "departureDate": "2022-03-26",
                "leadPax": "KV",
                "returnDate": "2022-04-08",
                "status": "live",
                "totalDueToSupplier": 123.0,
                "statusReason": "string",
                "supplierNames": "Hoseason",
                "supplierRef": "A45EX",
                "totalPaid": 11392.0,
            },
        )
    ],
)
def test_claim_get_transaction_success(flask_client, patch_db, data, output_data):
    # Given
    transaction = patch_db.claims_file_details.insert_one(data)
    transaction_id = transaction.inserted_id
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one({"user_id": user_id, "clients": [ObjectId("62fb53e11a873172a59b2a1e")]})
    # When
    result = claim_service.claim_get_transaction(transaction_id, user_id)

    # Then
    assert result == output_data


@patch.object(
    ClaimService, "_ClaimService__handle_claims_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@pytest.mark.parametrize(
    "invalid_data",
    [
        {"amount": 600.0, "transactionId": "620b75d59b53aa92cd5dbb1"},
        {"amount": 700.0, "transactionId": "620b75d59b53aa92cd5dbb1a"},
        {"amount": 900.0, "transactionId": "620b75d59b53aa92cd5dbb1p"},
    ],
)
def test_claim_update_claim_transaction_validation_error(flask_client, invalid_data):
    data = invalid_data

    # Then
    assert_that(
        calling(claim_service.update_claim_transaction).with_args(data["transactionId"], data["amount"]),
        "1",
        raises(ValidationError),
    )


@pytest.mark.parametrize(
    "input_data, output_data",
    [
        (
            {
                "_id": ObjectId("620257e8697b3b6f30f37e50"),
                "booking_ref": "977957",
                "booking_date": "2022-03-02",
                "currency_code": "GBP",
                "client_id": ObjectId("12257912c51e741b53957643"),
                "claims_id": ObjectId("61fb95ede377dce36a47edc5"),
                "element": "Cruise",
                "amount": 600,
                "check": "quick-check",
                "deleted": False,
            },
            {
                "_id": "620257e8697b3b6f30f37e50",
                "amount": 600.0,
                "bookingRef": "977957",
                "check": "quick-check",
                "clientId": "1",
                "count": 1,
                "currencyCode": "GBP",
                "duplicates": 1,
                "element": "Cruise",
            },
        )
    ],
)
def test_get_check_type_success(flask_client, patch_db, input_data, output_data):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"c_id": "1", "_id": ObjectId("12257912c51e741b53957643")}
    )
    client_id = client_basic_info.inserted_id
    claims_id = "61fb95ede377dce36a47edc5"
    patch_db.claims_metadata.insert_one(
        {
            "_id": ObjectId(claims_id),
            "client_id": client_id,
            "claim_files": [
                {
                    "file_date": "2022-05-29",
                    "file_id": "1234",
                    "checked_amount": {"GBP": 9000, "USD": 3500, "INR": 7000},
                    "checks": {"GBP": 2, "USD": 5, "INR": 1},
                }
            ],
        }
    )
    patch_db.claims_file_details.insert_one(input_data)
    data = {"check": "quick-check", "claimsId": claims_id}
    query = ()
    # When
    result = claim_service.get_check_type(data["check"], data["claimsId"], query)

    # Then
    assert result[0] == output_data


@pytest.mark.parametrize(
    "status, output_status,count_unresolved",
    [
        (["Resolved"], "Resolved", 0),
        (["Resolved", "Unresolved"], "Unresolved", 1),
        (["Unresolved"], "Unresolved", 1),
    ],
)
@patch.object(
    AuthService,
    "ptt_users_list",
    MagicMock(
        return_value=[
            {
                "userId": "cf0142a5-7475-4910-9321-40c323d7a876",
                "email": "<EMAIL>",
                "name": "test",
            }
        ]
    ),
)
def test_get_claim_anomalies(patch_db, monkeypatch, status, output_status, count_unresolved):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one({"c_id": "1"})
    client_id = client_basic_info.inserted_id
    query = ()
    claims_id = "620245de6dda372675663e05"
    patch_db.claims_metadata.insert_one(
        {
            "_id": ObjectId(claims_id),
            "client_id": client_id,
            "claim_files": [
                {
                    "file_date": "2022-05-29",
                    "file_id": "1234",
                    "checked_amount": {"GBP": 9000, "USD": 3500, "INR": 7000},
                    "checks": {"GBP": 2, "USD": 5, "INR": 1},
                }
            ],
        }
    )
    monkeypatch.setattr(
        patch_db.anomaly_claims,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "metadata": [{"total": 1, "page": 1}],
                "data": [
                    {
                        "status": status,
                        "bookingRef": "977957",
                        "anomalyIds": ["621bf4f49bed97189d98ddcd", "621bf4f49bed97189d98ddce"],
                        "anomalyType": "Funds in Trust Exceed/less than Total Booking Value",
                        "leadPassenger": "Lady Emma Monson",
                        "bookingDate": "2022-11-05",
                        "dateOfTravel": "2022-04-01",
                        "dateOfReturn": "2022-07-06",
                        "balanceInTrust": -100,
                        "currency_code": ["GBP", "USD"],
                        "symbol": ["£", "$"],
                        "clientId": client_id,
                        "count": 2,
                        "modifiedBy": "cf0142a5-7475-4910-9321-40c323d7a876",
                        "totalClaim": 100,
                    },
                ],
            }
        ]
        if args[0][-1] != {"$count": "total_unresolved"}
        else iter([{"total_unresolved": count_unresolved}]),
    )

    # When
    result = claim_service.get_claim_anomalies(claims_id, query, "1", "10", "bookingRef", 1)

    # # Then
    assert result == {
        "content": [
            {
                "anomalyIds": ["621bf4f49bed97189d98ddcd", "621bf4f49bed97189d98ddce"],
                "anomalyType": "Funds in Trust Exceed/less than Total Booking Value",
                "balanceInTrust": -100.0,
                "bookingDate": "2022-11-05",
                "bookingRef": "977957",
                "clientId": "1",
                "currency_code": ["GBP", "USD"],
                "count": 2,
                "dateOfReturn": "2022-07-06",
                "dateOfTravel": "2022-04-01",
                "leadPassenger": "Lady Emma Monson",
                "modifiedBy": "test",
                "status": output_status,
                "symbol": ["£", "$"],
                "totalClaim": 100,
            },
        ],
        "anomalyCount": count_unresolved,
        "numberOfElements": 1,
        "pageNumber": 1,
        "totalElements": 1,
        "totalPages": 1,
    }


@pytest.mark.parametrize(
    "invalid_data",
    [
        {"transactionId": "620b75d59b53aa92cd5dbb1"},
        {"transactionId": "620b75d59b53aa92cd5dbb1p"},
        {"transactionId": "620b75d59b53aa92cd5dbb1p"},
        {"transactionId": "620b75d59b53aa92cd5dbb1p"},
    ],
)
def test_get_claim_anomalies_validation_error(flask_client, invalid_data):
    # Given

    data = invalid_data
    query = ()

    # Then
    assert_that(
        calling(claim_service.get_claim_anomalies).with_args(data["transactionId"], query, "1", "10", "", 1),
        raises(ServiceException),
    )


@pytest.mark.parametrize(
    "input_data, output_data",
    [
        (
            [
                {
                    "_id": ObjectId("62257912c51e741b53987661"),
                    "booking_ref": "991747",
                    "lead_pax": "Ms Emma Cullen",
                    "pax_count": 2,
                    "booking_date": "2021-04-10",
                    "departure_date": "2022-03-01",
                    "return_date": "2021-11-07",
                    "supplier_ref": None,
                    "supplier_names": None,
                    "element": "Deposit",
                    "currency_code": "USD",
                    "amount": 2500,
                    "type": "Direct",
                    "bonding": "ATOL Fully Bonded",
                    "dept_bal_lead_time": "2",
                    "total_booking_value": 3000,
                    "client_id": ObjectId("62257912c51e741b53987643"),
                    "claims_id": ObjectId("621c38cae22a9303b9b091c9"),
                    "nights": -114,
                    "file_id": "ffb91538-09b2-444b-abde-27b085c51574_2022-02-28T02:51:54.904587",
                    "deleted": False,
                    "created_at": datetime.fromisoformat("2022-02-28"),
                    "updated_at": datetime.fromisoformat("2022-02-28"),
                },
                {
                    "_id": ObjectId("62257912c51e741b53987662"),
                    "booking_ref": "991747",
                    "lead_pax": "Ms Emma Cullen",
                    "pax_count": 2,
                    "booking_date": "2021-04-10",
                    "departure_date": "2022-03-01",
                    "return_date": "2021-11-07",
                    "supplier_ref": None,
                    "supplier_names": None,
                    "element": "Deposit",
                    "currency_code": "USD",
                    "amount": 2000,
                    "type": "Direct",
                    "bonding": "ATOL Fully Bonded",
                    "dept_bal_lead_time": "2",
                    "total_booking_value": 3000,
                    "client_id": ObjectId("62257912c51e741b53987643"),
                    "claims_id": ObjectId("621c38cae22a9303b9b091c9"),
                    "nights": -114,
                    "file_id": "ffb91538-09b2-444b-abde-27b085c51574_2022-02-28T02:51:54.904587",
                    "deleted": False,
                    "created_at": datetime.fromisoformat("2022-02-28"),
                    "updated_at": datetime.fromisoformat("2022-02-28"),
                },
            ],
            {
                "totalTransactions": 2,
                "transactions": [
                    {
                        "_id": "62257912c51e741b53987661",
                        "amount": 2500.0,
                        "bookingRef": "991747",
                        "clientId": "1",
                        "count": 2,
                        "currencyCode": "USD",
                        "description": "Highest Amount, Median Amount",
                        "duplicates": 1,
                        "element": "Deposit",
                    },
                    {
                        "_id": "62257912c51e741b53987662",
                        "amount": 2000.0,
                        "bookingRef": "991747",
                        "clientId": "1",
                        "count": 2,
                        "currencyCode": "USD",
                        "description": "Lowest Amount, Median Amount",
                        "duplicates": 1,
                        "element": "Deposit",
                    },
                ],
            },
        )
    ],
)
def test_get_claim_automated_transaction_success(flask_client, patch_db, input_data, output_data):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"c_id": "1", "_id": ObjectId("62257912c51e741b53987643")})
    client_id = basic_info.inserted_id
    query = ()
    claims_id = "621c38cae22a9303b9b091c9"
    patch_db.claims_metadata.insert_one(
        {
            "_id": ObjectId(claims_id),
            "client_id": client_id,
            "claim_files": [
                {
                    "file_date": "2022-05-29",
                    "file_id": "1234",
                    "checked_amount": {"GBP": 9000, "USD": 3500, "INR": 7000},
                    "checks": {"GBP": 2, "USD": 5, "INR": 1},
                }
            ],
        }
    )
    patch_db.claims_file_details.insert_many(input_data)
    # When
    result = claim_service.get_claim_automated_transaction(claims_id, query)

    # Then
    assert result == output_data


@freeze_time("Jan 14 2012")
@patch("flaskr.services.claims_service.track_opening_closing_balance_changes", MagicMock(return_value=None))
@pytest.mark.parametrize(
    "existing_transaction, updated_transaction, new_balance, new_total_claimed , output_claims_metadata, is_claim_transaction_deleted",
    [
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1500,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
            },
            500,
            4500,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "claim_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "20220410-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 18, "USD": 5, "INR": 1},
                        "claim_total": {"GBP": 2500, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 00, "USD": 00, "INR": 00},
                        "checked_amount": {"GBP": 00, "USD": 00, "INR": 00},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            False,
        ),
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 500,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
            },
            1500,
            3500,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "claim_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "20220410-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 18, "USD": 5, "INR": 1},
                        "claim_total": {"GBP": 1500, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 0, "USD": 0, "INR": 0},
                        "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            False,
        ),
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
            },
            1000,
            4000,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "claim_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "20220410-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 18, "USD": 5, "INR": 1},
                        "claim_total": {"GBP": 2000, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 0, "USD": 0, "INR": 0},
                        "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            False,
        ),
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Reinstated",
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Cancelled",
            },
            2000,
            3000,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "claim_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "20220410-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 17, "USD": 5, "INR": 1},
                        "claim_total": {"GBP": 1000, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 0, "USD": 0, "INR": 0},
                        "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            True,
        ),
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": True,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Cancelled",
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": True,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Reinstated",
            },
            0,
            5000,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "claim_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "20220410-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 19, "USD": 5, "INR": 1},
                        "claim_total": {"GBP": 3000, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 0, "USD": 0, "INR": 0},
                        "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            False,
        ),
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Reinstated",
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 500,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Cancelled",
            },
            2000,
            3000,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "claim_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "20220410-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 17, "USD": 5, "INR": 1},
                        "claim_total": {"GBP": 1000, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 0, "USD": 0, "INR": 0},
                        "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            True,
        ),
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": True,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Cancelled",
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": True,
                "claims_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 500,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Reinstated",
            },
            500,
            4500,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "claim_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "20220410-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 19, "USD": 5, "INR": 1},
                        "claim_total": {"GBP": 2500, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 0, "USD": 0, "INR": 0},
                        "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            False,
        ),
    ],
)
def test_handle_claims_transaction_amount_and_status_change(
    patch_db,
    existing_transaction,
    updated_transaction,
    new_balance,
    new_total_claimed,
    output_claims_metadata,
    is_claim_transaction_deleted,
):
    # Given
    trust_fund = {
        "client_id": "2",
        "booking_ref": "123",
        "balance": 1000,
        "total_in_trust": 5000,
        "total_claimed": 4000,
        "currency_code": ["GBP"],
        "created_at": datetime(2012, 1, 14),
        "updated_at": datetime(2012, 1, 14),
    }
    claims_metadata = {
        "_id": ObjectId("621c4699b29069e5c611ca88"),
        "client_id": "2",
        "status": "Submitted",
        "claim_files": [
            {
                "file_id": "1234",
                "file_date": "2022-04-10",
                "status": "Submitted",
                "file_name": "20220410-All -claim-mapping02.xlsx",
                "item_count": {"GBP": 18, "USD": 5, "INR": 1},
                "claim_total": {"GBP": 2000, "USD": 3500, "INR": 7000},
                "checks": {"GBP": 0, "USD": 0, "INR": 0},
                "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
            }
        ],
        "created_at": datetime(2012, 1, 14),
        "updated_at": datetime(2012, 1, 14),
    }

    trust_fund_id = patch_db.trust_fund_v2.insert_one(trust_fund).inserted_id
    transaction_id = patch_db.claims_file_details.insert_one(updated_transaction).inserted_id
    claims_metadata_id = patch_db.claims_metadata.insert_one(claims_metadata).inserted_id
    # When
    claim_service = ClaimService()
    claim_service._ClaimService__handle_claims_transaction_amount_and_status_change(
        existing_transaction, updated_transaction, "2022-05-29", "1234", None
    )

    # Then
    updated_trust_fund = patch_db.trust_fund_v2.find_one({"_id": trust_fund_id})
    updated_claims_metadata = patch_db.claims_metadata.find_one({"_id": claims_metadata_id})
    updated_claims_transaction = patch_db.claims_file_details.find_one({"_id": transaction_id})
    assert (
        abs(
            updated_claims_metadata["claim_files"][-1]["claim_total"]["GBP"]
            - output_claims_metadata["claim_files"][-1]["claim_total"]["GBP"]
        )
        <= 1
    )

    assert updated_trust_fund["balance"] == new_balance
    assert updated_trust_fund["total_claimed"] == new_total_claimed
    assert updated_claims_transaction["deleted"] == is_claim_transaction_deleted


@pytest.mark.parametrize(
    "updated_transaction, existing_transaction, deleted, count",
    [
        (
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "supplier_names": "Test_Name",
            },
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "supplier_names": "Test_Name",
            },
            False,
            0,
        ),
        (
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "supplier_names": "Test_Name",
            },
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-11-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "supplier_names": "Test_Name",
            },
            True,
            1,
        ),
        (
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "supplier_names": "Test_Name",
            },
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-11-01",
                "departure_date": "2022-11-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "supplier_names": "Test_Name",
            },
            True,
            1,
        ),
        (
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "supplier_names": "Test_Name",
            },
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-11-01",
                "amount": 500,
                "supplier_names": "Test_Name",
            },
            True,
            1,
        ),
        (
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "supplier_names": "Test_Name",
            },
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 1000,
                "supplier_names": "Test_Name",
            },
            True,
            1,
        ),
        (
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "supplier_names": "Test_Name",
            },
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "supplier_names": "Updated",
            },
            True,
            1,
        ),
    ],
)
def test_handle_anomaly_recalculation(
    patch_db, flask_client, updated_transaction, existing_transaction, deleted, count
):
    # Given
    mock = Mock()
    patch("flaskr.services.claims_service.call_lambda", mock).start()
    anomaly_id = patch_db.anomaly_claims.insert_one(
        {
            "transaction_id": existing_transaction["_id"],
            "file_id": existing_transaction["file_id"],
            "client_id": existing_transaction["client_id"],
            "deleted": False,
            "anomaly_type": "Test Anomaly",
        }
    ).inserted_id

    # When
    claim_service._ClaimService__handle_anomaly_recalculation(updated_transaction, existing_transaction, None)

    # Then
    anomaly = patch_db.anomaly_claims.find_one({"_id": anomaly_id})
    assert anomaly["deleted"] == deleted
    assert mock.call_count == count


def test_claim_testing_success(flask_client, patch_db, monkeypatch):
    # Given
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "metadata": [{"total": 2, "page": 1}],
                "data": [
                    {
                        "_id": ObjectId("632bf1bdbc7e2d644cce7b76"),
                        "clientId": "1234",
                        "clientName": "abc",
                        "submittedDate": [datetime(2020, 10, 27, 0, 0)],
                        "fileDate": ["2020-10-27"],
                        "status": "In Progress",
                        "reasons": "string",
                        "count": [{"GBP": 11985}],
                        "items": [{"GBP": 17026508.210000042}],
                        "originalClaim": {"GBP": 1000},
                    },
                ],
            }
        ],
    )
    patch_db.lookup_currency.insert_many(
        [{"code": "GBP", "symbol": "£"}, {"code": "USD", "symbol": "$"}, {"code": "INR", "symbol": "₹"}]
    )

    data = {"query": "ab", "client": "", "fromDate": "", "toDate": "", "date": "", "page": 1, "size": 1}
    # When

    response = claim_service.claim_testing("123", data)

    # Then
    assert response == {
        "content": [
            {
                "_id": "632bf1bdbc7e2d644cce7b76",
                "clientId": "1234",
                "clientName": "abc",
                "fileDate": "2020-10-27",
                "items": [{"currency": "GBP", "revisedClaim": 17026508.210000042, "originalClaim": 1000}],
                "reasons": "string",
                "status": "In Progress",
                "submittedDate": "2020-10-27T00:00:00",
            }
        ],
        "empty": False,
        "first": True,
        "last": False,
        "numberOfElements": 1,
        "pageNumber": 1,
        "totalElements": 2,
        "totalPages": 2,
    }


def test_claim_testing_failure(flask_client, patch_db, monkeypatch):
    # Given
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "metadata": [{"total": 1, "page": 1}],
                "data": [
                    {
                        "_id": ObjectId("632bf1bdbc7e2d644cce7b76"),
                        "clientId": "1234",
                        "clientName": "abc",
                        "submittedDate": [datetime(2020, 10, 27, 0, 0)],
                        "fileDate": ["2020-10-27"],
                        "status": "In Progress",
                        "reasons": "string",
                        "count": [{"GBP": 11985}],
                        "items": [{"GBP": 1000}],
                        "originalClaim": {"GBP": 1000},
                    },
                ],
            }
        ],
    )
    patch_db.lookup_currency.insert_many(
        [{"code": "GBP", "symbol": "£"}, {"code": "USD", "symbol": "$"}, {"code": "INR", "symbol": "₹"}]
    )

    data = {"query": "ab", "client": "", "fromDate": "", "toDate": "", "date": "", "page": 1, "size": 1}
    # When

    response = claim_service.claim_testing("123", data)

    # Then
    assert response == {
        "content": [],
        "empty": True,
        "first": True,
        "last": True,
        "numberOfElements": 0,
        "pageNumber": 1,
        "totalElements": 1,
        "totalPages": 1,
    }


def test_update_claim_testing_success(flask_client, patch_db):
    # Given
    patch_db.claim_testing.insert_one(
        {"claims_id": ObjectId("632af99d7dc025fdb915f639"), "original_claim": {"GBP": 1000}, "reasons": "string"}
    )

    data = {"reasons": "hy"}

    # When
    claim_service.update_claim_testing("632af99d7dc025fdb915f639", data)

    # Then
    claim_testing_details = patch_db.claim_testing.find_one({"claims_id": ObjectId("632af99d7dc025fdb915f639")})
    assert claim_testing_details["reasons"] == "hy"


def test_claim_create_authorised_status(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "is_disabled": None, "claim_from_tbr": False, "company_alias": "TFN"}
    )
    client_id = basic_info.inserted_id
    client_details = ClaimsMetadataSchema().load(
        {
            "clientId": client_id,
            "clientName": "ABC",
            "createdAt": "2020-05-19T18:30:00.000+00:00",
            "notes": "string",
            "status": "Authorised",
            "updatedAt": "2020-08-24T18:30:00.000+00:00",
            "claimFiles": [
                {
                    "fileId": "111258412241665569",
                    "fileName": "20240510-TFN-Claims.xlsx",
                    "status": "Authorised",
                    "submittedDate": "2021-08-10T18:30:00.000+00:00",
                    "notes": "string",
                },
            ],
        }
    )
    patch_db.claims_metadata.insert_one(client_details)
    patch_db.user.insert_one({"user_id": "1", "role": "ptt-user"})

    # When
    assert_that(
        calling(claim_service.claim_create_presigned_url).with_args(
            str(client_id), "20240510-TFN-Claims.xlsx", False, "1"
        ),
        raises(ServiceException, "No action is allowed since the file is authorised."),
    )
