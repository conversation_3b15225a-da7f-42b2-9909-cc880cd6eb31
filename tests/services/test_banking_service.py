import re
from flask import current_app
from marshmallow import ValidationError
from mock import Mock
import pytest
from flaskr.models.banking.metadata import BankingMetadataSchema
from flaskr.services.auth_service import AuthService
from flaskr.services.banking_service import BankingService, banking_service
from freezegun import freeze_time
from datetime import datetime
from hamcrest import assert_that, calling, raises
from flaskr.services.exceptions import ServiceException
from unittest.mock import MagicMock, patch
from mongomock import ObjectId


def test_banking_search_success(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = client_basic_info.inserted_id
    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "metadata": [{"total": 2, "page": 1}],
                "data": [
                    {
                        "_id": ObjectId("62564a4e600d53e857f9ae52"),
                        "clientId": client_id,
                        "cId": "123",
                        "clientName": "ABC",
                        "friendlyName": "edstem",
                        "count": [{"GBP": 13, "USD": 5, "INR": 1}],
                        "amount": [{"GBP": 104000.0, "USD": 35000, "INR": 7000}],
                        "fileDate": ["2022-04-13"],
                        "assignedTo": "62564a68600d53e857f9ae53",
                        "submittedDate": [datetime(2022, 4, 13, 3, 58, 6, 805000)],
                        "status": "Submitted",
                        "trust": "ATOL Escrow",
                    },
                    {
                        "_id": ObjectId("62a722aea2f265631c74046c"),
                        "clientId": client_id,
                        "cId": "123",
                        "clientName": "ABC",
                        "friendlyName": "edstem",
                        "count": [{"GBP": 12, "USD": 5, "INR": 1}],
                        "amount": [{"GBP": 84000, "USD": 35000, "INR": 7000}],
                        "fileDate": ["2022-03-10"],
                        "assignedTo": "62564a68600d53e857f9ae53",
                        "submittedDate": [datetime(2022, 4, 13, 3, 58, 6, 805000)],
                        "status": "Submitted",
                        "trust": "ATOL Escrow",
                    },
                ],
            },
        ],
    )
    patch_db.lookup_currency.insert_many(
        [{"code": "GBP", "symbol": "£"}, {"code": "USD", "symbol": "$"}, {"code": "INR", "symbol": "₹"}]
    )
    data = {
        "query": "",
        "client": client_id,
        "assignedTo": "62564a68600d53e857f9ae53",
        "status": "",
        "date": "",
        "page": 1,
        "size": 4,
        "fromDate": "",
        "toDate": "",
    }
    # When

    response = banking_service.banking_search.__wrapped__(banking_service, "123", data)

    # Then
    assert response == {
        "content": [
            {
                "assignedTo": "62564a68600d53e857f9ae53",
                "bankingId": "62564a4e600d53e857f9ae52",
                "clientId": str(client_id),
                "cId": "123",
                "clientName": "ABC",
                "friendlyName": "edstem",
                "fileDate": "2022-04-13",
                "items": [
                    {"amount": 104000.0, "count": 13, "currency": "GBP", "symbol": "£"},
                    {"amount": 35000, "count": 5, "currency": "USD", "symbol": "$"},
                    {"amount": 7000, "count": 1, "currency": "INR", "symbol": "₹"},
                ],
                "notes": "",
                "status": "Submitted",
                "submittedDate": "2022-04-13T03:58:06.805000",
                "trustAccount": "ATOL Escrow",
            },
            {
                "assignedTo": "62564a68600d53e857f9ae53",
                "bankingId": "62a722aea2f265631c74046c",
                "clientId": str(client_id),
                "cId": "123",
                "clientName": "ABC",
                "friendlyName": "edstem",
                "fileDate": "2022-03-10",
                "items": [
                    {"amount": 84000, "count": 12, "currency": "GBP", "symbol": "£"},
                    {"amount": 35000, "count": 5, "currency": "USD", "symbol": "$"},
                    {"amount": 7000, "count": 1, "currency": "INR", "symbol": "₹"},
                ],
                "notes": "",
                "status": "Submitted",
                "submittedDate": "2022-04-13T03:58:06.805000",
                "trustAccount": "ATOL Escrow",
            },
        ],
        "empty": False,
        "first": True,
        "last": True,
        "numberOfElements": 2,
        "pageNumber": 1,
        "totalElements": 2,
        "totalPages": 1,
    }


@patch(
    "flaskr.services.banking_service.generate_presigned_url",
    MagicMock(return_value="https://test-url"),
)
@freeze_time("May 5th 2022")
@pytest.mark.parametrize(
    "file_name",
    [
        "********-TFN-Banking.xlsx",
        "********-TFN-Banking.xls",
        "********-TFN-Banking.csv",
    ],
)
def test_banking_create_presigned_url(flask_client, patch_db, file_name):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1", "is_disabled": None, "company_alias": "TFN"})
    client_id = basic_info.inserted_id
    user_id = "1"

    # When
    response = banking_service.banking_create_presigned_url(str(client_id), file_name, user_id)
    # Then
    assert "fileId" in response
    assert response["presignedUrl"] == "https://test-url"


def test_banking_create_presigned_url_invalid_file_date(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1", "is_disabled": None, "company_alias": "TFN"})
    client_id = basic_info.inserted_id
    user_id = "1"
    # When
    assert_that(
        calling(banking_service.banking_create_presigned_url).with_args(
            str(client_id), "********-TFN-Banking.xlsx", user_id
        ),
        raises(
            ServiceException,
            re.escape(f"File date cannot be older than {current_app.config['FILE_DATE_AGE_IN_MONTHS']} month(s)."),
        ),
    )


@pytest.mark.parametrize(
    "invalid_file_name",
    [
        "********Document1Banking.xlsx",
        "********Document1-Banking.xlsx",
    ],
)
def test_banking_create_presigned_url_invalid_file_name(flask_client, patch_db, invalid_file_name):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1", "is_disabled": None, "company_alias": "TFN"})
    client_id = basic_info.inserted_id
    user_id = "1"
    # When
    assert_that(
        calling(banking_service.banking_create_presigned_url).with_args(str(client_id), invalid_file_name, user_id),
        raises(
            ServiceException,
            re.escape("Invalid file name"),
        ),
    )


def test_banking_create_presigned_url_invalid_file_date_format(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1", "is_disabled": None, "company_alias": "TFN"})
    client_id = basic_info.inserted_id
    user_id = "1"
    # When
    assert_that(
        calling(banking_service.banking_create_presigned_url).with_args(
            str(client_id), "********-HYB-Banking.xlsx", user_id
        ),
        raises(
            ServiceException,
            re.escape("Invalid file name, failed to extract file date."),
        ),
    )


def test_banking_create_presigned_url_invalid_client(flask_client, patch_db):
    # Given
    invalid_client_id = "12257912c51e741b53957643"
    user_id = "1"
    # When
    assert_that(
        calling(banking_service.banking_create_presigned_url).with_args(
            invalid_client_id, "********-TFN-Banking.xlsx", user_id
        ),
        raises(ServiceException, "client not found"),
    )


@patch(
    "flaskr.services.banking_service.generate_presigned_url",
    MagicMock(return_value="https://test-url"),
)
@freeze_time("May 5th 2022")
def test_banking_create_authorised_status(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1", "is_disabled": None, "company_alias": "TFN"})
    client_id = basic_info.inserted_id
    banking_data = {
        "clientId": str(client_id),
        "status": "Authorised",
        "bankingFiles": [
            {
                "fileId": "111258412241665569",
                "fileName": "********-TFN-Banking.xlsx",
                "status": "Authorised",
                "submittedDate": datetime(2021, 8, 10, 18, 30).isoformat(),
                "notes": "string",
            },
        ],
    }
    banking_metadata = BankingMetadataSchema().load(banking_data)
    patch_db.banking_metadata.insert_one(banking_metadata)
    patch_db.user.insert_one({"user_id": "1", "role": "ptt-user"})

    # When
    assert_that(
        calling(banking_service.banking_create_presigned_url).with_args(
            str(client_id), "********-TFN-Banking.xlsx", "1"
        ),
        raises(ServiceException, "No action is allowed since the file is authorised."),
    )


@patch("flaskr.services.banking_service.call_lambda", MagicMock(return_value=None))
@freeze_time("May 5th 2022")
@pytest.mark.parametrize(
    "file_name,content_type",
    [
        ("********-TFN-Banking.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
        ("********-TFN-Banking.xls", "application/vnd.ms-excel"),
        ("********-TFN-Banking.csv", "text/csv"),
    ],
)
def test_banking_create_one(flask_client, patch_db, file_name, content_type):
    # Given
    file_id = "test-id"
    sftp = False
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1", "is_disabled": None, "company_alias": "TFN"})
    client_id = basic_info.inserted_id
    user_id = "1"
    sftp_key = ""
    # When
    with patch("flaskr.services.banking_service.head_object", MagicMock(return_value={"ContentType": content_type})):
        banking_service.banking_create(str(client_id), file_name, file_id, sftp, user_id, sftp_key)
    # Then
    banking_metadata = patch_db.banking_metadata.find_one({"client_id": client_id})
    assert banking_metadata["banking_files"][0]["file_name"] == file_name


@freeze_time("May 5th 2022")
def test_banking_create_invalid_file_date(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1", "is_disabled": None, "company_alias": "TFN"})
    client_id = basic_info.inserted_id
    user_id = "1"
    # When
    assert_that(
        calling(banking_service.banking_create).with_args(
            str(client_id), "********-TFN-Banking.xlsx", "test-id", False, user_id, ""
        ),
        raises(
            ServiceException,
            re.escape(f"File date cannot be older than {current_app.config['FILE_DATE_AGE_IN_MONTHS']} month(s)."),
        ),
    )


@freeze_time("May 5th 2022")
@patch("flaskr.services.banking_service.delete_object", MagicMock())
@patch("flaskr.services.banking_service.head_object", MagicMock(return_value={"ContentType": "application/pdf"}))
def test_banking_create_invalid(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1", "is_disabled": None, "company_alias": "TFN"})
    client_id = basic_info.inserted_id
    user_id = "1"
    # When
    assert_that(
        calling(banking_service.banking_create).with_args(
            str(client_id), "********-TFN-Banking.pdf", "test-id", False, user_id, ""
        ),
        raises(ServiceException, "File format not supported, use xls, xlsx or csv files"),
    )


@pytest.mark.parametrize(
    "invalid_file_name",
    [
        "********Document1Banking.xlsx",
        "********Document1-Banking.xlsx",
    ],
)
def test_banking_create_invalid_file_name(flask_client, patch_db, invalid_file_name):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1", "is_disabled": None, "company_alias": "TFN"})
    client_id = basic_info.inserted_id
    user_id = "1"
    # When
    assert_that(
        calling(banking_service.banking_create).with_args(
            str(client_id), invalid_file_name, "test-id", False, user_id, ""
        ),
        raises(
            ServiceException,
            re.escape("Invalid file name"),
        ),
    )


def test_banking_create_invalid_file_date_format(flask_client, patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1", "is_disabled": None, "company_alias": "TFN"})
    client_id = basic_info.inserted_id
    user_id = "1"
    # When
    assert_that(
        calling(banking_service.banking_create).with_args(
            str(client_id), "********-TFN-Banking.xlsx", "test-id", False, user_id, ""
        ),
        raises(
            ServiceException,
            re.escape("Invalid file name, failed to extract file date."),
        ),
    )


def test_banking_create_invalid_client(flask_client, patch_db):
    # Given
    invalid_client_id = "12257912c51e741b53957643"
    # When
    assert_that(
        calling(banking_service.banking_create).with_args(
            invalid_client_id, "********-Document1.xls", "test-id", False, "1", ""
        ),
        raises(ServiceException, "client not found"),
    )


@patch("flaskr.services.banking_service.call_lambda", MagicMock(return_value=None))
@pytest.mark.parametrize(
    "data, output_data",
    [
        ({"notes": "string", "bankingId": "6202181f1eed04813999f1e7"}, {"notes": "string"}),
        ({"status": "string1", "bankingId": "6202181f1eed04813999f1e7"}, {"status": "string1"}),
        (
            {"assignedTo": "62023bc91eed04813999f20f", "bankingId": "6202181f1eed04813999f1e7"},
            {"assigned_to": "62023bc91eed04813999f20f"},
        ),
        (
            {"notes": "string", "status": "string1", "bankingId": "6202181f1eed04813999f1e7"},
            {"notes": "string", "status": "string1"},
        ),
        (
            {
                "notes": "string",
                "status": "string1",
                "assignedTo": "62023bc91eed04813999f20f",
                "bankingId": "6202181f1eed04813999f1e7",
            },
            {"notes": "string", "status": "string1", "assigned_to": "62023bc91eed04813999f20f"},
        ),
    ],
)
def test_update_banking_metadata_success(flask_client, patch_db, data, output_data):
    # Given
    banking_id = patch_db.banking_metadata.insert_one(
        {
            "status": "Submitted",
            "client_id": "1",
            "updated_at": datetime(2012, 1, 14),
            "banking_files": [
                {
                    "status": "Submitted",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.now(),
                    "file_id": "628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748",
                    "file_date": "2022-01-21",
                    "item_count": 14,
                    "deposit": 45920,
                }
            ],
            "created_at": datetime(2012, 1, 14),
        }
    ).inserted_id

    data = {
        "notes": data.get("notes"),
        "status": data.get("status"),
        "assignedTo": data.get("assignedTo"),
        "bankingId": str(banking_id),
    }
    # When
    banking_service.update_banking_metadata(data, "1")

    # Then
    metadata = patch_db.banking_metadata.find_one({"_id": banking_id})
    for key, value in output_data.items():
        assert (key, value) in metadata.items()


@pytest.mark.parametrize(
    "invalid_data",
    [
        {"notes": 123, "bankingId": "6202181f1eed04813999f1e7"},
        {"status": 123, "bankingId": "6202181f1eed04813999f1e7"},
        {"assignedTo": 12334, "bankingId": "6202181f1eed04813999f1e7"},
        {"status": 123, "notes": "string", "bankingId": "6202181f1eed04813999f1e7"},
        {"status": 123, "notes": "string", "bankingId": "6202181f1eed04813999f1e7"},
    ],
)
def test_update_banking_metadata_validation_error(flask_client, patch_db, invalid_data):
    # Given
    data = invalid_data

    # Then
    assert_that(calling(banking_service.update_banking_metadata).with_args(data, "1"), raises(ValidationError))


@pytest.mark.parametrize(
    "input_banking_files, output_banking_files",
    [
        (
            [
                {
                    "fileId": "111258412241665569",
                    "fileName": "string",
                    "status": "string",
                    "items": {
                        "GBP": 18,
                        "INR": 1,
                        "USD": 5,
                    },
                    "deposit": {"GBP": 2500.0, "INR": 7000.0, "USD": 3500.0},
                    "submittedDate": "2021-08-10T18:30:00.000+00:00",
                    "notes": "string",
                }
            ],
            [
                {
                    "submittedDate": "2021-08-10T18:30:00",
                    "fileId": "111258412241665569",
                    "fileName": "string",
                    "status": "string",
                    "items": {"GBP": 18, "USD": 5, "INR": 1},
                    "deposit": {"GBP": 2500.0, "USD": 3500.0, "INR": 7000.0},
                    "notes": "string",
                }
            ],
        ),
        (
            [
                {
                    "fileId": "111258412241665569",
                    "fileName": "string",
                    "status": "string",
                    "submittedDate": "2021-08-10T18:30:00.000+00:00",
                    "notes": "string",
                }
            ],
            [
                {
                    "submittedDate": "2021-08-10T18:30:00",
                    "fileId": "111258412241665569",
                    "fileName": "string",
                    "status": "string",
                    "notes": "string",
                }
            ],
        ),
    ],
)
def test_banking_details(flask_client, patch_db, input_banking_files, output_banking_files):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"c_id": "1", "full_name": "ABC", "friendly_name": "edstem"})
    client_id = basic_info.inserted_id
    client_details = BankingMetadataSchema().load(
        {
            "clientId": client_id,
            "clientName": "ABC",
            "createdAt": "2020-05-19T18:30:00.000+00:00",
            "notes": "string",
            "status": "string",
            "updatedAt": "2020-08-24T18:30:00.000+00:00",
            "bankingFiles": input_banking_files,
        }
    )
    meta_data = patch_db.banking_metadata.insert_one(client_details)

    # When
    result = banking_service.get_banking_details.__wrapped__(banking_service, meta_data.inserted_id)

    # Then
    assert result == {
        "_id": str(meta_data.inserted_id),
        "clientId": str(client_id),
        "clientName": "ABC",
        "friendlyName": "edstem",
        "createdAt": "2020-05-19T18:30:00",
        "notes": "string",
        "status": "string",
        "updatedAt": "2020-08-24T18:30:00",
        "bankingFiles": output_banking_files,
    }


def test_banking_id_validation_error(flask_client):
    # Given
    data = "123"

    # Then
    assert_that(
        calling(banking_service.get_banking_details.__wrapped__).with_args(banking_service, data),
        raises(ServiceException),
    )


@pytest.mark.parametrize(
    "input,sort_key,output",
    [
        ([], "", {"content": [], "numberOfElements": 0, "pageNumber": 1, "totalElements": 0, "totalPages": 0}),
        (
            [
                {
                    "_id": ObjectId("62160dcda883a1dc69096729"),
                    "banking_id": ObjectId("6214b765e359368a84c3d544"),
                    "file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60",
                    "booking_ref": "456",
                    "booking_date": "2022-01-31T18:30:00.000+00:00",
                    "payment_date": "2022-01-31T18:30:00.000+00:00",
                    "currency_code": "EUR",
                    "amount": 9000.0,
                    "customer_type": "abcd",
                    "departure_date": "2021-06-08T18:30:00.000+00:00",
                    "return_date": "2021-08-12T18:30:00.000+00:00",
                    "deleted": False,
                    "client_id": "1",
                    "status": "Reinstated",
                },
                {
                    "_id": ObjectId("62160dcda883a1dc69096730"),
                    "banking_id": ObjectId("6214b765e359368a84c3d544"),
                    "file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60",
                    "booking_ref": "123",
                    "booking_date": "2022-01-31T18:30:00.000+00:00",
                    "payment_date": "2022-01-31T18:30:00.000+00:00",
                    "currency_code": "EUR",
                    "amount": 7000.0,
                    "customer_type": "abcd",
                    "departure_date": "2021-06-08T18:30:00.000+00:00",
                    "return_date": "2021-08-12T18:30:00.000+00:00",
                    "deleted": False,
                    "client_id": "1",
                },
            ],
            "booking_ref",
            {
                "content": [
                    {
                        "_id": "62160dcda883a1dc69096730",
                        "bookingDate": "2022-01-31T18:30:00.000+00:00",
                        "paymentDate": "2022-01-31T18:30:00.000+00:00",
                        "customerType": "abcd",
                        "amount": 7000.0,
                        "bookingRef": "123",
                        "departureDate": "2021-06-08T18:30:00.000+00:00",
                        "currencyCode": "EUR",
                        "returnDate": "2021-08-12T18:30:00.000+00:00",
                    },
                    {
                        "_id": "62160dcda883a1dc69096729",
                        "bookingDate": "2022-01-31T18:30:00.000+00:00",
                        "paymentDate": "2022-01-31T18:30:00.000+00:00",
                        "customerType": "abcd",
                        "amount": 9000.0,
                        "bookingRef": "456",
                        "departureDate": "2021-06-08T18:30:00.000+00:00",
                        "currencyCode": "EUR",
                        "returnDate": "2021-08-12T18:30:00.000+00:00",
                        "status": "Reinstated",
                    },
                ],
                "pageNumber": 1,
                "numberOfElements": 2,
                "totalElements": 2,
                "totalPages": 1,
            },
        ),
        (
            [
                {
                    "_id": ObjectId("62160dcda883a1dc69096729"),
                    "banking_id": ObjectId("6214b765e359368a84c3d544"),
                    "file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60",
                    "booking_ref": "123",
                    "booking_date": "2022-01-31T18:30:00.000+00:00",
                    "payment_date": "2022-01-31T18:30:00.000+00:00",
                    "currency_code": "GBP",
                    "amount": 9000.0,
                    "customer_type": "abcd",
                    "departure_date": "2021-06-08T18:30:00.000+00:00",
                    "return_date": "2021-08-12T18:30:00.000+00:00",
                    "deleted": True,
                    "client_id": "1",
                    "status": "Cancelled",
                },
                {
                    "_id": ObjectId("62160dcda883a1dc69096730"),
                    "banking_id": ObjectId("6214b765e359368a84c3d544"),
                    "file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60",
                    "booking_ref": "123",
                    "booking_date": "2022-01-31T18:30:00.000+00:00",
                    "payment_date": "2022-01-31T18:30:00.000+00:00",
                    "currency_code": "EUR",
                    "amount": 7000.0,
                    "customer_type": "abcd",
                    "departure_date": "2021-06-08T18:30:00.000+00:00",
                    "return_date": "2021-08-12T18:30:00.000+00:00",
                    "deleted": False,
                    "client_id": "1",
                },
            ],
            "currency_code",
            {
                "content": [
                    {
                        "_id": "62160dcda883a1dc69096730",
                        "bookingDate": "2022-01-31T18:30:00.000+00:00",
                        "paymentDate": "2022-01-31T18:30:00.000+00:00",
                        "customerType": "abcd",
                        "amount": 7000.0,
                        "bookingRef": "123",
                        "departureDate": "2021-06-08T18:30:00.000+00:00",
                        "currencyCode": "EUR",
                        "returnDate": "2021-08-12T18:30:00.000+00:00",
                    },
                    {
                        "_id": "62160dcda883a1dc69096729",
                        "bookingDate": "2022-01-31T18:30:00.000+00:00",
                        "paymentDate": "2022-01-31T18:30:00.000+00:00",
                        "customerType": "abcd",
                        "amount": 9000.0,
                        "bookingRef": "123",
                        "departureDate": "2021-06-08T18:30:00.000+00:00",
                        "currencyCode": "GBP",
                        "returnDate": "2021-08-12T18:30:00.000+00:00",
                        "status": "Cancelled",
                    },
                ],
                "pageNumber": 1,
                "numberOfElements": 2,
                "totalElements": 2,
                "totalPages": 1,
            },
        ),
        (
            [
                {
                    "_id": ObjectId("62160dcda883a1dc69096729"),
                    "banking_id": ObjectId("6214b765e359368a84c3d544"),
                    "file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60",
                    "booking_ref": "123",
                    "booking_date": "2022-01-31T18:30:00.000+00:00",
                    "payment_date": "2022-01-31T18:30:00.000+00:00",
                    "currency_code": "EUR",
                    "amount": 9000.0,
                    "customer_type": "efg",
                    "departure_date": "2021-06-08T18:30:00.000+00:00",
                    "return_date": "2021-08-12T18:30:00.000+00:00",
                    "deleted": False,
                    "client_id": "1",
                },
                {
                    "_id": ObjectId("62160dcda883a1dc69096730"),
                    "banking_id": ObjectId("6214b765e359368a84c3d544"),
                    "file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60",
                    "booking_ref": "123",
                    "booking_date": "2022-01-31T18:30:00.000+00:00",
                    "payment_date": "2022-01-31T18:30:00.000+00:00",
                    "currency_code": "EUR",
                    "amount": 7000.0,
                    "customer_type": "abcd",
                    "departure_date": "2021-06-08T18:30:00.000+00:00",
                    "return_date": "2021-08-12T18:30:00.000+00:00",
                    "deleted": False,
                    "client_id": "1",
                },
            ],
            "customer_type",
            {
                "content": [
                    {
                        "_id": "62160dcda883a1dc69096730",
                        "bookingDate": "2022-01-31T18:30:00.000+00:00",
                        "paymentDate": "2022-01-31T18:30:00.000+00:00",
                        "customerType": "abcd",
                        "amount": 7000.0,
                        "bookingRef": "123",
                        "departureDate": "2021-06-08T18:30:00.000+00:00",
                        "currencyCode": "EUR",
                        "returnDate": "2021-08-12T18:30:00.000+00:00",
                    },
                    {
                        "_id": "62160dcda883a1dc69096729",
                        "bookingDate": "2022-01-31T18:30:00.000+00:00",
                        "paymentDate": "2022-01-31T18:30:00.000+00:00",
                        "customerType": "efg",
                        "amount": 9000.0,
                        "bookingRef": "123",
                        "departureDate": "2021-06-08T18:30:00.000+00:00",
                        "currencyCode": "EUR",
                        "returnDate": "2021-08-12T18:30:00.000+00:00",
                    },
                ],
                "pageNumber": 1,
                "numberOfElements": 2,
                "totalElements": 2,
                "totalPages": 1,
            },
        ),
        (
            [
                {
                    "_id": ObjectId("62160dcda883a1dc69096729"),
                    "banking_id": ObjectId("6214b765e359368a84c3d544"),
                    "file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60",
                    "booking_ref": "123",
                    "booking_date": "2022-01-31T18:30:00.000+00:00",
                    "payment_date": "2022-01-31T18:30:00.000+00:00",
                    "currency_code": "EUR",
                    "amount": 9000.0,
                    "customer_type": "abcd",
                    "departure_date": "2021-06-08T18:30:00.000+00:00",
                    "return_date": "2021-08-12T18:30:00.000+00:00",
                    "deleted": False,
                    "client_id": "1",
                },
                {
                    "_id": ObjectId("62160dcda883a1dc69096730"),
                    "banking_id": ObjectId("6214b765e359368a84c3d544"),
                    "file_id": "********-2ee3-416e-9874-35953f3a7a6d",
                    "booking_ref": "123",
                    "booking_date": "2022-01-31T18:30:00.000+00:00",
                    "payment_date": "2022-01-31T18:30:00.000+00:00",
                    "currency_code": "EUR",
                    "amount": 7000.0,
                    "customer_type": "abcd",
                    "departure_date": "2021-06-08T18:30:00.000+00:00",
                    "return_date": "2021-08-12T18:30:00.000+00:00",
                    "deleted": False,
                    "client_id": "1",
                },
            ],
            "amount",
            {
                "content": [
                    {
                        "_id": "62160dcda883a1dc69096729",
                        "bookingDate": "2022-01-31T18:30:00.000+00:00",
                        "paymentDate": "2022-01-31T18:30:00.000+00:00",
                        "customerType": "abcd",
                        "amount": 9000.0,
                        "bookingRef": "123",
                        "departureDate": "2021-06-08T18:30:00.000+00:00",
                        "currencyCode": "EUR",
                        "returnDate": "2021-08-12T18:30:00.000+00:00",
                    },
                ],
                "pageNumber": 1,
                "numberOfElements": 1,
                "totalElements": 1,
                "totalPages": 1,
            },
        ),
    ],
)
def test_banking_payments(patch_db, input, sort_key, output):
    # Given
    query = ()

    patch_db.banking_metadata.insert_one(
        {
            "_id": ObjectId("6214b765e359368a84c3d544"),
            "client_id": "1",
            "banking_files": [
                {"file_id": "********-2ee3-416e-9874-35953f3a7a6d"},
                {"file_id": "6fb2e7e3-296b-42d3-a24e-2bd879856d60"},
            ],
        }
    )
    if input:
        patch_db.banking_file_details.insert_many(input)

    # When
    result = banking_service.get_payments(("6214b765e359368a84c3d544"), query, "1", "10", sort_key, 1)

    # Then
    assert result == output


def test_payment_banking_id_validation_error(flask_client):
    # Given
    data = "123"
    # Then
    assert_that(
        calling(banking_service.get_banking_details.__wrapped__).with_args(banking_service, data),
        raises(ServiceException),
    )


@pytest.mark.parametrize(
    "status, output_status,",
    [
        (
            ["Resolved"],
            "Resolved",
        ),
        (
            ["Resolved", "Unresolved"],
            "Unresolved",
        ),
        (
            ["Unresolved"],
            "Unresolved",
        ),
    ],
)
@patch.object(
    AuthService,
    "ptt_users_list",
    MagicMock(
        return_value=[
            {
                "userId": "cf0142a5-7475-4910-9321-40c323d7a876",
                "email": "<EMAIL>",
                "name": "test",
            }
        ]
    ),
)
def test_banking_anomalies(patch_db, monkeypatch, status, output_status):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"c_id": "CR7", "full_name": "abc", "friendly_name": "a"})
    client_id = basic_info.inserted_id
    query = ()
    banking_id = "620245de6dda372675663e05"
    patch_db.banking_metadata.insert_one(
        {
            "_id": ObjectId(banking_id),
            "client_id": client_id,
            "banking_files": [
                {
                    "file_date": "2022-05-29",
                    "file_id": "1234",
                }
            ],
        }
    )
    monkeypatch.setattr(
        patch_db.anomaly_banking,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "metadata": [{"total": 2, "page": 1}],
                "data": [
                    {
                        "status": status,
                        "bookingRef": "977957",
                        "anomalyIds": ["621bf4f49bed97189d98ddcd", "621bf4f49bed97189d98ddce"],
                        "anomalyType": "Funds in Trust Exceed/less than Total Booking Value",
                        "leadPassenger": "Lady Emma Monson",
                        "bookingDate": "2022-11-05",
                        "dateOfTravel": "2022-04-01",
                        "dateOfReturn": "2022-07-06",
                        "balanceInTrust": -100,
                        "currency_code": ["GBP", "USD"],
                        "symbol": ["£", "$"],
                        "clientId": client_id,
                        "friendlyName": "a",
                        "count": 2,
                        "modifiedBy": "cf0142a5-7475-4910-9321-40c323d7a876",
                    },
                    {
                        "status": status,
                        "bookingRef": "977957",
                        "anomalyIds": ["621bf4f49bed97189d98ddcf"],
                        "anomalyType": "Claim Too Early For Departure Date",
                        "leadPassenger": "Lady Emma Monson",
                        "bookingDate": "2022-11-05",
                        "dateOfTravel": "2022-04-01",
                        "dateOfReturn": "2022-07-06",
                        "balanceInTrust": -100,
                        "currency_code": ["GBP", "USD"],
                        "symbol": ["£", "$"],
                        "clientId": client_id,
                        "friendlyName": "a",
                        "count": 1,
                        "modifiedBy": "cf0142a5-7475-4910-9321-40c323d7a876",
                    },
                ],
            }
        ]
        if args[0][-1] != {"$count": "total_unresolved"}
        else iter([{"total_unresolved": 2}]),
    )
    # When
    result = banking_service.get_banking_anomalies(banking_id, query, "1", "10", "bookingRef", 1)

    # # Then
    assert result == {
        "content": [
            {
                "anomalyIds": ["621bf4f49bed97189d98ddcd", "621bf4f49bed97189d98ddce"],
                "anomalyType": "Funds in Trust Exceed/less than Total Booking Value",
                "balanceInTrust": -100.0,
                "bookingDate": "2022-11-05",
                "bookingRef": "977957",
                "clientId": "CR7",
                "friendlyName": "a",
                "count": 2,
                "currency_code": ["GBP", "USD"],
                "dateOfReturn": "2022-07-06",
                "dateOfTravel": "2022-04-01",
                "leadPassenger": "Lady Emma Monson",
                "modifiedBy": "test",
                "status": output_status,
                "symbol": ["£", "$"],
            },
            {
                "anomalyIds": ["621bf4f49bed97189d98ddcf"],
                "anomalyType": "Claim Too Early For Departure Date",
                "balanceInTrust": -100.0,
                "bookingDate": "2022-11-05",
                "bookingRef": "977957",
                "clientId": "CR7",
                "friendlyName": "a",
                "count": 1,
                "currency_code": ["GBP", "USD"],
                "dateOfReturn": "2022-07-06",
                "dateOfTravel": "2022-04-01",
                "leadPassenger": "Lady Emma Monson",
                "modifiedBy": "test",
                "status": output_status,
                "symbol": ["£", "$"],
            },
        ],
        "anomalyCount": 2,
        "numberOfElements": 2,
        "pageNumber": 1,
        "totalElements": 2,
        "totalPages": 1,
    }


@patch.object(
    BankingService, "_BankingService__handle_banking_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@patch.object(BankingService, "_BankingService__handle_banking_anomaly_recalculation", MagicMock(return_value=None))
@patch("flaskr.services.banking_service.track_opening_closing_balance_changes", MagicMock(return_value=None))
def test_update_banking_transaction_success(flask_client, patch_db):
    # Given
    trust_account = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "type_of_trust_account": trust_account.inserted_id}
    )
    client_id = basic_info.inserted_id
    patch_db.banking_metadata.insert_one(
        {
            "_id": ObjectId("628783cf556173a598ea4322"),
            "status": client_id,
            "client_id": "string",
            "banking_files": [{"file_date": "2022-05-29", "file_id": "1234"}],
        }
    )
    id = patch_db.banking_file_details.insert_one(
        {
            "client_id": client_id,
            "status": "string",
            "banking_id": ObjectId("628783cf556173a598ea4322"),
            "amount": 77,
            "original_amount": 110,
            "file_id": "1234",
        },
    ).inserted_id
    data = {"status": "string", "transactionId": id, "previous_status": "", "amount": 70}

    # When
    banking_service.update_banking_transaction(id, data, "1")

    # Then
    transaction = patch_db.banking_file_details.find_one({"_id": data["transactionId"]})
    assert transaction["status"] == data["status"]


@pytest.mark.parametrize(
    "previous_amount,previous_original_amount ,new_amount,new_original_amount",
    [
        (70, 100, 77, 110),
        (77, 110, 70, 100),
        (70, 100, 70, 100),
    ],
)
@patch.object(
    BankingService, "_BankingService__handle_banking_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@patch.object(BankingService, "_BankingService__handle_banking_anomaly_recalculation", MagicMock(return_value=None))
@patch("flaskr.services.banking_service.track_opening_closing_balance_changes", MagicMock(return_value=None))
def test_update_banking_transaction_success_escrow_client(
    flask_client, patch_db, previous_amount, previous_original_amount, new_amount, new_original_amount
):
    # Given
    trust_account = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "type_of_trust_account": trust_account.inserted_id}
    )
    client_id = basic_info.inserted_id
    patch_db.banking_metadata.insert_one(
        {
            "_id": ObjectId("628783cf556173a598ea4322"),
            "status": "Submitted",
            "client_id": client_id,
            "banking_files": [{"file_date": "2022-05-29", "file_id": "1234"}],
        }
    )
    id = patch_db.banking_file_details.insert_one(
        {
            "client_id": client_id,
            "status": "string",
            "banking_id": ObjectId("628783cf556173a598ea4322"),
            "amount": previous_amount,
            "original_amount": previous_original_amount,
            "file_id": "1234",
        },
    ).inserted_id
    data = {"status": "string", "transactionId": id, "previous_status": "", "amount": new_amount}

    # When
    banking_service.update_banking_transaction(id, data, "1")

    # Then
    transaction = patch_db.banking_file_details.find_one({"_id": data["transactionId"]})
    assert transaction["status"] == data["status"]
    assert transaction["amount"] == new_amount
    assert transaction["original_amount"] == new_original_amount


@pytest.mark.parametrize(
    "previous_transaction,new_transaction,escrow_multiplier",
    [
        (
            {"amount": 50, "original_amount": 100, "booking_date": "2022-04-01"},
            {"amount": 100, "original_amount": 200, "booking_date": "2022-04-01"},
            0.5,
        ),
        (
            {"amount": 100, "original_amount": 200, "booking_date": "2022-04-01"},
            {"amount": 50, "original_amount": 100, "booking_date": "2022-04-01"},
            0.5,
        ),
        (
            {"amount": 100, "original_amount": 200, "booking_date": "2022-04-01"},
            {"amount": 100, "original_amount": 200, "booking_date": "2022-04-01"},
            0.5,
        ),
        (
            {"amount": 50, "original_amount": 100, "booking_date": None},
            {"amount": 100, "original_amount": 200, "booking_date": None},
            0.5,
        ),
        (
            {"amount": 70, "original_amount": 100, "booking_date": "2022-04-02"},
            {"amount": 77, "original_amount": 110, "booking_date": "2022-04-03"},
            0.7,
        ),
        (
            {"amount": 77, "original_amount": 110, "booking_date": "2022-04-02"},
            {"amount": 70, "original_amount": 100, "booking_date": "2022-04-03"},
            0.7,
        ),
        (
            {"amount": 70, "original_amount": 100, "booking_date": "2022-04-02"},
            {"amount": 70, "original_amount": 100, "booking_date": "2022-04-03"},
            0.7,
        ),
        (
            {"amount": 70, "original_amount": 100, "booking_date": None},
            {"amount": 77, "original_amount": 110, "booking_date": None},
            0.7,
        ),
        (
            {"amount": 70, "original_amount": 100, "booking_date": "2022-04-03"},
            {"amount": 77, "original_amount": 110, "booking_date": "2022-04-03"},
            None,
        ),
    ],
)
@patch.object(
    BankingService, "_BankingService__handle_banking_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@patch.object(BankingService, "_BankingService__handle_banking_anomaly_recalculation", MagicMock(return_value=None))
@patch("flaskr.services.banking_service.track_opening_closing_balance_changes", MagicMock(return_value=None))
def test_update_banking_transaction_success_major_travel(
    flask_client, patch_db, previous_transaction, new_transaction, escrow_multiplier
):
    # Given
    trust_account = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "2193", "type_of_trust_account": trust_account.inserted_id}
    )
    client_id = basic_info.inserted_id
    patch_db.banking_metadata.insert_one(
        {
            "_id": ObjectId("628783cf556173a598ea4322"),
            "status": "Submitted",
            "client_id": client_id,
            "banking_files": [{"file_date": "2022-05-29", "file_id": "1234"}],
        }
    )
    id = patch_db.banking_file_details.insert_one(
        {
            "client_id": client_id,
            "status": "string",
            "banking_id": ObjectId("628783cf556173a598ea4322"),
            "amount": previous_transaction["amount"],
            "original_amount": previous_transaction["original_amount"],
            "file_id": "1234",
            "booking_date": previous_transaction["booking_date"],
            "escrow_multiplier": escrow_multiplier,
        },
    ).inserted_id
    data = {
        "status": "string",
        "transactionId": id,
        "previous_status": "",
        "amount": new_transaction["amount"],
        "bookingDate": new_transaction["booking_date"],
    }

    # When
    banking_service.update_banking_transaction(id, data, "1")

    # Then
    transaction = patch_db.banking_file_details.find_one({"_id": data["transactionId"]})
    assert transaction["status"] == data["status"]
    assert transaction["amount"] == new_transaction["amount"]
    assert transaction["original_amount"] == new_transaction["original_amount"]
    assert transaction["booking_date"] == new_transaction["booking_date"]


@patch.object(
    BankingService, "_BankingService__handle_banking_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@pytest.mark.parametrize(
    "invalid_data",
    [
        {"status": 123, "transactionId": "620b75d59b53aa92cd5dbb1"},
    ],
)
def test_update_banking_transaction_validation_error(flask_client, patch_db, invalid_data):
    # Given

    data = invalid_data

    # Then
    assert_that(
        calling(banking_service.update_banking_transaction).with_args(data["transactionId"], data, "1"),
        raises(ValidationError),
    )


@patch.object(
    BankingService, "_BankingService__handle_banking_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@patch.object(BankingService, "_BankingService__handle_banking_anomaly_recalculation", MagicMock(return_value=None))
@patch("flaskr.services.banking_service.track_opening_closing_balance_changes", MagicMock(return_value=None))
@pytest.mark.parametrize(
    "input_booking_date,existing_booking_date",
    [
        ("2022-04-03", "2022-03-01"),
        ("2022-03-01", "2022-04-03"),
        ("2022-04-01", "2022-03-31"),
        ("2022-03-31", "2022-04-01"),
    ],
)
def test_update_banking_transaction_service_exception(
    flask_client, patch_db, input_booking_date, existing_booking_date
):
    # Given

    data = {"bookingDate": input_booking_date}
    trust_account = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("621c4699b29069e5c622ca88"),
            "client_id": "2193",
            "type_of_trust_account": trust_account.inserted_id,
        }
    )
    patch_db.banking_metadata.insert_one(
        {
            "_id": ObjectId("628783cf556173a598ea4322"),
            "status": "Submitted",
            "client_id": ObjectId("621c4699b29069e5c622ca88"),
            "banking_files": [{"file_date": "2022-05-29", "file_id": "1234"}],
        }
    )
    id = patch_db.banking_file_details.insert_one(
        {
            "client_id": ObjectId("621c4699b29069e5c622ca88"),
            "status": "string",
            "banking_id": ObjectId("628783cf556173a598ea4322"),
            "amount": 77,
            "original_amount": 110,
            "file_id": "1234",
            "booking_date": existing_booking_date,
            "escrow_multiplier": 0.5,
        },
    ).inserted_id
    current_app.config["MAJOR_TRAVEL"] = "621c4699b29069e5c622ca88"

    # Then
    assert_that(
        calling(banking_service.update_banking_transaction).with_args(id, data, "1"),
        raises(ServiceException, "Transaction is not editable for the given booking date"),
    )


@freeze_time("Jan 14 2012")
@patch("flaskr.services.banking_service.track_opening_closing_balance_changes", MagicMock(return_value=None))
@pytest.mark.parametrize(
    "existing_transaction, updated_transaction, new_balance, new_total_in_trust , output_banking_metadata, is_banking_transaction_deleted",
    [
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1500,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
            },
            1500,
            5500,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "banking_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "********-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 18, "USD": 5, "INR": 1},
                        "deposit": {"GBP": 2500, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 00, "USD": 00, "INR": 00},
                        "checked_amount": {"GBP": 00, "USD": 00, "INR": 00},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            False,
        ),
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 500,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
            },
            500,
            4500,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "banking_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "********-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 18, "USD": 5, "INR": 1},
                        "deposit": {"GBP": 1500, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 0, "USD": 0, "INR": 0},
                        "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            False,
        ),
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
            },
            1000,
            5000,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "banking_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "********-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 18, "USD": 5, "INR": 1},
                        "deposit": {"GBP": 2000, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 0, "USD": 0, "INR": 0},
                        "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            False,
        ),
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Reinstated",
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Cancelled",
            },
            0,
            4000,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "banking_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "********-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 17, "USD": 5, "INR": 1},
                        "deposit": {"GBP": 1000, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 0, "USD": 0, "INR": 0},
                        "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            True,
        ),
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": True,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Cancelled",
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": True,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Reinstated",
            },
            2000,
            6000,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "banking_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "********-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 19, "USD": 5, "INR": 1},
                        "deposit": {"GBP": 3000, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 0, "USD": 0, "INR": 0},
                        "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            False,
        ),
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Reinstated",
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": False,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 500,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Cancelled",
            },
            0,
            4000,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "banking_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "********-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 17, "USD": 5, "INR": 1},
                        "deposit": {"GBP": 1000, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 0, "USD": 0, "INR": 0},
                        "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            True,
        ),
        (
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": True,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 1000,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Cancelled",
            },
            {
                "_id": ObjectId("623c1a34a93d253120cb3fc1"),
                "client_id": "2",
                "booking_ref": "123",
                "deleted": True,
                "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                "file_id": "1234",
                "currency_code": "GBP",
                "amount": 500,
                "check": "",
                "created_at": datetime(2022, 1, 14),
                "updated_at": datetime(2022, 1, 14),
                "status": "Reinstated",
            },
            1500,
            5500,
            {
                "_id": ObjectId("621c4699b29069e5c611ca88"),
                "updated_at": datetime(2012, 1, 14),
                "client_id": "2",
                "status": "Submitted",
                "banking_files": [
                    {
                        "file_id": "1234",
                        "file_date": "2022-04-10",
                        "status": "Submitted",
                        "file_name": "********-All -claim-mapping02.xlsx",
                        "item_count": {"GBP": 19, "USD": 5, "INR": 1},
                        "deposit": {"GBP": 2500, "USD": 3500, "INR": 7000},
                        "checks": {"GBP": 0, "USD": 0, "INR": 0},
                        "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
                    }
                ],
                "created_at": datetime(2012, 1, 14),
            },
            False,
        ),
    ],
)
def test_handle_banking_transaction_amount_and_status_change(
    patch_db,
    existing_transaction,
    updated_transaction,
    new_balance,
    new_total_in_trust,
    output_banking_metadata,
    is_banking_transaction_deleted,
):
    # Given
    trust_fund = {
        "client_id": "2",
        "booking_ref": "123",
        "balance": 1000,
        "total_in_trust": 5000,
        "total_claimed": 4000,
        "currency_code": ["GBP"],
        "created_at": datetime(2012, 1, 14),
        "updated_at": datetime(2012, 1, 14),
    }
    banking_metadata = {
        "_id": ObjectId("621c4699b29069e5c611ca88"),
        "client_id": "2",
        "status": "Submitted",
        "banking_files": [
            {
                "file_id": "1234",
                "file_date": "2022-04-10",
                "status": "Submitted",
                "file_name": "********-All -claim-mapping02.xlsx",
                "item_count": {"GBP": 18, "USD": 5, "INR": 1},
                "deposit": {"GBP": 2000, "USD": 3500, "INR": 7000},
                "checks": {"GBP": 0, "USD": 0, "INR": 0},
                "checked_amount": {"GBP": 0, "USD": 0, "INR": 0},
            }
        ],
        "created_at": datetime(2012, 1, 14),
        "updated_at": datetime(2012, 1, 14),
    }

    trust_fund_id = patch_db.trust_fund_v2.insert_one(trust_fund).inserted_id
    transaction_id = patch_db.banking_file_details.insert_one(updated_transaction).inserted_id
    banking_metadata_id = patch_db.banking_metadata.insert_one(banking_metadata).inserted_id
    # When
    claim_service = BankingService()
    claim_service._BankingService__handle_banking_transaction_amount_and_status_change(
        existing_transaction, updated_transaction, "2022-05-29", "1234", None
    )

    # Then
    updated_trust_fund = patch_db.trust_fund_v2.find_one({"_id": trust_fund_id})
    updated_banking_metadata = patch_db.banking_metadata.find_one({"_id": banking_metadata_id})
    updated_banking_transaction = patch_db.banking_file_details.find_one({"_id": transaction_id})
    assert (
        abs(
            updated_banking_metadata["banking_files"][-1]["deposit"]["GBP"]
            - output_banking_metadata["banking_files"][-1]["deposit"]["GBP"]
        )
        <= 1
    )

    assert updated_trust_fund["balance"] == new_balance
    assert updated_trust_fund["total_in_trust"] == new_total_in_trust
    assert updated_banking_transaction["deleted"] == is_banking_transaction_deleted


@pytest.mark.parametrize(
    "updated_transaction, existing_transaction, deleted, count",
    [
        (
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
            },
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
            },
            False,
            0,
        ),
        (
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
            },
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-11-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
            },
            True,
            1,
        ),
        (
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
            },
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-11-01",
                "departure_date": "2022-11-01",
                "return_date": "2022-01-01",
                "amount": 500,
            },
            True,
            1,
        ),
        (
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
            },
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-11-01",
                "amount": 500,
            },
            True,
            1,
        ),
        (
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
            },
            {
                "_id": ObjectId("626b5c822267ddf89ddee5d6"),
                "file_id": "test_123",
                "client_id": "1",
                "booking_date": "2022-01-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 1000,
            },
            True,
            1,
        ),
    ],
)
def test_handle_banking_anomaly_recalculation(
    patch_db, flask_client, updated_transaction, existing_transaction, deleted, count
):
    # Given
    mock = Mock()
    patch("flaskr.services.banking_service.call_lambda", mock).start()
    anomaly_id = patch_db.anomaly_banking.insert_one(
        {
            "transaction_id": existing_transaction["_id"],
            "file_id": existing_transaction["file_id"],
            "client_id": existing_transaction["client_id"],
            "deleted": False,
            "anomaly_type": "Test Anomaly",
        }
    ).inserted_id

    # When
    banking_service._BankingService__handle_banking_anomaly_recalculation(
        updated_transaction, existing_transaction, None
    )

    # Then
    anomaly = patch_db.anomaly_banking.find_one({"_id": anomaly_id})
    assert anomaly["deleted"] == deleted
    assert mock.call_count == count


@patch.object(
    BankingService, "_BankingService__handle_banking_transaction_amount_and_status_change", MagicMock(return_value=None)
)
@pytest.mark.parametrize(
    "data, output_data",
    [
        (
            {
                "client_id": ObjectId("62fb53e11a873172a59b2a1e"),
                "booking_ref": "977957",
                "lead_pax": "KV",
                "booking_date": "2020-08-21",
                "departure_date": "2022-03-26",
                "return_date": "2022-04-08",
                "supplier_ref": "A45EX",
                "supplier_names": "Hoseason",
                "total_paid": 11392.0,
                "status": "live",
                "status_reason": "string",
                "amount": 600.0,
                "total_due_to_supplier": 123.0,
                "deleted": False,
            },
            {
                "amount": 600.0,
                "bookingDate": "2020-08-21",
                "bookingRef": "977957",
                "departureDate": "2022-03-26",
                "leadPax": "KV",
                "returnDate": "2022-04-08",
                "status": "live",
                "statusReason": "string",
                "supplierNames": "Hoseason",
                "supplierRef": "A45EX",
                "clientId": "62fb53e11a873172a59b2a1e",
            },
        )
    ],
)
def test_banking_get_transaction_success(flask_client, patch_db, data, output_data):
    # Given
    transaction = patch_db.banking_file_details.insert_one(data)
    transaction_id = transaction.inserted_id
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one({"user_id": user_id, "clients": [ObjectId("62fb53e11a873172a59b2a1e")]})
    trust_type = patch_db.lookup_trust_type.insert_one({"name": "ATOL Standard"})
    patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("62fb53e11a873172a59b2a1e"),
            "client_id": "1",
            "full_name": "abc",
            "friendly_name": "a",
            "type_of_trust_account": trust_type.inserted_id,
        }
    )

    # When
    result = banking_service.banking_get_transaction(transaction_id, user_id)

    # Then
    assert result == output_data


def test_get_uploaded_banking_transaction_success(flask_client, patch_db):
    # Given
    client_id = "62419f08888c2b5ed06235d9"
    banking = patch_db.banking_file_details.insert_one(
        {
            "client_id": ObjectId("62419f08888c2b5ed06235d9"),
            "booking_ref": "977957",
            "lead_pax": "KV",
            "booking_date": "2020-08-21",
            "departure_date": "2022-03-26",
            "return_date": "2022-04-08",
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "total_paid": 11392.0,
            "status": "live",
            "status_reason": "string",
            "amount": 600.0,
            "total_due_to_supplier": 123.0,
            "deleted": False,
        }
    )
    supplier_name = "Hoseason"
    trust_type = patch_db.lookup_trust_type.insert_one({"name": "ATOL Standard"})
    patch_db.client_basic_info.insert_one(
        {"client_id": "1", "full_name": "abc", "friendly_name": "a", "type_of_trust_account": trust_type.inserted_id}
    )

    # When
    result = banking_service.get_uploaded_banking_transaction(supplier_name, client_id)

    # Then
    assert result == {"_id": str(banking.inserted_id)}


def test_get_uploaded_banking_transaction_service_exception(flask_client, patch_db):
    # Given
    supplier_name = "Hoseason"
    client_id = "62419f08888c2b5ed06235d9"

    # Then
    assert_that(
        calling(banking_service.get_uploaded_banking_transaction).with_args(supplier_name, client_id),
        raises(ServiceException, "Transaction not found"),
    )
