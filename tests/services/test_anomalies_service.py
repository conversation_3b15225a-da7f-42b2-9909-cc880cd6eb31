from datetime import datetime
from hamcrest import assert_that, calling, raises
from flaskr.services.anomalies_service import anomaly_service
from flaskr.services.exceptions import ServiceException


def test_banking_anomaly_search_success(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "full_name": "abc", "friendly_name": "edstem"}
    )
    banking_meta_data = patch_db.banking_metadata.insert_one(
        {
            "client_id": client_basic_info.inserted_id,
            "status": "saved",
            "banking_files": [
                {"submitted_date": datetime.fromisoformat("2021-10-02"), "file_date": "2021-10-02", "file_name": "abc"}
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
        }
    )
    patch_db.anomaly_banking.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": client_basic_info.inserted_id,
            "banking_id": banking_meta_data.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda x: [
            {
                "metadata": [{"total": 2, "page": 1}],
                "data": [
                    {
                        "_id": banking_meta_data.inserted_id,
                        "clientId": client_basic_info.inserted_id,
                        "fileDate": "2021-10-02",
                        "fileName": "abc",
                        "clientName": "abc",
                        "friendlyName": "edstem",
                        "count": 1,
                        "file_id": "675552",
                    },
                ],
            }
        ],
    )
    # When
    result = anomaly_service.banking_anomaly_search(
        {
            "clientId": client_basic_info.inserted_id,
            "query": None,
            "fromDate": None,
            "toDate": None,
            "page": 1,
            "size": 4,
        }
    )
    # Then
    assert len(result["content"]) == 1


def test_banking_anomaly_search_query_success(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "full_name": "abc", "friendly_name": "edstem"}
    )
    banking_meta_data = patch_db.banking_metadata.insert_one(
        {
            "client_id": client_basic_info.inserted_id,
            "status": "saved",
            "banking_files": [
                {"submitted_date": datetime.fromisoformat("2021-10-02"), "file_date": "2021-10-02", "file_name": "abc"}
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
        }
    )
    patch_db.anomaly_banking.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": client_basic_info.inserted_id,
            "banking_id": banking_meta_data.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda x: [
            {
                "metadata": [{"total": 2, "page": 1}],
                "data": [
                    {
                        "_id": banking_meta_data.inserted_id,
                        "clientId": client_basic_info.inserted_id,
                        "fileDate": "2021-10-02",
                        "fileName": "abc",
                        "clientName": "abc",
                        "friendlyName": "edstem",
                        "count": 1,
                        "file_id": "675552",
                    },
                ],
            }
        ],
    )
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "2", "full_name": "xyz", "friendly_name": "test"})
    banking_meta_data_1 = patch_db.banking_metadata.insert_one(
        {
            "client_id": basic_info.inserted_id,
            "status": "saved",
            "banking_files": [
                {"submitted_date": datetime.fromisoformat("2021-10-02"), "file_date": "2021-10-02", "file_name": "xyz"}
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
        }
    )
    patch_db.anomaly_banking.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": basic_info.inserted_id,
            "banking_id": banking_meta_data_1.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    # When
    result = anomaly_service.banking_anomaly_search(
        {
            "clientId": client_basic_info.inserted_id,
            "query": "abc",
            "fromDate": None,
            "toDate": None,
            "page": 1,
            "size": 4,
        }
    )

    # Then
    assert len(result["content"]) == 1

    assert result["content"][0]["clientName"] == "abc"


def test_banking_anomaly_search_count_success(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "full_name": "abc", "friendly_name": "a"}
    )
    banking_meta_data = patch_db.banking_metadata.insert_one(
        {
            "client_id": client_basic_info.inserted_id,
            "status": "saved",
            "banking_files": [
                {"submitted_date": datetime.fromisoformat("2021-10-02"), "file_date": "2021-10-02", "file_name": "abc"}
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
        }
    )
    patch_db.anomaly_banking.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": client_basic_info.inserted_id,
            "banking_id": banking_meta_data.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda x: [
            {
                "metadata": [{"total": 2, "page": 1}],
                "data": [
                    {
                        "_id": banking_meta_data.inserted_id,
                        "clientId": client_basic_info.inserted_id,
                        "fileDate": "2021-10-02",
                        "fileName": "abc",
                        "friendlyName": "a",
                        "clientName": "abc",
                        "count": 1,
                        "file_id": "675552",
                    },
                ],
            }
        ],
    )
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "2", "full_name": "xyz", "friendly_name": "x"})
    banking_meta_data_1 = patch_db.banking_metadata.insert_one(
        {
            "client_id": basic_info.inserted_id,
            "status": "saved",
            "banking_files": [
                {"submitted_date": datetime.fromisoformat("2021-10-02"), "file_date": "2021-10-02", "file_name": "xyz"}
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
        }
    )
    patch_db.anomaly_banking.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": basic_info.inserted_id,
            "banking_id": banking_meta_data_1.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Resolved",
        }
    )
    # When
    result = anomaly_service.banking_anomaly_search(
        {
            "clientId": client_basic_info.inserted_id,
            "query": None,
            "fromDate": None,
            "toDate": None,
            "page": 1,
            "size": 4,
        }
    )

    # Then
    assert len(result["content"]) == 1

    assert result["content"][0]["count"] == 1


def test_banking_anomaly_search_date_success(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "full_name": "abc", "friendly_name": "a"}
    )
    banking_meta_data = patch_db.banking_metadata.insert_one(
        {
            "client_id": client_basic_info.inserted_id,
            "status": "saved",
            "banking_files": [
                {"submitted_date": datetime.fromisoformat("2021-10-02"), "file_date": "2021-10-02", "file_name": "abc"}
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
        }
    )
    patch_db.anomaly_banking.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": client_basic_info.inserted_id,
            "banking_id": banking_meta_data.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda x: [
            {
                "metadata": [{"total": 2, "page": 1}],
                "data": [
                    {
                        "_id": banking_meta_data.inserted_id,
                        "clientId": client_basic_info.inserted_id,
                        "fileDate": "2021-10-02",
                        "fileName": "abc",
                        "clientName": "abc",
                        "friendlyName": "a",
                        "count": 1,
                        "file_id": "675552",
                    },
                ],
            }
        ],
    )
    # When
    result = anomaly_service.banking_anomaly_search(
        {
            "clientId": client_basic_info.inserted_id,
            "query": None,
            "fromDate": "2021-10-02",
            "toDate": "2021-10-02",
            "page": 1,
            "size": 4,
        }
    )

    # Then
    assert len(result["content"]) == 1

    assert result["content"][0]["clientId"] == client_basic_info.inserted_id


def test_claims_anomaly_search_success(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "full_name": "abc", "friendly_name": "a"}
    )
    claims_meta_data = patch_db.claims_metadata.insert_one(
        {
            "client_id": client_basic_info.inserted_id,
            "status": "saved",
            "claim_files": [
                {"submitted_date": datetime.fromisoformat("2021-10-02"), "file_date": "2021-10-02", "file_name": "abc"}
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
        }
    )
    patch_db.anomaly_claims.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": client_basic_info.inserted_id,
            "claims_id": claims_meta_data.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda x: [
            {
                "metadata": [{"total": 2, "page": 1}],
                "data": [
                    {
                        "_id": claims_meta_data.inserted_id,
                        "clientId": client_basic_info.inserted_id,
                        "fileDate": "2021-10-02",
                        "fileName": "abc",
                        "clientName": "abc",
                        "friendlyName": "a",
                        "count": 1,
                        "file_id": "675552",
                    },
                ],
            }
        ],
    )
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "2", "full_name": "xyz", "friendly_name": "x"})
    claim_meta_datas = patch_db.claims_metadata.insert_one(
        {
            "client_id": basic_info.inserted_id,
            "status": "Accepted",
            "claim_files": [
                {
                    "claim_total": 1234,
                    "submitted_date": datetime.fromisoformat("2021-10-02"),
                    "file_date": "2022-02-20",
                    "file_name": "def",
                }
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
            "updated_at": datetime.fromisoformat("2021-10-02"),
            "frequency": "Weekly",
        }
    )
    patch_db.anomaly_claims.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": client_basic_info.inserted_id,
            "claims_id": claim_meta_datas.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    basic_info1 = patch_db.client_basic_info.insert_one({"client_id": "3", "full_name": "123", "friendly_name": "1"})
    claim_meta_datas = patch_db.claims_metadata.insert_one(
        {
            "client_id": basic_info1.inserted_id,
            "status": "Submitted",
            "claim_files": [
                {
                    "claim_total": 1234,
                    "submitted_date": datetime.fromisoformat("2022-02-02"),
                    "file_date": "2022-02-20",
                    "file_name": "xyz",
                }
            ],
            "created_at": datetime.fromisoformat("2022-02-02"),
            "updated_at": datetime.fromisoformat("2022-02-02"),
            "frequency": "Weekly",
        }
    )
    patch_db.anomaly_claims.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": basic_info1.inserted_id,
            "claims_id": claim_meta_datas.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )

    # When
    result = anomaly_service.claims_anomaly_search(
        {
            "clientId": client_basic_info.inserted_id,
            "query": None,
            "fromDate": None,
            "toDate": None,
            "page": 1,
            "size": 4,
        }
    )
    # Then
    assert len(result["content"]) == 1


def test_claims_anomaly_search_query_success(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "full_name": "abc", "friendly_name": "a"}
    )
    claims_meta_data = patch_db.claims_metadata.insert_one(
        {
            "client_id": client_basic_info.inserted_id,
            "status": "saved",
            "claim_files": [
                {"submitted_date": datetime.fromisoformat("2021-10-02"), "file_date": "2021-10-02", "file_name": "abc"}
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
        }
    )
    patch_db.anomaly_claims.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": client_basic_info.inserted_id,
            "claims_id": claims_meta_data.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda x: [
            {
                "metadata": [{"total": 2, "page": 1}],
                "data": [
                    {
                        "_id": claims_meta_data.inserted_id,
                        "clientId": client_basic_info.inserted_id,
                        "fileDate": "2021-10-02",
                        "fileName": "abc",
                        "clientName": "abc",
                        "friendlyName": "a",
                        "count": 1,
                        "file_id": "675552",
                    },
                ],
            }
        ],
    )
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "2", "full_name": "xyz", "friendly_name": "x"})
    claim_meta_datas = patch_db.claims_metadata.insert_one(
        {
            "client_id": basic_info.inserted_id,
            "status": "Accepted",
            "claim_files": [
                {
                    "claim_total": 1234,
                    "submitted_date": datetime.fromisoformat("2021-10-02"),
                    "file_date": "2022-02-20",
                    "file_name": "def",
                }
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
            "updated_at": datetime.fromisoformat("2021-10-02"),
            "frequency": "Weekly",
        }
    )
    patch_db.anomaly_claims.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": basic_info.inserted_id,
            "claims_id": claim_meta_datas.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    # When
    result = anomaly_service.claims_anomaly_search(
        {
            "clientId": client_basic_info.inserted_id,
            "query": "abc",
            "fromDate": None,
            "toDate": None,
            "page": 1,
            "size": 4,
        }
    )
    # Then
    assert len(result["content"]) == 1

    assert result["content"][0]["clientName"] == "abc"


def test_claims_anomaly_search_count_success(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "full_name": "abc", "friendly_name": "a"}
    )
    claims_meta_data = patch_db.claims_metadata.insert_one(
        {
            "client_id": client_basic_info.inserted_id,
            "status": "saved",
            "claim_files": [
                {"submitted_date": datetime.fromisoformat("2021-10-02"), "file_date": "2021-10-02", "file_name": "abc"}
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
        }
    )
    patch_db.anomaly_claims.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": client_basic_info.inserted_id,
            "claims_id": claims_meta_data.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda x: [
            {
                "metadata": [{"total": 2, "page": 1}],
                "data": [
                    {
                        "_id": claims_meta_data.inserted_id,
                        "clientId": client_basic_info.inserted_id,
                        "fileDate": "2021-10-02",
                        "fileName": "abc",
                        "clientName": "abc",
                        "friendlyName": "a",
                        "count": 1,
                        "file_id": "675552",
                    },
                ],
            }
        ],
    )

    basic_info = patch_db.client_basic_info.insert_one({"client_id": "2", "full_name": "xyz", "friendly_name": "x"})
    claim_meta_datas = patch_db.claims_metadata.insert_one(
        {
            "client_id": basic_info.inserted_id,
            "status": "Accepted",
            "claim_files": [
                {
                    "claim_total": 1234,
                    "submitted_date": datetime.fromisoformat("2021-10-02"),
                    "file_date": "2022-02-20",
                    "file_name": "def",
                }
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
            "updated_at": datetime.fromisoformat("2021-10-02"),
            "frequency": "Weekly",
        }
    )
    patch_db.anomaly_claims.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": basic_info.inserted_id,
            "claims_id": claim_meta_datas.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Resolved",
        }
    )
    basic_info1 = patch_db.client_basic_info.insert_one({"client_id": "3", "full_name": "123", "friendly_name": "1"})
    claim_meta_datas = patch_db.claims_metadata.insert_one(
        {
            "client_id": basic_info1.inserted_id,
            "status": "Submitted",
            "claim_files": [
                {
                    "claim_total": 1234,
                    "submitted_date": datetime.fromisoformat("2022-02-02"),
                    "file_date": "2022-02-20",
                    "file_name": "xyz",
                }
            ],
            "created_at": datetime.fromisoformat("2022-02-02"),
            "updated_at": datetime.fromisoformat("2022-02-02"),
            "frequency": "Weekly",
        }
    )
    patch_db.anomaly_claims.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": basic_info1.inserted_id,
            "claims_id": claim_meta_datas.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    # When
    result = anomaly_service.claims_anomaly_search(
        {"clientId": basic_info1.inserted_id, "query": None, "fromDate": None, "toDate": None, "page": 1, "size": 4}
    )

    # Then
    assert len(result["content"]) == 1

    assert result["content"][0]["count"] == 1


def test_claims_anomaly_search_date_success(flask_client, patch_db, monkeypatch):
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "full_name": "abc", "friendly_name": "a"}
    )
    claims_meta_data = patch_db.claims_metadata.insert_one(
        {
            "client_id": client_basic_info.inserted_id,
            "status": "saved",
            "claim_files": [
                {"submitted_date": datetime.fromisoformat("2021-10-02"), "file_date": "2021-10-02", "file_name": "abc"}
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
        }
    )
    patch_db.anomaly_claims.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": client_basic_info.inserted_id,
            "claims_id": claims_meta_data.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    monkeypatch.setattr(
        patch_db.claims_metadata,
        "aggregate",
        lambda x: [
            {
                "metadata": [{"total": 2, "page": 1}],
                "data": [
                    {
                        "_id": claims_meta_data.inserted_id,
                        "clientId": client_basic_info.inserted_id,
                        "fileDate": "2021-10-02",
                        "fileName": "abc",
                        "clientName": "abc",
                        "friendlyName": "a",
                        "count": 1,
                        "file_id": "675552",
                    },
                ],
            }
        ],
    )

    basic_info = patch_db.client_basic_info.insert_one({"client_id": "2", "full_name": "xyz", "friendly_name": "x"})
    claim_meta_datas = patch_db.claims_metadata.insert_one(
        {
            "client_id": basic_info.inserted_id,
            "status": "Accepted",
            "claim_files": [
                {
                    "claim_total": 1234,
                    "submitted_date": datetime.fromisoformat("2021-10-02"),
                    "file_date": "2022-02-20",
                    "file_name": "def",
                }
            ],
            "created_at": datetime.fromisoformat("2021-10-02"),
            "updated_at": datetime.fromisoformat("2021-10-02"),
            "frequency": "Weekly",
        }
    )
    patch_db.anomaly_claims.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": basic_info.inserted_id,
            "claims_id": claim_meta_datas.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Resolved",
        }
    )
    basic_info1 = patch_db.client_basic_info.insert_one({"client_id": "3", "full_name": "123", "friendly_name": "1"})
    claim_meta_datas = patch_db.claims_metadata.insert_one(
        {
            "client_id": basic_info1.inserted_id,
            "status": "Submitted",
            "claim_files": [
                {
                    "claim_total": 1234,
                    "submitted_date": datetime.fromisoformat("2022-02-02"),
                    "file_date": "2022-02-20",
                    "file_name": "xyz",
                }
            ],
            "created_at": datetime.fromisoformat("2022-02-02"),
            "updated_at": datetime.fromisoformat("2022-02-02"),
            "frequency": "Weekly",
        }
    )
    patch_db.anomaly_claims.insert_one(
        {
            "booking_ref": "977957",
            "transaction_id": "61fb95ede377dce36a47edc5",
            "client_id": basic_info1.inserted_id,
            "claims_id": claim_meta_datas.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Negative funds in Trust",
            "anomaly_id": "61f24f0bb77c44b304ac9798",
            "status": "Unresolved",
        }
    )
    # When
    result = anomaly_service.claims_anomaly_search(
        {
            "clientId": client_basic_info.inserted_id,
            "query": None,
            "fromDate": "2022-02-02",
            "toDate": "2022-02-02",
            "page": 1,
            "size": 4,
        }
    )

    # Then
    assert len(result["content"]) == 1

    assert result["content"][0]["clientId"] == client_basic_info.inserted_id


def test_anomaly_banking_status(flask_client, patch_db):
    # Given
    anomaly_id_1 = patch_db.anomaly_banking.insert_one({"status": ""}).inserted_id
    anomaly_id_2 = patch_db.anomaly_banking.insert_one({"status": ""}).inserted_id

    # When
    anomaly_service.anomaly_update_status(
        "banking", {"anomalyIds": [anomaly_id_1, anomaly_id_2], "status": "Resolved"}, ""
    )

    # Then
    anomaly_banking = patch_db.anomaly_banking.find({"_id": {"$in": [anomaly_id_1, anomaly_id_2]}})
    for item in anomaly_banking:
        assert item["status"] == "Resolved"


def test_anomaly_claims_status(flask_client, patch_db):
    # Given
    anomaly_id_1 = patch_db.anomaly_claims.insert_one({"status": ""}).inserted_id
    anomaly_id_2 = patch_db.anomaly_claims.insert_one({"status": ""}).inserted_id

    # When
    anomaly_service.anomaly_update_status(
        "claim", {"anomalyIds": [anomaly_id_1, anomaly_id_2], "status": "Resolved"}, ""
    )

    # Then
    anomaly_claims = patch_db.anomaly_claims.find({"_id": {"$in": [anomaly_id_1, anomaly_id_2]}})
    for item in anomaly_claims:
        assert item["status"] == "Resolved"


def test_update_anomaly_invalid_file_error(flask_client, patch_db):
    # Given
    anomaly_id = patch_db.anomaly_claims.insert_one({"status": ""}).inserted_id

    # Then
    assert_that(
        calling(anomaly_service.anomaly_update_status).with_args(
            "", {"anomalyIds": [anomaly_id], "status": "done"}, ""
        ),
        raises(ServiceException, "Invalid file catagory"),
    )


def test_update_anomaly_invalid_status_datatype_error(flask_client, patch_db):
    # Given
    anomaly_id = patch_db.anomaly_claims.insert_one({"status": ""}).inserted_id

    # Then
    assert_that(
        calling(anomaly_service.anomaly_update_status).with_args(
            "claim", {"anomalyIds": anomaly_id, "status": 123}, ""
        ),
        raises(ServiceException, "status should be a valid string"),
    )


def test_update_anomaly_invalid_id(flask_client, patch_db):
    # Given
    patch_db.anomaly_claims.insert_one({"status": ""}).inserted_id

    # Then
    assert_that(
        calling(anomaly_service.anomaly_update_status).with_args("claim", {"anomalyIds": "1234", "status": "done"}, ""),
        raises(ServiceException, "invalid AnomalyId"),
    )
