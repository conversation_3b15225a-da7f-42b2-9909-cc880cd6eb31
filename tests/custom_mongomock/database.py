from mongomock.database import Database
from tests.custom_mongomock.collection import CustomCollection
from mongomock import read_preferences
from mongomock import codec_options as mongomock_codec_options


class CustomDatabase(Database):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def get_collection(self, name, codec_options=None, read_preference=None, write_concern=None, read_concern=None):
        if read_preference is not None:
            read_preferences.ensure_read_preference_type("read_preference", read_preference)
        mongomock_codec_options.is_supported(codec_options)
        try:
            return self._collection_accesses[name].with_options(
                codec_options=codec_options or self._codec_options,
                read_preference=read_preference or self.read_preference,
                read_concern=read_concern,
                write_concern=write_concern,
            )
        except KeyError:
            self._ensure_valid_collection_name(name)
            collection = self._collection_accesses[name] = CustomCollection(
                self,
                name=name,
                read_concern=read_concern,
                write_concern=write_concern,
                read_preference=read_preference or self.read_preference,
                codec_options=codec_options or self._codec_options,
                _db_store=self._store,
            )
            return collection

    def drop_collection(self, name_or_collection, session=None):
        if isinstance(name_or_collection, CustomCollection):
            name_or_collection._store.drop()
        else:
            self._store[name_or_collection].drop()
