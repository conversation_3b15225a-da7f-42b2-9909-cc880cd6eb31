from mock import MagicMock
from mongomock import MongoClient
from tests.custom_mongomock.database import CustomDatabase


class CustomMongoClient(MongoClient):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def start_session(self, causal_consistency=True, default_transaction_options=None):
        """Start a logical session."""
        return MagicMock(return_value=None)

    def drop_database(self, name_or_db):
        def drop_collections_for_db(_db):
            for col_name in _db.list_collection_names():
                _db.drop_collection(col_name)

        if isinstance(name_or_db, CustomDatabase):
            db = next(db for db in self._database_accesses.values() if db is name_or_db)
            if db:
                drop_collections_for_db(db)

        elif name_or_db in self._store:
            db = self.get_database(name_or_db)
            drop_collections_for_db(db)

    def get_database(self, name=None, codec_options=None, read_preference=None, write_concern=None):
        if name is None:
            db = self.get_default_database(
                codec_options=codec_options,
                read_preference=read_preference,
                write_concern=write_concern,
            )
        else:
            db = self._database_accesses.get(name)
        if db is None:
            db_store = self._store[name]
            db = self._database_accesses[name] = CustomDatabase(
                self,
                name,
                read_preference=read_preference or self.read_preference,
                codec_options=codec_options or self._codec_options,
                _store=db_store,
            )
        return db
