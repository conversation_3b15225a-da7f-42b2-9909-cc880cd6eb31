import pytest
import boto3
from moto import mock_cognitoidp
import flaskr.models
from flaskr import create_app
from tests.custom_mongomock.mongo_client import CustomMongoClient


@pytest.fixture
@mock_cognitoidp
def cognito_client():
    conn = boto3.client("cognito-idp", "eu-west-2")
    return conn


@pytest.fixture(scope="function")
def flask_client():
    app = create_app("test")
    with app.test_client() as client:
        ctx = app.app_context()
        ctx.push()
        yield client
        ctx.pop()


@pytest.fixture(scope="function")
def flask_app():
    app = create_app("test")
    with app.app_context():
        yield app


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(flaskr.models, "mongo", CustomMongoClient())
    yield flaskr.models.get_db()
