import json


class ClientCheckInfoRequestBuilder:
    request = """{
        "checkName": "string",
        "shortName": "string",
        "relatedElement": "string",
        "description": "string"
    }"""

    update_request = """{
        "checkName": "string1",
        "shortName": "string1",
        "relatedElement": "string1",
        "description": "string1"
    }"""

    def __init__(self):
        self.client_check_info_request = json.loads(self.request)

    @staticmethod
    def a_client_check_info_request():
        return ClientCheckInfoRequestBuilder()

    def with_check_name(self, check_name):
        self.client_check_info_request["checkName"] = check_name
        return self

    def with_short_name(self, short_name):
        self.client_check_info_request["shortName"] = short_name
        return self

    def with_related_element(self, related_element):
        self.client_check_info_request["relatedElement"] = related_element
        return self

    def with_description(self, description):
        self.client_check_info_request["description"] = description
        return self

    def with_updated_request(self):
        self.client_check_info_request = json.loads(self.update_request)
        return self

    def build(self):
        return self.client_check_info_request


class ClientCheckInfoRequestInvalidData:
    CHECK_NAME_MISSING = ClientCheckInfoRequestBuilder.a_client_check_info_request().with_check_name(None).build()
    SHORT_NAME_MISSING = ClientCheckInfoRequestBuilder.a_client_check_info_request().with_short_name(None).build()
    RELATED_ELEMENT_MISSING = (
        ClientCheckInfoRequestBuilder.a_client_check_info_request().with_related_element(None).build()
    )
    DESCRIPTION_MISSING = ClientCheckInfoRequestBuilder.a_client_check_info_request().with_description(None).build()

    CHECK_NAME_EMPTY = ClientCheckInfoRequestBuilder.a_client_check_info_request().with_check_name("").build()
    SHORT_NAME_EMPTY = ClientCheckInfoRequestBuilder.a_client_check_info_request().with_short_name("").build()
    RELATED_ELEMENT_EMPTY = ClientCheckInfoRequestBuilder.a_client_check_info_request().with_related_element("").build()
    DESCRIPTION_EMPTY = ClientCheckInfoRequestBuilder.a_client_check_info_request().with_description("").build()

    CHECK_NAME_INVALID = ClientCheckInfoRequestBuilder.a_client_check_info_request().with_check_name(3.14).build()
    SHORT_NAME_INVALID = ClientCheckInfoRequestBuilder.a_client_check_info_request().with_short_name(3.14).build()
    RELATED_ELEMENT_INVALID = (
        ClientCheckInfoRequestBuilder.a_client_check_info_request().with_related_element(3.14).build()
    )
    DESCRIPTION_INVALID = ClientCheckInfoRequestBuilder.a_client_check_info_request().with_description(3.14).build()
