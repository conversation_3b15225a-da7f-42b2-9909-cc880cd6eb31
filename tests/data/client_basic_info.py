import json


class ClientBasicInfoRequestBuilder:
    request = """{
        "clientId":"81ee2f1dcf10b991afdaf548",
        "create": true,
        "friendlyName": "string",
        "fullName": "string",
        "typeOfTrustAccount": "61ef4bff0a719ab106fdc146",
        "cId": "101",
        "existingClient": true,
        "goLiveDate": "2021-10-02",
        "reuseOldBooking": false,
        "email": "<EMAIL>",
        "pointOfContact": "string",
        "additionChecks": [],
        "address":{
            },
        "limits":{
        },
        "frequency":[],
        "claimFromTBR": false,
        "sftpLocation" :["folder1","folder2"]
    }"""
    update_request = """{
        "friendlyName": "string1",
        "fullName": "string1",
        "typeOfTrustAccount": "61ef4bff0a719ab106fdc146",
        "cId": "101",
        "existingClient": true,
        "goLiveDate": "2021-10-02",
        "reuseOldBooking": false,
        "additionChecks": [],
        "sftpLocation" :["folder1","folder2","folder3"]
    }"""

    def __init__(self):
        self.client_basic_info_request = json.loads(self.request)

    @staticmethod
    def a_client_basic_info_request():
        return ClientBasicInfoRequestBuilder()

    def with_client_id(self, client_id):
        self.client_basic_info_request["cId"] = client_id
        return self

    def with_friendly_name(self, friendly_name):
        self.client_basic_info_request["friendlyName"] = friendly_name
        return self

    def with_full_name(self, full_name):
        self.client_basic_info_request["fullName"] = full_name
        return self

    def with_type_of_trust_account(self, type_of_trust_account):
        self.client_basic_info_request["typeOfTrustAccount"] = type_of_trust_account
        return self

    def with_existing_client(self, existing_client):
        self.client_basic_info_request["existingClient"] = existing_client
        return self

    def with_go_live_date(self, go_live_date):
        self.client_basic_info_request["goLiveDate"] = go_live_date
        return self

    def with_reuse_old_booking(self, reuse_old_booking):
        self.client_basic_info_request["reuseOldBooking"] = reuse_old_booking
        return self

    def with_updated_request(self):
        self.client_basic_info_request = json.loads(self.update_request)
        return self

    def with_email(self, email):
        self.client_basic_info_request["email"] = email
        return self

    def with_point_of_contact(self, point_of_contact):
        self.client_basic_info_request["pointOfContact"] = point_of_contact
        return self

    def with_sftp_location(self, sftp_location):
        self.client_basic_info_request["sftpLocation"] = sftp_location
        return self

    def build(self):
        return self.client_basic_info_request


class ClientBasicInfoRequestInvalidData:
    CLIENT_ID_MISSING = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_client_id(None).build()
    FRIENDLY_NAME_MISSING = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_friendly_name(None).build()
    FULL_NAME_MISSING = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_full_name(None).build()
    TYPE_OF_TRUST_ACCOUNT_MISSING = (
        ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_type_of_trust_account(None).build()
    )
    GO_LIVE_DATE_MISSING = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_go_live_date(None).build()

    CLIENT_ID_EMPTY = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_client_id("").build()
    FRIENDLY_NAME_EMPTY = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_friendly_name("").build()
    FULL_NAME_EMPTY = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_full_name("").build()
    GO_LIVE_DATE_EMPTY = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_go_live_date("").build()

    CLIENT_ID_INVALID = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_client_id(3.14).build()
    FRIENDLY_NAME_INVALID = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_friendly_name(1).build()
    FULL_NAME_INVALID = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_full_name(1).build()
    TYPE_OF_TRUST_ACCOUNT_INVALID = (
        ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_type_of_trust_account("").build()
    )
    EXISTING_CLIENT_INVALID = (
        ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_existing_client("").build()
    )
    GO_LIVE_DATE_INVALID = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_go_live_date("").build()
    REUSE_OLD_BOOKING_INVALID = (
        ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_reuse_old_booking("").build()
    )
    EMAIL_INVALID = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_email("").build()
    POINT_OF_CONTACT_INVALID = (
        ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_point_of_contact("").build()
    )
    SFTP_LOCATION_INVALID = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_sftp_location("").build()

    SFTP_LOCATION_INVALID_FIELD_TYPE = (
        ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_sftp_location([1]).build()
    )
