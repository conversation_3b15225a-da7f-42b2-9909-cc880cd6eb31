import json


class ClientLimitRequestBuilder:
    request = """{
       "maximumNoOfClaims": 1,
        "totalAnnualRevenue" : 1000,
        "currency":"GBP"
    }"""
    update_request = """{
        "maximumNoOfClaims": 2,
        "totalAnnualRevenue" : 10000,
        "currency":"GBP"
    }"""

    def __init__(self):
        self.client_limit_request = json.loads(self.request)

    @staticmethod
    def a_client_limit_request():
        return ClientLimitRequestBuilder()

    def with_maximum_no_of_claims(self, maximum_no_of_claims):
        self.client_limit_request["maximumNoOfClaims"] = maximum_no_of_claims
        return self

    def with_total_revenue(self, total_annual_revenue):
        self.client_limit_request["totalAnnualRevenue"] = total_annual_revenue
        return self

    def with_updated_request(self):
        self.client_limit_request = json.loads(self.update_request)
        return self

    def build(self):
        return self.client_limit_request


class ClientLimitRequestInvalidData:
    MAXIMUM_NO_OF_CLAIMS_INVALID = (
        ClientLimitRequestBuilder.a_client_limit_request().with_maximum_no_of_claims("1").build()
    )
    TOTAL_ANNUAL_REVENUE_INVALID = ClientLimitRequestBuilder.a_client_limit_request().with_total_revenue("1").build()
