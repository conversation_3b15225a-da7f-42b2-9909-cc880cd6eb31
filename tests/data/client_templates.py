import json


class ClientTemplatesRequestBuilder:
    request = """{
        "clientId": "1a2ww",
        "claimsFile": "string",
        "bankingFile": "string",
        "status": "draft"
    }"""

    updated_request = """{
        "clientId": "1a2ww",
        "claimsFile": "string",
        "bankingFile": "string",
        "status": "published"
    }"""

    def __init__(self):
        self.client_templates_request = json.loads(self.request)

    @staticmethod
    def a_client_templates_request():
        return ClientTemplatesRequestBuilder()

    def with_updated_request(self):
        self.raid_action_request = json.loads(self.updated_request)
        return self

    def with_client_id(self, client_id):
        self.client_templates_request["clientId"] = client_id
        return self

    def with_claims_file(self, claims_file):
        self.client_templates_request["claimsFile"] = claims_file
        return self

    def with_banking_file(self, banking_file):
        self.client_templates_request["bankingFile"] = banking_file
        return self

    def build(self):
        return self.client_templates_request


class ClientTemplatesRequestInvalidData:
    CLIENT_ID_MISSING = ClientTemplatesRequestBuilder.a_client_templates_request().with_client_id(None).build()
    CLAIMS_FILE_MISSING = ClientTemplatesRequestBuilder.a_client_templates_request().with_claims_file(None).build()
    BANKING_FILE_MISSING = ClientTemplatesRequestBuilder.a_client_templates_request().with_banking_file(None).build()

    CLIENT_ID_EMPTY = ClientTemplatesRequestBuilder.a_client_templates_request().with_client_id("").build()
    CLAIMS_FILE_EMPTY = ClientTemplatesRequestBuilder.a_client_templates_request().with_claims_file("").build()
    BANKING_FILE_EMPTY = ClientTemplatesRequestBuilder.a_client_templates_request().with_banking_file("").build()

    CLIENT_ID_INVALID = ClientTemplatesRequestBuilder.a_client_templates_request().with_client_id(3.14).build()
    CLAIMS_FILE_INVALID = ClientTemplatesRequestBuilder.a_client_templates_request().with_claims_file(1).build()
    BANKING_FILE_INVALID = ClientTemplatesRequestBuilder.a_client_templates_request().with_banking_file(1).build()
