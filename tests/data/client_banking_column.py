import json


class ClientBankingColumnRequestBuilder:
    request = """{
        "columns": ["61efc57260cc72bf21f1c441"]
        }"""
    update_request = """{
        "columns": ["61efc57260cc72bf21f1c441"]
        }"""

    def __init__(self):
        self.client_banking_column_request = json.loads(self.request)

    @staticmethod
    def a_client_banking_column_request():
        return ClientBankingColumnRequestBuilder()

    def with_column(self, column):
        self.client_banking_column_request["columns"] = column
        return self

    def with_updated_request(self):
        self.client_banking_column_request = json.loads(self.update_request)
        return self

    def build(self):
        return self.client_banking_column_request


class ClientBankingColumnRequestInvalidData:
    COLUMN_MISSING = ClientBankingColumnRequestBuilder.a_client_banking_column_request().with_column(None).build()

    COLUMN_EMPTY = ClientBankingColumnRequestBuilder.a_client_banking_column_request().with_column("").build()

    COLUMN_INVALID = ClientBankingColumnRequestBuilder.a_client_banking_column_request().with_column("1").build()
