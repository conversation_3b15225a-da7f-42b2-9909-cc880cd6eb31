import json


class ClientBankInfoRequestBuilder:
    request = """{
        "bankName": "string",
        "accountNumber": "string",
        "sortCode": "string",
        "currency": "string",
        "iban": "string",
        "accountType": "string",
        "excludeFromReport": false
    }"""
    update_request = """{
        "bankName": "ADCB",
        "accountNumber": "9875464",
        "sortCode": "string",
        "currency": "AED",
        "iban": "67548",
        "accountType": "Deposit",
        "excludeFromReport": true
    }"""

    def __init__(self):
        self.client_bank_info_request = json.loads(self.request)

    @staticmethod
    def a_client_bank_info_request():
        return ClientBankInfoRequestBuilder()

    def with_updated_request(self):
        self.client_bank_info_request = json.loads(self.update_request)
        return self

    def with_client_id(self, client_id):
        self.client_bank_info_request["clientId"] = client_id
        return self

    def with_bank_name(self, bank_name):
        self.client_bank_info_request["bankName"] = bank_name
        return self

    def with_account_no(self, account_no):
        self.client_bank_info_request["accountNumber"] = account_no
        return self

    def with_sort_code(self, sort_code):
        self.client_bank_info_request["sortCode"] = sort_code
        return self

    def with_currency(self, currency):
        self.client_bank_info_request["currency"] = currency
        return self

    def with_iban(self, iban):
        self.client_bank_info_request["iban"] = iban
        return self

    def with_account_type(self, account_type):
        self.client_bank_info_request["accountType"] = account_type
        return self

    def with_exclude_from_report(self, exclude_from_report):
        self.client_bank_info_request["excludeFromReport"] = exclude_from_report
        return self

    def build(self):
        return self.client_bank_info_request


class ClientBankInfoRequestInvalidData:
    CLIENT_ID_MISSING = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_client_id(None).build()
    BANK_NAME_MISSING = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_bank_name(None).build()
    ACCOUNT_NUMBER_MISSING = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_account_no(None).build()
    SORT_CODE_MISSING = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_sort_code(None).build()
    CURRENCY_MISSING = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_currency(None).build()
    IBAN_MISSING = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_iban(None).build()

    CLIENT_ID_EMPTY = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_client_id("").build()
    BANK_NAME_EMPTY = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_bank_name("").build()
    ACCOUNT_NUMBER_EMPTY = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_account_no("").build()
    SORT_CODE_EMPTY = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_sort_code("").build()
    CURRENCY_EMPTY = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_currency("").build()
    IBAN_EMPTY = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_iban("").build()

    CLIENT_ID_INVALID = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_client_id(3.14).build()
    BANK_NAME_INVALID = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_bank_name(1).build()
    ACCOUNT_NUMBER_INVALID = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_account_no(1).build()
    SORT_CODE_INVALID = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_sort_code(1).build()
    CURRENCY_INVALID = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_currency(1).build()
    IBAN_INVALID = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_iban(1).build()
    ACCOUNT_TYPE_INVALID = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_account_type(1).build()
    EXCLUDE_FROM_REPORT_INVALID = (
        ClientBankInfoRequestBuilder.a_client_bank_info_request().with_exclude_from_report("test").build()
    )
