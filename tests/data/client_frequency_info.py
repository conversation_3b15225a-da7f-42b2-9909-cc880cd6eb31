import json


class ClientClaimFrequencyRequestBuilder:
    request = """{
        "frequency": ["61ee2f1dcf10b991afdaf548"]
        }"""
    update_request = """{
        "frequency": []
        }"""

    def __init__(self):
        self.client_claim_frequency_request = json.loads(self.request)

    @staticmethod
    def a_client_claim_frequency_request():
        return ClientClaimFrequencyRequestBuilder()

    def with_frequency(self, frequency):
        self.client_claim_frequency_request["frequency"] = frequency
        return self

    def with_updated_request(self):
        self.client_claim_frequency_request = json.loads(self.update_request)
        return self

    def build(self):
        return self.client_claim_frequency_request


class ClientClaimFrequencyRequestInvalidData:
    FREQUENCY_MISSING = (
        ClientClaimFrequencyRequestBuilder.a_client_claim_frequency_request().with_frequency(None).build()
    )

    FREQUENCY_EMPTY = ClientClaimFrequencyRequestBuilder.a_client_claim_frequency_request().with_frequency("").build()

    FREQUENCY_INVALID = (
        ClientClaimFrequencyRequestBuilder.a_client_claim_frequency_request().with_frequency("1").build()
    )
