import json


class ClientAtolInfoRequestBuilder:
    request = """{

            "license": "222",
            "startDate": "2019-01-01",
            "expiryDate": "2025-01-01",
            "files" : ["be761366-9579-4e0d-890b-8ab1e6b82d3a"]
            }"""
    update_request = """{

                "license": "222",
                "startDate": "2020-10-21",
                "expiryDate": "2019-11-17",
                "files" : ["be761366-9579-4e0d-890b-8ab1e6b82d3a","be761366-9579-4e0d-890b-8ab1e6b82d3b"]
                }"""

    def __init__(self):
        self.client_atol_info_request = json.loads(self.request)

    @staticmethod
    def a_client_atol_info_request():
        return ClientAtolInfoRequestBuilder()

    def with_updated_request(self):
        self.client_atol_info_request = json.loads(self.update_request)
        return self

    def with_client_id(self, client_id):
        self.client_atol_info_request["clientId"] = client_id
        return self

    def with_license(self, license):
        self.client_atol_info_request["license"] = license
        return self

    def with_start_date(self, start_date):
        self.client_atol_info_request["startDate"] = start_date
        return self

    def with_expiry_date(self, expiry_date):
        self.client_atol_info_request["expiryDate"] = expiry_date
        return self

    def with_files(self, files):
        self.client_insurance_info_request["files"] = files
        return self

    def build(self):
        return self.client_atol_info_request


class ClientAtolInfoRequestInvalidData:
    CLIENT_ID_MISSING = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_client_id(None).build()
    LICENSE_MISSING = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_license(None).build()
    START_DATE_MISSING = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_start_date(None).build()
    EXPIRY_DATE_MISSING = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_expiry_date(None).build()

    CLIENT_ID_EMPTY = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_client_id("").build()
    LICENSE_EMPTY = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_license("").build()
    START_DATE_EMPTY = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_start_date("").build()
    EXPIRY_DATE_EMPTY = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_expiry_date("").build()

    CLIENT_ID_INVALID = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_client_id(1).build()
    LICENSE_INVALID = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_license(123).build()
    START_DATE_INVALID = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_start_date(3).build()
    EXPIRY_DATE_INVALID = ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_expiry_date(3).build()
