import json


class ClientAddressRequestBuilder:
    request = """{
        "line1": "string",
        "line2": "string",
        "line3": "string",
        "town" : "string",
        "country": "string",
        "postcode": "string"
        }"""
    update_request = """{
        "line1": "string",
        "line2": "string1",
        "line3": "string1",
        "town" : "string",
        "country": "string1",
        "postcode": "string1"
    }"""

    def __init__(self):
        self.client_address_request = json.loads(self.request)

    @staticmethod
    def a_client_address_request():
        return ClientAddressRequestBuilder()

    def with_line_1(self, line1):
        self.client_address_request["line1"] = line1
        return self

    def with_line_2(self, line2):
        self.client_address_request["line2"] = line2
        return self

    def with_line_3(self, line3):
        self.client_address_request["line3"] = line3
        return self

    def with_town(self, town):
        self.client_address_request["town"] = town
        return self

    def with_country(self, country):
        self.client_address_request["country"] = country
        return self

    def with_postcode(self, postcode):
        self.client_address_request["postcode"] = postcode
        return self

    def with_updated_request(self):
        self.client_address_request = json.loads(self.update_request)
        return self

    def build(self):
        return self.client_address_request


class ClientAddressRequestInvalidData:
    LINE1_INVALID = ClientAddressRequestBuilder.a_client_address_request().with_line_1(5).build()
    LINE2_INVALID = ClientAddressRequestBuilder.a_client_address_request().with_line_2(5).build()
    LINE3_INVALID = ClientAddressRequestBuilder.a_client_address_request().with_line_3(5).build()
    TOWN_INVALID = ClientAddressRequestBuilder.a_client_address_request().with_town(5).build()
    COUNTRY_INVALID = ClientAddressRequestBuilder.a_client_address_request().with_country(5).build()
    POSTCODE_INVALID = ClientAddressRequestBuilder.a_client_address_request().with_postcode("").build()
