import json


class ClientInsuranceInfoRequestBuilder:
    request = """{
            "policyNumber": "222",
            "provider": "ABCD",
            "expiryDate": "2025-02-02",
            "files" : ["be761366-9579-4e0d-890b-8ab1e6b82d3a"],
            "supplierListFile": "be761366-9579-4e0d-890b-8ab1e6b82dfh"
    }"""
    update_request = """{

                "policyNumber": "222",
                "provider": "ABC",
                "expiryDate": "2025-02-02",
                "files" : ["be761366-9579-4e0d-890b-8ab1e6b82d3a","be761366-9579-4e0d-890b-8ab1e6b85d7a"],
                "supplierListFile": "be761366-9579-4e0d-890b-8ab1e6b82dfh"
        }"""

    def __init__(self):
        self.client_insurance_info_request = json.loads(self.request)

    @staticmethod
    def a_client_insurance_info_request():
        return ClientInsuranceInfoRequestBuilder()

    def with_updated_request(self):
        self.client_insurance_info_request = json.loads(self.update_request)
        return self

    def with_client_id(self, client_id):
        self.client_insurance_info_request["clientId"] = client_id
        return self

    def with_policy_no(self, policy_no):
        self.client_insurance_info_request["policyNumber"] = policy_no
        return self

    def with_provider(self, provider):
        self.client_insurance_info_request["provider"] = provider
        return self

    def with_expiry_date(self, expiry_date):
        self.client_insurance_info_request["expiryDate"] = expiry_date
        return self

    def with_files(self, files):
        self.client_insurance_info_request["files"] = files
        return self

    def build(self):
        return self.client_insurance_info_request


class ClientInsuranceInfoRequestInvalidData:
    CLIENT_ID_MISSING = ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_client_id(None).build()
    POLICY_NUMBER_MISSING = (
        ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_policy_no(None).build()
    )
    PROVIDER_MISSING = ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_provider(None).build()
    EXPIRY_DATE_MISSING = (
        ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_expiry_date(None).build()
    )

    CLIENT_ID_EMPTY = ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_client_id("").build()
    POLICY_NUMBER_EMPTY = ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_policy_no("").build()
    PROVIDER_EMPTY = ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_provider("").build()
    EXPIRY_DATE_EMPTY = ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_expiry_date("").build()

    CLIENT_ID_INVALID = ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_client_id(3.14).build()
    POLICY_NUMBER_INVALID = (
        ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_policy_no(3.14).build()
    )
    PROVIDER_INVALID = ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_provider(3).build()
    EXPIRY_DATE_INVALID = (
        ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_expiry_date(3).build()
    )
