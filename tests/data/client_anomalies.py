import json


class ClientAnomalyRequestBuilder:
    request = """{
        "anomalies": [{"anomalyId":"11ef4bff0a719ab106fdc14e","customFieldValue":0}]
    }"""

    update_request = """{
        "anomalies": [{"anomalyId":"11ef4bff0a719ab106fdc14e","customFieldValue":47}]
    }"""

    def __init__(self):
        self.client_anomaly_request = json.loads(self.request)

    @staticmethod
    def a_client_anomaly_request():
        return ClientAnomalyRequestBuilder()

    def with_updated_request(self):
        self.client_anomaly_request = json.loads(self.update_request)
        return self

    def with_client_id(self, client_id):
        self.client_anomaly_request["clientId"] = client_id
        return self

    def with_anomaly_id(self, anomaly_id):
        self.client_anomaly_request["anomalyId"] = anomaly_id
        return self

    def with_custom_field_value(self, custom_field_value):
        self.client_anomaly_request["customFieldValue"] = custom_field_value
        return self

    def build(self):
        return self.client_anomaly_request


class ClientAnomalyRequestInvalidData:
    CLIENT_ID_MISSING = ClientAnomalyRequestBuilder.a_client_anomaly_request().with_client_id(None).build()
    ANOMALY_ID_MISSING = ClientAnomalyRequestBuilder.a_client_anomaly_request().with_anomaly_id([]).build()

    CLIENT_ID_EMPTY = ClientAnomalyRequestBuilder.a_client_anomaly_request().with_client_id("").build()

    CLIENT_ID_INVALID = ClientAnomalyRequestBuilder.a_client_anomaly_request().with_client_id(3.14).build()
    ANOMALY_ID_INVALID = ClientAnomalyRequestBuilder.a_client_anomaly_request().with_anomaly_id(1).build()
    CUSTOM_FIELD_VALUE_INVALID = (
        ClientAnomalyRequestBuilder.a_client_anomaly_request().with_custom_field_value("string").build()
    )
