from mock import MagicMock, patch

from flaskr import ControllerException
from flaskr.services.exceptions import ServiceException
from flaskr.services.dashboard_service import DashboardService
from tests.helpers import skip_auth


@patch.object(DashboardService, "ytd_clients", lambda x, y, z: {})
def test_ytd_clients(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/dashboard/clients?year=2022")

    # Then
    assert response.status_code == 200


@patch.object(DashboardService, "ytd_clients", MagicMock(side_effect=ServiceException))
def test_ytd_clients_error(flask_client):
    # When
    response = flask_client.get("/api/dashboard/clients?year=")

    # Then
    assert response.status_code == 400


@patch.object(DashboardService, "dashboard_details", lambda w, x, y, z: {})
def test_dashboard_details(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/dashboard/details?currency=EUR")

    # Then
    assert response.status_code == 200


@patch.object(DashboardService, "dashboard_details", MagicMock(side_effect=ServiceException))
def test_dashboard_details_error(flask_client):
    # When
    response = flask_client.get("/api/dashboard/details?currency=")

    # Then

    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(DashboardService, "ytd_bookings", lambda a, b, c, d, e, f: {})
def test_ytd_bookings(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/dashboard/bookings?clientId=CR7&currency=GBP&year=2022")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(DashboardService, "ytd_bookings", MagicMock(side_effect=ServiceException))
def test_ytd_bookings_error(flask_client):
    # When
    response = flask_client.get("/api/dashboard/bookings?clientId=&currencyCode=&year=")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(DashboardService, "ytd_payments", lambda a, b, c, d, e, f: {})
def test_ytd_payments(flask_client):
    skip_auth()
    # When
    response = flask_client.get("api/dashboard/payments?year=2022&clientId=TSL007&currency=GBP")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(DashboardService, "ytd_payments", MagicMock(side_effect=ServiceException))
def test_ytd_payments_error(flask_client):
    skip_auth()

    # When
    response = flask_client.get("/api/dashboard/payments?year=2022&clientId=TSL007&currency=")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(DashboardService, "ytd_claims", lambda a, b, c, d, e, f: {})
def test_ytd_claims(flask_client):
    skip_auth()
    # When
    response = flask_client.get("api/dashboard/claims?year=2022&clientId=TSL007&currency=GBP")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(DashboardService, "ytd_claims", MagicMock(side_effect=ServiceException))
def test_ytd_claims_error(flask_client):
    skip_auth()

    # When
    response = flask_client.get("/api/dashboard/claims?year=2022&clientId=TSL007&currency=")

    # Then
    assert response.status_code == 400


@patch.object(
    DashboardService,
    "ytd_clients_category",
    MagicMock(
        return_value=[
            {"typeOfTrust": "ATOL Standard", "clients": 0},
            {"typeOfTrust": "Tripartite MA", "clients": 2},
            {"typeOfTrust": "Tripartite Tour Op", "clients": 0},
            {"typeOfTrust": "Non-Flight PTR 2018", "clients": 0},
            {"typeOfTrust": "Non PTR 2018", "clients": 0},
            {"typeOfTrust": "ATOL Gold", "clients": 0},
            {"typeOfTrust": "ATOL Escrow", "clients": 1},
        ]
    ),
)
def test_ytd_clients_category(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/dashboard/clients-category")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(DashboardService, "risk_exposure", MagicMock(return_value=[]))
def test_risk_exposure_success(flask_client):
    skip_auth()
    request = {"currency": "GBP"}
    # When
    response = flask_client.get("/api/dashboard/risk-exposure", query_string=request)
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(
    DashboardService, "risk_exposure", MagicMock(side_effect=ControllerException(message="Missing required details"))
)
def test_risk_exposure_error(flask_client):
    # Given

    request = {}
    # When
    response = flask_client.get("/api/dashboard/risk-exposure", query_string=request)

    # Then

    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(DashboardService, "risk_exposure_status", MagicMock(return_value={}))
def test_risk_exposure_status_success(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/dashboard/risk-exposure/status")
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(DashboardService, "movement_of_funds", MagicMock(return_value={}))
def test_movement_of_funds_dashboard(flask_client):
    skip_auth()
    # Given
    request = {"currency": "GBP"}
    # When
    response = flask_client.get("/api/dashboard/movement-of-funds", query_string=request)

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(DashboardService, "movement_of_funds", MagicMock(side_effect=ServiceException))
def test_movement_of_funds_dashboard_service_exception(flask_client):
    skip_auth()
    # Given
    request = {}
    # When
    response = flask_client.get("/api/dashboard/movement-of-funds", query_string=request)

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(DashboardService, "expiry_details", MagicMock(return_value=[]))
def test_expiry_details_success(flask_client):
    skip_auth()
    request = {"currency": "GBP", "fromDate": "2022-02-01", "toDate": "2022-02-10"}
    # When
    response = flask_client.get("/api/dashboard/expiry", query_string=request)
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(
    DashboardService, "expiry_details", MagicMock(side_effect=ControllerException(message="Missing required details"))
)
def test_expiry_details_error(flask_client):
    # Given

    request = {}
    # When
    response = flask_client.get("/api/dashboard/expiry", query_string=request)

    # Then

    assert response.status_code == 400
