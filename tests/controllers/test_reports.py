import json
import tempfile
from unittest.mock import PropertyMock
from mock import MagicMock, patch
import pytest
from flaskr.services.exceptions import ServiceException
from flaskr.services.lookup_service import LookupService
from flaskr.services.reporting_service import ReportingService
from flaskr.services.client_service import ClientService
from tests.helpers import skip_auth
from datetime import datetime
from freezegun import freeze_time


@pytest.mark.skip("To be completed later")
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "trust_balance_report", lambda x, y: {"content": iter([])})
def test_trust_balance_report(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/reports/trust-balance?client=123&page=&size=")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "trust_balance_report_progress",
    lambda x, y: {"in_progress": True, "client_id": "62cc1b5116133e88d93dba49", "full_name": "Sample Client"},
)
def test_trust_balance_report_progress(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/reports/trust-balance/progress")

    # Then
    assert response.status_code == 200
    data = response.get_json()
    assert data["in_progress"] is True
    assert data["client_id"] == "62cc1b5116133e88d93dba49"
    assert data["full_name"] == "Sample Client"


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "trust_balance_report", MagicMock(side_effect=ServiceException))
def test_trust_balance_report_error(flask_client):
    # When
    response = flask_client.get("/api/reports/trust-balance?client=123&page=&size=")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "banking_and_claim_between_dates", lambda a, b, c, d, e, f: {})
@patch.object(LookupService, "get_bank_accounts", lambda a, b, c, d: [])
def test_weekly_fortnightly_report(flask_client):
    ok_response_mock = MagicMock()
    type(ok_response_mock).status_code = PropertyMock(return_value=200)
    ok_response_mock.json.return_value = {"funds_in": 0, "funds_out": 0}
    with patch(
        "flaskr.controllers.reports.requests.post",
        MagicMock(return_value=ok_response_mock),
    ):
        skip_auth()
        # When
        response = flask_client.get(
            "/api/reports/weekly-fortnightly?client=1&fromDate=2022-01-02&toDate=2022-01-02&currency=GBP&weekly_fornightly=True"
        )

        # Then
        assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "banking_and_claim_between_dates", MagicMock(side_effect=ServiceException))
def test_weekly_fortnightly_report_error(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/reports/weekly-fortnightly?client=123&fromDate=&toDate=&currency=&banking=")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "bank_reconciliation_statement_report", lambda a, b, c, d, e: {})
@patch.object(LookupService, "get_bank_accounts", lambda a, b, c, d: [])
@patch.object(ClientService, "get_client_friendly_name", lambda a, c: [])
def test_bank_reconciliation_statment_report(flask_client):
    ok_response_mock = MagicMock()
    type(ok_response_mock).status_code = PropertyMock(return_value=200)
    ok_response_mock.json.return_value = {
        "funds_in": 0,
        "funds_out": 0,
        "bank_charges": 0,
        "bank_fees": 0,
        "opening_balance": 0,
        "closing_balance_on_statement": 0,
        "closing_balance": 0,
        "interest_received": 0,
        "bank_charges_reversal": 0,
        "difference": 0,
        "transfer_abtot": 0,
        "transfer_cll": 0,
        "transfer_from_deposit": 0,
    }
    with patch(
        "flaskr.controllers.reports.requests.post",
        MagicMock(return_value=ok_response_mock),
    ):
        skip_auth()
        # When
        response = flask_client.get(
            "/api/reports/bank-reconciliation?client=CR7&currency=GBP&fromDate=2022-02-22&toDate=2022-02-22"
        )

        # Then
        assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "bank_reconciliation_statement_report", MagicMock(side_effect=ServiceException))
def test_bank_reconciliation_statment_report_error(flask_client):
    # When
    response = flask_client.get("/api/reports/bank-reconciliation?client=123&currency=&fromDate=&toDate=&banking=")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "movements_of_funds", lambda a, b, c, d, e, f: ([], {}))
@patch.object(LookupService, "get_bank_accounts", lambda a, b, c, d: [])
@patch.object(ClientService, "get_client_friendly_name", lambda a, b: [])
def test_movements_of_funds(flask_client):
    ok_response_mock = MagicMock()
    type(ok_response_mock).status_code = PropertyMock(return_value=200)
    ok_response_mock.json.return_value = {
        "funds_in": 0,
        "funds_out": 0,
        "funds_in_thousand": 0,
        "funds_out_thousand": 0,
        "opening_balance": 0,
        "closing_balance": 0,
        "closing_balance_thousand": 0,
        "last_year_opening_balance": 0,
        "last_year_closing_balance": 0,
        "last_year_fund_in": 0,
        "last_year_fund_out": 0,
        "opening_balance_thousand": 0,
        "last_year_opening_balance_thousand": 0,
        "last_year_closing_balance_thousand": 0,
        "last_year_fund_in_thousand": 0,
        "last_year_fund_out_thousand": 0,
        "opening_balance_comparison": 0,
        "receipts_comparison": 0,
        "payments_comparison": 0,
        "closing_balance_comparison": 0,
        "client_name": 0,
    }
    with patch(
        "flaskr.controllers.reports.requests.post",
        MagicMock(return_value=ok_response_mock),
    ):
        skip_auth()
        # When
        response = flask_client.get("/api/reports/movement-of-funds?client=123&toDate=2022-03-24&fromDate=2022-01-24")
        # Then
        assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "movements_of_funds", MagicMock(side_effect=ServiceException))
def test_movements_of_funds_error(flask_client):
    # When
    response = flask_client.get("/api/reports/movement-of-funds?client=123&fromDate=&toDate=")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "client_files_report", MagicMock(return_value={}))
def test_client_files_report1(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/reports/client-files?client=TU123&fromDate=2022-02-24&toDate=2022-02-24&page=1&size=3"
    )
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "client_files_report", MagicMock(side_effect=ServiceException))
def test_client_files_report_error(flask_client):
    # When
    response = flask_client.get("/api/reports/client-files?&fromDate=2022-02-24&toDate=2022-02-24&page=1&size=3")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "banking_claim_summary_report", lambda a, b, c, d, e, f: {})
def test_banking_claim_summary_report(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/reports/banking-claim-summary?client=CR7&fromDate=2022-01-01&toDate=2023-03-05&page=1&size=100"
    )
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "banking_claim_summary_report", MagicMock(side_effect=ServiceException))
def test_banking_claim_summary_report_error(flask_client):
    # When
    response = flask_client.get(
        "/api/reports/banking-claim-summary?client=CR7&fromDate=2022-01-01&toDate=2023-03-05&page=1&size=100"
    )

    # Then
    assert response.status_code == 400


@pytest.mark.skip("To be completed later")
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch("flaskr.controllers.reports.trust_balance_report_task.delay", lambda *args, **kwargs: None)
def test_trust_balance_report_export_success(flask_client, patch_db):
    skip_auth()
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one({"client_id": "CR7"})
    client_id = client_basic_info.inserted_id
    request = {}

    # When
    response = flask_client.get(
        f"/api/reports/trust-balance/export?client={client_id}&page=1&size=10",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    data = patch_db.report_files.find_one({"name": "TrustBalance", "client_id": client_id, "currency": None})
    assert response.status_code == 202
    assert json.loads(response.data.decode()) == {"message": "Generation of Trust Balance Reports initiated."}
    assert data is not None


@freeze_time("2022-11-28T15:00:00")
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch("flaskr.controllers.reports.trust_balance_report_task.delay", lambda *args, **kwargs: None)
def test_trust_balance_report_export_failure(flask_client, patch_db):
    skip_auth()
    # Given
    client_basic_info = patch_db.client_basic_info.insert_one({"client_id": "CR7"})
    client_id = client_basic_info.inserted_id
    patch_db.report_files.insert_one(
        {
            "name": "TrustBalance",
            "client_id": client_id,
            "currency": None,
            "status": "Generating New Report",
            "file_type": "xlsx",
            "file_id": "test-file-id",
            "created_at": datetime(2022, 11, 28, 14, 46, 0),
            "updated_at": datetime(2022, 11, 28, 14, 46, 0),
        }
    )
    request = {"client_id": str(client_id)}

    # When
    response = flask_client.get(
        f"/api/reports/trust-balance/export?client={client_id}&page=1&size=10",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 409
    assert json.loads(response.data.decode()) == {"message": "TBR is already under generation for the selected filters"}


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    ReportingService,
    "client_files_report",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "62a826d5a2f265631c74046d",
                    "bankingFileTotal": 111000.0,
                    "bankingItems": 12.0,
                    "claimFileTotal": 0,
                    "claimItems": 0,
                    "clientName": "Edstem Test User",
                    "currency": "GBP",
                    "fileDate": "2022-04-10",
                    "fileName": "********-All -Banking -mapping02.xlsx",
                    "fileStatus": "Submitted",
                    "fileSubmitted": "2022-04-13T05:54:12.661000",
                    "fileType": "Banking File",
                    "friendlyName": "Edstem Test User",
                    "validClaimAmount": 0,
                    "validClaimItems": 0,
                    "voidClaimAmount": 0,
                    "voidClaimItems": 0,
                },
            ],
            "empty": False,
            "first": True,
            "last": True,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
def test_export_client_files_report_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {}
    open(f"{temp_dir}/ClientFiles", "w")

    # When
    response = flask_client.get(
        "/api/reports/client-files/export?client=CR7&fromDate=&toDate=&page=1&size=3",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "export_weekly_fortnightly_report",
    MagicMock(return_value=None),
)
@patch.object(LookupService, "get_bank_accounts", lambda a, b, c, d: [])
@patch.object(
    ReportingService,
    "banking_and_claim_between_dates",
    MagicMock(return_value={"bankingAmount": 93000.0, "claimAmount": 17000.0, "clientName": "Edstem Test User"}),
)
def test_export_weekly_fortnightly_report_success(flask_client):
    ok_response_mock = MagicMock()
    type(ok_response_mock).status_code = PropertyMock(return_value=200)
    ok_response_mock.json.return_value = {
        "funds_in": 0,
        "funds_out": 0,
    }
    with patch(
        "flaskr.controllers.reports.requests.post",
        MagicMock(return_value=ok_response_mock),
    ):
        skip_auth()
        # Given
        temp_dir = tempfile.gettempdir()
        request = {}
        open(f"{temp_dir}/Weekly-fortnightly", "w")

        # When
        response = flask_client.get(
            "/api/reports/weekly-fortnightly/export?client=CR7&fromDate=2022-02-24&toDate=2022-10-24&currency=GBP",
            data=json.dumps(request),
            content_type="application/json",
        )

        # Then
        assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "export_bank_reconciliation_statement_report",
    MagicMock(return_value=None),
)
@patch.object(LookupService, "get_bank_accounts", lambda a, b, c, d: [])
@patch.object(
    ReportingService,
    "bank_reconciliation_statement_report",
    MagicMock(
        return_value={
            "bankingAmount": 93000.0,
            "claimAmount": 17000.0,
            "clientName": "Edstem Test User",
            "closingBalance": 0,
            "openingBalance": 0,
        }
    ),
)
@patch.object(ClientService, "get_client_friendly_name", lambda a, c: [])
def test_export_bank_reconciliation_statement_report_success(flask_client):
    ok_response_mock = MagicMock()
    type(ok_response_mock).status_code = PropertyMock(return_value=200)
    ok_response_mock.json.return_value = {
        "opening_balance": 0,
        "closing_balance": 0,
        "funds_in": 0,
        "funds_out": 0,
        "fee_charge": 0,
    }
    with patch(
        "flaskr.controllers.reports.requests.post",
        MagicMock(return_value=ok_response_mock),
    ):
        skip_auth()
        # Given
        temp_dir = tempfile.gettempdir()
        request = {}
        open(f"{temp_dir}/Bank Reconciliation Statement Of Trust Account", "w")

        # When
        response = flask_client.get(
            "/api/reports/bank-reconciliation/export?client=CR7&currency=GBP&fromDate=2022-02-22&toDate=2022-10-22",
            data=json.dumps(request),
            content_type="application/json",
        )

        # Then
        assert response.status_code == 200


@pytest.mark.skip("To be completed later")
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "export_movements_of_funds",
    MagicMock(return_value=None),
)
@patch.object(LookupService, "get_bank_accounts", lambda a, b, c, d: [])
@patch.object(
    ReportingService,
    "movements_of_funds",
    MagicMock(
        return_value={
            "items": [
                {
                    "clientId": "CR7",
                    "clientName": "Edstem Test User",
                    "closingBalanceComparison": 0,
                    "closingBalanceCurrent": 0,
                    "closingBalanceCurrentThousandth": 0,
                    "closingBalancePrevious": 0,
                    "closingBalancePreviousThousandth": 0,
                    "currencyCode": "GBP",
                    "friendlyName": "Edstem Test User",
                    "openingBalanceComparison": 0,
                    "openingBalanceCurrent": 99003.86,
                    "openingBalanceCurrentThousandth": 99.00386,
                    "openingBalancePrevious": 0,
                    "openingBalancePreviousThousandth": 0,
                    "paymentsComparison": 0,
                    "paymentsCurrent": 17000.0,
                    "paymentsCurrentThousandth": 17.0,
                    "paymentsPrevious": 0,
                    "paymentsPreviousThousandth": 0,
                    "receiptsComparison": 0,
                    "receiptsCurrent": 93000.0,
                    "receiptsCurrentThousandth": 93.0,
                    "receiptsPrevious": 0,
                    "receiptsPreviousThousandth": 0,
                },
            ]
        }
    ),
)
def test_export_movements_of_funds_success(flask_client):
    ok_response_mock = MagicMock()
    type(ok_response_mock).status_code = PropertyMock(return_value=200)
    ok_response_mock.json.return_value = {
        "funds_in": 0,
        "funds_out": 0,
        "funds_in_thousand": 0,
        "funds_out_thousand": 0,
        "opening_balance": 0,
        "closing_balance": 0,
        "closing_balance_thousand": 0,
        "last_year_opening_balance": 0,
        "last_year_closing_balance": 0,
        "last_year_fund_in": 0,
        "last_year_fund_out": 0,
        "opening_balance_thousand": 0,
        "last_year_opening_balance_thousand": 0,
        "last_year_closing_balance_thousand": 0,
        "last_year_fund_in_thousand": 0,
        "last_year_fund_out_thousand": 0,
        "opening_balance_comparison": 0,
        "receipts_comparison": 0,
        "payments_comparison": 0,
        "closing_balance_comparison": 0,
        "client_name": 0,
    }
    with patch(
        "flaskr.controllers.reports.requests.post",
        MagicMock(return_value=ok_response_mock),
    ):
        skip_auth()
        # Given
        temp_dir = tempfile.gettempdir()
        request = {}
        open(f"{temp_dir}/MovementOfFunds", "w")

        # When
        response = flask_client.get(
            "/api/reports/movement-of-funds/export?toDate=2022-10-24&fromDate=2022-01-24&client=123",
            data=json.dumps(request),
            content_type="application/json",
        )

        # Then
        assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "export_banking_claim_summary_report",
    MagicMock(return_value=None),
)
@patch.object(
    ReportingService,
    "banking_claim_summary_report",
    MagicMock(
        return_value={
            "content": [
                {
                    "apcFee": 0.0,
                    "atol": 0.0,
                    "balance": 0.0,
                    "bankTransfer": 0.0,
                    "bsp": 0.0,
                    "cancellation": 0.0,
                    "card": 0.0,
                    "cash": 0.0,
                    "charterFlightsOrScheduledFlights": 0.0,
                    "cheque": 0.0,
                    "clientId": "CR7",
                    "clientName": "Edstem Test User",
                    "closingBalance": 0,
                    "commission": 0.0,
                    "cropCard": 0.0,
                    "cruise": 0.0,
                    "currency": "INR",
                    "deposit": 0.0,
                    "friendlyName": "Edstem Test User",
                    "insurance": 0.0,
                    "lcf": 0.0,
                    "nonTrust": 0.0,
                    "openingBalance": 0,
                    "otherElementAmount": 0.0,
                    "otherPaymentAmount": 10000.0,
                    "paymentDate": ["2020-12-01"],
                    "paysafeAmount": 0.0,
                    "performance": 0.0,
                    "refund": 0.0,
                    "reportDate": "10/04/2022",
                    "totalBanking": 10000.0,
                    "totalClaim": 0,
                }
            ],
            "empty": False,
            "first": True,
            "last": True,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
def test_export_banking_claim_summary_report_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {}
    open(f"{temp_dir}/Banking and Claim Summary Report", "w")

    # When
    response = flask_client.get(
        "/api/reports/banking-claim-summary/export?client=CR7&fromDate=2022-01-01&toDate=2022-05-01",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "compliance_computation_report", lambda a, b, c, d, e: {})
def test_compliance_computation_report(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/reports/compliance-computation?client=2146&fromDate=2022-08-01&toDate=2022-08-16&currency=GBP"
    )
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "compliance_computation_report", MagicMock(side_effect=ServiceException))
def test_compliance_computation_report_error(flask_client):
    # When
    response = flask_client.get("/api/reports/compliance-computation?client=2146&fromDate=2022-08-01&toDate=2022-08-16")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "export_compliance_computation_report",
    MagicMock(return_value=None),
)
@patch.object(
    ReportingService,
    "compliance_computation_report",
    MagicMock(
        return_value={
            "amount": ********.*********,
            "bankingAmount": ********.*********,
            "claimAmount": ********.*********,
            "closingBalanceCurrent": 8000.0,
            "closingBalancePrevious": 1000.0,
            "originalAmount": 0,
            "clientName": "abc",
        }
    ),
)
def test_export_compliance_computation_report_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {}
    open(f"{temp_dir}/Compliance Computation Report", "w")

    # When
    response = flask_client.get(
        "/api/reports/compliance-computation/export?client=2146&currency=GBP&fromDate=2020-10-27&toDate=2020-10-30",
        data=json.dumps(request),
        content_type="text/plain",
    )

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "report_files",
    lambda *args, **kwargs: {
        "fileId": "test_file_id",
        "fileName": "test_file_name",
        "fileType": "xlsx",
        "status": "status",
        "currency": None,
        "generatedAt": "2022-09-22T00:00:00",
        "createdAt": "2022-09-22T00:00:00",
        "updatedAt": "2022-09-22T00:00:00",
    },
)
def test_report_files(flask_client, patch_db):
    # Given
    skip_auth()
    request = {
        "name": "TrustBalance",
        "clientId": "1",
    }

    # When
    response = flask_client.post(
        "/api/reports/report-files",
        json=request,
    )

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "fileId": "test_file_id",
        "fileName": "test_file_name",
        "fileType": "xlsx",
        "status": "status",
        "currency": None,
        "generatedAt": "2022-09-22T00:00:00",
        "createdAt": "2022-09-22T00:00:00",
        "updatedAt": "2022-09-22T00:00:00",
    }


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "atol_renewal_tracker_list", lambda x, y: {})
def test_atol_renewal_tracker_list_report(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/reports/atol-renewal-tracker-list?client=633e6670102c602adb09c933=")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    ReportingService,
    "atol_renewal_tracker_list",
    MagicMock(
        return_value=[
            {"clientName": "joemon", "expiryDate": "2025-02-02", "license": "222", "startDate": "2020-02-02"},
            {"clientName": "joemon", "expiryDate": "2025-02-02", "license": "223", "startDate": "2020-02-02"},
        ]
    ),
)
def test_export_atol_renewal_tracker_list_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    open(f"{temp_dir}/Atol Renewal Tracker", "w")

    # When
    response = flask_client.get(
        "/api/reports/atol-renewal-tracker-list/export?client=633e6670102c602adb09c933",
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "insurance_renewal_tracker_list", lambda a, b: {})
def test_insurance_renewal_tracker_list(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/reports/insurance-renewal-tracker-list?client=62419f08888c2b5ed06235d9")
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    ReportingService,
    "insurance_renewal_tracker_list",
    MagicMock(
        return_value=[
            {
                "clientName": "CR7",
                "expiryDate": "02/02/2025",
                "maxCapPerSupplier": 1000,
                "policyNumber": "222",
                "provider": "ABCD",
                "supplierName": "supplier1",
                "totalMaxCap": 3000,
            },
            {
                "clientName": "",
                "expiryDate": "",
                "maxCapPerSupplier": 2000,
                "policyNumber": "",
                "provider": "",
                "supplierName": "supplier2",
                "totalMaxCap": "",
            },
        ]
    ),
)
def test_export_insurance_renewal_tracker_list(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {
        "client": "CR7",
    }
    open(f"{temp_dir}/Insurance Renewal Tracker", "w")

    # When
    response = flask_client.get(
        "/api/reports/insurance-renewal-tracker-list/export",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "daily_agent_exceptions_report", lambda x, y: {})
def test_daily_agent_exceptions(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/reports/daily-agent-exceptions?client=62fb53eb8af7efee9e56e080&page=&size=")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(ReportingService, "daily_agent_exceptions_report", MagicMock(side_effect=ServiceException))
def test_daily_agent_exceptions_error(flask_client):
    # When
    response = flask_client.get("/api/reports/daily-agent-exceptions?client=123&page=&size=")

    # Then
    assert response.status_code == 400


@patch.object(ReportingService, "tbr_sftp_upload", lambda a, b: {})
def test_tbr_sftp_upload(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/reports/trust-balance/sftp-upload?type=xlsx&client=62ce5983bff9a1862ca13424&fileId=0474d39f.xlsx"
    )
    # Then
    assert response.status_code == 200


@patch.object(ReportingService, "tbr_sftp_upload", MagicMock(side_effect=ServiceException))
def test_tbr_sftp_upload_error(flask_client):
    # When
    response = flask_client.get("/api/reports/trust-balance/sftp-upload?type=xlsx&client=62ce5983bff9a1862ca13424")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "export_hsbc_bank_reconciliation_statement_report",
    MagicMock(return_value=None),
)
@patch.object(LookupService, "get_bank_accounts", lambda a, b, c, d: [])
@patch.object(
    ReportingService,
    "bank_reconciliation_statement_report",
    MagicMock(
        return_value={
            "bankingAmount": 93000.0,
            "claimAmount": 17000.0,
            "clientName": "Edstem Test User",
            "closingBalance": 0,
            "openingBalance": 0,
        }
    ),
)
@patch.object(ClientService, "get_client_friendly_name", lambda a, c: [])
def test_export_hsbc_bank_reconciliation_statement_report_success(flask_client):
    ok_response_mock = MagicMock()
    type(ok_response_mock).status_code = PropertyMock(return_value=200)
    ok_response_mock.json.return_value = {
        "opening_balance": 0,
        "closing_balance": 0,
        "unreconciled_transactions": 0,
        "trust_funds_sent_to_ptt": 0,
        "trust_non_trust_payment_received": 0,
    }
    with patch(
        "flaskr.controllers.reports.requests.post",
        MagicMock(return_value=ok_response_mock),
    ):
        skip_auth()
        # Given
        temp_dir = tempfile.gettempdir()
        request = {}
        open(f"{temp_dir}/HSBC Bank Reconciliation Statement Of Trust Account", "w")

        # When
        response = flask_client.get(
            "/api/reports/hsbc-bank-reconciliation/export?client=CR7&currency=GBP&fromDate=2022-02-22&toDate=2022-10-22",
            data=json.dumps(request),
            content_type="application/json",
        )

        # Then
        assert response.status_code == 200
