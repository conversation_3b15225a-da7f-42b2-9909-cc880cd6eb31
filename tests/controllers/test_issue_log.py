import tempfile

from mock import <PERSON><PERSON>ock, patch
import json

from flaskr.controllers.exceptions import ControllerException
from flaskr.services.auth_service import AuthService
from flaskr.services.issue_log_service import IssueLogService
from flaskr.services.reporting_service import ReportingService
from tests.helpers import skip_auth
from marshmallow import ValidationError


@patch.object(IssueLogService, "create_issue_log", MagicMock(return_value=None))
def test_create_issue_logs(flask_client):
    skip_auth()
    # Given
    request = {
        "clientId": "62fb53eb8af7efee9e56e08d",
        "opened": "string",
        "shortDescription": "string",
        "priority": "string",
        "resolutionNotes": "string",
        "status": "string",
        "dateResolved": "string",
    }

    # When
    response = flask_client.post("/api/issue-log", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch.object(IssueLogService, "create_issue_log", MagicMock(side_effect=ValidationError("Invalid data")))
def test_create_issue_logs_error(flask_client):
    skip_auth()
    # Given
    request = {}

    # When
    response = flask_client.post("/api/issue-log", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch.object(IssueLogService, "create_issue_log", MagicMock(side_effect=ControllerException("Missing ClientId")))
def test_create_issue_logs_controller_error(flask_client):
    skip_auth()
    # Given
    request = {"status": "Submitted"}

    # When
    response = flask_client.post("/api/issue-log", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch.object(IssueLogService, "list_issue_log", MagicMock(return_value=None))
def test_list_issue_logs(flask_client):
    skip_auth()
    # Given
    request = {
        "query": None,
        "client": None,
        "status": None,
        "toDate": None,
        "page": 1,
        "size": 1,
    }

    # When
    response = flask_client.get("/api/issue-log", query_string=request, content_type="application/json")

    # Then
    assert response.status_code == 200


@patch.object(IssueLogService, "list_issue_log", MagicMock(side_effect=ControllerException(message="invalid sort key")))
def test_list_issue_logs_invalid_sort_key(flask_client):
    skip_auth()
    # Given
    request = {
        "query": None,
        "sortKey": "Opened",
        "client": None,
        "status": None,
        "toDate": None,
        "page": 1,
        "size": 1,
    }

    # When
    response = flask_client.get("/api/issue-log", query_string=request, content_type="application/json")

    # Then
    assert response.status_code == 400


@patch.object(
    IssueLogService, "list_issue_log", MagicMock(side_effect=ControllerException(message="invalid sort order"))
)
def test_list_issue_logs_invalid_sort_order(flask_client):
    skip_auth()
    # Given
    request = {
        "query": None,
        "sortKey": "opened",
        "sortOrder": 0,
        "client": None,
        "status": None,
        "toDate": None,
        "page": 1,
        "size": 1,
    }

    # When
    response = flask_client.get("/api/issue-log", query_string=request, content_type="application/json")

    # Then
    assert response.status_code == 400


@patch.object(IssueLogService, "list_issue_log", MagicMock(side_effect=ValidationError("Invalid data")))
def test_list_issue_logs_error(flask_client):
    skip_auth()
    # Given
    request = {
        "query": "Invalid",
    }

    # When
    response = flask_client.get("/api/issue-log", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch.object(IssueLogService, "update_issue_log", MagicMock(return_value=None))
def test_update_issue_logs(flask_client):
    skip_auth()
    # Given
    request = {
        "clientId": "62fb53eb8af7efee9e56e08d",
        "opened": "string",
        "shortDescription": "string",
        "priority": "string",
        "resolutionNotes": "string",
        "status": "string",
        "dateResolved": "string",
        "deleted": False,
    }

    # When
    response = flask_client.put(
        "/api/issue-log/62fb53eb8af7efee9e56e08d", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 200


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    AuthService,
    "ptt_users_list",
    MagicMock(
        return_value=[
            {
                "userId": "cf0142a5-7475-4910-9321-40c323d7a876",
                "email": "<EMAIL>",
                "name": "test",
            }
        ]
    ),
)
@patch.object(
    IssueLogService,
    "list_issue_log",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "62fb53eb8af7efee9e56e088",
                    "clientId": "1",
                    "clientName": "Annie",
                    "status": "Authorized",
                    "resolutionNotes": "resolution notes2",
                    "priority": "Medium",
                    "shortDescription": "description2",
                    "opened": "string",
                    "dateResolved": "2022-02-02",
                }
            ],
            "empty": False,
            "first": True,
            "last": True,
            "pageNumber": 1,
            "numberOfElements": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_export_issue_log_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {
        "query": None,
        "client": None,
        "status": None,
        "toDate": None,
        "page": 1,
        "size": 1,
    }
    open(f"{temp_dir}/IssueLogs", "w")

    # When
    response = flask_client.get("/api/issue-log/export", query_string=request, content_type="application/json")

    # Then
    assert response.status_code == 200


@patch.object(IssueLogService, "list_issue_log", MagicMock(side_effect=ControllerException(message="invalid sort key")))
def test_export_list_issue_log_invalid_sort_key(flask_client):
    skip_auth()
    # Given
    request = {
        "query": None,
        "sortKey": "Opened",
        "client": None,
        "status": None,
        "toDate": None,
        "page": 1,
        "size": 1,
    }

    # When
    response = flask_client.get("/api/issue-log/export", query_string=request, content_type="application/json")

    # Then
    assert response.status_code == 400


@patch.object(
    IssueLogService, "list_issue_log", MagicMock(side_effect=ControllerException(message="invalid sort order"))
)
def test_export_list_issue_log_invalid_sort_order(flask_client):
    skip_auth()
    # Given
    request = {
        "query": None,
        "sortKey": "opened",
        "sortOrder": 0,
        "client": None,
        "status": None,
        "toDate": None,
        "page": 1,
        "size": 1,
    }

    # When
    response = flask_client.get("/api/issue-log/export", query_string=request, content_type="application/json")

    # Then
    assert response.status_code == 400
