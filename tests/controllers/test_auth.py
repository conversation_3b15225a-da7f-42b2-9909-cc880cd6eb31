import json
import pytest
from tests.helpers import skip_auth
from mock import Magic<PERSON>ock, patch
from flaskr.services.auth_service import AuthService


@patch.object(AuthService, "login", MagicMock(return_value="token"))
def test_login(flask_client):
    skip_auth()
    # Given
    request = {"username": "test_user", "password": "password"}
    # When
    response = flask_client.post("/api/login", data=json.dumps(request), content_type="application/json")
    # Then
    assert response.status_code == 200


@patch.object(AuthService, "login", MagicMock(return_value=None))
def test_login_abort(flask_client):
    skip_auth()
    # Given
    request = {"password": "password"}
    # When
    response = flask_client.post("/api/login", data=json.dumps(request), content_type="application/json")
    # Then
    assert response.status_code == 400


@patch.object(AuthService, "login", MagicMock(return_value=None))
def test_login_fail(flask_client):
    skip_auth()
    # Given
    request = {"username": "test_user", "password": "password"}
    # When
    response = flask_client.post("/api/login", data=json.dumps(request), content_type="application/json")
    # Then
    assert response.status_code == 401


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(AuthService, "change_password", MagicMock(return_value={}))
def test_change_password(flask_client):
    skip_auth()
    # Given
    request = {"currentPassword": "password", "password": "Password@123"}
    # When
    response = flask_client.post("/api/change-password", data=json.dumps(request), content_type="application/json")
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(AuthService, "change_password", MagicMock(return_value=None))
def test_change_password_abort(flask_client):
    skip_auth()
    # Given
    request = {"password": "Password@123"}
    # When
    response = flask_client.post("/api/change-password", data=json.dumps(request), content_type="application/json")
    # Then
    assert response.status_code == 400


@patch.object(AuthService, "forgot_password", MagicMock(return_value={}))
def test_forgot_password(flask_client):
    skip_auth()
    # Given
    request = {"username": "Test"}
    # When
    response = flask_client.post("/api/forgot-password", data=json.dumps(request), content_type="application/json")
    # Then
    assert response.status_code == 200


@patch.object(AuthService, "forgot_password", MagicMock(return_value=None))
def test_forgot_password_abort(flask_client):
    skip_auth()
    # Given
    request = {}
    # When
    response = flask_client.post("/api/forgot-password", data=json.dumps(request), content_type="application/json")
    # Then
    assert response.status_code == 400


@patch.object(AuthService, "confirm_forgot_password", MagicMock(return_value={}))
def test_confirm_forgot_password(flask_client):
    skip_auth()
    # Given
    request = {"username": "test", "password": "#Test2021$", "confirmation_code": "103153"}
    # When
    response = flask_client.post(
        "/api/confirm-forgot-password", data=json.dumps(request), content_type="application/json"
    )
    # Then
    assert response.status_code == 200


@patch.object(AuthService, "confirm_forgot_password", MagicMock(return_value=None))
def test_confirm_forgot_password_abort(flask_client):
    skip_auth()
    # Given
    request = {
        "username": "test",
        "password": "#Test2021$",
    }
    # When
    response = flask_client.post(
        "/api/confirm-forgot-password", data=json.dumps(request), content_type="application/json"
    )
    # Then
    assert response.status_code == 400


@patch.object(AuthService, "create_client", MagicMock(return_value={}))
def test_create_client(flask_client):
    skip_auth()
    # Given
    request = {"username": "Test", "email": "<EMAIL>", "name": "Test", "clientId": "t13"}
    # When
    response = flask_client.post("/api/create-client", data=json.dumps(request), content_type="application/json")
    # Then
    assert response.status_code == 200


@patch.object(AuthService, "create_client", MagicMock(return_value=None))
def test_create_client_abort(flask_client):
    skip_auth()
    # Given
    request = {}
    # When
    response = flask_client.post("/api/create-client", data=json.dumps(request), content_type="application/json")
    # Then
    assert response.status_code == 400


@pytest.mark.parametrize(
    "name, email, profilepic",
    [
        ("Emma", "<EMAIL>", "profilepic.jpeg"),
        ("", "<EMAIL>", "profilepic.jpeg"),
        ("Emma", "", "profilepic.jpeg"),
        ("Emma", "<EMAIL>", ""),
    ],
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(AuthService, "user_profile_update", MagicMock(return_value=None))
def test_user_profile_update(flask_client, name, email, profilepic):
    skip_auth()
    # Given
    request = {"name": name, "email": email, "profilePic": profilepic}
    # When
    response = flask_client.post("/api/profile-update", data=json.dumps(request), content_type="application/json")
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(AuthService, "user_profile_download", MagicMock(return_value="file"))
def test_user_profile_download(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/profile-download")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(AuthService, "get_profile_details", MagicMock(return_value={}))
def test_user_profile_details(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/profile-details")
    # Then
    assert response.status_code == 200


@patch.object(AuthService, "respond_to_auth_challenge", MagicMock(return_value={}))
def test_respond_to_auth(flask_client):
    skip_auth()
    # Given
    request = {"username": "test", "password": "Test2022$", "session": ""}
    # When
    response = flask_client.post(
        "/api/respond-to-auth-challenge", data=json.dumps(request), content_type="application/json"
    )
    # Then
    assert response.status_code == 200


@patch.object(AuthService, "logout", MagicMock(return_value={}))
def test_logout(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/logout")

    # Then
    assert response.status_code == 200


@patch.object(AuthService, "refresh_token", MagicMock(return_value={}))
def test_refresh_token(flask_client):
    skip_auth()
    # Given
    request = {"refreshToken": ""}
    # When
    response = flask_client.post("/api/refresh", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(AuthService, "get_user_details", MagicMock(return_value={}))
def test_get_user_details1(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/user-details")
    # Then
    assert response.status_code == 200


@patch.object(AuthService, "list_user_details", MagicMock(return_value=[]))
def test_get_users_list(flask_client):
    # Given
    skip_auth()

    # When
    response = flask_client.get("/api/list-user")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(AuthService, "update_user_details", MagicMock(return_value=None))
def test_update_user(flask_client):
    # Given
    skip_auth()
    request = {
        "userId": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
        "confirmationStatus": "",
        "role": "",
        "booking": True,
        "transaction": False,
        "reports": ["weekly fortnightly reporting", "trust balance"],
    }

    # When
    response = flask_client.put("/api/user-update", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
