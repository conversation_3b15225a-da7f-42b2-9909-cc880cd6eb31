from mock import patch, MagicMock
from flaskr.services.auth_service import AuthService
from flaskr.services.exceptions import ServiceException
from flaskr.services.lookup_service import LookupService
from tests.helpers import skip_auth


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(LookupService, "get_trust_types", MagicMock(return_value=[]))
def test_trust_types(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/trust-types")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(LookupService, "get_trust_types", MagicMock(side_effect=ServiceException))
def test_get_trust_types_service_exception(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/trust-types")

    # Then
    assert response.status_code == 400


@patch.object(LookupService, "get_anomalies", MagicMock(return_value=[]))
def test_anomalies(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/anomalies/1")

    # Then
    assert response.status_code == 200


@patch.object(LookupService, "get_anomalies", MagicMock(side_effect=ServiceException))
def test_get_anomalies_service_exception(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/anomalies/1")

    # Then
    assert response.status_code == 400


@patch.object(LookupService, "get_frequencies", MagicMock(return_value=[]))
def test_frequencies(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/frequencies")

    # Then
    assert response.status_code == 200


@patch.object(LookupService, "get_frequencies", MagicMock(side_effect=ServiceException))
def test_get_frequencies_service_exception(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/frequencies")

    # Then
    assert response.status_code == 400


@patch.object(LookupService, "get_claim_columns", MagicMock(return_value=[]))
def test_claim_columns(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/claim-columns")

    # Then
    assert response.status_code == 200


@patch.object(LookupService, "get_claim_columns", MagicMock(side_effect=ServiceException))
def test_get_claim_columns_service_exception(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/claim-columns")

    # Then
    assert response.status_code == 400


@patch.object(LookupService, "get_banking_columns", MagicMock(return_value=[]))
def test_banking_columns(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/banking-columns")

    # Then
    assert response.status_code == 200


@patch.object(LookupService, "get_banking_columns", MagicMock(side_effect=ServiceException))
def test_get_banking_columns_service_exception(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/banking-columns")

    # Then
    assert response.status_code == 400


@patch.object(LookupService, "get_default_checks", MagicMock(return_value=[]))
def test_default_checks(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/default-checks")

    # Then
    assert response.status_code == 200


@patch.object(LookupService, "get_default_checks", MagicMock(side_effect=ServiceException))
def test_get_default_checks_service_exception(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/default-checks")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(LookupService, "get_currency_list", MagicMock(return_value=[]))
def test_currency_list(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/currency-list")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(LookupService, "get_currency_list", MagicMock(side_effect=ServiceException))
def test_get_currency_list_service_exception(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/currency-list")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(LookupService, "get_bank_list", MagicMock(return_value=[]))
def test_bank_list(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/bank-list")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(LookupService, "get_bank_list", MagicMock(side_effect=ServiceException))
def test_get_bank_list_service_exception(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/bank-list")

    # Then
    assert response.status_code == 400


@patch.object(AuthService, "ptt_users_list", MagicMock(return_value=[]))
def test_list_admin(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/list-admin")

    # Then
    assert response.status_code == 200


@patch.object(AuthService, "ptt_users_list", MagicMock(side_effect=ServiceException))
def test_ptt_users_list_service_exception(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/list-admin")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(LookupService, "get_claim_elements", MagicMock(return_value=[]))
def test_claim_elements(flask_client):
    # Given
    skip_auth()
    # When
    response = flask_client.get("/api/lookup/claim-elements")

    # Then
    assert response.status_code == 200
