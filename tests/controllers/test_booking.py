from datetime import datetime
import tempfile

from bson import ObjectId
from mock import MagicMock, patch
import json
from flaskr.controllers.exceptions import ControllerException
from flaskr.services.booking_service import BookingService
from flaskr.services.exceptions import ServiceException
from flaskr.services.reporting_service import ReportingService
from tests.helpers import skip_auth
import pytest


@patch.object(
    BookingService,
    "booking_search",
    MagicMock(
        return_value={
            "leadPax": "abc",
            "bookingDate": "2022-01-31",
            "departureDate": "2021-06-08",
            "returnDate": "2021-08-12",
            "nights": 2,
            "paxCount": 5,
            "bonding": "string",
            "operator": "geo",
            "totalBookingValue": 151,
            "createdAt": "2020-05-13",
            "updatedAt": "2020-06-24",
            "totalInTrust": 200,
            "totalClaimed": 300,
            "balance": 1500,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "username": "Name", "cognito:groups": ["ptt-admin"]})
def test_booking_search_success(flask_client, patch_db):
    skip_auth()
    # Given
    request = {"clientId": "62419f08888c2b5ed06235d9", "bookingReference": "123"}
    patch_db.user.insert_one({"user_id": "1", "clients": [ObjectId("62419f08888c2b5ed06235d9")]})

    # When
    response = flask_client.post("/api/booking/search", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "leadPax": "abc",
        "bookingDate": "2022-01-31",
        "departureDate": "2021-06-08",
        "returnDate": "2021-08-12",
        "nights": 2,
        "paxCount": 5,
        "bonding": "string",
        "operator": "geo",
        "totalBookingValue": 151,
        "createdAt": "2020-05-13",
        "updatedAt": "2020-06-24",
        "totalInTrust": 200,
        "totalClaimed": 300,
        "balance": 1500,
    }


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(BookingService, "booking_search", MagicMock(side_effect=ControllerException))
def test_booking_search_controller_exception(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "", "bookingReference": ""}

    # When
    response = flask_client.post("/api/booking/search", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    BookingService,
    "list_payments",
    MagicMock(
        return_value={
            "count": 1,
            "payments": [
                {
                    "amount": 9000,
                    "bookingDate": "2022-01-31",
                    "currencyCode": "EUR",
                    "customerType": "abcd",
                    "departureDate": "2021-06-08",
                    "returnDate": "2021-08-12",
                },
            ],
        },
    ),
)
def test_booking_payments_success(flask_client, patch_db):
    skip_auth()
    # Given
    patch_db.user.insert_one({"user_id": "1", "clients": [ObjectId("62419f08888c2b5ed06235d9")]})
    request = {"clientId": "62419f08888c2b5ed06235d9", "bookingReference": "123"}

    # When
    response = flask_client.post("/api/booking/payments", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(BookingService, "list_payments", MagicMock(side_effect=ControllerException))
def test_booking_payments_controller_exception(flask_client):
    skip_auth()
    request = {"clientId": "", "bookingReference": ""}
    # When
    response = flask_client.post("/api/booking/payments", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch.object(
    BookingService,
    "booking_claims",
    MagicMock(
        return_value={
            "count": 1,
            "claims": [
                {
                    "amount": 9000,
                    "claimDate": "2022-01-31",
                    "currencyCode": "EUR",
                },
            ],
        },
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
def test_booking_claims_success(flask_client, patch_db):
    skip_auth()
    # Given
    request = {"clientId": "62419f08888c2b5ed06235d9", "bookingReference": "123"}
    patch_db.user.insert_one({"user_id": "1", "clients": [ObjectId("62419f08888c2b5ed06235d9")]})

    # When
    response = flask_client.post("/api/booking/claims", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(BookingService, "booking_claims", MagicMock(side_effect=ControllerException))
def test_booking_claims_controller_exception(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "", "bookingReference": ""}

    # When
    response = flask_client.post("/api/booking/claims", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    BookingService,
    "booking_claim_checks",
    MagicMock(
        return_value={
            "checks": [
                {
                    "name": "abc",
                    "notes": "string",
                    "selected": False,
                    "client_id": "1",
                    "transaction_id": "61f3614d14dfe6ab9303719f",
                    "modifiedBy": "testName",
                    "updatedAt": datetime.fromisoformat("2022-02-28").isoformat(),
                },
            ],
        },
    ),
)
def test_booking_claim_checks_success(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "1", "transactionId": "61f3614d14dfe6ab9303719f"}

    # When
    response = flask_client.post("/api/booking/claim-checks", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(BookingService, "booking_claim_checks", MagicMock(side_effect=ControllerException))
def test_booking_claim_checks_controller_exception(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "", "transactionId": ""}

    # When
    response = flask_client.post("/api/booking/claim-checks", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch.object(
    BookingService,
    "update_booking_claim_checks",
    MagicMock(return_value=None),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
def test_update_booking_claim_checks_success(flask_client):
    skip_auth()
    # Given
    request = {
        "clientId": "1",
        "transactionId": "61f3614d14dfe6ab9303719f",
        "checks": [
            {
                "_id": "620e00d78d955383b14ab2c2",
                "notes": "string",
                "selected": False,
            },
        ],
    }

    # When
    response = flask_client.put("/api/booking/claim-checks", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(BookingService, "update_booking_claim_checks", MagicMock(side_effect=ControllerException))
def test_update_booking_claim_checks_controller_exception(flask_client):
    skip_auth()
    # Given
    request = {
        "clientId": "",
        "transactionId": "",
        "checks": [
            {
                "_id": "620e00d78d955383b14ab2c2",
                "notes": "string",
                "selected": False,
            },
        ],
    }

    # When
    response = flask_client.put("/api/booking/claim-checks", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    BookingService,
    "list_anomalies",
    MagicMock(
        return_value={
            "anomalies": [
                {
                    "_id": "620e00d78d955383b14ab2c2",
                    "anomalyType": "string",
                    "count": 12,
                    "status": "string",
                },
            ],
        },
    ),
)
def test_booking_list_anomalies_success(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "1", "bookingReference": "123"}

    # When
    response = flask_client.post("/api/booking/anomalies", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(BookingService, "list_anomalies", MagicMock(side_effect=ControllerException))
def test_booking_list_anomalies_controller_exception(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "", "bookingReference": ""}

    # When
    response = flask_client.post("/api/booking/anomalies", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(BookingService, "booking_update", MagicMock(return_value=None))
def test_booking_update_success(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "62160dcda883a1dc69096743", "bookingRef": "123", "leadPax": "Emma"}

    # When
    response = flask_client.put("/api/booking/update", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(BookingService, "booking_update", MagicMock(side_effect=ControllerException))
def test_booking_update_controller_exception(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "", "bookingReference": ""}

    # When
    response = flask_client.put("/api/booking/update", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@pytest.mark.parametrize(
    "non_editable_field",
    [
        "currency_code",
        "balance",
        "remaining_cap_amount",
        "total_in_trust",
        "total_claimed",
        "pax_count",
        "total_booking_value",
        "bonding",
        "created_at",
        "updated_at",
    ],
)
@patch.object(BookingService, "booking_update", MagicMock(return_value=None))
def test_booking_update_controller_exception_not_editable_field(flask_client, non_editable_field):
    skip_auth()
    # Given
    request = {"clientId": "1", "bookingRef": "123", non_editable_field: "string"}

    # When
    response = flask_client.put("/api/booking/update", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    BookingService,
    "list_anomalies",
    MagicMock(
        return_value={
            "anomalies": [
                {
                    "anomalyCategory": "banking",
                    "anomalyType": "Commission Claim Anomaly",
                    "count": 1,
                    "status": "Unresolved",
                },
                {
                    "anomalyCategory": "claims",
                    "anomalyType": "Commission Claim Anomaly",
                    "count": 1,
                    "status": "Unresolved",
                },
            ]
        }
    ),
)
def test_booking_anomaly_export_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {"clientId": "2", "bookingReference": "string", "query": ""}
    open(f"{temp_dir}/BookingAnomalies", "w")

    # When
    response = flask_client.post(
        "/api/booking/anomalies/export",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    BookingService,
    "list_anomalies",
    MagicMock(
        return_value={
            "anomalies": [
                {
                    "anomalyCategory": "banking",
                    "anomalyType": "Commission Claim Anomaly",
                    "count": 1,
                    "status": "Unresolved",
                },
                {
                    "anomalyCategory": "claims",
                    "anomalyType": "Commission Claim Anomaly",
                    "count": 1,
                    "status": "Unresolved",
                },
            ]
        }
    ),
)
def test_booking_anomaly_export_exception(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {"clientId": "2", "bookingReference": "", "query": ""}
    open(f"{temp_dir}/BookingAnomalies", "w")

    # When
    response = flask_client.post(
        "/api/booking/anomalies/export",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    BookingService,
    "list_payments",
    MagicMock(
        return_value={
            "count": 2,
            "deposits": 16000.0,
            "payments": [
                {
                    "_id": "6241905ba12309b31c0e0381",
                    "amount": 8000.0,
                    "bankingId": "6242e4f15155178d668855a4",
                    "bookingDate": "2020-11-02",
                    "currencyCode": "GBP",
                    "customerType": "Direct",
                    "departureDate": "2022-04-04",
                    "paymentDate": "2020-12-01",
                    "returnDate": "2021-10-16",
                },
                {
                    "_id": "624193e6c4049f9e25e2bfbc",
                    "amount": 8000.0,
                    "bankingId": "624193e5888c2b5ed06235d6",
                    "bookingDate": "2020-11-02",
                    "currencyCode": "GBP",
                    "customerType": "Direct",
                    "departureDate": "2022-04-04",
                    "paymentDate": "2020-12-01",
                    "returnDate": "2021-10-16",
                },
            ],
            "refunds": 0,
            "total": 16000.0,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
def test_booking_payments(flask_client, patch_db):
    skip_auth()
    # Given
    patch_db.user.insert_one({"user_id": "1", "clients": [ObjectId("62419f08888c2b5ed06235d9")]})
    temp_dir = tempfile.gettempdir()
    request = {"clientId": "62419f08888c2b5ed06235d9", "bookingReference": "977957", "query": ""}
    open(f"{temp_dir}/BookingPayments", "w")
    # When
    response = flask_client.post(
        "/api/booking/payments/export",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(BookingService, "list_payments", MagicMock(side_effect=ServiceException))
def test_booking_payments_service_exception(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "ED123", "bookingReference": "", "query": ""}
    # When
    response = flask_client.post(
        "/api/booking/payments/export", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    BookingService,
    "booking_claims",
    MagicMock(
        return_value={
            "claims": [
                {
                    "_id": "6256c381c2c97c776cd3dbf9",
                    "amount": 14.0,
                    "claimDate": "2022-04-13",
                    "claimsId": "6256c37bf0be250e15eafaf9",
                    "currencyCode": "GBP",
                    "element": "Deposit",
                    "supplierNames": "None",
                    "supplierRef": "None",
                },
                {
                    "_id": "625e475566e9e838c61dd33d",
                    "amount": 4900.0,
                    "claimDate": "2022-04-13",
                    "claimsId": "625e47519b8d80989e4ff28f",
                    "currencyCode": "GBP",
                    "element": "Deposit",
                },
            ],
            "count": 2,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
def test_booking_claims_export(flask_client, patch_db):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {"clientId": "62419f08888c2b5ed06235d9", "bookingReference": "977957", "query": ""}
    patch_db.user.insert_one({"user_id": "1", "clients": [ObjectId("62419f08888c2b5ed06235d9")]})
    open(f"{temp_dir}/BookingClaims", "w")
    # When
    response = flask_client.post(
        "/api/booking/claims/export",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1", "cognito:groups": ["ptt-admin"]})
@patch.object(BookingService, "booking_claims", MagicMock(side_effect=ServiceException))
def test_booking_claims_service_exception_export(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "ED123", "bookingReference": "", "query": ""}
    # When
    response = flask_client.post(
        "/api/booking/claims/export", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 400
