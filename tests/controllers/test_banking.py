import tempfile
from mock import <PERSON><PERSON>ock, patch
import json

from mongomock import ObjectId
from flaskr.services.auth_service import AuthService


from flaskr.services.banking_service import BankingService
from marshmallow import ValidationError
from flaskr.services.exceptions import ServiceException
from flaskr.services.reporting_service import ReportingService
from tests.helpers import skip_auth
import pytest


@patch.object(
    BankingService,
    "banking_search",
    MagicMock(
        return_value={
            "content": [
                {
                    "bankingId": "61fb95ac47fcbd3d72d5478e",
                    "clientId": "a12",
                    "clientName": "string",
                    "fileDate": "2022-02-03",
                    "items": [{"amount": 26910.86, "count": 7, "currency": "GBP"}],
                    "status": "open",
                    "submission": "2022-02-03",
                }
            ],
            "empty": False,
            "first": True,
            "last": True,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 2,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_banking_search_success(flask_client):
    skip_auth()
    # Given
    request = {
        "query": "str",
        "client": None,
        "assignedTo": 12,
        "status": "complete",
        "date": None,
        "page": 1,
        "size": 4,
    }

    # When
    response = flask_client.post("/api/banking/search", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [
            {
                "bankingId": "61fb95ac47fcbd3d72d5478e",
                "clientId": "a12",
                "clientName": "string",
                "fileDate": "2022-02-03",
                "items": [{"amount": 26910.86, "count": 7, "currency": "GBP"}],
                "status": "open",
                "submission": "2022-02-03",
            }
        ],
        "empty": False,
        "first": True,
        "last": True,
        "numberOfElements": 1,
        "pageNumber": 1,
        "totalElements": 2,
        "totalPages": 1,
    }


@patch.object(
    BankingService,
    "banking_search",
    MagicMock(
        return_value={
            "content": [],
            "empty": True,
            "first": True,
            "last": True,
            "numberOfElements": 0,
            "pageNumber": 1,
            "totalElements": 2,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_banking_search_success_empty(flask_client):
    skip_auth()
    # Given
    request = {
        "query": "str",
        "client": None,
        "assignedTo": 12,
        "status": "complete",
        "date": None,
        "page": 1,
        "size": 4,
    }

    # When
    response = flask_client.post("/api/banking/search", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [],
        "empty": True,
        "first": True,
        "last": True,
        "numberOfElements": 0,
        "pageNumber": 1,
        "totalElements": 2,
        "totalPages": 1,
    }


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch(
    "flaskr.helpers.auth.client_access",
    MagicMock(return_value=None),
)
@patch.object(
    BankingService,
    "banking_create_presigned_url",
    MagicMock(return_value={"fileId": "test-id", "presignedURl": "https://test-url"}),
)
def test_banking_create_presigned_urlsucess(flask_client, patch_db):
    skip_auth()
    # Given
    patch_db.user.insert_one({"user_id": "1", "clients": [ObjectId("62419f08888c2b5ed06235d9")]})
    data = {"fileName": "*********-HYB-Banking.xlsx", "clientId": "62419f08888c2b5ed06235d9"}
    # When
    response = flask_client.get("/api/banking/upload/presigned-url", query_string=data)
    # Then
    assert response.status_code == 200
    assert response.json == {"fileId": "test-id", "presignedURl": "https://test-url"}


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@pytest.mark.parametrize("invalid_request", [{"clientId": 1}, {"fileName": "test.xlsx"}])
def test_banking_create__presigned_url_invalid_request(flask_client, invalid_request):
    skip_auth()
    # Given
    data = invalid_request
    # When
    response = flask_client.get("/api/banking/upload/presigned-url", query_string=data)
    # Then
    assert response.status_code == 400
    assert response.json == {"errors": ["Missing required details"]}


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(BankingService, "banking_create", MagicMock())
def test_banking_create_sucess(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "1", "fileName": "*********-HYB-Banking.xlsx", "fileId": "test-id"}
    # When
    response = flask_client.post("/api/banking/upload", json=request)
    # Then
    assert response.status_code == 201


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@pytest.mark.parametrize(
    "invalid_request",
    [
        {"clientId": "1"},
        {"fileName": "*********-HYB-Banking.xlsx"},
        {"fileId": "test-id"},
        {"clientId": "1", "fileName": "test.xlsx"},
        {"clientId": "1", "fileId": "test-id"},
        {"fileName": "*********-HYB-Banking.xlsx", "fileId": "test-id"},
    ],
)
def test_banking_create_invalid_request(flask_client, invalid_request):
    skip_auth()
    # Given
    request = invalid_request
    # When
    response = flask_client.post("/api/banking/upload", json=request)
    # Then
    assert response.status_code == 400
    assert response.json == {"errors": ["Missing required details"]}


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(BankingService, "update_banking_metadata", MagicMock(return_value=None))
def test_update_banking_metadata(flask_client):
    skip_auth()
    # Given
    request = {
        "notes": "string",
        "status": "string",
        "assignedTo": "62023bc91eed04813999f20f",
        "bankingId": "62023b691eed04813999f201",
    }

    # When
    response = flask_client.put("/api/banking/update", data=json.dumps(request), content_type="application/json")

    # Then"
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(BankingService, "update_banking_metadata", MagicMock(side_effect=ValidationError("Invalid data")))
def test_update_banking_metadata_validation_error(flask_client):
    skip_auth()
    # Given
    request = {"notes": 123, "status": 123, "assignedTo": 123, "bankingId": "62023b691eed04813999f201"}
    # When
    response = flask_client.put("/api/banking/update", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@pytest.mark.parametrize(
    "invalid_request", [{"bankingId": "62023b691eed04813999f201"}, {"notes": "string"}, {"status": "string"}, {}]
)
def test_update_banking_metadata_inavalid_request(flask_client, invalid_request):
    skip_auth()
    # Given
    request = invalid_request
    # When
    response = flask_client.put("/api/banking/update", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch.object(
    BankingService,
    "get_banking_details",
    MagicMock(
        return_value={
            "clientId": "1",
            "clientName": "ABC",
            "createdAt": "2020-05-19",
            "notes": "string",
            "status": "string",
            "updatedAt": "2020-08-24",
            "bankingFiles": [
                {
                    "fileId": "111258412241665569",
                    "deposit": 584,
                    "fileName": "string",
                    "items": 5,
                    "status": "string",
                    "submittedDate": "2021-08-10",
                }
            ],
        },
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_banking_details(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/banking/details/{claim_id}")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(BankingService, "get_banking_details", MagicMock(side_effect=ServiceException))
def test_banking_details_service_exception(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/banking/details/invalid_banking_details")

    # Then
    assert response.status_code == 400


@patch.object(
    BankingService,
    "get_payments",
    MagicMock(
        return_value={
            "bookingRef": "123",
            "bookingDate": "2020-05-20",
            "currencyCode": "EUR",
            "amount": 9000,
            "departureDate": "2021-06-09",
            "returnDate": "2021-08-14",
            "customerType": "string",
        },
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_banking_payments(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/banking/payments/61f6ef73d0a2915b6d252942?query=&page=1&size=1&sortKey=&sortOrder="
    )

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(BankingService, "get_payments", MagicMock(side_effect=ServiceException))
def test_banking_payments_service_exception(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/banking/payments/61f6ef73d0a2915b6d252942?query=&page=&size=&sortKey=&sortOrder=")

    # Then
    assert response.status_code == 400


@patch.object(
    BankingService,
    "get_banking_anomalies",
    MagicMock(
        return_value={
            "anomaly_id": "62171f66f9487bcc2b82f6a5",
            "status": "string",
            "booking_ref": "123",
            "anomaly_type": "string",
            "lead_pax": "abc",
            "booking_date": "2022-01-31",
            "departure_date": "2021-06-08",
            "return_date": "2021-08-12",
            "currency_code": "EUR",
            "value": 9000.0,
            "symbol": "€",
            "clientId": "1",
        },
    ),
)
def test_banking_anomaly(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/banking/anomalies/620de324d9ee8adf72897c85?query=&sortKey=&sortOrder=")
    # Then
    assert response.status_code == 200


@patch.object(BankingService, "get_banking_anomalies", MagicMock(side_effect=ServiceException))
def test_banking_anomaly_service_exception(flask_client):
    skip_auth()
    # When
    banking_id = "invalid"
    response = flask_client.get(f"/api/banking/anomalies/{banking_id}?query=&sortKey=&sortOrder=")
    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(BankingService, "update_banking_transaction", MagicMock(return_value=None))
def test_update_banking_transaction(flask_client):
    skip_auth()
    # Given
    request = {"amount": 2500}
    # When
    response = flask_client.put(
        "/api/banking/transaction/620b75d59b53aa92cd5dbb1", data=json.dumps(request), content_type="application/json"
    )
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(BankingService, "update_banking_transaction", MagicMock(side_effect=ValidationError("Invalid data")))
def test_update_banking_transaction_validation_error(flask_client):
    skip_auth()
    # Given
    request = {"check": 123}
    transactionId = "invalid"
    # When
    response = flask_client.put(
        f"/api/banking/transaction/{transactionId}", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_update_banking_transaction_inavalid_request(flask_client):
    skip_auth()
    # Given
    request = {"bookingRef": "123"}
    transaction_id = "620b75d59b53aa92cd5dbb1"
    # When
    response = flask_client.put(
        f"/api/banking/transaction/{transaction_id}", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 400


@patch.object(
    BankingService,
    "banking_get_transaction",
    MagicMock(
        return_value={
            "amount": 600.0,
            "bookingDate": "2020-08-21",
            "bookingRef": "977957",
            "departureDate": "2022-03-26",
            "leadPax": "KV",
            "returnDate": "2022-04-08",
            "status": "live",
            "statusReason": "string",
            "supplierNames": "Hoseason",
            "supplierRef": "A45EX",
            "totalDueSupplier": 123.0,
            "totalPaid": 11392.0,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_banking_get_transaction(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/banking/get-transaction/{transaction_id}")

    # Then
    assert response.status_code == 200


@patch.object(BankingService, "banking_get_transaction", MagicMock(side_effect=ServiceException))
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_banking_get_transaction_exception(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/banking/get-transaction/invalid_banking_details")

    # Then
    assert response.status_code == 400


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    BankingService,
    "get_banking_anomalies",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "62b048932d8745162f2c520e",
                    "anomalyId": "6246c97784259494f15aad4e",
                    "anomalyType": "Negative Funds in Trust",
                    "bookingDate": "2020-11-02",
                    "bookingRef": "977957",
                    "clientId": "CR7",
                    "createdAt": "Mon, 20 Jun 2022 10:14:43 GMT",
                    "currency_code": "USD",
                    "dateOfReturn": "2021-10-16",
                    "dateOfTravel": "2022-04-04",
                    "leadPassenger": "aswathy",
                    "modifiedBy": None,
                    "status": "Resolved",
                    "symbol": "$",
                    "updatedAt": "Mon, 20 Jun 2022 10:14:43 GMT",
                    "value": 14.0,
                },
            ],
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
def test_banking_anomaly_search_export_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {"clientId": "a12", "query": "str", "fromDate": "", "toDate": "", "page": "", "size": ""}
    open(f"{temp_dir}/BankingAnomalies", "w")

    # When
    response = flask_client.get(
        "/api/banking/anomalies/625f96b0c4b57cdb24ef5e4c/export?query=&sortKey=&sortOrder=",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    BankingService,
    "get_banking_anomalies",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "62b048932d8745162f2c520e",
                    "anomalyId": "6246c97784259494f15aad4e",
                    "anomalyType": "Negative Funds in Trust",
                    "bookingDate": "2020-11-02",
                    "bookingRef": "977957",
                    "clientId": "CR7",
                    "createdAt": "Mon, 20 Jun 2022 10:14:43 GMT",
                    "currency_code": "USD",
                    "dateOfReturn": "2021-10-16",
                    "dateOfTravel": "2022-04-04",
                    "leadPassenger": "aswathy",
                    "modifiedBy": None,
                    "status": "Resolved",
                    "symbol": "$",
                    "updatedAt": "Mon, 20 Jun 2022 10:14:43 GMT",
                    "value": 14.0,
                },
            ],
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
def test_banking_get_anomalies_export_exception(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "a12", "query": "str", "fromDate": "", "toDate": "", "page": "", "size": ""}
    # When
    response = flask_client.get(
        "/api/banking/anomalies/625f96b0c4b57cdb24ef5e4c/export?query=&sortKey=aaa&sortOrder=0",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 400


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    BankingService,
    "get_payments",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "6242e4f567a9295f72c0bdc1",
                    "amount": 11000.0,
                    "bookingDate": "2020-11-30",
                    "bookingRef": "130000",
                    "currencyCode": "EUR",
                    "customerType": "Customer",
                    "departureDate": "2022-02-22",
                    "paymentDate": "2020-12-01",
                    "returnDate": "2022-04-26",
                },
                {
                    "_id": "6242e4f567a9295f72c0bdb4",
                    "amount": 10000.0,
                    "bookingDate": "2020-11-30",
                    "bookingRef": "976287",
                    "currencyCode": "GBP",
                    "customerType": "Direct",
                    "departureDate": "2022-10-20",
                    "paymentDate": "2020-12-01",
                    "returnDate": "2022-04-26",
                },
            ],
            "numberOfElements": 2,
            "pageNumber": 1,
            "totalElements": 2,
            "totalPages": 1,
        }
    ),
)
def test_banking_get_payment_export_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {"query": ""}
    open(f"{temp_dir}/Payments", "w")

    # When
    response = flask_client.get(
        "/api/banking/payments/6242e4f15155178d668855a4/export?query=&page=&size=18&sortKey=currencyCode&sortOrder=1",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    BankingService,
    "get_payments",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "6242e4f567a9295f72c0bdc1",
                    "amount": 11000.0,
                    "bookingDate": "2020-11-30",
                    "bookingRef": "130000",
                    "currencyCode": "EUR",
                    "customerType": "Customer",
                    "departureDate": "2022-02-22",
                    "paymentDate": "2020-12-01",
                    "returnDate": "2022-04-26",
                },
                {
                    "_id": "6242e4f567a9295f72c0bdb4",
                    "amount": 10000.0,
                    "bookingDate": "2020-11-30",
                    "bookingRef": "976287",
                    "currencyCode": "GBP",
                    "customerType": "Direct",
                    "departureDate": "2022-10-20",
                    "paymentDate": "2020-12-01",
                    "returnDate": "2022-04-26",
                },
            ],
            "numberOfElements": 2,
            "pageNumber": 1,
            "totalElements": 2,
            "totalPages": 1,
        }
    ),
)
def test_banking_get_payment_export_exception(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {"query": ""}
    open(f"{temp_dir}/Payments", "w")

    # When
    response = flask_client.get(
        "/api/banking/payments/6242e4f15155178d668855a4/export?query=&page=&size=18&sortKey=currency&sortOrder=0",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 400


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    AuthService,
    "ptt_users_list",
    MagicMock(
        return_value=[
            {
                "userId": "cf0142a5-7475-4910-9321-40c323d7a876",
                "email": "<EMAIL>",
                "name": "test",
            }
        ]
    ),
)
@patch.object(
    BankingService,
    "banking_search",
    MagicMock(
        return_value={
            "content": [
                {
                    "assignedTo": "628e12574b4030a075695435",
                    "bankingId": "62a826d5a2f265631c74046d",
                    "clientId": "CR7",
                    "clientName": "Edstem Test User",
                    "fileDate": "2022-04-10",
                    "items": [
                        {"amount": 93000.0, "count": 12, "currency": "GBP", "symbol": "£"},
                        {"amount": 35000.0, "count": 5, "currency": "USD", "symbol": "$"},
                        {"amount": 7000.0, "count": 1, "currency": "INR", "symbol": "₹"},
                    ],
                    "notes": "",
                    "status": "Submitted",
                    "submission": "2022-04-13T05:54:12.661000",
                }
            ],
            "empty": False,
            "first": True,
            "last": True,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_banking_get_search_export_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {"query": "", "client": "CR7", "assignedTo": "", "status": "", "date": "", "page": 1, "size": 5}
    open(f"{temp_dir}/BankingSearch", "w")

    # When
    response = flask_client.post(
        "/api/banking/search/export",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(
    BankingService,
    "get_uploaded_banking_transaction",
    MagicMock(
        return_value={
            "amount": 600.0,
            "bookingDate": "2020-08-21",
            "bookingRef": "977957",
            "departureDate": "2022-03-26",
            "leadPax": "KV",
            "returnDate": "2022-04-08",
            "supplierNames": "f0be81e0-72a5-4234-92ad-85f5a929267e",
            "status": "live",
            "statusReason": "string",
            "supplierRef": "A45EX",
            "totalDueSupplier": 123.0,
            "totalPaid": 11392.0,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_get_uploaded_banking_transaction(flask_client, patch_db):
    skip_auth()
    request_data = {"supplierName": "f0be81e0-72a5-4234-92ad-85f5a929267e", "clientId": "62419f08888c2b5ed06235d9"}
    patch_db.user.insert_one({"user_id": "1", "clients": [ObjectId("62419f08888c2b5ed06235d9")]})
    # When
    response = flask_client.get("/api/banking/get-transaction", query_string=request_data)

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_get_uploaded_banking_transaction_exception(flask_client):
    skip_auth()
    request_data = {}
    # When
    response = flask_client.get("/api/banking/get-transaction", query_string=request_data)

    # Then
    assert response.status_code == 400
    assert response.json == {"errors": ["Missing required details"]}

    patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_get_uploaded_banking_transaction_unauthorized(flask_client, patch_db):
    skip_auth()
    patch_db.user.insert_one({"user_id": "1", "clients": [ObjectId("62419f08888c2b5ed06235d1")]})
    request_data = {"supplierName": "f0be81e0-72a5-4234-92ad-85f5a929267e", "clientId": "62419f08888c2b5ed06235d9"}
    # When
    response = flask_client.get("/api/banking/get-transaction", query_string=request_data)

    # Then
    assert response.status_code == 403
