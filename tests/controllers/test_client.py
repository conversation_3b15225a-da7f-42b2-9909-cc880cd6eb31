from mock import MagicMock, patch
import json
import pytest
from werkzeug.datastructures import ImmutableMultiDict

from flaskr.controllers.exceptions import ControllerException
from tests.data.client_banking_column import ClientBankingColumnRequestBuilder
from tests.data.client_claims_column import ClientClaimColumnRequestBuilder
from tests.helpers import skip_auth
from flaskr.services.exceptions import ServiceException
from marshmallow import ValidationError
from flaskr.services.client_service import ClientService
from tests.data.client_basic_info import ClientBasicInfoRequestBuilder
from tests.data.client_insurance_info import ClientInsuranceInfoRequestBuilder
from tests.data.client_anomalies import ClientAnomalyRequestBuilder, ClientAnomalyRequestInvalidData
from tests.data.client_atol_info import ClientAtolInfoRequestBuilder

from tests.data.client_bank_info import ClientBankInfoRequestBuilder


@patch.object(ClientService, "upsert_basic_info", MagicMock(return_value=None))
def test_upsert_basic_info(flask_client):
    skip_auth()
    # Given
    request = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_updated_request().build()

    # When
    response = flask_client.put("/api/client/basic-info", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch.object(ClientService, "upsert_basic_info", MagicMock(side_effect=ValidationError("Invalid data")))
def test_upsert_basic_info_validation_error(flask_client):
    skip_auth()
    # Given
    request = ClientBasicInfoRequestBuilder.a_client_basic_info_request().with_updated_request().build()

    # When
    response = flask_client.put("/api/client/basic-info", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch.object(ClientService, "upsert_bank_info", MagicMock(return_value=None))
def test_upsert_bank_info_success(flask_client):
    # Given
    request = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_updated_request().build()

    # When
    response = flask_client.put("/api/client/bank-account", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch.object(ClientService, "upsert_bank_info", MagicMock(side_effect=ValidationError("Invalid data")))
def test_upsert_bank_info_validation_error(flask_client):
    # Given
    request = ClientBankInfoRequestBuilder.a_client_bank_info_request().with_updated_request().build()

    # When
    response = flask_client.put("/api/client/bank-account", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch.object(ClientService, "upsert_insurance_atol", MagicMock(return_value=None))
def test_upsert_insurance_atol_info_success(flask_client):
    skip_auth()
    # Given
    request = {
        "atol": [ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_updated_request().build()],
        "clientId": 1,
    }

    # When

    response = flask_client.put(
        "/api/client/insurance-and-atol", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 200


@patch.object(ClientService, "upsert_insurance_atol", MagicMock(side_effect=ValidationError("Invalid data")))
def test_upsert_insurance_info_validation_error(flask_client, patch_db):
    # Given
    request = {
        "insurance": [
            json.dumps(
                ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_updated_request().build()
            )
        ],
        "atol": [json.dumps(ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_updated_request().build())],
        "clientId": "1a2ww",
    }

    # When
    response = flask_client.put(
        "/api/client/insurance-and-atol", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 400


@pytest.mark.parametrize(
    "invalid_data",
    [
        {"atol": [ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_updated_request().build()]},
        {
            "insurance": [
                ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_updated_request().build()
            ]
        },
        {
            "insurance": [
                ClientInsuranceInfoRequestBuilder.a_client_insurance_info_request().with_updated_request().build()
            ],
            "atol": [ClientAtolInfoRequestBuilder.a_client_atol_info_request().with_updated_request().build()],
        },
    ],
)
@patch.object(ClientService, "upsert_insurance_atol", MagicMock(side_effect=None))
def test_upsert_insurance_atol_info_invalid_data(flask_client, invalid_data):
    # Given
    request = invalid_data

    # When
    response = flask_client.put(
        "/api/client/insurance-and-atol", data=json.dumps(request), content_type="application" "/json"
    )

    # Then
    assert response.status_code == 400
    assert response.json == {"errors": ["Missing required details"]}


@patch.object(ClientService, "upsert_anomalies", MagicMock(return_value=None))
def test_upsert_anomalies_success(flask_client):
    skip_auth()
    # Given

    request = ClientAnomalyRequestBuilder.a_client_anomaly_request().with_updated_request().build()

    # When
    response = flask_client.put("/api/client/anomalies", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch.object(ClientService, "upsert_anomalies", MagicMock(side_effect=ValidationError("Invalid data")))
def test_upsert_anomalies_validation_error(flask_client):
    skip_auth()
    # Given

    request = ClientAnomalyRequestBuilder.a_client_anomaly_request().with_updated_request().build()

    # When

    response = flask_client.put("/api/client/anomalies", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@pytest.mark.parametrize(
    "data",
    [
        {"anomalyId": {**ClientAnomalyRequestInvalidData.ANOMALY_ID_MISSING}},
        {
            "clientId": "1a2ww",
        },
    ],
)
@patch.object(ClientService, "upsert_anomalies", MagicMock(side_effect=None))
def test_upsert_anomalies_invalid1(flask_client, data):
    skip_auth()
    # Given
    request = data

    # When
    response = flask_client.put("/api/client/anomalies", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400
    assert response.json == {"errors": ["anomalies cannot be empty"]}


@patch.object(
    ClientService,
    "get_client",
    MagicMock(
        return_value={
            "_id": "6215e46650af15f0062b7548",
            "additionChecks": [
                {
                    "_id": "6215e46664eee884113c3a94",
                    "checkName": "updated",
                    "clientId": "1",
                    "description": "string1",
                    "relatedElement": "string1",
                    "shortName": "string1",
                }
            ],
            "address": {
                "clientId": "1",
                "country": "string",
                "id": "6215e46650af15f0062b754f",
                "line1": "updated",
                "line2": "string",
                "line3": "string",
                "postcode": "string",
                "town": "string",
            },
            "anomalies": [],
            "atol": [],
            "bankDetails": [],
            "bankingColumns": ["61efc57260cc72bf21f1c441"],
            "claimColumns": [],
            "clientId": "1",
            "email": "<EMAIL>",
            "existingClient": True,
            "frequency": ["61ee2f1dcf10b991afdaf54a"],
            "friendlyName": "updated",
            "fullName": "ABC",
            "goLiveDate": "2021-10-02",
            "insurance": [],
            "limits": {
                "clientId": "1",
                "currency": "EUR",
                "id": "6215e46650af15f0062b7551",
                "maximumNoOfClaims": 1.0,
                "totalAnnualRevenue": 12.0,
            },
            "pointOfContact": "string",
            "reuseOldBooking": False,
            "status": "draft",
            "typeOfTrustAccount": "61ef4e0da0d0ef7c56884319",
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_client_details(flask_client):
    # When
    response = flask_client.get("/api/client/1")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClientService, "get_client", MagicMock(side_effect=ServiceException))
def test_fetch_client_service_exception(flask_client):
    # When
    response = flask_client.get("/api/client/1")

    # Then
    assert response.status_code == 400


@patch.object(
    ClientService,
    "clients_details_list",
    MagicMock(
        return_value=[
            {
                "clientId": "1",
                "currency": "string",
                "friendlyName": "ab",
                "insuranceExpiryDate": "2022-01-01",
                "atolExpiryDate": "2022-01-01",
                "showInsuranceExpiry": True,
                "showAtolExpiry": True,
                "typeOfTrustAccount": 1,
            }
        ]
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_search_client_list_success(flask_client):
    # Given

    request = {"query": "client"}

    # When
    response = flask_client.post("/api/client/search", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [
            {
                "clientId": "1",
                "currency": "string",
                "friendlyName": "ab",
                "insuranceExpiryDate": "2022-01-01",
                "atolExpiryDate": "2022-01-01",
                "showInsuranceExpiry": True,
                "showAtolExpiry": True,
                "typeOfTrustAccount": 1,
            }
        ],
        "empty": False,
    }


@patch.object(ClientService, "clients_details_list", MagicMock(return_value=[]))
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_search_client_list_success_empty(flask_client):
    # Given
    request = {"query": "client"}

    # When
    response = flask_client.post("/api/client/search", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {"content": [], "empty": True}


@patch.object(ClientService, "client_file_upload", MagicMock(side_effect=ControllerException))
def test_file_upload_missing_details(flask_client):
    skip_auth()
    # Given
    request = {"file": None, "clientId": "1", "type": "insurance"}
    # When
    response = flask_client.post("/api/client/file", data=json.dumps(request), content_type="application/json")
    # Then
    assert response.status_code == 400
    assert response.json == {"errors": ["Missing required details"]}


@pytest.mark.parametrize(
    "data",
    [
        ({"file_type": "atol", "file_id": "be761366-9579-4e0d-890b-8ab1e6b82d3a"}),
        ({"file_type": "insurance", "file_id": "be761366-9579-4e0d-890b-8ab1e6b82d3a"}),
    ],
)
@patch.object(ClientService, "client_file_download", MagicMock(return_value="a file"))
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_client_file_download12(flask_client, data):
    skip_auth()
    file_type = data["file_type"]
    file_id = data["file_id"]
    # When
    response = flask_client.get(f"/api/client/file/{file_type}/{file_id}", content_type="multipart/form-data")
    assert response


@pytest.mark.parametrize(
    "data",
    [
        ({"file_type": "", "file_id": ""}),
        ({"file_type": "atol", "file_id": ""}),
        ({"file_type": "insurance", "file_id": ""}),
        ({"file_type": "", "file_id": "be761366-9579-4e0d-890b-8ab1e6b82d3a"}),
    ],
)
@patch.object(ClientService, "client_file_download", MagicMock(side_effect=ValidationError("Invalid data")))
def test_client_file_download_invalid12(flask_client, data):
    file_type = data["file_type"]
    file_id = data["file_id"]

    # When
    response = flask_client.get(f"/api/client/file/{file_type}/{file_id}", content_type="multipart/form-data")

    assert response


@patch.object(ClientService, "upsert_banking_columns", MagicMock(return_value=None))
def test_upsert_banking_column_success(flask_client):
    skip_auth()
    # Given

    request = {
        "columns": [
            json.dumps(
                ClientBankingColumnRequestBuilder.a_client_banking_column_request().with_updated_request().build()
            )
        ],
        "clientId": 1,
    }
    # When
    response = flask_client.put(
        "/api/client/banking-columns", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 200


@patch.object(ClientService, "upsert_banking_columns", MagicMock(side_effect=ValidationError("Invalid data")))
def test_upsert_banking_column_validation_error(flask_client):
    skip_auth()
    # Given

    request = {
        "clientId": 1,
    }

    # When

    response = flask_client.put(
        "/api/client/banking-columns", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 400


@patch.object(ClientService, "upsert_claim_columns", MagicMock(return_value=None))
def test_upsert_claim_column_success(flask_client):
    skip_auth()
    # Given
    request = {
        "columns": [
            json.dumps(ClientClaimColumnRequestBuilder.a_client_claim_column_request().with_updated_request().build())
        ],
        "clientId": 1,
    }

    # When
    response = flask_client.put("/api/client/claim-columns", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch.object(ClientService, "upsert_claim_columns", MagicMock(side_effect=ValidationError("Invalid data")))
def test_upsert_claim_column_validation_error(flask_client):
    skip_auth()
    # Given

    request = {
        "clientId": 1,
    }

    # When
    response = flask_client.put("/api/client/claim-columns", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClientService, "list_clients", MagicMock(return_value=[]))
def test_get_list_success(flask_client):
    # Given
    skip_auth()

    # When
    response = flask_client.get("/api/client/list?claimFromTBR=True")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClientService, "list_clients", MagicMock(return_value=[]))
def test_get_list_success_without_arg(flask_client):
    # Given
    skip_auth()

    # When
    response = flask_client.get("/api/client/list")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClientService, "update_client_users", MagicMock(return_value=None))
def test_update_client_user(flask_client):
    # Given
    skip_auth()

    # When
    response = flask_client.put("/api/client/1/users", json=json.dumps({"test": "data"}))

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClientService, "update_client_users", MagicMock(side_effect=ServiceException))
def test_update_client_user_exception(flask_client):
    # Given
    skip_auth()

    # When
    response = flask_client.put("/api/client/1/users")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClientService, "get_client_users", MagicMock(return_value={}))
def test_get_client_user(flask_client):
    # Given
    skip_auth()

    # When
    response = flask_client.get("/api/client/1/users")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClientService, "get_client_users", MagicMock(side_effect=ServiceException))
def test_get_client_user_exception(flask_client):
    # Given
    skip_auth()

    # When
    response = flask_client.get("/api/client/1/users")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClientService, "client_file_upload", MagicMock(side_effect=ServiceException))
def test_get_client_file_upload_exception(flask_client):
    # Given
    skip_auth()
    request = ImmutableMultiDict([("clientId", "1"), ("type", "banking")])

    # When
    response = flask_client.post("/api/client/file", data=json.dumps(request), content_type="form-data/multipart")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClientService, "update_escrow_multiplier", MagicMock(return_value=None))
def test_update_escrow_multiplier(flask_client):
    skip_auth()
    # Given
    request = {"date": "2022-09-01", "multiplier": 5, "debitMultiplier": 0.4, "creditMultiplier": 0.9}
    # When
    response = flask_client.put(
        "/api/client/2146/escrow_multiplier", data=json.dumps(request), content_type="application/json"
    )
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClientService, "update_escrow_multiplier", MagicMock(return_value=None))
def test_update_escrow_multiplier_without_credit_debit_multiplier(flask_client):
    skip_auth()
    # Given
    request = {"date": "2022-09-01", "multiplier": 5}
    # When
    response = flask_client.put(
        "/api/client/2146/escrow_multiplier", data=json.dumps(request), content_type="application/json"
    )
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClientService, "update_escrow_multiplier", MagicMock(side_effect=ServiceException))
def test_update_escrow_multiplier_service_exception(flask_client):
    skip_auth()
    # Given
    request = {"multiplier": 5}
    # When
    response = flask_client.put(
        "/api/client/2146/escrow_multiplier", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(
    ClientService,
    "get_escrow_multiplier",
    MagicMock(return_value={"modified_by": "string", "multiplier": 9}),
)
def test_get_escrow_multiplier(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/client/2146/escrow_multiplier?date=2022-09-05")

    # Then
    assert response.status_code == 200


@patch.object(
    ClientService,
    "get_escrow_amount_banking",
    MagicMock(return_value=[{"currency": "GBP", "count": 1, "escrowAmount": 17000.0, "actualAmount": 34000.0}]),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_get_escrow_amount_banking(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/client/CR7/escrow_amount?fileType=banking&id=62aafc5336feb4b25b99f9f5")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClientService, "list_atol_standard_clients", MagicMock(return_value=[]))
def test_get_client_atol_standard_list_success(flask_client):
    # Given
    skip_auth()

    # When
    response = flask_client.get("/api/client/atol-clients")

    # Then
    assert response.status_code == 200
