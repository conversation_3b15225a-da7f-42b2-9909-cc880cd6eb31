from mock import patch, MagicMock
from tests.helpers import skip_auth
from flaskr.services.powerbi_service import powerbi_service


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth._verify_claims", lambda x, y: True)  # Mock auth verification to return True
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
@patch.object(powerbi_service, "get_report_embed_info")
def test_get_dashboard_embed_info_success(mock_get_report_embed_info, flask_client, monkeypatch):
    # Given
    skip_auth()
    mock_get_report_embed_info.return_value = {
        "embedUrl": "https://app.powerbi.com/reportEmbed",
        "token": "dummy-token-123",
        "reportId": "report-456",
        "workspaceId": "workspace-123",
    }
    monkeypatch.setattr(
        "flask.current_app.config.get",
        lambda key: "workspace-123"
        if key == "POWERBI_WORKSPACE_ID"
        else "report-456"
        if key == "POWERBI_REPORT_ID"
        else None,
    )

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", return_value=True):
        response = flask_client.get("/api/powerbi/dashboard/embed-info")

    # Then
    assert response.status_code == 200
    data = response.get_json()
    assert data["embedUrl"] == "https://app.powerbi.com/reportEmbed"
    assert data["embedToken"] == "dummy-token-123"
    assert data["reportId"] == "report-456"
    assert data["workspaceId"] == "workspace-123"
    mock_get_report_embed_info.assert_called_once_with("report-456", "workspace-123", "test-user-id")


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth._verify_claims", lambda x, y: True)  # Mock auth verification to return True
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
@patch.object(powerbi_service, "get_report_embed_info")
def test_get_dashboard_embed_info_missing_config(mock_get_report_embed_info, flask_client, monkeypatch):
    # Given
    skip_auth()
    monkeypatch.setattr("flask.current_app.config.get", lambda key: None)

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", return_value=True):
        response = flask_client.get("/api/powerbi/dashboard/embed-info")

    # Then
    assert response.status_code == 500
    data = response.get_json()
    assert "error" in data
    assert "PowerBI configuration not found" in data["error"]
    mock_get_report_embed_info.assert_not_called()


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth._verify_claims", lambda x, y: True)  # Mock auth verification to return True
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
def test_get_dashboard_embed_info_service_error(flask_client, monkeypatch):
    # Given
    skip_auth()
    monkeypatch.setattr(
        "flask.current_app.config.get",
        lambda key: "workspace-123"
        if key == "POWERBI_WORKSPACE_ID"
        else "report-456"
        if key == "POWERBI_REPORT_ID"
        else None,
    )

    # Mock the service to raise an exception
    monkeypatch.setattr(powerbi_service, "get_report_embed_info", MagicMock(side_effect=Exception("PowerBI API error")))

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", return_value=True):
        response = flask_client.get("/api/powerbi/dashboard/embed-info")

    # Then
    assert response.status_code == 500
    data = response.get_json()
    assert "error" in data
    assert "Error getting PowerBI dashboard info" in data["error"] or "PowerBI API error" in data["error"]


# Test for ERV report
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth._verify_claims", lambda x, y: True)  # Mock auth verification to return True
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
@patch.object(powerbi_service, "get_report_embed_info")
@patch.object(powerbi_service, "get_erv_powerbi_dashboard_config")
def test_get_erv_dashboard_embed_info_success(mock_get_erv_config, mock_get_report_embed_info, flask_client, monkeypatch):
    # Given
    skip_auth()
    mock_get_erv_config.return_value = {
        "report_id": "erv-report-789",
        "name": "ERV Dashboard"
    }
    mock_get_report_embed_info.return_value = {
        "embedUrl": "https://app.powerbi.com/reportEmbed",
        "token": "dummy-token-123",
        "reportId": "erv-report-789",
        "workspaceId": "workspace-123",
    }
    monkeypatch.setattr(
        "flask.current_app.config.get",
        lambda key: "workspace-123"
        if key == "POWERBI_WORKSPACE_ID"
        else "erv-report-789"
        if key == "POWERBI_ERV_REPORT_ID"
        else None,
    )

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", return_value=True):
        response = flask_client.get("/api/powerbi/dashboard/embed-info?report_type=erv")

    # Then
    assert response.status_code == 200
    data = response.get_json()
    assert data["embedUrl"] == "https://app.powerbi.com/reportEmbed"
    assert data["embedToken"] == "dummy-token-123"
    assert data["reportId"] == "erv-report-789"
    assert data["workspaceId"] == "workspace-123"
    mock_get_report_embed_info.assert_called_once_with("erv-report-789", "workspace-123", "test-user-id")


# Test for unauthorized access to ERV report
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth._verify_claims", lambda x, y: False)  # Mock auth verification to return False
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
def test_get_erv_dashboard_embed_info_unauthorized(flask_client):
    # Given
    skip_auth()

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", return_value=False):
        response = flask_client.get("/api/powerbi/dashboard/embed-info?report_type=erv")

    # Then
    assert response.status_code == 403
    data = response.get_json()
    assert "msg" in data
    assert "ERV dashboard access restricted" in data["msg"]


# Test for unauthorized access to default report
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth._verify_claims", lambda x, y: False)  # Mock auth verification to return False
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
def test_get_dashboard_embed_info_unauthorized(flask_client):
    # Given
    skip_auth()

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", return_value=False):
        response = flask_client.get("/api/powerbi/dashboard/embed-info")

    # Then
    assert response.status_code == 200
    data = response.get_json()


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
def test_get_dashboard_embed_info_token_error(flask_client, monkeypatch):
    # Given
    skip_auth()

    # When
    with patch("flaskr.controllers.powerbi.get_token", side_effect=Exception("Invalid token")), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", return_value=True):
        response = flask_client.get("/api/powerbi/dashboard/embed-info")

    # Then
    assert response.status_code == 500
    data = response.get_json()
    assert "error" in data
    assert "Authentication error" in data["error"]


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
def test_get_dashboard_embed_info_claims_error(flask_client, monkeypatch):
    # Given
    skip_auth()

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", side_effect=Exception("Invalid claims")
    ), patch("flaskr.controllers.powerbi._verify_claims", return_value=True):
        response = flask_client.get("/api/powerbi/dashboard/embed-info")

    # Then
    assert response.status_code == 500
    data = response.get_json()
    assert "error" in data
    assert "Authentication error" in data["error"]


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
def test_get_erv_dashboard_embed_info_verify_claims_error(flask_client, monkeypatch):
    # Given
    skip_auth()
    monkeypatch.setattr(
        "flask.current_app.config.get",
        lambda key: "workspace-123"
        if key == "POWERBI_WORKSPACE_ID"
        else "erv-report-789"
        if key == "POWERBI_ERV_REPORT_ID"
        else None,
    )

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", side_effect=Exception("Verification error")):
        response = flask_client.get("/api/powerbi/dashboard/embed-info?report_type=erv")

    # Then
    assert response.status_code == 500
    data = response.get_json()
    assert "error" in data
    assert "Authorization error" in data["error"]


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
def test_get_dashboard_embed_info_verify_claims_error(flask_client, monkeypatch):
    # Given
    skip_auth()
    monkeypatch.setattr(
        "flask.current_app.config.get",
        lambda key: "workspace-123"
        if key == "POWERBI_WORKSPACE_ID"
        else "report-456"
        if key == "POWERBI_REPORT_ID"
        else None,
    )

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", side_effect=Exception("Verification error")):
        response = flask_client.get("/api/powerbi/dashboard/embed-info")

    # Then
    assert response.status_code == 500
    data = response.get_json()
    assert "error" in data
    assert "Error getting PowerBI embed info" in data["error"]


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth._verify_claims", lambda x, y: True)  # Mock auth verification to return True
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
def test_get_dashboard_embed_info_general_exception(flask_client, monkeypatch):
    # Given
    skip_auth()

    # Simulate a general exception in the route
    with patch("flaskr.controllers.powerbi.get_token", side_effect=Exception("Unexpected error")):
        response = flask_client.get("/api/powerbi/dashboard/embed-info")

    # Then
    assert response.status_code == 500
    data = response.get_json()
    assert "error" in data


# Test for ERV dashboard configuration endpoints
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth._verify_claims", lambda x, y: True)  # Mock auth verification to return True
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
@patch.object(powerbi_service, "get_erv_powerbi_dashboard_config")
def test_get_erv_dashboard_config_success(mock_get_config, flask_client):
    # Given
    skip_auth()
    mock_get_config.return_value = {
        "report_id": "erv-report-123",
        "name": "ERV Dashboard"
    }

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", return_value=True):
        response = flask_client.get("/api/powerbi/erv-powerbi-dashboard-config")

    # Then
    assert response.status_code == 200
    data = response.get_json()
    assert data["config"]["report_id"] == "erv-report-123"
    assert data["config"]["name"] == "ERV Dashboard"


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth._verify_claims", lambda x, y: True)  # Mock auth verification to return True
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
@patch.object(powerbi_service, "update_erv_dashboard_config")
def test_update_erv_dashboard_config_success(mock_update_config, flask_client):
    # Given
    skip_auth()
    mock_update_config.return_value = True

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", return_value=True):
        response = flask_client.put(
            "/api/powerbi/erv-powerbi-dashboard-config",
            json={"reportId": "new-erv-report-456", "reportName": "New ERV Dashboard"}
        )

    # Then
    assert response.status_code == 200
    data = response.get_json()
    assert data["success"] == True
    assert "updated successfully" in data["message"]
    mock_update_config.assert_called_once_with("new-erv-report-456", "New ERV Dashboard")


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth._verify_claims", lambda x, y: True)  # Mock auth verification to return True
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
def test_update_erv_dashboard_config_missing_data(flask_client):
    # Given
    skip_auth()

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", return_value=True):
        response = flask_client.put(
            "/api/powerbi/erv-powerbi-dashboard-config",
            json={"reportId": "new-erv-report-456"}  # Missing reportName
        )

    # Then
    assert response.status_code == 400
    data = response.get_json()
    assert "error" in data
    assert "Missing reportId or reportName" in data["error"]


# Test for updated ERV dashboard embed info with database config
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "test-user-id"})
@patch("flaskr.helpers.auth._verify_claims", lambda x, y: True)  # Mock auth verification to return True
@patch("flaskr.helpers.auth.get_token", lambda x: "test-token")  # Mock get_token to return a test token
@patch.object(powerbi_service, "get_report_embed_info")
@patch.object(powerbi_service, "get_erv_powerbi_dashboard_config")
def test_get_erv_dashboard_embed_info_with_db_config(mock_get_erv_config, mock_get_report_embed_info, flask_client, monkeypatch):
    # Given
    skip_auth()
    mock_get_erv_config.return_value = {
        "report_id": "erv-report-from-db",
        "name": "ERV Dashboard from DB",
        "is_default": False
    }
    mock_get_report_embed_info.return_value = {
        "embedUrl": "https://app.powerbi.com/reportEmbed",
        "token": "dummy-token-123",
        "reportId": "erv-report-from-db",
        "workspaceId": "workspace-123",
    }
    monkeypatch.setattr(
        "flask.current_app.config.get",
        lambda key: "workspace-123" if key == "POWERBI_WORKSPACE_ID" else None,
    )

    # When
    with patch("flaskr.controllers.powerbi.get_token", return_value="test-token"), patch(
        "flaskr.controllers.powerbi.jwt.get_unverified_claims", return_value={"sub": "test-user-id"}
    ), patch("flaskr.controllers.powerbi._verify_claims", return_value=True):
        response = flask_client.get("/api/powerbi/dashboard/embed-info?report_type=erv")

    # Then
    assert response.status_code == 200
    data = response.get_json()
    assert data["embedUrl"] == "https://app.powerbi.com/reportEmbed"
    assert data["embedToken"] == "dummy-token-123"
    assert data["reportId"] == "erv-report-from-db"
    assert data["workspaceId"] == "workspace-123"
    mock_get_report_embed_info.assert_called_once_with("erv-report-from-db", "workspace-123", "test-user-id")
