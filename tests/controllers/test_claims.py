import tempfile
from mongomock import ObjectId
import pytest
import json
from flaskr.services.auth_service import AuthService
from flaskr.services.reporting_service import ReportingService
from tests.helpers import skip_auth
from mock import MagicMock, patch
from marshmallow import ValidationError
from flaskr.services.exceptions import ServiceException
from flaskr.services.claims_service import ClaimService


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(
    ClaimService,
    "claim_create_presigned_url",
    MagicMock(return_value={"fileId": "test-id", "presignedURl": "https://test-url"}),
)
def test_claim_create_presigned_urlsucess(flask_client, patch_db):
    skip_auth()
    # Given
    patch_db.user.insert_one({"user_id": "1", "clients": [ObjectId("62419f08888c2b5ed06235d9")]})
    data = {"fileName": "test.xlsx", "clientId": "62419f08888c2b5ed06235d9", "user_id": "1"}
    # When
    response = flask_client.get("/api/claim/upload/presigned-url", query_string=data)
    # Then
    assert response.status_code == 200
    assert response.json == {"fileId": "test-id", "presignedURl": "https://test-url"}


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@pytest.mark.parametrize("invalid_request", [{"clientId": 1}, {"fileName": "test.xlsx"}])
def test_claim_create__presigned_url_invalid_request(flask_client, invalid_request):
    skip_auth()
    # Given
    data = invalid_request
    # When
    response = flask_client.get("/api/claim/upload/presigned-url", query_string=data)
    # Then
    assert response.status_code == 400
    assert response.json == {"errors": ["Missing required details"]}


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClaimService, "claim_create", MagicMock())
def test_claim_create_sucess(flask_client):
    skip_auth()
    # Given
    request = {
        "clientId": "1",
        "fileName": "20221010-Claim.xlsx",
        "fileId": "test-id",
        "claimFromTBR": False,
        "user_id": "1",
    }
    # When
    response = flask_client.post("/api/claim/upload", json=request)
    # Then
    assert response.status_code == 201


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@pytest.mark.parametrize(
    "invalid_request",
    [
        {"clientId": "1"},
        {"fileName": "test.xlsx"},
        {"fileId": "test-id"},
        {"clientId": "1", "fileName": "test.xlsx"},
        {"clientId": "1", "fileId": "test-id"},
        {"fileName": "test.xlsx", "fileId": "test-id"},
    ],
)
def test_claim_create_invalid_request(flask_client, invalid_request):
    skip_auth()
    # Given
    request = invalid_request
    # When
    response = flask_client.post("/api/claim/upload", json=request)
    # Then
    assert response.status_code == 400
    assert response.json == {"errors": ["Missing required details"]}


@patch.object(
    ClaimService,
    "get_claim_details",
    MagicMock(
        return_value={
            "clientId": "1",
            "clientName": "ABC",
            "createdAt": "2020-05-19",
            "notes": "string",
            "status": "string",
            "updatedAt": "2020-08-24",
            "claimFiles": [
                {
                    "claimTotal": 125,
                    "deposit": 584,
                    "fileName": "string",
                    "items": 5,
                    "status": "string",
                    "submittedDate": "2021-08-11",
                }
            ],
        },
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_details(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/claim/details/{claim_id}")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClaimService, "get_claim_details", MagicMock(side_effect=ServiceException))
def test_claim_details_service_exception(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/claim/details/invalid_claim_details")

    # Then
    assert response.status_code == 400


@patch.object(
    ClaimService,
    "get_claim_transaction",
    MagicMock(
        return_value={
            "bookingRef": "123",
            "amount": 9000,
            "currencyCode": "euro",
            "status": "string",
            "count": 2,
            "duplicates": 1,
        },
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_transaction(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/claim/transaction/234257e8697b3b6f30f37e50?query=&page=1&size=1&sortKey=&sortOrder="
    )

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClaimService, "get_claim_transaction", MagicMock(side_effect=ServiceException))
def test_claim_transaction_service_exception(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/claim/transaction/234257e8697b3b6f30f37e50?query=&page=&size=&sortKey=&sortOrder="
    )

    # Then
    assert response.status_code == 400


@patch.object(
    ClaimService,
    "claim_search_latest",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "620244c16dda372675663e03",
                    "amount": "1020",
                    "claimDate": "Fri, 03 Dec 2021 08:43:24 GMT",
                    "clientId": "1",
                    "clientName": "abc",
                    "items": [{"amount": 3404, "count": 2, "currency": "GBP"}],
                    "status": "Check in Progress",
                }
            ],
            "empty": False,
            "first": True,
            "last": True,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 4,
            "totalPages": 4,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_search_latest_success(flask_client):
    skip_auth()
    # Given
    request = {
        "query": None,
        "client": None,
        "assignedTo": 12,
        "status": None,
        "fromDate": None,
        "toDate": None,
        "page": 1,
        "size": 1,
    }

    # When
    response = flask_client.post("/api/claim/search/latest", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [
            {
                "_id": "620244c16dda372675663e03",
                "amount": "1020",
                "claimDate": "Fri, 03 Dec 2021 08:43:24 GMT",
                "clientId": "1",
                "clientName": "abc",
                "items": [{"amount": 3404, "count": 2, "currency": "GBP"}],
                "status": "Check in Progress",
            }
        ],
        "empty": False,
        "first": True,
        "last": True,
        "numberOfElements": 1,
        "pageNumber": 1,
        "totalElements": 4,
        "totalPages": 4,
    }


@patch.object(
    ClaimService,
    "claim_search_latest",
    MagicMock(
        return_value={
            "content": [],
            "empty": True,
            "first": True,
            "last": False,
            "numberOfElements": 0,
            "pageNumber": 1,
            "totalElements": 4,
            "totalPages": 4,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_search_latest_success_empty(flask_client):
    skip_auth()
    # Given
    request = {"query": "111", "status": None, "assignedTo": 1, "fromDate": None, "toDate": None, "page": 1, "size": 1}

    # When
    response = flask_client.post("/api/claim/search/latest", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [],
        "empty": True,
        "first": True,
        "last": False,
        "numberOfElements": 0,
        "pageNumber": 1,
        "totalElements": 4,
        "totalPages": 4,
    }


@patch.object(
    ClaimService,
    "claim_search_summary",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "620244c16dda372675663e03",
                    "amount": "1020",
                    "claimDate": "Fri, 03 Dec 2021 08:43:24 GMT",
                    "clientId": "1",
                    "clientName": "abc",
                    "element": "Flight - BSP, Flight - BSP",
                    "frequency": "Weekly",
                    "status": "Check in Progress",
                }
            ],
            "empty": False,
            "first": True,
            "last": False,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 4,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_search_summary_success(flask_client):
    skip_auth()
    # Given
    request = {
        "query": None,
        "client": None,
        "status": None,
        "fromDate": None,
        "toDate": None,
        "page": 1,
        "size": 1,
    }

    # When
    response = flask_client.post("/api/claim/search/summary", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [
            {
                "_id": "620244c16dda372675663e03",
                "amount": "1020",
                "claimDate": "Fri, 03 Dec 2021 08:43:24 GMT",
                "clientId": "1",
                "clientName": "abc",
                "element": "Flight - BSP, Flight - BSP",
                "frequency": "Weekly",
                "status": "Check in Progress",
            }
        ],
        "empty": False,
        "first": True,
        "last": False,
        "numberOfElements": 1,
        "pageNumber": 1,
        "totalElements": 4,
        "totalPages": 1,
    }


@patch.object(
    ClaimService,
    "claim_search_summary",
    MagicMock(
        return_value={
            "content": [],
            "empty": True,
            "first": True,
            "last": False,
            "numberOfElements": 0,
            "pageNumber": 1,
            "totalElements": 4,
            "totalPages": 4,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_search_summary_success_empty(flask_client):
    skip_auth()
    # Given
    request = {"query": "111", "status": None, "assignedTo": 1, "fromDate": None, "toDate": None, "page": 1, "size": 1}

    # When
    response = flask_client.post("/api/claim/search/summary", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [],
        "empty": True,
        "first": True,
        "last": False,
        "numberOfElements": 0,
        "pageNumber": 1,
        "totalElements": 4,
        "totalPages": 4,
    }


@patch.object(
    ClaimService,
    "claim_summary",
    MagicMock(
        return_value=[
            {
                "valid": [
                    {
                        "content": [
                            {
                                "count": 1,
                                "currency": "GBP",
                                "element": "ASPP",
                                "maxAmount": 3000.0,
                                "minAmount": 3000.0,
                                "total": 3000.0,
                            }
                        ],
                        "currency": "GBP",
                        "symbol": "£",
                        "totalAmount": 3000.0,
                        "totalCount": 1,
                    },
                    {
                        "content": [
                            {
                                "count": 2,
                                "currency": "USD",
                                "element": "Performance",
                                "maxAmount": 8000.0,
                                "minAmount": 8000.0,
                                "total": 16000.0,
                            },
                            {
                                "count": 1,
                                "currency": "USD",
                                "element": "Claim",
                                "maxAmount": 8000.0,
                                "minAmount": 8000.0,
                                "total": 8000.0,
                            },
                        ],
                        "currency": "USD",
                        "symbol": "$",
                        "totalAmount": 24000.0,
                        "totalCount": 3,
                    },
                ],
                "void": [],
            }
        ],
    ),
)
def test_claim_summary_only_valid_transactions(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/claim/summary/{claim_id}?query=&sortKey=&sortOrder=")

    # Then
    assert response.status_code == 200


@patch.object(
    ClaimService,
    "claim_summary",
    MagicMock(
        return_value=[
            {
                "valid": [
                    {
                        "content": [
                            {
                                "count": 1,
                                "currency": "GBP",
                                "element": "ASPP",
                                "maxAmount": 3000.0,
                                "minAmount": 3000.0,
                                "total": 3000.0,
                            }
                        ],
                        "currency": "GBP",
                        "symbol": "£",
                        "totalAmount": 3000.0,
                        "totalCount": 1,
                    },
                ],
                "void": [
                    {
                        "content": [
                            {
                                "count": 1,
                                "currency": "GBP",
                                "element": "Deposit",
                                "maxAmount": 8000.0,
                                "minAmount": 8000.0,
                                "total": 8000.0,
                            }
                        ],
                        "currency": "GBP",
                        "symbol": "£",
                        "totalAmount": 8000.0,
                        "totalCount": 1,
                    }
                ],
            }
        ],
    ),
)
def test_claim_summary_void_and_valid_transactions(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/claim/summary/{claim_id}?query=&sortKey=&sortOrder=")

    # Then
    assert response.status_code == 200


@patch.object(ClaimService, "claim_summary", MagicMock(side_effect=ServiceException))
def test_claim_summary_service_exception(flask_client):
    # When
    response = flask_client.get("/api/claim/summary/{claim_id}?query=&sortKey=&sortOrder=")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClaimService, "update_claims_metadata", MagicMock(return_value=None))
def test_update_claims_metadata(flask_client):
    skip_auth()
    # Given
    request = {
        "notes": "string",
        "status": "string1",
        "assignedTo": "62023bc91eed04813999f20f",
        "frequqncy": "Daily",
        "claimId": "62023b691eed04813999f201",
    }

    # When
    response = flask_client.put("/api/claim/update", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClaimService, "update_claims_metadata", MagicMock(side_effect=ValidationError("Invalid data")))
def test_update_claims_metadata_validation_error(flask_client):
    skip_auth()
    # Given
    request = {"notes": 123, "status": 123, "assignedTo": 123, "claimId": "62023b691eed04813999f201"}
    # When
    response = flask_client.put("/api/claim/update", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@pytest.mark.parametrize(
    "invalid_request", [{"claimId": "62023b691eed04813999f201"}, {"notes": "string"}, {"status": "string"}, {}]
)
def test_update_claims_metadata_inavalid_request(flask_client, invalid_request):
    skip_auth()
    # Given
    request = invalid_request
    # When
    response = flask_client.put("/api/claim/update", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 400


@pytest.mark.parametrize(
    "data",
    [
        {"check": ""},
        {"check": "quick-check"},
        {"check": "full-check", "totalDueToSupplier": 1234},
    ],
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClaimService, "update_claim_transaction", MagicMock(return_value=None))
def test_update_claim_transaction(flask_client, data):
    skip_auth()
    # Given
    request = data
    # When
    response = flask_client.put(
        "/api/claim/transaction/620b75d59b53aa92cd5dbb1", data=json.dumps(request), content_type="application/json"
    )
    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClaimService, "update_claim_transaction", MagicMock(side_effect=ValidationError("Invalid data")))
def test_update_claim_transaction_validation_error(flask_client):
    skip_auth()
    # Given
    request = {"check": 123}
    transactionId = "invalid"
    # When
    response = flask_client.put(
        f"/api/claim/transaction/{transactionId}", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(
    ClaimService,
    "update_claim_transaction",
    MagicMock(return_value=None),
)
@pytest.mark.parametrize(
    "data",
    [
        {"bookingRef": "123"},
        {"check": "full-check", "status": "Cancelled"},
        {"check": "full-check", "amount": 100},
        {"check": "full-check", "status": "Cancelled", "amount": 100},
    ],
)
def test_update_claim_transaction_inavalid_request(flask_client, data):
    skip_auth()
    # Given
    request = data
    transaction_id = "620b75d59b53aa92cd5dbb1"
    # When
    response = flask_client.put(
        f"/api/claim/transaction/{transaction_id}", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 400


@patch.object(
    ClaimService,
    "claim_get_transaction",
    MagicMock(
        return_value={
            "amount": 600.0,
            "bookingDate": "2020-08-21",
            "bookingRef": "977957",
            "departureDate": "2022-03-26",
            "leadPax": "KV",
            "returnDate": "2022-04-08",
            "status": "live",
            "statusReason": "string",
            "supplierNames": "Hoseason",
            "supplierRef": "A45EX",
            "totalDueSupplier": 123.0,
            "totalPaid": 11392.0,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_get_transaction(flask_client):
    # When
    response = flask_client.get("/api/claim/get-transaction/{transaction_id}")

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(ClaimService, "claim_get_transaction", MagicMock(side_effect=ServiceException))
def test_claim_get_transaction_exception(flask_client):
    # When
    response = flask_client.get("/api/claim/get-transaction/invalid_claim_details")

    # Then
    assert response.status_code == 400


@patch.object(ClaimService, "get_claim_anomalies", MagicMock(return_value=[]))
def test_claim_anomaly(flask_client, patch_db):
    skip_auth()
    # When
    response = flask_client.get("/api/claim/anomalies/6261066585575913d85dba39?query=&sortKey=&sortOrder=")
    # Then
    assert response.status_code == 200


@patch.object(ClaimService, "get_claim_anomalies", MagicMock(side_effect=ServiceException))
def test_claim_anomaly_service_exception(flask_client):
    skip_auth()
    # When
    claim_id = "invalid"
    response = flask_client.get(f"/api/claim/anomalies/{claim_id}?query=&sortKey=&sortOrder=")
    # Then
    assert response.status_code == 400


@patch.object(ClaimService, "get_check_type", MagicMock(return_value=[]))
def test_get_check_type(flask_client, patch_db):
    skip_auth()
    # When
    response = flask_client.get("/api/claim/quick_check/62037d1ac57a15c58fa5f8bf?query=")
    # Then
    assert response.status_code == 200


@patch.object(ClaimService, "get_check_type", MagicMock(side_effect=ServiceException))
def test_get_check_type_service_exception(flask_client):
    skip_auth()
    # When
    claim_id = "invalid"
    response = flask_client.get(f"/api/claim/quick_check/{claim_id}?query=")
    # Then
    assert response.status_code == 400


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    ClaimService,
    "get_claim_anomalies",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "62b048932d8745162f2c520e",
                    "anomalyId": "6246c97784259494f15aad4e",
                    "anomalyType": "Negative Funds in Trust",
                    "bookingDate": "2020-11-02",
                    "bookingRef": "977957",
                    "clientId": "CR7",
                    "createdAt": "Mon, 20 Jun 2022 10:14:43 GMT",
                    "currency_code": "USD",
                    "dateOfReturn": "2021-10-16",
                    "dateOfTravel": "2022-04-04",
                    "leadPassenger": "aswathy",
                    "modifiedBy": None,
                    "status": "Resolved",
                    "symbol": "$",
                    "updatedAt": "Mon, 20 Jun 2022 10:14:43 GMT",
                    "value": 14.0,
                },
            ],
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
def test_claim_anomaly_search_export_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {"clientId": "a12", "query": "str", "fromDate": "", "toDate": "", "page": "", "size": ""}
    open(f"{temp_dir}/ClaimAnomalies", "w")

    # When
    response = flask_client.get(
        "/api/claim/anomalies/625f96b0c4b57cdb24ef5e4c/export?query=&sortKey=&sortOrder=",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    ClaimService,
    "get_claim_automated_transaction",
    MagicMock(
        return_value={
            "totalTransactions": 2,
            "transactions": [
                {
                    "_id": "62564aec189d579b1653288f",
                    "amount": 8000.0,
                    "bookingRef": "991645",
                    "clientId": "ED123",
                    "count": 7,
                    "currencyCode": "GBP",
                    "description": "Highest Amount",
                    "duplicates": 5,
                    "element": "Deposit",
                },
                {
                    "_id": "625e5522a10e7579b8882894",
                    "amount": 7000.0,
                    "bookingRef": "977957",
                    "clientId": "ED123",
                    "count": 6,
                    "currencyCode": "GBP",
                    "description": "Lowest Amount",
                    "duplicates": 2,
                    "element": "Performance",
                },
            ],
        }
    ),
)
def test_claim_automated_transaction_export_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {"query": ""}
    open(f"{temp_dir}/AutomatedTransactions", "w")

    # When
    response = flask_client.get(
        "/api/claim/automated-transaction/621c38cae22a9303b9b091b9/export?query=",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    ClaimService,
    "get_check_type",
    MagicMock(
        return_value=[
            {
                "_id": "62564aec189d579b1653288f",
                "amount": 8000.0,
                "bookingRef": "991645",
                "check": "full_check",
                "clientId": "ED123",
                "count": 7,
                "currencyCode": "GBP",
                "duplicates": 5,
                "element": "Deposit",
            }
        ]
    ),
)
def test_claim_get_check_type_export_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {"query": ""}
    open(f"{temp_dir}/Checks", "w")

    # When
    response = flask_client.get(
        "/api/claim/full_check/621c38cae22a9303b9b091b9/export?query=",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    ClaimService,
    "get_claim_transaction",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "625e5522a10e7579b88828a4",
                    "amount": 7000.0,
                    "bookingRef": "130000",
                    "clientId": "ED123",
                    "count": 6,
                    "currencyCode": "USD",
                    "duplicates": 2,
                    "element": "Performance",
                },
                {
                    "_id": "625e5522a10e7579b88828a5",
                    "amount": 7000.0,
                    "bookingRef": "140000",
                    "clientId": "ED123",
                    "count": 6,
                    "currencyCode": "USD",
                    "duplicates": 2,
                    "element": "Performance",
                },
            ],
            "numberOfElements": 2,
            "pageNumber": 1,
            "totalElements": 2,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_get_transaction_export_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {}
    open(f"{temp_dir}/Transactions", "w")

    # When
    response = flask_client.get(
        "/api/claim/transaction/625e551d9b8d80989e4ff2c4?query=&page=2&size=&sortKey=element&sortOrder=-1",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    ClaimService,
    "get_claim_transaction",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "625e5522a10e7579b88828a4",
                    "amount": 7000.0,
                    "bookingRef": "130000",
                    "clientId": "ED123",
                    "count": 6,
                    "currencyCode": "USD",
                    "duplicates": 2,
                    "element": "Performance",
                },
                {
                    "_id": "625e5522a10e7579b88828a5",
                    "amount": 7000.0,
                    "bookingRef": "140000",
                    "clientId": "ED123",
                    "count": 6,
                    "currencyCode": "USD",
                    "duplicates": 2,
                    "element": "Performance",
                },
            ],
            "numberOfElements": 2,
            "pageNumber": 1,
            "totalElements": 2,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_get_transaction_export_exception(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {}
    open(f"{temp_dir}/Transactions", "w")

    # When
    response = flask_client.get(
        "/api/claim/transaction/625e551d9b8d80989e4ff2c4?query=&page=2&size=&sortKey=currenci&sortOrder=0",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 400
    assert response.json == {"errors": ["invalid sort key"]}


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    ClaimService,
    "claim_summary",
    MagicMock(
        return_value=[
            {
                "currency": "GBP",
                "totalCount": 14,
                "totalAmount": 105000.0,
                "symbol": "£",
                "content": [
                    {
                        "element": "ASPP",
                        "currency": "GBP",
                        "count": 2,
                        "minAmount": 3000.0,
                        "maxAmount": 3000.0,
                        "total": 6000.0,
                    },
                    {
                        "element": "Balance",
                        "currency": "GBP",
                        "count": 1,
                        "minAmount": 8000.0,
                        "maxAmount": 8000.0,
                        "total": 8000.0,
                    },
                ],
            }
        ]
    ),
)
def test_claim_get_total_summary_export_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {}
    open(f"{temp_dir}/ClaimSummary", "w")

    # When
    response = flask_client.get(
        "/api/claim/summary/62aafc5336feb4b25b99f9f5/export?query=&sortKey=maxAmount&sortOrder=",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    ClaimService,
    "claim_summary",
    MagicMock(
        return_value=[
            {
                "currency": "GBP",
                "totalCount": 14,
                "totalAmount": 105000.0,
                "symbol": "£",
                "content": [
                    {
                        "element": "ASPP",
                        "currency": "GBP",
                        "count": 2,
                        "minAmount": 3000.0,
                        "maxAmount": 3000.0,
                        "total": 6000.0,
                    },
                    {
                        "element": "Balance",
                        "currency": "GBP",
                        "count": 1,
                        "minAmount": 8000.0,
                        "maxAmount": 8000.0,
                        "total": 8000.0,
                    },
                ],
            }
        ]
    ),
)
def test_claim_get_total_summary_export_exception(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {}
    open(f"{temp_dir}/ClaimSummary", "w")

    # When
    response = flask_client.get(
        "/api/claim/summary/62aafc5336feb4b25b99f9f5/export?query=&sortKey=amount&sortOrder=0",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 400


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    AuthService,
    "ptt_users_list",
    MagicMock(
        return_value=[
            {
                "userId": "cf0142a5-7475-4910-9321-40c323d7a876",
                "email": "<EMAIL>",
                "name": "test",
            }
        ]
    ),
)
@patch.object(
    ClaimService,
    "claim_search_latest",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "62aafc5336feb4b25b99f9f5",
                    "anomalies": 3,
                    "assignedTo": "6204d9d61eed04813999f227",
                    "claimDate": "2022-04-24",
                    "clientId": "CR7",
                    "clientName": "Edstem Test User",
                    "friendlyName": "Edstem Test User",
                    "items": [
                        {
                            "amount": 17000.0,
                            "checkedAmount": 10000,
                            "checks": 2,
                            "count": 2,
                            "currency": "GBP",
                            "percentageCheck": 100.0,
                            "percentageTotal": 58.8235294117647,
                            "symbol": "£",
                        },
                        {
                            "amount": 35000.0,
                            "checkedAmount": 4000,
                            "checks": 3,
                            "count": 5,
                            "currency": "USD",
                            "percentageCheck": 60.0,
                            "percentageTotal": 11.428571428571429,
                            "symbol": "$",
                        },
                        {
                            "amount": 2000.0,
                            "checkedAmount": 1000,
                            "checks": 1,
                            "count": 1,
                            "currency": "INR",
                            "percentageCheck": 100.0,
                            "percentageTotal": 50.0,
                            "symbol": "₹",
                        },
                    ],
                    "notes": "Submitted",
                    "status": "Submitted",
                    "submittedDate": "2020-10-27T00:00:00",
                    "trust": "Tripartite MA",
                }
            ],
            "empty": False,
            "first": True,
            "last": True,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_export_claim_search_latest_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {
        "query": "",
        "client": "CR7",
        "assignedTo": "",
        "status": "",
        "fromDate": "",
        "toDate": "",
        "page": 1,
        "size": 5,
    }
    open(f"{temp_dir}/ClaimSearchLatest", "w")

    # When
    response = flask_client.post(
        "/api/claim/search/latest/export",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    ClaimService,
    "claim_search_summary",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "62aafc5336feb4b25b99f9f5",
                    "clientId": "CR7",
                    "clientName": "Edstem Test User",
                    "element": "Claim, Deposit, Performance, Cruise, Balance, ASPP, Flights",
                    "frequency": "Weekly",
                    "friendlyName": "Edstem Test User",
                    "items": [
                        {"amount": 17000.0, "count": 2, "currency": "GBP", "symbol": "£"},
                        {"amount": 35000.0, "count": 5, "currency": "USD", "symbol": "$"},
                        {"amount": 2000.0, "count": 1, "currency": "INR", "symbol": "₹"},
                    ],
                    "notes": "Submitted",
                    "status": "Submitted",
                    "submittedDate": "2020-10-27T00:00:00",
                }
            ],
            "empty": False,
            "first": True,
            "last": True,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_export_claim_search_summary_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {
        "query": "",
        "client": "CR7",
        "assignedTo": "",
        "status": "",
        "fromDate": "",
        "toDate": "",
        "page": 1,
        "size": 5,
    }
    open(f"{temp_dir}/ClaimSearchSummary", "w")

    # When
    response = flask_client.post(
        "/api/claim/search/summary/export",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(
    ClaimService,
    "claim_testing",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "62ce598bbff9a1862ca1a241",
                    "claimId": "62ce598bbff9a1862ca1a241",
                    "clientId": "2146",
                    "clientName": "G Touring Ltd",
                    "fileDate": "2020-10-27",
                    "items": [{"count": 11985, "currency": "GBP", "originalClaim": 17026508.210000042, "symbol": "£"}],
                    "reasons": "string",
                    "revisedClaim": 12344.0,
                    "status": "Authorised",
                    "submittedDate": "2020-10-27T00:00:00",
                }
            ],
            "empty": False,
            "first": True,
            "last": False,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_testing_success(flask_client):
    skip_auth()
    # Given
    request = {"query": "", "client": "", "fromDate": "", "toDate": "", "date": "", "page": 1, "size": 1}

    # When
    response = flask_client.post("/api/claim/claim-testing", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [
            {
                "_id": "62ce598bbff9a1862ca1a241",
                "claimId": "62ce598bbff9a1862ca1a241",
                "clientId": "2146",
                "clientName": "G Touring Ltd",
                "fileDate": "2020-10-27",
                "items": [{"count": 11985, "currency": "GBP", "originalClaim": 17026508.210000042, "symbol": "£"}],
                "reasons": "string",
                "revisedClaim": 12344.0,
                "status": "Authorised",
                "submittedDate": "2020-10-27T00:00:00",
            }
        ],
        "empty": False,
        "first": True,
        "last": False,
        "numberOfElements": 1,
        "pageNumber": 1,
        "totalElements": 1,
        "totalPages": 1,
    }


@patch.object(
    ClaimService,
    "claim_testing",
    MagicMock(
        return_value={
            "content": [],
            "empty": True,
            "first": True,
            "last": False,
            "numberOfElements": 0,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_testing_success_empty(flask_client):
    skip_auth()
    # Given
    request = {"query": "abc", "client": "", "fromDate": "", "toDate": "", "date": "", "page": 1, "size": 1}

    # When
    response = flask_client.post("/api/claim/claim-testing", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [],
        "empty": True,
        "first": True,
        "last": False,
        "numberOfElements": 0,
        "pageNumber": 1,
        "totalElements": 1,
        "totalPages": 1,
    }


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    ClaimService,
    "claim_testing",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "62ce598bbff9a1862ca1a241",
                    "claimId": "62ce598bbff9a1862ca1a241",
                    "clientId": "2146",
                    "clientName": "G Touring Ltd",
                    "fileDate": "2020-10-27",
                    "items": [
                        {
                            "count": 11985,
                            "currency": "GBP",
                            "revisedClaim": 17026508.210000042,
                            "symbol": "£",
                            "originalClaim": 17026508.210000042,
                        }
                    ],
                    "reasons": "string",
                    "status": "Authorised",
                    "submittedDate": "2020-10-27T00:00:00",
                }
            ],
            "empty": False,
            "first": True,
            "last": False,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_export_claim_testing_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {
        "query": "",
        "client": "CR7",
        "assignedTo": "",
        "status": "",
        "fromDate": "",
        "toDate": "",
        "page": 1,
        "size": 1,
    }
    open(f"{temp_dir}/ClaimTesting", "w")

    # When
    response = flask_client.post(
        "/api/claim/claim-testing/export",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(ClaimService, "update_claim_testing", MagicMock(return_value=None))
@pytest.mark.parametrize(
    "data",
    [
        {"reasons": "string123"},
    ],
)
def test_update_claim_testing(flask_client, data):
    skip_auth()
    # Given
    request = data

    # When
    response = flask_client.put(
        "/api/claim/claim-testing/62ce598bbff9a1862ca1a241", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 200


@patch.object(ClaimService, "update_claim_testing", MagicMock(side_effect=ValidationError("Invalid data")))
@pytest.mark.parametrize(
    "invalid_data",
    [
        {"revisedClaim": 890},
        {"reasons": "string123"},
        {"revisedClaim": "", "reasons": ""},
        {"revisedClaim": 12344, "reasons": "string"},
    ],
)
def test_update_claim_testing_validation_error(flask_client, invalid_data):
    skip_auth()
    # Given
    request = invalid_data
    # When
    response = flask_client.put(
        "/api/claim/claim-testing/62ce598bbff9a1862ca1a24123", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 400


@patch.object(ClaimService, "update_claim_testing", MagicMock(return_value=None))
def test_update_claim_testing_without_reason(flask_client):
    skip_auth()
    # Given
    request = {}
    # When
    response = flask_client.put(
        "/api/claim/claim-testing/62ce598bbff9a1862ca1a241", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 200


@patch.object(
    ClaimService,
    "claim_escrow_search_summary",
    MagicMock(
        return_value={
            "content": [
                {
                    "clientId": "1",
                    "clientName": "abc",
                }
            ],
            "empty": False,
            "first": True,
            "last": False,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 4,
            "totalPages": 1,
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_escrow_search_summary_success(flask_client):
    skip_auth()
    # Given
    request = {
        "query": None,
        "client": None,
        "status": None,
        "fromDate": None,
        "toDate": None,
        "page": 1,
        "size": 1,
    }

    # When
    response = flask_client.post(
        "/api/claim/search/summary_escrow", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [
            {
                "clientId": "1",
                "clientName": "abc",
            }
        ],
        "empty": False,
        "first": True,
        "last": False,
        "numberOfElements": 1,
        "pageNumber": 1,
        "totalElements": 4,
        "totalPages": 1,
    }
