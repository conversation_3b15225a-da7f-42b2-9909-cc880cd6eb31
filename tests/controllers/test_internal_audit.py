import tempfile
from mock import MagicMock, patch
from flaskr.services.internal_audit_service import InternalAuditService
from flaskr.services.reporting_service import ReportingService
from tests.helpers import skip_auth
import json


@patch.object(
    InternalAuditService,
    "banking_report",
    MagicMock(
        return_value={
            "content": [
                {
                    "AmountasperBank": "",
                    "BankingReportAmount": [{"amount": ********.********, "currency": "GBP"}],
                    "BusinessRules": "Movement of funds between bookings, Claim Performance Before Return",
                    "Notes": "",
                    "ResolutionNotes": "",
                    "Risk": "",
                    "Samples": "",
                    "Status": "",
                    "TestingStatus": "",
                    "_id": "62ce5998bff9a1862ca22b6b",
                    "fileName": "Bank Testing File-1",
                },
                {
                    "AmountasperBank": "",
                    "BankingReportAmount": [
                        {"amount": 9, "currency": "GBP"},
                        {"amount": 7890.0, "currency": "EUR"},
                        {"amount": 567422.0, "currency": "DD"},
                    ],
                    "BusinessRules": "",
                    "Notes": "",
                    "ResolutionNotes": "",
                    "Risk": "",
                    "Samples": "",
                    "Status": "",
                    "TestingStatus": "",
                    "_id": "6524f3f7adde3547620b3f20",
                    "fileName": "Bank Testing File-3",
                },
            ],
        }
    ),
)
def test_banking_report_success(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/internal-audit/reports/banking?clientId=632af99d7dc025fdb915f639&fromDate=2020-01-01&toDate=2022-12-30"
    )

    # Then
    assert response.status_code == 200


def test_banking_report_exception(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/internal-audit/reports/banking?clientId=632af99d7dc025fdb915f639&toDate=2022-12-30"
    )

    # Then
    assert response.status_code == 400
    assert response.json == {"errors": ["Missing required details"]}


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    InternalAuditService,
    "banking_report",
    MagicMock(
        return_value={
            "content": [
                {
                    "AmountasperBank": "",
                    "BankingReportAmount": [{"amount": ********.********, "currency": "GBP"}],
                    "BusinessRules": "Movement of funds between bookings, Claim Performance Before Return",
                    "Notes": "",
                    "ResolutionNotes": "",
                    "Risk": "",
                    "Samples": "",
                    "Status": "",
                    "TestingStatus": "",
                    "_id": "62ce5998bff9a1862ca22b6b",
                    "fileName": "Bank Testing File-1",
                },
                {
                    "AmountasperBank": "",
                    "BankingReportAmount": [
                        {"amount": 9, "currency": "GBP"},
                        {"amount": 7890.0, "currency": "EUR"},
                        {"amount": 567422.0, "currency": "DD"},
                    ],
                    "BusinessRules": "",
                    "Notes": "",
                    "ResolutionNotes": "",
                    "Risk": "",
                    "Samples": "",
                    "Status": "",
                    "TestingStatus": "",
                    "_id": "6524f3f7adde3547620b3f20",
                    "fileName": "Bank Testing File-3",
                },
            ],
        }
    ),
)
def test_banking_report_export_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    open(f"{temp_dir}/Banking Internal Audit Report", "w")

    # When
    response = flask_client.get(
        "/api/internal-audit/reports/banking?clientId=632af99d7dc025fdb915f639&fromDate=2020-01-01&toDate=2022-12-30"
    )

    # Then
    assert response.status_code == 200


@patch.object(InternalAuditService, "update_banking_report", MagicMock(return_value=None))
def test_update_banking_report(flask_client):
    skip_auth()
    # Given
    request = {
        "clientId": "632af99d7dc025fdb915f639",
        "status": "Submitted",
        "notes": "Something",
        "resolutionNotes": "",
        "risk": "Low",
        "testingstatus": "Something",
        "deleted": "false",
    }

    # When
    response = flask_client.put(
        "/api/internal-audit/reports/banking/62ce5998bff9a1862ca22b6f",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(InternalAuditService, "update_claims_report", MagicMock(return_value=None))
def test_update_claims_report(flask_client):
    skip_auth()
    # Given
    request = {
        "clientId": "632af99d7dc025fdb915f639",
        "status": "Submitted",
        "notes": "Something",
        "resolutionNotes": "",
        "risk": "Low",
        "deleted": "false",
    }

    # When
    response = flask_client.put(
        "/api/internal-audit/reports/claims/62ce5998bff9a1862ca22b6f",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(
    InternalAuditService,
    "claim_report",
    MagicMock(
        return_value={
            "content": [
                {
                    "AmountasperBank": "",
                    "BusinessRules": "Claim Too Early For Departure Date",
                    "Notes": "",
                    "ResolutionNotes": "",
                    "Risk": "",
                    "Status": "",
                    "SubsetofFullChecks": "",
                    "_id": "632bf1bdbc7e2d644cce7b76",
                    "fileName": "Initial data load",
                    "items": [
                        {
                            "MandatoryChecks": None,
                            "bookingRef": None,
                            "checkedAmount": 4083.22,
                            "checks": 1,
                            "currency": "GBP",
                            "element": None,
                            "originalClaim": None,
                            "percentageCheck": 0.01,
                            "percentageTotal": 0.02,
                            "revisedClaim": ********.*********,
                        },
                        {
                            "MandatoryChecks": "Booking Terms and Conditions",
                            "bookingRef": "A9589",
                            "checkedAmount": 34567,
                            "checks": 1,
                            "currency": "USD",
                            "element": "Commission",
                            "originalClaim": None,
                            "percentageCheck": 3.12,
                            "percentageTotal": 101667.65,
                            "revisedClaim": 34,
                        },
                    ],
                },
                {
                    "AmountasperBank": "",
                    "BusinessRules": "Claim Too Early For Departure Date",
                    "Notes": "",
                    "ResolutionNotes": "",
                    "Risk": "",
                    "Status": "",
                    "SubsetofFullChecks": "",
                    "_id": "632bf1bdbc7e2d644cce7b76",
                    "fileName": "Initial data load",
                    "items": [
                        {
                            "MandatoryChecks": "Supplier invoice(s)/statement(s), Payment confirmation",
                            "bookingRef": "1234",
                            "checkedAmount": 4083.22,
                            "checks": 1,
                            "currency": "GBP",
                            "element": "Deposit",
                            "originalClaim": None,
                            "percentageCheck": 0.01,
                            "percentageTotal": 0.02,
                            "revisedClaim": ********.*********,
                        },
                        {
                            "MandatoryChecks": None,
                            "bookingRef": None,
                            "checkedAmount": 34567,
                            "checks": 1,
                            "currency": "USD",
                            "element": None,
                            "originalClaim": None,
                            "percentageCheck": 3.12,
                            "percentageTotal": 101667.65,
                            "revisedClaim": 34,
                        },
                    ],
                },
            ]
        }
    ),
)
def test_claim_report_success(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/internal-audit/reports/claim?clientId=632af99d7dc025fdb915f639&fromDate=2020-01-01&toDate=2022-12-30"
    )

    # Then
    assert response.status_code == 200


def test_claim_report_exception(flask_client):
    skip_auth()
    # When
    response = flask_client.get("/api/internal-audit/reports/claim?clientId=632af99d7dc025fdb915f639&toDate=2022-12-30")

    # Then
    assert response.status_code == 400
    assert response.json == {"errors": ["Missing required details"]}


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    InternalAuditService,
    "claim_report",
    MagicMock(
        return_value={
            "content": [
                {
                    "AmountasperBank": "",
                    "BusinessRules": "Claim Too Early For Departure Date",
                    "Notes": "",
                    "ResolutionNotes": "",
                    "Risk": "",
                    "Status": "",
                    "SubsetofFullChecks": "",
                    "_id": "632bf1bdbc7e2d644cce7b76",
                    "fileName": "Initial data load",
                    "items": [
                        {
                            "MandatoryChecks": None,
                            "bookingRef": None,
                            "checkedAmount": 4083.22,
                            "checks": 1,
                            "currency": "GBP",
                            "element": None,
                            "originalClaim": None,
                            "percentageCheck": 0.01,
                            "percentageTotal": 0.02,
                            "revisedClaim": ********.*********,
                        },
                        {
                            "MandatoryChecks": "Booking Terms and Conditions",
                            "bookingRef": "A9589",
                            "checkedAmount": 34567,
                            "checks": 1,
                            "currency": "USD",
                            "element": "Commission",
                            "originalClaim": None,
                            "percentageCheck": 3.12,
                            "percentageTotal": 101667.65,
                            "revisedClaim": 34,
                        },
                    ],
                },
                {
                    "AmountasperBank": "",
                    "BusinessRules": "Claim Too Early For Departure Date",
                    "Notes": "",
                    "ResolutionNotes": "",
                    "Risk": "",
                    "Status": "",
                    "SubsetofFullChecks": "",
                    "_id": "632bf1bdbc7e2d644cce7b76",
                    "fileName": "Initial data load",
                    "items": [
                        {
                            "MandatoryChecks": "Supplier invoice(s)/statement(s), Payment confirmation",
                            "bookingRef": "1234",
                            "checkedAmount": 4083.22,
                            "checks": 1,
                            "currency": "GBP",
                            "element": "Deposit",
                            "originalClaim": None,
                            "percentageCheck": 0.01,
                            "percentageTotal": 0.02,
                            "revisedClaim": ********.*********,
                        },
                        {
                            "MandatoryChecks": None,
                            "bookingRef": None,
                            "checkedAmount": 34567,
                            "checks": 1,
                            "currency": "USD",
                            "element": None,
                            "originalClaim": None,
                            "percentageCheck": 3.12,
                            "percentageTotal": 101667.65,
                            "revisedClaim": 34,
                        },
                    ],
                },
            ]
        }
    ),
)
def test_claim_report_export_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    open(f"{temp_dir}/Claim Internal Audit Report", "w")

    # When
    response = flask_client.get(
        "/api/internal-audit/reports/claim?clientId=632af99d7dc025fdb915f639&fromDate=2020-01-01&toDate=2022-12-30"
    )

    # Then
    assert response.status_code == 200


@patch.object(
    InternalAuditService,
    "claim_analysis",
    MagicMock(
        return_value={
            "elementByCurrency": [
                {
                    "element": "General",
                    "elementList": [
                        {"currency": "USD", "elementAmount": 31240.0, "percentageAmount": 0.0, "percentageCheck": 0.0},
                        {
                            "currency": "GBP",
                            "elementAmount": 464523.85,
                            "percentageAmount": 7.6693887730414705,
                            "percentageCheck": 2.96028880866426,
                        },
                    ],
                },
                {
                    "element": "Low Cost Flight",
                    "elementList": [
                        {
                            "currency": "GBP",
                            "elementAmount": 64471367.03,
                            "percentageAmount": 3.9781379364990954,
                            "percentageCheck": 0.721219516637223,
                        },
                        {
                            "currency": "USD",
                            "elementAmount": 10000359.96,
                            "percentageAmount": 0.0,
                            "percentageCheck": 0.0,
                        },
                    ],
                },
            ],
            "totalByCurrency": [
                {"amount": 100011997.8, "currency": "INR", "currencyCount": 3, "totalBookings": 1, "variance": 0},
                {
                    "amount": 20266435.74,
                    "currency": "EUR",
                    "currencyCount": 81473,
                    "totalBookings": 78235,
                    "variance": 20218435.74,
                },
                {
                    "amount": 1283979417.11,
                    "currency": "GBP",
                    "currencyCount": 2253768,
                    "totalBookings": 643706,
                    "variance": 1283947417.11,
                },
                {
                    "amount": 28308898.08,
                    "currency": "USD",
                    "currencyCount": 15477,
                    "totalBookings": 14628,
                    "variance": 28268898.08,
                },
                {
                    "amount": 1990084189.07,
                    "currency": "NOK",
                    "currencyCount": 793362,
                    "totalBookings": 748611,
                    "variance": 1990032189.07,
                },
            ],
        }
    ),
)
@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
def test_claim_analysis_success(flask_client):
    skip_auth()
    # Given

    # When
    response = flask_client.get(
        "/api/internal-audit/reports/claim-analysis?client=632af99d7dc025fdb915f639&fromDate=2020-01-01&toDate=2022-12-30&currency=GBP"
    )

    # Then
    assert response.status_code == 200


@patch.object(
    InternalAuditService,
    "client_cash_flow_forecasts",
    MagicMock(
        return_value={
            "claimsData": [
                {"amount": 1, "bookings": ["A466571", "A464022"], "bookings_count": 2, "month": "Jan"},
                {"amount": 0, "bookings": [], "bookings_count": 0, "month": "Feb"},
                {"amount": 2, "bookings": ["A476452", "A479289"], "bookings_count": 3, "month": "Mar"},
                {"amount": 0, "bookings": [], "bookings_count": 0, "month": "Apr"},
                {"amount": 0, "bookings": [], "bookings_count": 0, "month": "May"},
                {"amount": 0, "bookings": [], "bookings_count": 0, "month": "Jun"},
                {
                    "amount": 2,
                    "bookings": ["A461702", "A466135", "A479730", "A478766"],
                    "bookings_count": 4,
                    "month": "Jul",
                },
                {"amount": 0, "bookings": [], "bookings_count": 0, "month": "Aug"},
                {"amount": 0, "bookings": [], "bookings_count": 0, "month": "Sep"},
                {"amount": 0, "bookings": [], "bookings_count": 0, "month": "Oct"},
                {
                    "amount": 4.547473508864641e-13,
                    "bookings": ["A460722", "A448187", "A445523"],
                    "bookings_count": 3,
                    "month": "Nov",
                },
                {"amount": 0, "bookings": [], "bookings_count": 0, "month": "Dec"},
            ],
            "totalClaimSum": 9.547473508864641e-13,
        }
    ),
)
def test_client_cash_flow_forecasts_success(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/internal-audit/reports/client-cash-flow?clientId=62ce5983bff9a1862ca13424&year=2020&currency=GBP&returnDate=2020-03-01"
    )

    # Then
    assert response.status_code == 200


def test_client_cash_flow_forecasts_exception(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/internal-audit/reports/client-cash-flow?clientId=62ce5983bff9a1862ca13424&year=2020"
    )

    # Then
    assert response.status_code == 400
    assert response.json == {"errors": ["Missing required details"]}


@patch.object(
    InternalAuditService,
    "client_performance",
    MagicMock(
        return_value={
            "monthly": [
                {
                    "amount": 0,
                    "budgeted_amount": 0.0,
                    "max_no_of_claims": 0,
                    "month": "March",
                    "no_of_claims": 3,
                    "total_amount": 0,
                    "total_claims": 2.0,
                },
                {
                    "amount": 0,
                    "budgeted_amount": 0.0,
                    "max_no_of_claims": 0,
                    "month": "April",
                    "no_of_claims": 3,
                    "total_amount": 0,
                    "total_claims": 2.0,
                },
                {
                    "amount": 4000,
                    "budgeted_amount": 0.0,
                    "max_no_of_claims": 0,
                    "month": "May",
                    "no_of_claims": 3,
                    "total_amount": 12000,
                    "total_claims": 2.0,
                },
                {
                    "amount": 0,
                    "budgeted_amount": 0.0,
                    "max_no_of_claims": 0,
                    "month": "June",
                    "no_of_claims": 3,
                    "total_amount": 0,
                    "total_claims": 2.0,
                },
                {
                    "amount": 0,
                    "budgeted_amount": 0.0,
                    "max_no_of_claims": 0,
                    "month": "July",
                    "no_of_claims": 3,
                    "total_amount": 0,
                    "total_claims": 2.0,
                },
                {
                    "amount": 0,
                    "budgeted_amount": 0.0,
                    "max_no_of_claims": 0,
                    "month": "August",
                    "no_of_claims": 3,
                    "total_amount": 0,
                    "total_claims": 2.0,
                },
                {
                    "amount": 0,
                    "budgeted_amount": 0.0,
                    "max_no_of_claims": 0,
                    "month": "September",
                    "no_of_claims": 3,
                    "total_amount": 0,
                    "total_claims": 2.0,
                },
                {
                    "amount": 0,
                    "budgeted_amount": 0.0,
                    "max_no_of_claims": 0,
                    "month": "October",
                    "no_of_claims": 3,
                    "total_amount": 0,
                    "total_claims": 2.0,
                },
                {
                    "amount": 0,
                    "budgeted_amount": 0.0,
                    "max_no_of_claims": 0,
                    "month": "November",
                    "no_of_claims": 3,
                    "total_amount": 0,
                    "total_claims": 2.0,
                },
                {
                    "amount": 0,
                    "budgeted_amount": 0.0,
                    "max_no_of_claims": 0,
                    "month": "December",
                    "no_of_claims": 3,
                    "total_amount": 0,
                    "total_claims": 2.0,
                },
                {
                    "amount": 8000,
                    "budgeted_amount": 0.0,
                    "max_no_of_claims": 0,
                    "month": "January",
                    "no_of_claims": 3,
                    "total_amount": 8000,
                    "total_claims": 2.0,
                },
                {
                    "amount": 0,
                    "budgeted_amount": 0.0,
                    "max_no_of_claims": 0,
                    "month": "February",
                    "no_of_claims": 3,
                    "total_amount": 0,
                    "total_claims": 2.0,
                },
            ],
            "yearly": {
                "actualClaims": 3,
                "totalAnnualActualRevenue": 12000,
                "totalAnnualRevenue": 0,
                "totalClaims": 2.0,
            },
        }
    ),
)
def test_client_performance(flask_client):
    skip_auth()
    # When
    response = flask_client.get(
        "/api/internal-audit/reports/client-performance?fromDate=2022-03-05&toDate=2023-04-05&client=62419f08888c2b5ed06235d9"
    )

    # Then
    assert response.status_code == 200
