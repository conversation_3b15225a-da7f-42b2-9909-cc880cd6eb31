import tempfile
from mock import <PERSON><PERSON>ock, patch
import json
from flaskr.controllers.exceptions import ControllerException
from flaskr.services.anomalies_service import AnomalyService
from flaskr.services.reporting_service import ReportingService
from tests.helpers import skip_auth


@patch.object(
    AnomalyService,
    "banking_anomaly_search",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "61fb95ac47fcbd3d72d5478f",
                    "bankingDate": "2022-02-03T08:43:24.865000",
                    "clientId": "a12",
                    "clientName": "xyz",
                    "count": 1,
                    "fileName": "********-ABC-Banking1 1 (1)-new (1).xls",
                    "status": "Unresolved Anomalies",
                }
            ],
            "empty": False,
            "first": True,
            "last": True,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 2,
            "totalPages": 1,
        }
    ),
)
def test_banking_anomaly_search_success(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "a12", "query": "str", "fromDate": "", "toDate": "", "page": "", "size": ""}

    # When
    response = flask_client.post("/api/anomaly/banking", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [
            {
                "_id": "61fb95ac47fcbd3d72d5478f",
                "bankingDate": "2022-02-03T08:43:24.865000",
                "clientId": "a12",
                "clientName": "xyz",
                "count": 1,
                "fileName": "********-ABC-Banking1 1 (1)-new (1).xls",
                "status": "Unresolved Anomalies",
            }
        ],
        "empty": False,
        "first": True,
        "last": True,
        "numberOfElements": 1,
        "pageNumber": 1,
        "totalElements": 2,
        "totalPages": 1,
    }


@patch.object(
    AnomalyService,
    "banking_anomaly_search",
    MagicMock(
        return_value={
            "content": [],
            "empty": True,
            "first": True,
            "last": False,
            "numberOfElements": 0,
            "pageNumber": 1,
            "totalElements": 0,
            "totalPages": 0,
        }
    ),
)
def test_banking_anomaly_search_success_empty(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "12345", "query": "", "fromDate": "", "toDate": "", "page": 1, "size": 10}

    # When
    response = flask_client.post("/api/anomaly/banking", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [],
        "empty": True,
        "first": True,
        "last": False,
        "numberOfElements": 0,
        "pageNumber": 1,
        "totalElements": 0,
        "totalPages": 0,
    }


@patch.object(
    AnomalyService,
    "claims_anomaly_search",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "620245de6dda372675663e05",
                    "claimDate": "2022-02-03T08:43:24.865000",
                    "clientId": "a12",
                    "clientName": "xyz",
                    "count": 7,
                    "fileName": "********-ABC-Banking1 1 (1)-new (1).xls",
                    "status": "Unresolved Anomalies",
                }
            ],
            "empty": False,
            "first": True,
            "last": True,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
def test_claims_anomaly_search_success(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "a12", "query": "", "fromDate": "", "toDate": "", "page": 1, "size": 10}

    # When
    response = flask_client.post("/api/anomaly/claims", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [
            {
                "_id": "620245de6dda372675663e05",
                "claimDate": "2022-02-03T08:43:24.865000",
                "clientId": "a12",
                "clientName": "xyz",
                "count": 7,
                "fileName": "********-ABC-Banking1 1 (1)-new (1).xls",
                "status": "Unresolved Anomalies",
            }
        ],
        "empty": False,
        "first": True,
        "last": True,
        "numberOfElements": 1,
        "pageNumber": 1,
        "totalElements": 1,
        "totalPages": 1,
    }


@patch.object(
    AnomalyService,
    "claims_anomaly_search",
    MagicMock(
        return_value={
            "content": [],
            "empty": True,
            "first": True,
            "last": False,
            "numberOfElements": 0,
            "pageNumber": 1,
            "totalElements": 0,
            "totalPages": 0,
        }
    ),
)
def test_claims_anomaly_search_success_empty(flask_client):
    skip_auth()
    # Given
    request = {"clientId": "12345", "query": "", "fromDate": "", "toDate": "", "page": 1, "size": 10}

    # When
    response = flask_client.post("/api/anomaly/claims", data=json.dumps(request), content_type="application/json")

    # Then
    assert response.status_code == 200
    assert json.loads(response.data.decode()) == {
        "content": [],
        "empty": True,
        "first": True,
        "last": False,
        "numberOfElements": 0,
        "pageNumber": 1,
        "totalElements": 0,
        "totalPages": 0,
    }


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(
    AnomalyService,
    "anomaly_update_status",
    MagicMock(return_value=None),
)
def test_anomaly_update_status_success(flask_client):
    skip_auth()
    # Given
    request = {"anomalyIds": ["621bf4f49bed97189d98ddcd", "621bf4f49bed97189d98ddce"], "status": "resolved"}

    # When
    response = flask_client.put(
        "/api/anomaly/claims/update-status", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(AnomalyService, "anomaly_update_status", MagicMock(side_effect=ControllerException))
def test_anomaly_update_status_controller_exception(flask_client):
    skip_auth()
    # Given
    request = {}

    # When
    response = flask_client.put(
        "/api/anomaly/claims/update-status", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 400


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(
    AnomalyService,
    "anomaly_update_status",
    MagicMock(return_value=None),
)
def test_anomaly_banking_update_status_success(flask_client):
    skip_auth()
    # Given
    request = {"anomalyIds": ["621bf4f49bed97189d98ddcd", "621bf4f49bed97189d98ddce"], "status": "resolved"}

    # When
    response = flask_client.put(
        "/api/anomaly/banking/update-status", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 200


@patch("jose.jwt.get_unverified_claims", lambda x: {"sub": "1"})
@patch.object(AnomalyService, "anomaly_update_status", MagicMock(side_effect=ControllerException))
def test_anomaly_banking_update_status_controller_exception(flask_client):
    skip_auth()
    # Given
    request = {}

    # When
    response = flask_client.put(
        "/api/anomaly/banking/update-status", data=json.dumps(request), content_type="application/json"
    )

    # Then
    assert response.status_code == 400


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    AnomalyService,
    "claims_anomaly_search",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "62aafc5336feb4b25b99f9f5",
                    "clientId": "CR7",
                    "clientName": "Edstem Test User",
                    "count": 1,
                    "fileDate": "2022-04-24",
                    "fileName": "********- ALLclaim-regression (1).xlsx",
                    "friendlyName": "Edstem Test User",
                    "status": "Unresolved Anomalies",
                }
            ],
            "empty": False,
            "first": True,
            "last": True,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
def test_export_claim_anomalies_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {"clientId": "CR7", "query": "", "fromDate": "", "toDate": "", "page": "", "size": ""}
    open(f"{temp_dir}/ClaimAnomalies", "w")

    # When
    response = flask_client.post(
        "/api/anomaly/claims/export",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200


@patch.object(
    ReportingService,
    "generate_excel_report",
    MagicMock(return_value=None),
)
@patch.object(
    AnomalyService,
    "banking_anomaly_search",
    MagicMock(
        return_value={
            "content": [
                {
                    "_id": "62aafc5336feb4b25b99f9f5",
                    "clientId": "CR7",
                    "clientName": "Edstem Test User",
                    "count": 1,
                    "fileDate": "2022-04-24",
                    "fileName": "********- ALLclaim-regression (1).xlsx",
                    "friendlyName": "Edstem Test User",
                    "status": "Unresolved Anomalies",
                }
            ],
            "empty": False,
            "first": True,
            "last": True,
            "numberOfElements": 1,
            "pageNumber": 1,
            "totalElements": 1,
            "totalPages": 1,
        }
    ),
)
def test_export_banking_anomalies_success(flask_client):
    skip_auth()
    # Given
    temp_dir = tempfile.gettempdir()
    request = {"clientId": "CR7", "query": "", "fromDate": "", "toDate": "", "page": "", "size": ""}
    open(f"{temp_dir}/BankingAnomalies", "w")

    # When
    response = flask_client.post(
        "/api/anomaly/banking/export",
        data=json.dumps(request),
        content_type="application/json",
    )

    # Then
    assert response.status_code == 200
