from mock import patch
import pytest

from flaskr.services.banking_and_claim_summary_service import bankingAndClaimService
from tests.helpers import skip_auth


@pytest.mark.skip("To be completed later")
@patch("jose.jwt.get_unverified_claims", lambda x: {"cognito:groups": ["ptt-user"]})
@patch.object(bankingAndClaimService, "get_banking_and_claim_summary", lambda x, y: {})
def test_banking_and_claim_summary(flask_client):
    skip_auth()

    # When
    response = flask_client.get("/api/banking-and-claims/list/banking-claim-summary?clientId=62fb53f02ce2345143250f96")

    # Then
    assert response.status_code == 200
