from datetime import datetime
from freezegun import freeze_time
import pytest
from flaskr.helpers import reporting_util, track_opening_closing_balance_changes, round


def test_extract_amount(flask_client):
    result = reporting_util.extract_amount(
        "performance", "elementType", [{"elementType": "performance", "amount": 20775}]
    )
    assert result == 20775


def test_extract_amount1(flask_client):
    result = reporting_util.extract_amount("commission", "elementType", [])
    assert result == 0


@freeze_time("May 30th 2022")
@pytest.mark.parametrize(
    "existing_txn_changes, output",
    [
        (
            None,
            {
                "client_id": "1",
                "currency": "GBP",
                "amount": 500,
                "file_date": "2022-05-28",
                "date": datetime(2022, 5, 30),
            },
        ),
        (
            [
                {
                    "client_id": "1",
                    "currency": "GBP",
                    "amount": 500,
                    "file_date": "2022-05-28",
                    "date": datetime(2022, 5, 30),
                }
            ],
            {
                "client_id": "1",
                "currency": "GBP",
                "amount": 1000,
                "file_date": "2022-05-28",
                "date": datetime(2022, 5, 30),
            },
        ),
    ],
)
def test_track_opening_closing_balance_changes(flask_client, patch_db, existing_txn_changes, output):
    # Given
    if existing_txn_changes:
        patch_db.opening_closing_balance_changes.insert_many(existing_txn_changes)
    transaction = {"client_id": "1", "currency_code": "GBP", "created_at": datetime(2022, 5, 29)}
    amount_difference = 500
    file_date = "2022-05-28"
    session = None

    # When
    track_opening_closing_balance_changes(transaction, amount_difference, file_date, session)

    # Then
    opening_closing_balance_changes = patch_db.opening_closing_balance_changes.find_one(
        {"client_id": "1", "currency": "GBP", "date": datetime.today()}, projection={"_id": 0}
    )
    assert opening_closing_balance_changes == output


@pytest.mark.parametrize(
    "number, output",
    [
        (100.255, 100.26),
        (100.252, 100.25),
        (100.055, 100.06),
        (100.065, 100.07),
        (100.0005, 100.00),
        (-100.312, -100.31),
    ],
)
def test_round(flask_client, number, output):
    result = round(number, 2)
    assert result == output
