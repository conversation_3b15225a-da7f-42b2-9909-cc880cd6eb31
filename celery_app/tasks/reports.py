from collections import OrderedDict
from datetime import datetime
import logging
import os

from bson import ObjectId
from flaskr.services.reporting_service import reporting_service
from flaskr.helpers.date_util import change_date_format
from flaskr.models import get_db
from flaskr.helpers.boto3_handler import upload_file
from celery_app.celery_holder import celery
from collections.abc import Generator
from flask import current_app as app

from pymongo import MongoClient
from flaskr.helpers.secret_manager_handler import get_secret
import time
import gc


logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize MongoDB & S3 clients
_mongo_uri = os.environ.get("DATABASE_URI") or get_secret("eu-west-2")
mongo_client = MongoClient(_mongo_uri)
db = mongo_client.get_database()


def transform_dates(client: str, data: Generator) -> Generator:
    for row in data:
        yield {
            **row,
            "bookingDate": datetime.strptime(row["bookingDate"], "%Y-%m-%d").strftime("%d-%m-%Y %H:%M:%S.%f")[:-3]
            if ((client == app.config.get("NAS") or client == app.config["FLYPOP"]) and row.get("bookingDate"))
            else change_date_format(row["bookingDate"], use_hyphens=True),
            "departureDate": datetime.strptime(row["departureDate"], "%Y-%m-%d").strftime("%d-%m-%Y %H:%M:%S.%f")[:-3]
            if ((client == app.config.get("NAS") or client == app.config["FLYPOP"]) and row.get("departureDate"))
            else change_date_format(row["departureDate"], use_hyphens=True),
            "returnDate": datetime.strptime(row["returnDate"], "%Y-%m-%d").strftime("%d-%m-%Y %H:%M:%S.%f")[:-3]
            if ((client == app.config.get("NAS") or client == app.config["FLYPOP"]) and row.get("returnDate"))
            else change_date_format(row["returnDate"], use_hyphens=True),
        }


@celery.task()
def trust_balance_report_task(data):
    client = data.get("client")
    file_id_xlsx = data.get("file_id_xlsx")
    file_id_csv = data.get("file_id_csv")
    if client == app.config.get("NAS") or client == app.config.get("FLYPOP"):
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        logging.info(f"client_basic_info: {client_basic_info}")
    if client == app.config.get("SWOOP") or client == app.config.get("SWOOP_TRAVEL"):
        details = reporting_service.multi_currency_trust_balance_report(data)
    # elif (client == app.config.get("NAS") or client == app.config.get("FLYPOP")) and client_basic_info.get(
    #     "new_workflow"
    # ):
    # TODO: Need to be changed later
    elif client == app.config.get("NAS") or client == app.config.get("FLYPOP"):
        logging.info("inside nas report>>>>>")
        details = reporting_service.trust_balance_report_nas(data)

        # Convert generator to list to ensure it's fully processed
        if isinstance(details["content"], Generator):
            details["content"] = list(details["content"])

        # logging.info(f"nas report details: {details}")
    elif client == app.config.get("GTL"):
        details = reporting_service.trust_balance_report_gtl(data)
    else:
        logging.info("not inside nas report but else>>>>")
        details = reporting_service.trust_balance_report(data)

    # Ensure content is transformed
    details["content"] = list(transform_dates(client, details["content"]))

    header_dict = OrderedDict(
        [
            ("cId", "Client Id"),
            ("friendlyName", "Client Name"),
            ("bookingRef", "Booking Ref"),
            ("leadPax", "Lead Pax"),
            ("bookingDate", "Booking Date"),
            ("departureDate", "Date of Travel"),
            ("returnDate", "Date of Return"),
            ("supplierName", "Supplier Names"),
            ("currency", "Currency"),
            ("deposits", "Deposits"),
            ("refundsFromDepositFile", "Refund From Deposit File"),
            ("totalBanked", "Total Banked"),
            ("totalClaimed", "Total Claimed"),
            ("balance", "Balance"),
            ("status", "Status"),
            ("type", "Type"),
            ("totalBookingValue", "Total Booking Value"),
        ]
    )
    if (client not in [app.config.get("NAS"), app.config.get("FLYPOP")]) or (
        client in [app.config.get("NAS"), app.config.get("FLYPOP")] and not client_basic_info.get("new_workflow")
    ):
        header_dict.pop("supplierName")
    bucket = app.config["REPORTS_BUCKET"]

    logging.info("csv and xlsx file generation started")
    reporting_service.generate_csv_and_excel_report(header_dict, details["content"], file_id_csv, file_id_xlsx)
    logging.info("csv and xlsx file generation completed")

    logging.info("Uploading csv report to S3")
    upload_file(bucket=bucket, key=file_id_csv, file_path=f"{app.config['TEMP_DIR']}/{file_id_csv}")
    logging.info("Uploaded csv report to S3")

    logging.info("Uploading xlsx report to S3")
    upload_file(bucket=bucket, key=file_id_xlsx, file_path=f"{app.config['TEMP_DIR']}/{file_id_xlsx}")
    logging.info("Uploaded xlsx report to S3")

    now = datetime.utcnow()
    get_db().report_files.update_many(
        {"file_id": {"$in": [file_id_csv, file_id_xlsx]}},
        {
            "$set": {
                "status": "Generated New Report",
                "generated_at": now,
                "updated_at": now,
            }
        },
    )
    logging.info("Updated report information in db")


@celery.task()
def trust_balance_report_nas(data):
        """
        Implementation of trust balance report for NAS clients
        Supports multiple currencies per booking reference
        
        Memory-optimized version that processes data in chunks
        """
        # Simplified logging without psutil dependency
        def log_checkpoint(label):
            """Log processing checkpoint"""
            logger.info(f"Processing checkpoint: {label}")
        
        start_time = time.time()
        log_checkpoint("start")
        
        client_id = data.get("client")
        if not client_id:
            logger.error("Client ID is missing in request data")
            raise ValueError("Client Id is missing")
        
        currency = data.get("currency") or ""
        collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"
        
        logger.info(f"Generating trust balance report for client: {client_id}, currency filter: {currency}")
        
        # Set up matching conditions - add balance filter early to reduce records
        client_and_currency_match_condition = [{
            "$match": {
                "$and": [
                    {"client_id": ObjectId(client_id)},
                    # Add balance filter early to reduce data volume
                    {"$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}]}
                ]
            }
        }]
        
        if currency:
            client_and_currency_match_condition[0]["$match"]["$and"].append({"currency_code": {"$all": [currency]}})
            logger.info(f"Added currency filter for: {currency}")
        
        # Count how many trust fund records we're querying
        count_pipeline = [*client_and_currency_match_condition, {"$count": "count"}]
        count_result = list(db[collection_name].aggregate(count_pipeline))
        total_records = count_result[0]["count"] if count_result else 0
        logger.info(f"Found {total_records} trust fund records for client {client_id}")
        
        # Safety limit - don't process more than MAX_RECORDS to prevent timeouts
        MAX_RECORDS = 500000
        if total_records > MAX_RECORDS:
            logger.warning(f"Too many records to process: {total_records}, limiting to {MAX_RECORDS}")
            total_records = MAX_RECORDS
        
        # Process data in larger chunks to improve performance
        CHUNK_SIZE = 2000  # Process 5000 records at a time instead of 200
        processed_data = []
        seen_bookings = {}
        currency_counts = {}
        multi_currency_bookings = set()
        
        # Stats for summary logging
        total_processed = 0
        total_with_mappings = 0
        total_without_mappings = 0
        
        # Define the aggregation pipeline once - optimize by adding projection early
        aggregation_pipeline = [
            *client_and_currency_match_condition,
            # Add sort to optimize data access patterns
            {"$sort": {"booking_ref": 1, "currency_code": 1}},
            # First $lookup to get currency details if they exist
            {
                "$lookup": {
                    "from": "currency_details",
                    "localField": "_id",
                    "foreignField": "trust_fund_id",
                    "as": "currency_details"
                }
            },
            # Custom processing to handle either direct balance or currency_mappings
            {
                "$addFields": {
                    "has_currency_mappings": {"$cond": [{"$gt": [{"$size": {"$ifNull": ["$currency_mappings", []]}}, 0]}, True, False]}
                }
            },
            {
                "$match": {
                    "$or": [
                        # For records with currency_mappings, we'll process them later
                        {"has_currency_mappings": True},
                        # For records without currency_mappings, we already applied the balance check earlier
                        {"has_currency_mappings": False}
                    ]
                }
            },
            # Optimize subsequent lookups
            {
                "$lookup": {
                    "from": "banking_file_details",
                    "let": {"current_client_id": "$client_id", "current_booking_ref": "$booking_ref"},
                    "localField": "booking_ref",
                    "foreignField": "booking_ref",
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$client_id", "$$current_client_id"]},
                                        {"$eq": ["$booking_ref", "$$current_booking_ref"]},
                                        {"$eq": ["$deleted", False]},
                                    ]
                                }
                            }
                        },
                        {
                            "$project": {
                                "_id": 1,
                                "amount": 1,
                                "type": {"$ifNull": ["$type", None]},
                                "supplierNames": {"$ifNull": ["$supplier_names", None]},
                                "currency_code": {"$ifNull": ["$currency_code", None]},  # Include currency
                            }
                        },
                    ],
                    "as": "bank",
                }
            },
            {
                "$lookup": {
                    "from": "claims_file_details",
                    "let": {"current_client_id": "$client_id", "current_booking_ref": "$booking_ref"},
                    "localField": "booking_ref",
                    "foreignField": "booking_ref",
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$client_id", "$$current_client_id"]},
                                        {"$eq": ["$booking_ref", "$$current_booking_ref"]},
                                        {"$eq": ["$deleted", False]},
                                    ]
                                }
                            }
                        },
                        {
                            "$project": {
                                "amount": 1,
                                "currency_code": {"$ifNull": ["$currency_code", None]},  # Include currency
                            }
                        },
                    ],
                    "as": "claim",
                }
            },
            {
                "$lookup": {
                    "from": "client_basic_info",
                    "localField": "client_id",
                    "foreignField": "_id",
                    "as": "client",
                }
            },
            {"$unwind": "$client"},
            {
                "$project": {
                    "_id": "$_id",
                    "clientId": "$client._id",
                    "cId": "$client.c_id",
                    "clientName": "$client.full_name",
                    "friendlyName": "$client.friendly_name",
                    "bookingRef": "$booking_ref",
                    "leadPax": "$lead_pax",
                    "departureDate": "$departure_date",
                    "returnDate": "$return_date",
                    "bookingDate": "$booking_date",
                    "banking_amount": "$bank.amount",
                    "banking_currency": "$bank.currency_code",  # Include currency from banking
                    "supplierNames": "$bank.supplierNames",
                    "claim_amount": "$claim.amount",
                    "claim_currency": "$claim.currency_code",  # Include currency from claims
                    "bookingStatus": "$booking_status",
                    "balance": {"$ifNull": ["$balance", 0.0]},
                    "currency_code": "$currency_code",
                    "currency_mappings": "$currency_mappings",  # Include currency mappings
                    "currency_details": "$currency_details",    # Include currency details
                    "has_currency_mappings": "$has_currency_mappings",
                    "types": "$bank.type",
                    "totalBookingValue": {"$ifNull": ["$total_booking_value", 0.0]},
                }
            },
        ]
        
        # Process in chunks to prevent memory issues
        logger.info(f"Starting to process data in chunks of {CHUNK_SIZE} records, total limit: {total_records}")
        
        # Add skip/limit to the pipeline for each chunk
        for offset in range(0, total_records, CHUNK_SIZE):
            chunk_start_time = time.time()
            logger.info(f"Processing chunk {offset//CHUNK_SIZE + 1} of {(total_records + CHUNK_SIZE - 1)//CHUNK_SIZE}")
            
            # Create a pipeline copy with skip/limit for this chunk
            chunk_pipeline = aggregation_pipeline.copy()
            chunk_pipeline.append({"$skip": offset})
            chunk_pipeline.append({"$limit": CHUNK_SIZE})
            
            # Run the chunk query
            trust_funds = db[collection_name].aggregate(
                chunk_pipeline,
                collation={"locale": "en", "strength": 1},
                allowDiskUse=True,
            )
            
            # Process each record in the chunk
            chunk_processed = 0
            for trust_fund in trust_funds:
                chunk_processed += 1
                total_processed += 1
                booking_ref = trust_fund.get("bookingRef")
                
                # Track mappings stats
                if trust_fund.get("has_currency_mappings", False):
                    total_with_mappings += 1
                else:
                    total_without_mappings += 1
                    
                # Check if this record has currency mappings
                if trust_fund.get("has_currency_mappings", False):
                    # Get the currency mappings
                    currency_mappings = trust_fund.get("currency_mappings", [])
                    currency_details = trust_fund.get("currency_details", [])
                    
                    # Log currency information for this booking
                    currencies = [cm.get("currency") for cm in currency_mappings]
                    
                    if len(currencies) > 1:
                        multi_currency_bookings.add(booking_ref)
                    
                    # Create a mapping of currency to details for easier access
                    currency_detail_map = {detail.get("currency"): detail for detail in currency_details}
                    
                    # Process each currency separately
                    for currency_map in currency_mappings:
                        currency = currency_map.get("currency")
                        
                        # Track currencies seen
                        if currency not in currency_counts:
                            currency_counts[currency] = 0
                        currency_counts[currency] += 1
                        
                        currency_detail = currency_detail_map.get(currency)
                        
                        if not currency_detail:
                            logger.warning(f"Missing currency detail for {booking_ref} with currency {currency}")
                            continue
                        
                        try:
                            # Filter banking amounts and claims by this currency
                            banking_amounts = []
                            for amount, bank_currency in zip(trust_fund.get("banking_amount", []), 
                                                            trust_fund.get("banking_currency", [])):
                                if bank_currency == currency:
                                    banking_amounts.append(amount)
                                    
                            claim_amounts = []
                            for amount, claim_currency in zip(trust_fund.get("claim_amount", []), 
                                                            trust_fund.get("claim_currency", [])):
                                if claim_currency == currency:
                                    claim_amounts.append(amount)
                            
                            # Calculate deposits and refunds
                            deposits = 0
                            refunds = 0
                            
                            for amount in banking_amounts:
                                if amount > 0:
                                    deposits += amount
                                else:
                                    refunds += abs(amount)
                            
                            # Calculate claims
                            total_claimed = sum(claim_amounts) if claim_amounts else 0
                            
                            # Use currency-specific balance from currency_detail
                            balance = currency_detail.get("balance", 0.0)
                            
                            # Skip insignificant balances
                            if abs(balance) < 0.005:
                                continue
                            
                            # Determine type (preserve the original if available)
                            types = trust_fund.get("types", [])
                            type_value = None
                            if types:
                                if "Purchase" in types:
                                    type_value = "Purchase"
                                elif "Refund (Credit)" in types:
                                    type_value = "Refund (Credit)"
                                elif types and len(types) > 0:
                                    type_value = types[0]
                                else:
                                    type_value = "Purchase" if deposits > refunds else "Refund (Credit)"
                            else:
                                type_value = "Purchase" if deposits > refunds else "Refund (Credit)"
                            
                            # Create final record with all required fields
                            record = {
                                "_id": str(trust_fund.get("_id")) + "_" + currency,
                                "clientId": trust_fund.get("clientId"),
                                "cId": trust_fund.get("cId"),
                                "clientName": trust_fund.get("clientName"),
                                "friendlyName": trust_fund.get("friendlyName"),
                                "bookingRef": booking_ref,
                                "leadPax": trust_fund.get("leadPax"),
                                "departureDate": trust_fund.get("departureDate"),
                                "returnDate": trust_fund.get("returnDate"),
                                "bookingDate": trust_fund.get("bookingDate"),
                                "currency": currency,  # Use the specific currency
                                "deposits": deposits,
                                "refundsFromDepositFile": refunds,
                                "totalBanked": deposits - refunds,
                                "totalClaimed": total_claimed,
                                "balance": balance,  # Use the currency-specific balance
                                "status": trust_fund.get("bookingStatus", "Active"),
                                "type": type_value,
                                "totalBookingValue": currency_detail.get("total_booking_value", 0.0),  # Use currency-specific value
                                "supplierName": next((s for s in trust_fund.get("supplierNames", []) if s is not None), None)
                            }
                            
                            # Check if we've seen this booking reference + currency combo before
                            key = f"{booking_ref}_{currency}"
                            if key not in seen_bookings:
                                seen_bookings[key] = True
                                processed_data.append(record)
                            else:
                                logger.warning(f"Duplicate booking+currency combination found: {key}")
                                    
                        except Exception as e:
                            logger.error(f"Error processing booking {booking_ref} with currency {currency}: {str(e)}", exc_info=True)
                else:
                    # Process as before for single currency records
                    try:
                        # Get the primary currency (or first in the list)
                        currency_code_value = trust_fund.get("currency_code", [])
                        if isinstance(currency_code_value, list) and currency_code_value:
                            currency = currency_code_value[0]
                        else:
                            currency = currency_code_value
                        
                        # Track currencies seen
                        if currency not in currency_counts:
                            currency_counts[currency] = 0
                        currency_counts[currency] += 1
                        
                        # Calculate deposits and refunds
                        banking_amounts = trust_fund.get("banking_amount", [])
                        deposits = 0
                        refunds = 0
                        
                        for amount in banking_amounts:
                            if amount > 0:
                                deposits += amount
                            else:
                                refunds += abs(amount)
                        
                        # Calculate claims
                        claim_amounts = trust_fund.get("claim_amount", [])
                        total_claimed = sum(claim_amounts) if claim_amounts else 0
                        
                        # Get balance
                        balance = trust_fund.get("balance", 0.0)
                        
                        # Skip insignificant balances
                        if abs(balance) < 0.005:
                            continue
                        
                        # Determine type (preserve the original if available)
                        types = trust_fund.get("types", [])
                        type_value = None
                        if types:
                            if "Purchase" in types:
                                type_value = "Purchase"
                            elif "Refund (Credit)" in types:
                                type_value = "Refund (Credit)"
                            elif types and len(types) > 0:
                                type_value = types[0]
                            else:
                                type_value = "Purchase" if deposits > refunds else "Refund (Credit)"
                        else:
                            type_value = "Purchase" if deposits > refunds else "Refund (Credit)"
                        
                        # Create final record with all required fields
                        record = {
                            "_id": str(trust_fund.get("_id")),
                            "clientId": trust_fund.get("clientId"),
                            "cId": trust_fund.get("cId"),
                            "clientName": trust_fund.get("clientName"),
                            "friendlyName": trust_fund.get("friendlyName"),
                            "bookingRef": booking_ref,
                            "leadPax": trust_fund.get("leadPax"),
                            "departureDate": trust_fund.get("departureDate"),
                            "returnDate": trust_fund.get("returnDate"),
                            "bookingDate": trust_fund.get("bookingDate"),
                            "currency": currency,
                            "deposits": deposits,
                            "refundsFromDepositFile": refunds,
                            "totalBanked": deposits - refunds,
                            "totalClaimed": total_claimed,
                            "balance": balance,
                            "status": trust_fund.get("bookingStatus", "Active"),
                            "type": type_value,
                            "totalBookingValue": trust_fund.get("totalBookingValue", 0.0),
                            "supplierName": next((s for s in trust_fund.get("supplierNames", []) if s is not None), None)
                        }
                        
                        # Check if we've seen this booking reference + currency combo before
                        key = f"{booking_ref}_{currency}"
                        if key not in seen_bookings:
                            seen_bookings[key] = True
                            processed_data.append(record)
                        else:
                            logger.warning(f"Duplicate booking+currency combination found: {key}")
                                
                    except Exception as e:
                        logger.error(f"Error processing legacy booking {booking_ref}: {str(e)}", exc_info=True)
                        
                # Check if we're approaching the 15-minute Lambda time limit (900 seconds)
                # If we're at 850 seconds (14 minutes and 10 seconds), stop processing and return what we have
                if time.time() - start_time > 850:
                    logger.warning(f"Approaching Lambda timeout limit - stopping after {total_processed} records")
                    # Force break out of both loops
                    break
            
            # Check if we need to stop due to timeout
            # if time.time() - start_time > 850:
            #     break
            
            # Log chunk completion and stats
            chunk_time = time.time() - chunk_start_time
            logger.info(f"Chunk processed {chunk_processed} records in {chunk_time:.2f} seconds")
            log_checkpoint(f"after chunk {offset//CHUNK_SIZE + 1}")
            
            # Force garbage collection between chunks
            gc.collect()
        
        process_end = time.time()
        total_time = process_end - start_time
        
        # Log summary statistics
        logger.info(f"Processed {len(processed_data)} unique booking records for NAS trust balance report")
        logger.info(f"Found {len(multi_currency_bookings)} bookings with multiple currencies")
        logger.info(f"Currency distribution: {currency_counts}")
        logger.info(f"Total processed: {total_processed}, with mappings: {total_with_mappings}, without: {total_without_mappings}")
        logger.info(f"Total report generation time: {total_time:.2f} seconds")
        log_checkpoint("end of processing")
        
        # Check for any potential issues in the results
        if len(processed_data) == 0:
            logger.warning("No records were generated for the report!")
        
        if len(currency_counts) == 0:
            logger.warning("No currencies were found in the data!")
        
        return {"content": processed_data}

