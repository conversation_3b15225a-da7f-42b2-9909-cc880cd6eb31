import os

import json
import logging
import boto3
from boto3.exceptions import S3UploadFailedError
from botocore.exceptions import ClientError
from flask import abort

from flaskr.services.exceptions import ServiceException


def validate_mime_type_pdf(mime_type):
    if mime_type != "application/pdf":
        raise ServiceException(message="File format not supported, use pdf files")
    else:
        return None


def validate_mime_type_png_jpeg(mime_type):
    if mime_type != "image/jpeg" and mime_type != "image/png":
        raise ServiceException(message="File format not supported, use jpeg or png files")
    else:
        return None


def validate_mime_type_file(mime_type):
    supported_mime_types = {
        "pdf": "application/pdf",
        "excel": [
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-excel",
            "text/csv",
        ],
        "word": [
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
        ],
        "email": [
            "message/rfc822",
            "application/vnd.ms-outlook",
        ],
    }

    if mime_type == supported_mime_types["pdf"]:
        return "pdf"

    elif mime_type in supported_mime_types["excel"]:
        return "excel"

    elif mime_type in supported_mime_types["word"]:
        return "word"

    elif mime_type in supported_mime_types["email"]:
        return "email"

    else:
        raise ServiceException(message="File format not supported, use pdf, word, excel, or email files")


def validate_mime_type_xls_csv(mime_type):
    if mime_type not in [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
        "text/csv",
    ]:
        return False
    return True


def validate_mime_type_pptx(mime_type):
    if mime_type != "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        raise ServiceException("Invalid file type. Only .pptx files are allowed.")
    else:
        return None


def delete_object(bucket, key):
    s3_client = boto3.client("s3")
    try:
        response = s3_client.delete_object(Bucket=bucket, Key=key)
    except ClientError as e:
        logging.error(e)
        raise ServiceException(message="Failed to delete object")
    return response


def head_object(bucket, key):
    s3_client = boto3.client("s3")

    try:
        response = s3_client.head_object(Bucket=bucket, Key=key)
    except ClientError as e:
        logging.error(e)
        if e.response["Error"]["Code"] == "404":
            abort(404)
        raise ServiceException(message="Failed to retrieve metadata")
    return response


def upload_file(bucket, key, file_path):
    s3_client = boto3.client("s3")

    try:
        response = s3_client.upload_file(file_path, bucket, key)
    except (S3UploadFailedError, ClientError) as e:
        logging.error(e)
        raise ServiceException(message="Failed to upload")
    return response


def call_lambda(func_name, payload):
    """
    Invoke Lambda function asynchronously with improved error handling
    
    Args:
        func_name: The name of the Lambda function to invoke
        payload: The payload data to send to the Lambda function
        
    Returns:
        None
    """
    import time
    
    logging.info(f"Preparing to invoke Lambda function: {func_name}")
    
    try:
        # Create the Lambda client with explicit timeout settings
        client = boto3.client(
            'lambda',
            config=boto3.session.Config(
                connect_timeout=10,  # Connection timeout in seconds
                read_timeout=10,     # Read timeout in seconds
                retries={'max_attempts': 2}
            )
        )
        
        # Log the start of the attempt
        start_time = time.time()
        logging.info(f"Invoking Lambda function {func_name} asynchronously (Event mode)")
        
        # Invoke the Lambda function
        response = client.invoke(
            FunctionName=func_name,
            InvocationType="Event",
            Payload=payload
        )
        
        # Calculate elapsed time
        elapsed = time.time() - start_time
        
        # Check response status code
        status_code = response.get('StatusCode')
        logging.info(f"Lambda invoke completed with status code {status_code} in {elapsed:.2f} seconds")
        
        if status_code != 202:
            logging.warning(f"Unexpected status code from Lambda invoke: {status_code}")
        
        return None
        
    except ClientError as e:
        error_code = e.response.get('Error', {}).get('Code', 'Unknown')
        error_message = e.response.get('Error', {}).get('Message', 'No message')
        logging.error(f"AWS Lambda invoke error: {error_code} - {error_message}")
        logging.error(f"Function name: {func_name}")
        logging.error(f"Exception details: {str(e)}")
        
        # Specific handling for common errors
        if error_code == 'ResourceNotFoundException':
            logging.error(f"Lambda function {func_name} does not exist or you don't have permission to access it")
        elif error_code == 'InvalidParameterValueException':
            logging.error("Invalid parameter value in Lambda request")
        elif error_code == 'ServiceException':
            logging.error("AWS Lambda service encountered an internal error")
        elif error_code == 'TooManyRequestsException':
            logging.error("Lambda throttling error - too many concurrent requests")
        elif error_code in ('AccessDeniedException', 'UnrecognizedClientException'):
            logging.error("Authentication or authorization error - check AWS credentials")
        
        # Avoid silently failing - let the caller know there was an issue
        raise ServiceException(message=f"Failed to invoke Lambda: {error_code} - {error_message}")
        
    except Exception as e:
        # Handle any other exceptions
        logging.error(f"Unexpected error invoking Lambda function {func_name}: {str(e)}")
        logging.exception("Exception details:")
        raise ServiceException(message=f"Unexpected error invoking Lambda: {str(e)}")


def download_file(bucket, key, file_path):
    s3_client = boto3.client("s3")

    try:
        response = s3_client.download_file(bucket, key, file_path)
    except ClientError as e:
        logging.error(e)
        if e.response["Error"]["Code"] == "404":
            abort(404)
        raise ServiceException(message="Failed to download")
    return response


def get_secret(region_name):

    secret_name = os.environ["SECRET_NAME"]

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name=region_name)

    try:
        get_secret_value_response = client.get_secret_value(SecretId=secret_name)
    except ClientError as e:
        if e.response["Error"]["Code"] == "DecryptionFailureException":
            # Secrets Manager can't decrypt the protected secret text using the provided KMS key.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response["Error"]["Code"] == "InternalServiceErrorException":
            # An error occurred on the server side.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response["Error"]["Code"] == "InvalidParameterException":
            # You provided an invalid value for a parameter.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response["Error"]["Code"] == "InvalidRequestException":
            # You provided a parameter value that is not valid for the current state of the resource.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response["Error"]["Code"] == "ResourceNotFoundException":
            # We can't find the resource that you asked for.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
    else:
        # Decrypts secret using the associated KMS key.
        if "SecretString" in get_secret_value_response:
            secret = get_secret_value_response["SecretString"]
            return json.loads(secret).get("mongodb-uri")


def generate_presigned_url(action: str, bucket: str, key: str, expiration: int):
    try:
        response = boto3.client("s3").generate_presigned_url(
            action, Params={"Bucket": bucket, "Key": key}, ExpiresIn=expiration
        )
    except ClientError as e:
        logging.error(e)
        return None
    return response
