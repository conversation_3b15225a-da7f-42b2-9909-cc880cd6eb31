def extract_amount(value, type, type_list):
    value = next(filter(lambda x: x[type] == value, type_list), 0)
    if value != 0:
        value = value["amount"]
    return value


def movements_of_funds_open_banking_data(open_banking_data):
    open_banking_data["openingBalanceCurrent"] = open_banking_data.pop("opening_balance")
    open_banking_data["receiptsCurrent"] = open_banking_data.pop("funds_in")
    open_banking_data["paymentsCurrent"] = open_banking_data.pop("funds_out")
    open_banking_data["closingBalanceCurrent"] = open_banking_data.pop("closing_balance")
    open_banking_data["openingBalancePrevious"] = open_banking_data.pop("last_year_opening_balance")
    open_banking_data["receiptsPrevious"] = open_banking_data.pop("last_year_fund_in")
    open_banking_data["paymentsPrevious"] = open_banking_data.pop("last_year_fund_out")
    open_banking_data["closingBalancePrevious"] = open_banking_data.pop("last_year_closing_balance")
    open_banking_data["openingBalanceCurrentThousandth"] = open_banking_data.pop("opening_balance_thousand")
    open_banking_data["receiptsCurrentThousandth"] = open_banking_data.pop("funds_in_thousand")
    open_banking_data["paymentsCurrentThousandth"] = open_banking_data.pop("funds_out_thousand")
    open_banking_data["closingBalanceCurrentThousandth"] = open_banking_data.pop("closing_balance_thousand")
    open_banking_data["openingBalancePreviousThousandth"] = open_banking_data.pop("last_year_opening_balance_thousand")
    open_banking_data["receiptsPreviousThousandth"] = open_banking_data.pop("last_year_fund_in_thousand")
    open_banking_data["paymentsPreviousThousandth"] = open_banking_data.pop("last_year_fund_out_thousand")
    open_banking_data["closingBalancePreviousThousandth"] = open_banking_data.pop("last_year_closing_balance_thousand")
    open_banking_data["openingBalanceComparison"] = open_banking_data.pop("opening_balance_comparison")
    open_banking_data["receiptsComparison"] = open_banking_data.pop("receipts_comparison")
    open_banking_data["paymentsComparison"] = open_banking_data.pop("payments_comparison")
    open_banking_data["closingBalanceComparison"] = open_banking_data.pop("closing_balance_comparison")
    open_banking_data["clientName"] = open_banking_data.pop("client_name")

    return open_banking_data
