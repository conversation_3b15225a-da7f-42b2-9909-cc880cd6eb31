def get_currency_symbol(currency):
    currency_symbols = {
        "GBP": "£",  # Pound Sterling
        "USD": "$",  # US Dollar
        "EUR": "€",  # Euro
        "NOK": "kr",  # Norwegian Krone
        "DKK": "kr",  # Danish Krone
        "SEK": "kr",  # Swedish Krona
    }

    return currency_symbols.get(currency, "")


def amount_decimal_conversion(value):
    result = value / 100
    return result
