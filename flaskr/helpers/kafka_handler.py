from confluent_kafka import Producer
import os

import json


kafka_config = {
    "bootstrap_servers": os.environ.get("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
}
broker_address = kafka_config["bootstrap_servers"]
num_partitions = 3
replication_factor = 1


def publish_to_kafka(kafka_topic, message):
    # Ensure the message is a bytes object
    if isinstance(message, dict):
        message = json.dumps(message).encode("utf-8")  # Serialize dict to JSON and encode to bytes
    elif isinstance(message, str):
        message = message.encode("utf-8")  # Encode string to bytes

    config = {"bootstrap.servers": broker_address, "security.protocol": "SSL"}
    producer = Producer(config)

    producer.produce(
        kafka_topic,
        message,
        callback=lambda err, msg: print(
            f"Failed to deliver: {err}" if err else f"Delivered: {msg.value().decode('utf-8')}"
        ),
    )
    producer.poll(0)
    producer.flush()
