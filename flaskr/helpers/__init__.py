from datetime import datetime
from flaskr.models import get_db
import math


def track_opening_closing_balance_changes(transaction: dict, amount_difference: float, file_date: str, session) -> None:
    datetime_today_min = datetime.combine(datetime.utcnow(), datetime.min.time())
    if transaction["created_at"] < datetime_today_min:
        get_db().opening_closing_balance_changes.update_one(
            {
                "client_id": transaction["client_id"],
                "currency": transaction["currency_code"],
                "file_date": file_date,
                "date": datetime_today_min,
            },
            {
                "$setOnInsert": {
                    "client_id": transaction["client_id"],
                    "currency": transaction["currency_code"],
                    "file_date": file_date,
                    "date": datetime_today_min,
                },
                "$inc": {"amount": amount_difference},
            },
            upsert=True,
            session=session,
        )


def get_client_and_trust() -> dict:
    clients = get_db().client_basic_info.aggregate(
        [
            {
                "$lookup": {
                    "from": "lookup_trust_type",
                    "localField": "type_of_trust_account",
                    "foreignField": "_id",
                    "as": "trust",
                }
            },
            {"$unwind": "$trust"},
            {"$project": {"_id": 0, "client_id": "$_id", "trust_type": "$trust.name"}},
        ]
    )
    trust_type_mapper = {}
    for client in clients:
        trust_type_mapper[client["client_id"]] = client["trust_type"]
    return trust_type_mapper


def round(n, decimals=0):
    multiplier = 10 ** decimals
    return math.floor(n * multiplier + 0.5) / multiplier
