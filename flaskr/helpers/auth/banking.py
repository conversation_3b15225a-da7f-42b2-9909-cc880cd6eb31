from functools import wraps

from bson import ObjectId
from flask import request
from jose import jwt
from flaskr.helpers.auth import get_token
from flaskr.models import get_db
from flaskr.services.exceptions import AccessDeniedException


def banking_search_access():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            claims = jwt.get_unverified_claims(access_token)
            if next(filter(lambda x: x in ["ptt-admin", "ptt-user"], claims.get("cognito:groups", [])), None):
                return fn(*args, **kwargs)
            else:

                response = fn(*args, **kwargs)
                not_allowed_fields = ["assignedTo", "notes"]
                for item in response["content"]:
                    for key in list(item.keys()):
                        if key in not_allowed_fields:
                            del item[key]
                return response

        decorator.__wrapped__ = fn
        return decorator

    return wrapper


def banking_details_access():
    def wrapper(fn):
        @wraps(fn)
        def decorator(obj, banking_id, *args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            claims = jwt.get_unverified_claims(access_token)
            if next(filter(lambda x: x in ["ptt-admin", "ptt-user"], claims.get("cognito:groups", [])), None):
                return fn(obj, banking_id, *args, **kwargs)
            else:
                user_id = claims["sub"]
                metadata = get_db().banking_metadata.find_one_or_404({"_id": ObjectId(banking_id)})
                client_id = metadata["client_id"]
                if get_db().user.find_one({"user_id": user_id, "clients": client_id}):
                    response = fn(obj, banking_id, *args, **kwargs)
                    not_allowed_fields = ["notes"]
                    filtered_response = {k: v for k, v in response.items() if k not in not_allowed_fields}
                    return filtered_response
            raise AccessDeniedException(f"Banking Details access Denied for user with username {claims['username']}")

        decorator.__wrapped__ = fn
        return decorator

    return wrapper
