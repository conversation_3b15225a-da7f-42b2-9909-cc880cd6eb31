from functools import wraps
from flask import request
from jose import jwt
from flaskr.helpers.auth import get_token
from flaskr.models import get_db
from flaskr.services.exceptions import AccessDeniedException
from bson import ObjectId


def client_details_access():
    def wrapper(fn):
        @wraps(fn)
        def decorator(obj, client_id, *args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            claims = jwt.get_unverified_claims(access_token)
            if next(filter(lambda x: x in ["ptt-admin", "ptt-user"], claims.get("cognito:groups", [])), None):
                return fn(obj, client_id, *args, **kwargs)
            else:
                user_id = claims["sub"]
                if get_db().user.find_one({"user_id": user_id, "clients": ObjectId(client_id)}):
                    response = fn(obj, client_id, *args, **kwargs)
                    allowed_fields = [
                        "clientId",
                        "fullName",
                        "friendlyName",
                        "goLiveDate",
                        "typeOfTrustAccount",
                        "reuseOldBooking",
                        "address",
                        "trustAccountName",
                    ]
                    filtered_response = {k: v for k, v in response.items() if k in allowed_fields}
                    return filtered_response

            raise AccessDeniedException(f"Client Details access Denied for user with username {claims['username']}")

        decorator.__wrapped__ = fn
        return decorator

    return wrapper
