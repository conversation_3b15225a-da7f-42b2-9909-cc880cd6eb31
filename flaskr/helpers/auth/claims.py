from functools import wraps
from flask import request
from jose import jwt
from bson import ObjectId
from flaskr.helpers.auth import get_token
from flaskr.models import get_db
from flaskr.services.exceptions import AccessDeniedException


def claim_search_latest_access():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            claims = jwt.get_unverified_claims(access_token)
            if next(filter(lambda x: x in ["ptt-admin", "ptt-user"], claims.get("cognito:groups", [])), None):
                return fn(*args, **kwargs)
            else:
                response = fn(*args, **kwargs)
                not_allowed_fields = [
                    "assignedTo",
                    "notes",
                    "checks",
                    "checkedAmount",
                    "percentageCheck",
                    "percentageTotal",
                ]
                for item in response["content"]:
                    for k in list(item.keys()):
                        if k in not_allowed_fields:
                            del item[k]
                if not response["content"]:
                    return response
                for item in response["content"][0]["items"]:
                    for k in list(item.keys()):
                        if k in not_allowed_fields:
                            del item[k]
                return response

        decorator.__wrapped__ = fn
        return decorator

    return wrapper


def claim_search_summary_access():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            claims = jwt.get_unverified_claims(access_token)
            if next(filter(lambda x: x in ["ptt-admin", "ptt-user"], claims.get("cognito:groups", [])), None):
                return fn(*args, **kwargs)
            else:
                response = fn(*args, **kwargs)
                not_allowed_fields = ["notes"]
                for item in response["content"]:
                    for k in list(item.keys()):
                        if k in not_allowed_fields:
                            del item[k]
                return response

        decorator.__wrapped__ = fn
        return decorator

    return wrapper


def claim_details_access():
    def wrapper(fn):
        @wraps(fn)
        def decorator(obj, claims_id, *args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            claims = jwt.get_unverified_claims(access_token)
            if next(filter(lambda x: x in ["ptt-admin", "ptt-user"], claims.get("cognito:groups", [])), None):
                return fn(obj, claims_id, *args, **kwargs)
            else:
                user_id = claims["sub"]
                metadata = get_db().claims_metadata.find_one_or_404({"_id": ObjectId(claims_id)})
                client_id = metadata["client_id"]
                if get_db().user.find_one({"user_id": user_id, "clients": client_id}):
                    response = fn(obj, claims_id, *args, **kwargs)
                    not_allowed_fields = ["notes"]
                    filtered_response = {k: v for k, v in response.items() if k not in not_allowed_fields}
                    return filtered_response
            raise AccessDeniedException(f"Claim Details access Denied for user with username {claims['username']}")

        decorator.__wrapped__ = fn
        return decorator

    return wrapper


def claim_transaction_access():
    def wrapper(fn):
        @wraps(fn)
        def decorator(obj, claims_id, *args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            claims = jwt.get_unverified_claims(access_token)
            if next(filter(lambda x: x in ["ptt-admin", "ptt-user"], claims.get("cognito:groups", [])), None):
                return fn(obj, claims_id, *args, **kwargs)
            else:
                user_id = claims["sub"]
                metadata = get_db().claims_metadata.find_one_or_404({"_id": ObjectId(claims_id)})
                client_id = metadata["client_id"]
                if get_db().user.find_one({"user_id": user_id, "clients": client_id}):
                    response = fn(obj, claims_id, *args, **kwargs)
                    return response
            raise AccessDeniedException(f"Claim Details access Denied for user with username {claims['username']}")

        decorator.__wrapped__ = fn
        return decorator

    return wrapper
