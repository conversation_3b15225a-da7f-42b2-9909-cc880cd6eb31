import json
import re
import time
import urllib.request
from functools import wraps

from bson import ObjectId
from flask import request, current_app, jsonify, g, abort
from jose import jwk, jwt
from jose.utils import base64url_decode

import config
from flaskr.services.exceptions import AccessDeniedException

PREFIX = "Bearer"
public_keys_url = "https://cognito-idp.{}.amazonaws.com/{}/.well-known/jwks.json".format(
    config.Config.AWS_REGION, config.Config.USER_POOL_ID
)

from flaskr.models import get_db


def _download_public_keys():
    with urllib.request.urlopen(public_keys_url) as f:
        response = f.read()
    g.public_keys = json.loads(response.decode("utf-8"))["keys"]


def get_token(header):
    bearer, _, token = header.partition(" ")
    if bearer != PREFIX:
        raise ValueError("Invalid token")

    return token


def _verify_jwt(access_token):
    jwt_headers = jwt.get_unverified_header(access_token)
    kid = jwt_headers["kid"]
    # search for the kid in the downloaded public keys
    if g.get("public_keys") is None:
        _download_public_keys()
    key_index = -1
    for i in range(len(g.public_keys)):
        if kid == g.public_keys[i]["kid"]:
            key_index = i
            break
    if key_index == -1:
        current_app.logger.error("Public key not found in jwks.json")
        return False
    public_key = jwk.construct(g.public_keys[key_index])

    # get the last two sections of the token,
    # message and signature (encoded in base64)
    message, encoded_signature = str(access_token).rsplit(".", 1)
    # decode the signature
    decoded_signature = base64url_decode(encoded_signature.encode("utf-8"))
    # verify the signature
    if not public_key.verify(message.encode("utf8"), decoded_signature):
        current_app.logger.error("Signature verification failed")
        return False
    current_app.logger.info("Signature successfully verified")
    return True


def _verify_claims(access_token, groups_to_check=None):
    # use the unverified claims
    claims = jwt.get_unverified_claims(access_token)
    # additionally we can verify the token expiration
    if time.time() > claims["exp"]:
        current_app.logger.error("Token is expired")
        return False
    if claims["client_id"] != current_app.config["APP_CLIENT_ID"]:
        current_app.logger.error("Token was not issued for this client")
        return False
    if groups_to_check is not None:
        group_found = False
        for group_in_claims in claims.get("cognito:groups", []):
            if group_in_claims in groups_to_check:
                group_found = True
                break
        if not group_found:
            return False
    return True


def user_required():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            claims = jwt.get_unverified_claims(access_token)
            if (
                _verify_jwt(access_token)
                and "ptt-sftp-user" not in claims.get("cognito:groups", [])
                and _verify_claims(access_token)
            ):
                return fn(access_token, *args, **kwargs)
            else:
                return jsonify(msg="Users except sftp users!"), 403

        return decorator

    return wrapper


def ptt_sftp_user_required():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            if _verify_jwt(access_token) and _verify_claims(access_token):
                return fn(access_token, *args, **kwargs)
            else:
                return jsonify(msg="Users only!"), 403

        return decorator

    return wrapper


def ptt_user_required():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            if _verify_jwt(access_token) and _verify_claims(access_token, ["ptt-admin", "ptt-user"]):
                return fn(access_token, *args, **kwargs)
            else:
                return jsonify(msg="Ptt users only!"), 403

        return decorator

    return wrapper


def admin_required():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            if _verify_jwt(access_token) and _verify_claims(access_token, ["ptt-admin"]):
                return fn(access_token, *args, **kwargs)
            else:
                return jsonify(msg="Admins only!"), 403

        return decorator

    return wrapper



def auth_error_handler(e):
    uppercase = re.findall("Password must have uppercase characters", str(e))
    lowercase = re.findall("Password must have lowercase characters", str(e))
    numeric = re.findall("Password must have numeric characters", str(e))
    special_char = re.findall("Password must have symbol characters", str(e))

    if uppercase or lowercase or numeric or special_char:
        return (
            jsonify(
                {"message": "Password must have atleast an uppercase, lowercase, numeric, and a special character"}
            ),
            400,
        )
    length = re.findall("Password did not conform with policy: Password not long enough", str(e))
    length_forgot = re.findall("Password does not conform to policy: Password not long enough", str(e))
    if length or length_forgot:
        return jsonify({"message": "Password must contain atleast 8 characters"}), 400
    invalid_user = re.findall("Username/client id combination not found.", str(e)) or re.findall(
        "Value at 'username' failed to satisfy constraint", str(e)
    )
    if invalid_user:
        return jsonify({"message": "User not found"}), 400
    invalid_cred = re.findall("Incorrect username or password", str(e))
    if invalid_cred:
        return jsonify({"message": "Invalid credentials"}), 400
    else:
        return e.response["Error"]["Message"], 500


def client_access(user_id, client_id):
    user = get_db().user.find_one({"user_id": user_id}, projection={"_id": 0, "clients": 1})
    clients = user["clients"]
    if ObjectId(client_id) not in clients:
        abort(403)


def booking_access(request):
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            claims = jwt.get_unverified_claims(access_token)
            client_id = request.json.get("clientId")
            if next(filter(lambda x: x in ["ptt-admin", "ptt-user"], claims.get("cognito:groups", [])), None):
                return fn(access_token, *args, **kwargs)
            else:
                user_id = claims["sub"]
                if get_db().user.find_one(
                    {
                        "user_id": user_id,
                        "clients": ObjectId(client_id),
                        "$or": [{"booking": True}, {"transaction": True}],
                    }
                ):
                    response = fn(access_token, *args, **kwargs)
                    return response

            raise AccessDeniedException(f"Banking Details access Denied for user with username {claims['username']}")

        decorator.__wrapped__ = fn
        return decorator

    return wrapper


def reports_access(request, report):
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            client_id = request.args.get("client") or request.json.get("clientId")
            access_token = get_token(request.headers.get("Authorization"))
            claims = jwt.get_unverified_claims(access_token)
            if next(filter(lambda x: x in ["ptt-admin", "ptt-user"], claims.get("cognito:groups", [])), None):
                return fn(access_token, *args, **kwargs)
            else:
                user_id = claims["sub"]
                if get_db().user.find_one({"user_id": user_id, "clients": ObjectId(client_id), "reports": report}):
                    response = fn(access_token, *args, **kwargs)
                    return response

            raise AccessDeniedException(f"Client Details access Denied for user with username {claims['username']}")

        decorator.__wrapped__ = fn
        return decorator

    return wrapper


def ptt_management_required():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            if _verify_jwt(access_token) and _verify_claims(access_token, ["ptt-management"]):
                return fn(access_token, *args, **kwargs)
            else:
                return jsonify(msg="Ptt Management only!"), 403

        return decorator

    return wrapper

def ptt_powerbi_admin_required():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            if _verify_jwt(access_token) and _verify_claims(access_token, ["ptt-powerbi-admin"]):
                return fn(access_token, *args, **kwargs)
            else:
                return jsonify(msg="PTT PowerBi Admins Only!"), 403

        return decorator

    return wrapper

def ptt_erv_dashboard_required():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            if _verify_jwt(access_token) and _verify_claims(access_token, ["ptt-erv-dashboard"]):
                return fn(access_token, *args, **kwargs)
            else:
                return jsonify(msg="Ptt ERV dashboard only!"), 403

        return decorator

    return wrapper

def admin_or_ptt_erv_dashboard_required():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            
            if _verify_jwt(access_token) and (
                _verify_claims(access_token, ["ptt-erv-dashboard"]) or _verify_claims(access_token, ["ptt-admin"])
            ):
                return fn(access_token, *args, **kwargs)
            else:
                return jsonify(msg="Access restricted: You must be either a PTT ERV dashboard user or an admin."), 403

        return decorator

    return wrapper

def ptt_czech_client_required():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            if _verify_jwt(access_token) and _verify_claims(access_token, ["ptt-czech-client"]):
                return fn(access_token, *args, **kwargs)
            else:
                return jsonify(msg="Ptt czech client only!"), 403

        return decorator

    return wrapper

def admin_or_ptt_erv_dashboard_or_ptt_czech_client_required():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))
            
            if _verify_jwt(access_token) and (
                _verify_claims(access_token, ["ptt-erv-dashboard"]) or _verify_claims(access_token, ["ptt-admin"]) or _verify_claims(access_token, ["ptt-czech-client"])
            ):
                return fn(access_token, *args, **kwargs)
            else:
                return jsonify(msg="Unauthorized access!"), 403

        return decorator

    return wrapper

def user_or_ptt_czech_client_required():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            access_token = get_token(request.headers.get("Authorization"))

            user_access = _verify_jwt(access_token) and "ptt-sftp-user" not in jwt.get_unverified_claims(access_token).get("cognito:groups", []) and _verify_claims(access_token)
            ptt_czech_client_access = _verify_jwt(access_token) and _verify_claims(access_token, ["ptt-czech-client"])

            if user_access or ptt_czech_client_access:
                return fn(access_token, *args, **kwargs)
            else:
                return jsonify(msg="Unauthorized access!"), 403

        return decorator

    return wrapper



