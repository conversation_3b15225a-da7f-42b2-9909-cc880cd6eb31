from flask import current_app, jsonify
from flaskr.models import get_db
from flaskr.models.lookup.anomaly import LookupAnomalySchema
from flaskr.models.lookup.claim_elements import LookupClaimElementsSchema
from flaskr.models.lookup.currency import LookupCurrencySchema
from flaskr.models.lookup.frequency import LookupFrequencySchema
from flaskr.models.lookup.trust_type import LookupTrustTypeSchema
from flaskr.models.lookup.claim import LookupClaimSchema
from flaskr.models.lookup.banking import LookupBankingSchema
from flaskr.models.lookup.default_checks import LookupDefaultChecksSchema
from bson import ObjectId
from flaskr.models.user import UserSchema
from flaskr.services.auth_service import auth_service


class LookupService:
    def get_trust_types(self):
        trust_types = list(get_db().lookup_trust_type.find())
        trust_type_list = [LookupTrustTypeSchema().dump(trust_type) for trust_type in trust_types]
        return trust_type_list

    def get_anomalies(self, client_id):
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        count = client_basic_info.get("max_cap_rollover_anomaly_count")
        anomalies = list(get_db().lookup_anomaly.find({"name": {"$ne": "Max Cap Claim Anomaly (Rollover)"}}))
        count = count if count else 1
        max_cap_rollover_anomalies = list(
            get_db().lookup_anomaly.find({"name": "Max Cap Claim Anomaly (Rollover)"}, sort=[("_id", 1)], limit=count)
        )
        anomalies = anomalies + max_cap_rollover_anomalies
        anomalies_list = [LookupAnomalySchema().dump(anomaly) for anomaly in anomalies]
        return anomalies_list

    def get_frequencies(self, clientId):
        client_match_condition = [{"$match": {"$and": []}}] if clientId else []
        if clientId:
            client_match_condition[0]["$match"]["$and"].append({"client_id": ObjectId(clientId)})
            frequencies = list(
                get_db().client_claim_frequency.aggregate(
                    [
                        *client_match_condition,
                        {
                            "$lookup": {
                                "from": "lookup_frequency",
                                "localField": "frequency_id",
                                "foreignField": "_id",
                                "as": "frequency",
                            }
                        },
                        {"$unwind": "$frequency"},
                        {"$project": {"_id": "$frequency_id", "name": "$frequency.name"}},
                    ]
                )
            )
            frequencies_list = [LookupFrequencySchema().dump(frequency) for frequency in frequencies]
            return frequencies_list

        else:
            frequencies = list(get_db().lookup_frequency.find())
            frequencies_list = [LookupFrequencySchema().dump(frequency) for frequency in frequencies]
            return frequencies_list

    def get_claim_columns(self):
        claims = list(get_db().lookup_claim.find({"preferred": {"$exists": True}}))
        claim_list = [LookupClaimSchema().dump(claim) for claim in claims]
        return claim_list

    def get_banking_columns(self):
        banking = list(get_db().lookup_banking.find({"preferred": {"$exists": True}}))
        banking_list = [LookupBankingSchema().dump(item) for item in banking]
        return banking_list

    def get_default_checks(self):
        checks = list(get_db().lookup_default_checks.find())
        checks_list = [LookupDefaultChecksSchema().dump(check) for check in checks]
        return checks_list

    def get_currency_list(self, clientId):
        if clientId:
            data = get_db().client_bank_acct_details.aggregate(
                [
                    {
                        "$match": {
                            "client_id": ObjectId(clientId),
                        }
                    },
                    {
                        "$group": {
                            "_id": "$currency",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "lookup_currency",
                            "localField": "_id",
                            "foreignField": "code",
                            "as": "currency",
                        }
                    },
                    {"$unwind": "$currency"},
                    {"$sort": {"currency.order": 1}},
                    {
                        "$project": {
                            "_id": "$currency._id",
                            "code": "$currency.code",
                            "name": "$currency.name",
                            "symbol": "$currency.symbol",
                            "order": "$currency.order",
                        }
                    },
                ]
            )

            currencies_list = [LookupCurrencySchema().dump(currency) for currency in data]
            return currencies_list

        else:
            currencies = get_db().lookup_currency.find().sort("order")
            currencies_list = [LookupCurrencySchema().dump(currency) for currency in currencies]
            return currencies_list

    def get_bank_list(self, clientId):
        if clientId:
            data = get_db().client_bank_acct_details.aggregate(
                [
                    {
                        "$match": {
                            "client_id": ObjectId(clientId),
                        }
                    },
                    {
                        "$group": {
                            "_id": "$bank_name",
                        }
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "bank_name": "$_id",
                        }
                    },
                    {"$sort": {"bank_name": 1}},
                ]
            )

            banks_list = [bank for bank in data]
            return banks_list

        else:
            banks = get_db().client_bank_acct_details.distinct("bank_name")
            banks_list = [{"bank_name": bank} for bank in sorted(banks)]
            return banks_list

    def export_column_mapping(self, column_name):
        if column_name == "banking-column":
            columns = list(
                get_db()
                .lookup_banking.find({}, projection={"name": 1, "column_name": 1, "data_type": 1, "_id": 0})
                .sort("column_name", 1)
            )
        elif column_name == "claim-column":
            columns = list(
                get_db()
                .lookup_claim.find({}, projection={"name": 1, "column_name": 1, "data_type": 1, "_id": 0})
                .sort("column_name", 1)
            )
        return columns

    def get_claim_elements(self):
        claim_elements = list(get_db().lookup_claim_elements.find())
        claim_elements_list = [LookupClaimElementsSchema().dump(claim_element) for claim_element in claim_elements]
        return claim_elements_list

    def get_bank_accounts(self, client_id: str, currency: str or None, bank_name: str or None):
        query = {"client_id": ObjectId(client_id), "exclude_from_report": {"$ne": True}}
        if client_id == current_app.config.get("CALEDONIAN"):
            query = {"client_id": ObjectId(client_id), "exclude_from_report": {"$ne": True}, "account_type": "Trust"}
        if currency:
            query["currency"] = currency
        if bank_name:
            query["bank_name"] = bank_name
        bank_acct_details = get_db().client_bank_acct_details.aggregate(
            [
                {"$match": query},
                {
                    "$lookup": {
                        "from": "lookup_banks",
                        "localField": "bank_name",
                        "foreignField": "name",
                        "as": "bank",
                    }
                },
                {"$unwind": "$bank"},
                {
                    "$project": {
                        "bank_id": "$bank.bank_id",
                        "bank_name": "$bank.name",
                        "account_no": 1,
                        "account_type": 1,
                        "_id": 0,
                    }
                },
            ]
        )
        return list(bank_acct_details)

    def list_all_users(access_token):
        try:
            # Get all users from the database
            users = get_db().user.find({}, projection={"_id": 0, "user_id": 1, "role": 1, "username": 1})
            user_list = [UserSchema().dump(user) for user in users]

            ptt_users = auth_service.ptt_users_list()
            clients = auth_service.get_list_clients()
            all_users = ptt_users + clients

            final_users_list = []
            for user in user_list:
                # Filtering out the ones not existing in cognito
                user_details = next(filter(lambda x: x.get("userId") == user.get("userId"), all_users), None)
                if user_details:
                    final_users_list.append(
                        {
                            "userId": user.get("userId"),
                            "name": user_details.get("name"),
                            "email": user_details.get("email"),
                            "username": user_details.get("username"),
                            "role": user.get("role"),
                        }
                    )
            return jsonify(final_users_list)
        except Exception as e:
            error_msg = f"Error listing users: {str(e)}"
            current_app.logger.error(error_msg)
            return jsonify({"error": error_msg}), 500


lookup_service = LookupService()
