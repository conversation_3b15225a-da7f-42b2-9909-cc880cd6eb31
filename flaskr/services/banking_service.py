import json
import re
from math import ceil
import os
from flaskr.helpers.auth.banking import banking_search_access, banking_details_access
from flaskr.services.auth_service import auth_service
from flaskr.services.exceptions import ServiceException
from flask import abort
import uuid
from pymongo import ReturnDocument
from datetime import datetime
from dateutil.relativedelta import relativedelta
from bson import ObjectId
from flask import current_app
from flaskr.helpers.boto3_handler import (
    delete_object,
    validate_mime_type_xls_csv,
    call_lambda,
    generate_presigned_url,
    head_object,
)
from flaskr.helpers.kafka_handler import publish_to_kafka
from flaskr.models import get_db
from bson.errors import InvalidId
from flaskr.models.banking.banking_file_details import BankingFileDetailsSchema
from flaskr.models.banking.metadata import BankingMetadataSchema
from flaskr.helpers import track_opening_closing_balance_changes, round
from config import Config


class BankingService:
    def __handle_banking_anomaly_recalculation(self, updated_transaction, existing_transaction, session):
        if any(
            [
                updated_transaction.get("booking_date") != existing_transaction.get("booking_date"),
                updated_transaction.get("departure_date") != existing_transaction.get("departure_date"),
                updated_transaction.get("return_date") != existing_transaction.get("return_date"),
                updated_transaction.get("amount") != existing_transaction.get("amount"),
            ]
        ):
            get_db().anomaly_banking.update_many(
                {"transaction_id": existing_transaction["_id"]},
                {"$set": {"deleted": True, "updated_at": datetime.utcnow()}},
                session=session,
            )
            current_app.logger.info(
                f"Initiating recalculation of anomalies for file_id {existing_transaction['file_id']} and client_id {existing_transaction['client_id']}"
            )
            call_lambda(
                f"banking-anomaly-detector-lambda-{os.environ['ENVIRONMENT']}",
                json.dumps(
                    {
                        "fileId": existing_transaction["file_id"],
                        "clientId": str(existing_transaction["client_id"]),
                        "transactions": [str(existing_transaction["_id"])],
                    }
                ),
            )

    def __handle_banking_transaction_amount_and_status_change(
        self, existing_transaction, updated_transaction, file_date, file_id, session
    ):
        amount_difference = 0
        count_difference = 0
        if existing_transaction.get("status") != "Cancelled" and updated_transaction.get("status") == "Cancelled":
            get_db().banking_file_details.update_one(
                {"_id": updated_transaction["_id"], "deleted": False},
                {"$set": {"deleted": True}},
                session=session,
            )

            amount_difference = -1 * existing_transaction["amount"]
            count_difference = -1

        elif existing_transaction.get("status") == "Cancelled" and updated_transaction.get("status") != "Cancelled":
            get_db().banking_file_details.update_one(
                {"_id": updated_transaction["_id"], "deleted": True},
                {"$set": {"deleted": False}},
                session=session,
            )

            amount_difference = updated_transaction["amount"]
            count_difference = 1

        else:
            amount_difference = updated_transaction["amount"] - existing_transaction["amount"]
        inc_items = {
            f'banking_files.$.deposit.{updated_transaction["currency_code"]}': amount_difference,
            f'banking_files.$.item_count.{updated_transaction["currency_code"]}': count_difference,
        }
        get_db().banking_metadata.update_one(
            {"_id": updated_transaction["banking_id"], "banking_files": {"$elemMatch": {"file_id": file_id}}},
            {
                "$inc": inc_items,
                "$set": {"updated_at": datetime.utcnow()},
            },
            session=session,
        )
        
        collection_name = "trust_fund" if updated_transaction["client_id"] == os.environ.get("NAS") else "trust_fund_v2"
        get_db()[collection_name].update_one(
            {"client_id": updated_transaction["client_id"], "booking_ref": updated_transaction["booking_ref"]},
            {"$inc": {"balance": round(amount_difference, 2), "total_in_trust": amount_difference}},
            collation={"locale": "en", "strength": 1},
            session=session,
        )
        get_db().anomaly_banking.update_many(
            {"existing_transaction_id": existing_transaction["_id"]},
            {"$set": {"deleted": True, "updated_at": datetime.utcnow()}},
            session=session,
        )
        track_opening_closing_balance_changes(updated_transaction, amount_difference, file_date, session)

    def banking_create_presigned_url(self, client_id, file_name, user_id):
        file_id = None
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})

        if not client_basic_info:
            raise ServiceException("client not found")
        if client_basic_info.get("is_disabled") is True:
            abort(403)
        company_alias = [item.strip() for item in client_basic_info["company_alias"].split(",")]

        now = datetime.utcnow()
        try:
            file_datetime = datetime.strptime(file_name.split("-")[0], "%Y%m%d")
        except ValueError:
            raise ServiceException("Invalid file name, failed to extract file date.")
        if client_id == os.environ.get("BLUESTYLE"):
            pattern = f"\\d{{8}}[-]\\d{{6}}[-]({'|'.join(company_alias)})[-]Banking"
        else:
            pattern = f"\\d{{8}}[-]({'|'.join(company_alias)})[-]*Banking"
        if re.match(pattern, file_name, re.IGNORECASE) is None:
            raise ServiceException("Invalid file name")
        last_valid_file_date = now - relativedelta(months=current_app.config["FILE_DATE_AGE_IN_MONTHS"])
        if file_datetime < last_valid_file_date:
            raise ServiceException(
                f"File date cannot be older than {current_app.config['FILE_DATE_AGE_IN_MONTHS']} month(s)."
            )
        existing_metadata = get_db().banking_metadata.find_one(
            {"client_id": ObjectId(client_id), "banking_files": {"$elemMatch": {"file_name": file_name}}}
        )

        if not existing_metadata:
            file_id = f"{str(uuid.uuid4())}_{now.isoformat()}"
        else:
            if existing_metadata["status"] == "Scanning":
                raise ServiceException("Previous scan for the item is not completed.")
            if existing_metadata["status"] == "Updating Status":
                raise ServiceException("The status of the file is being updated. No action allowed now.")
            if existing_metadata["status"] == "Authorised":
                user = get_db().user.find_one({"user_id": user_id}, projection={"role": 1, "_id": 0})
                if user["role"] in ["ptt-user", "ptt-client"]:
                    raise ServiceException("No action is allowed since the file is authorised.")
            file_id = f"{existing_metadata['banking_files'][0]['file_id'].split('_')[0]}_{now.isoformat()}"

        presigned_url = generate_presigned_url("put_object", current_app.config["BANKING_FILE_BUCKET"], file_id, 300)
        return {"fileId": file_id, "presignedUrl": presigned_url}

    def banking_create(self, client_id, file_name, file_id, sftp, user_id, sftp_key):
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if not client_basic_info:
            raise ServiceException("client not found")
        if client_basic_info.get("is_disabled") is True:
            abort(403)
        company_alias = [item.strip() for item in client_basic_info["company_alias"].split(",")]
        now = datetime.utcnow()
        try:
            file_datetime = datetime.strptime(file_name.split("-")[0], "%Y%m%d")
        except ValueError:
            raise ServiceException("Invalid file name, failed to extract file date.")
        if client_id == os.environ.get("BLUESTYLE"):
            pattern = f"\\d{{8}}[-]\\d{{6}}[-]({'|'.join(company_alias)})[-]Banking"
        else:
            pattern = f"\\d{{8}}[-]({'|'.join(company_alias)})[-]*Banking"
        if re.match(pattern, file_name, re.IGNORECASE) is None:
            raise ServiceException("Invalid file name")
        file_date = file_datetime.strftime("%Y-%m-%d")
        last_valid_file_date = now - relativedelta(months=current_app.config["FILE_DATE_AGE_IN_MONTHS"])
        if file_datetime < last_valid_file_date:
            raise ServiceException(
                f"File date cannot be older than {current_app.config['FILE_DATE_AGE_IN_MONTHS']} month(s)."
            )

        current_app.logger.info("Retrieval of file metadata started")
        file_metadata = head_object(current_app.config["BANKING_FILE_BUCKET"], file_id)
        current_app.logger.info("Retrieval of file metadata completed")
        mime_type = file_metadata["ContentType"]
        current_app.logger.info("File content type validation started")
        if validate_mime_type_xls_csv(mime_type) is False:
            delete_object(current_app.config["BANKING_FILE_BUCKET"], file_id)
            raise ServiceException(message="File format not supported, use xls, xlsx or csv files")
        current_app.logger.info("File content type validation completed")

        current_app.logger.info("Banking metadata and anomaly banking updation started")
        existing_metadata = get_db().banking_metadata.find_one(
            {"client_id": ObjectId(client_id), "banking_files": {"$elemMatch": {"file_name": file_name}}}
        )

        if not existing_metadata:
            metadata = BankingMetadataSchema().load(
                {
                    "clientId": ObjectId(client_id),
                    "createdAt": now.isoformat(),
                    "updatedAt": now.isoformat(),
                    "bankingFiles": [
                        {
                            "fileId": file_id,
                            "fileName": file_name,
                            "fileDate": file_date,
                            "submittedDate": now.isoformat(),
                            "status": "Scanning",
                        }
                    ],
                    "status": "Scanning",
                }
            )
            get_db().banking_metadata.insert_one(metadata)
        else:
            if existing_metadata["status"] == "Scanning":
                raise ServiceException("Previous scan for the item is not completed.")
            if existing_metadata["status"] == "Updating Status":
                raise ServiceException("The status of the file is being updated. No action allowed now.")
            if existing_metadata["status"] == "Authorised":
                user = get_db().user.find_one({"user_id": user_id}, projection={"role": 1, "_id": 0})
                if user["role"] in ["ptt-user", "ptt-client"]:
                    raise ServiceException("No action is allowed since the file is authorised.")
            banking_files = existing_metadata["banking_files"]
            banking_files[-1]["status"] = "Cancelled by System"
            with get_db().client.start_session() as session:
                with session.start_transaction():
                    get_db().banking_metadata.update_one(
                        {
                            "client_id": ObjectId(client_id),
                            "banking_files": {"$elemMatch": {"file_name": file_name}},
                        },
                        {
                            "$set": {
                                "banking_files": [
                                    *banking_files,
                                    {
                                        "file_id": file_id,
                                        "file_name": file_name,
                                        "file_date": file_date,
                                        "submitted_date": now,
                                        "status": "Scanning",
                                    },
                                ],
                                "status": "Scanning",
                                "updated_at": now,
                            }
                        },
                        session=session,
                    )
                    get_db().anomaly_banking.update_many(
                        {"banking_id": existing_metadata["_id"]},
                        {"$set": {"deleted": True, "updated_at": now}},
                        session=session,
                    )
        current_app.logger.info("Banking metadata and anomaly banking updation completed")
        payload = {"clientId": client_id, "fileId": file_id, "fileType": mime_type, "sftp": sftp, "sftpKey": sftp_key}
        payload["replace"] = True if existing_metadata else False
        # if client_id in []:
        #     current_app.logger.info("Kafka message initiated")
        #     message = json.dumps(payload)
        #     kafka_topic = f"banking-file-processor-topic-{current_app.config['ENVIRONMENT']}"
        #     try:
        #         publish_to_kafka(kafka_topic, message)
        #         return f"Message sent {message}"
        #     except Exception as e:
        #         print(f"Error sending message: {e}")
        # else:
        current_app.logger.info("Lambda initiated")
        call_lambda(f"banking-trigger-lambda-{current_app.config['ENVIRONMENT']}", json.dumps(payload))

    @banking_search_access()
    def banking_search(self, user_id, data):
        query = data.get("query") or ""
        client = data.get("client")
        status = data.get("status") or ""
        from_date = data.get("fromDate") or None
        to_date = data.get("toDate") or None
        assigned_to = data.get("assignedTo")
        date = data.get("date") or None
        from_date = datetime.fromisoformat(from_date) if from_date else datetime.min
        to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time()) if to_date else datetime.max
        client_id = current_app.config.get("WLH_NEW")

        page = data["page"] or 1
        size = data["size"] or 10
        offset = (page - 1) * size
        client_match_condition = [{"client_id": ObjectId(client)}] if client else []
        match_condition = [
            *client_match_condition,
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        date if date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        date if date else datetime.max.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$banking_files.submitted_date", -1]},
                        from_date,
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$banking_files.submitted_date", -1]},
                        to_date,
                    ]
                }
            },
            {"status": {"$regex": f"^.*{status}.*$", "$options": "i"}},
        ]
        if assigned_to:
            match_condition.append({"assigned_to": assigned_to})

        data = list(
            get_db().banking_metadata.aggregate(
                [
                    {"$addFields": {"user_id": user_id}},
                    {"$match": {"$and": match_condition}},
                    {
                        "$lookup": {
                            "from": "user",
                            "localField": "user_id",
                            "foreignField": "user_id",
                            "let": {"client_id": "$client_id"},
                            "pipeline": [{"$match": {"$expr": {"$in": ["$$client_id", "$clients"]}}}],
                            "as": "user",
                        }
                    },
                    {"$unwind": "$user"},
                    {
                        "$lookup": {
                            "from": "client_basic_info",
                            "localField": "client_id",
                            "foreignField": "_id",
                            "as": "client",
                        }
                    },
                    {
                        "$match": {
                            "$or": [
                                {"client.full_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                {"client.friendly_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                            ]
                        }
                    },
                    {"$unwind": "$client"},
                    {
                        "$lookup": {
                            "from": "lookup_trust_type",
                            "localField": "client.type_of_trust_account",
                            "foreignField": "_id",
                            "as": "trust",
                        }
                    },
                    {"$unwind": {"path": "$trust", "preserveNullAndEmptyArrays": True}},
                    {
                        "$project": {
                            "clientId": "$client._id",
                            "cId": "$client.c_id",
                            "clientName": "$client.full_name",
                            "friendlyName": "$client.friendly_name",
                            "count": {"$slice": ["$banking_files.item_count", -1]},
                            "amount": {
                                "$cond": {
                                    "if": {"$eq": ["$client_id", {"$toObjectId": client_id}]},
                                    "then": {"$ifNull": [{"$slice": ["$banking_files.net_amount", -1]}, 0.0]},
                                    "else": {"$slice": ["$banking_files.deposit", -1]},
                                }
                            },
                            "fileDate": {"$slice": ["$banking_files.file_date", -1]},
                            "assignedTo": "$assigned_to",
                            "submittedDate": {"$slice": ["$banking_files.submitted_date", -1]},
                            "status": "$status",
                            "notes": "$notes",
                            "trust": "$trust.name",
                        }
                    },
                    {"$sort": {"submittedDate": -1}},
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ],
                allowDiskUse=True,
            )
        )
        content = []
        symbols = list(
            get_db().lookup_currency.find(
                {},
                projection={"_id": 0, "order": 0, "name": 0},
            )
        )
        for row in data[0]["data"]:
            banking_dict = {
                "clientId": str(row["clientId"]),
                "cId": row["cId"],
                "clientName": row["clientName"],
                "friendlyName": row["friendlyName"],
                "bankingId": str(row["_id"]),
                "fileDate": row["fileDate"][-1],
                "assignedTo": row.get("assignedTo"),
                "submittedDate": row["submittedDate"][-1].isoformat(),
                "status": row.get("status"),
                "notes": row["notes"] if row.get("notes") else "",
                "trustAccount": row.get("trust"),
            }
            items = []
            if row.get("amount"):
                for currency_code, amount in row["amount"][0].items():
                    item_dict = {}
                    currency_matches = list(filter(lambda x: x["code"] == currency_code, symbols))
                    if currency_matches:
                        currency = currency_matches[0]
                        amount = amount if amount else 0
                        item_dict.update(
                            {
                                "currency": currency_code,
                                "count": row["count"][0][currency_code],
                                "amount": amount,
                                "symbol": currency["symbol"],
                            }
                        )
                        items.append(item_dict)
            banking_dict["items"] = items
            content.append(banking_dict)
        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)
        empty = True if not content else False
        first = True if page == 1 else False
        last = True if page == ceil((total_elements) / size) else False

        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def get_payments(self, banking_id, query, page, size, sort_key, sort_order):
        banking_metadata = get_db().banking_metadata.find_one({"_id": ObjectId(banking_id)})
        page = int(page or 1)
        size = int(size or 10)
        offset = (page - 1) * size

        match_conditions = [
            {"booking_ref": {"$regex": f"^.*{query}.*$", "$options": "i"}},
            {"currency_code": {"$regex": f"^.*{query}.*$", "$options": "i"}},
            {"customer_type": {"$regex": f"^.*{query}.*$", "$options": "i"}},
            {"element": {"$regex": f"^.*{query}.*$", "$options": "i"}},
        ]

        pipeline = [
            {"$match": {"file_id": banking_metadata["banking_files"][-1]["file_id"], "$or": match_conditions}},
            {
                "$project": {
                    "booking_ref": 1,
                    "booking_date": 1,
                    "currency_code": 1,
                    "amount": 1,
                    "departure_date": 1,
                    "return_date": 1,
                    "payment_date": 1,
                    "customer_type": 1,
                    "status": 1,
                    "element": 1,
                }
            },
            {"$sort": {sort_key: sort_order}},
            {"$skip": offset},
            {"$limit": size},
        ]

        payments = list(get_db().banking_file_details.aggregate(pipeline))
        payment_list = [BankingFileDetailsSchema().dump(payment) for payment in payments]
        total_elements = get_db().banking_file_details.count_documents(
            {"file_id": banking_metadata["banking_files"][-1]["file_id"], "$or": match_conditions}
        )
        total_pages = ceil(total_elements / size)
        response = {
            "content": payment_list,
            "pageNumber": page,
            "numberOfElements": len(payment_list),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    @banking_details_access()
    def get_banking_details(self, banking_id):
        try:
            banking_details = get_db().banking_metadata.find_one({"_id": ObjectId(banking_id)})
        except InvalidId:
            raise ServiceException("invalid banking id")
        client_id = banking_details["client_id"]
        client = get_db().client_basic_info.find_one({"_id": client_id})
        return {
            "clientName": client["full_name"],
            "friendlyName": client["friendly_name"],
            **BankingMetadataSchema().dump(banking_details),
        }

    def update_banking_metadata(self, data, user_id):
        banking_details = BankingMetadataSchema().load(data)
        try:
            existing_metadata = get_db().banking_metadata.find_one({"_id": ObjectId(data["bankingId"])})
        except InvalidId:
            raise ServiceException("invalid bankingId")
        if not existing_metadata:
            raise ServiceException("banking file not found")

        previous_status = existing_metadata["status"]
        new_status = data.get("status")
        is_status_handle_required = False
        is_deleted = False
        if new_status:
            if previous_status in ("Scanning", "Updating Status"):
                raise ServiceException("No action is allowed while the file is under modification by system.")
            if previous_status == "Authorised":
                user = get_db().user.find_one({"user_id": user_id}, projection={"role": 1, "_id": 0})
                if user["role"] == "ptt-user":
                    raise ServiceException("No action is allowed since the file is authorised.")
            if previous_status != "Cancelled" and new_status == "Cancelled":
                is_status_handle_required = True
                banking_details["status"] = "Updating Status"
            if previous_status == "Cancelled" and new_status != "Cancelled":
                is_status_handle_required = True
                is_deleted = True
                banking_details["status"] = "Updating Status"
            banking_details["banking_files.$.status"] = banking_details["status"]

        file_id = existing_metadata["banking_files"][-1]["file_id"]
        file_date = existing_metadata["banking_files"][-1]["file_date"]
        with get_db().client.start_session() as session:
            with session.start_transaction():
                get_db().banking_metadata.update_one(
                    {
                        "_id": ObjectId(data["bankingId"]),
                        "banking_files.file_id": file_id,
                    },
                    {
                        "$set": {
                            **banking_details,
                            "updated_at": datetime.utcnow(),
                        }
                    },
                )
                if is_status_handle_required:
                    call_lambda(
                        f"banking-status-change-trigger-lambda-{os.environ['ENVIRONMENT']}",
                        json.dumps(
                            {
                                "bankingId": data["bankingId"],
                                "fileId": file_id,
                                "clientId": str(existing_metadata["client_id"]),
                                "previousStatus": previous_status,
                                "newStatus": new_status,
                                "createdAt": existing_metadata["created_at"].isoformat(),
                                "fileDate": file_date,
                                "isDeleted": is_deleted,
                            }
                        ),
                    )

    def get_banking_anomalies(self, banking_id, query, page, size, sort_key, sort_order):
        banking_metadata = get_db().banking_metadata.find_one({"_id": ObjectId(banking_id)})
        client_basic_info = get_db().client_basic_info.find_one({"_id": banking_metadata["client_id"]})

        page = int(page or 1)
        size = int(size or 10)
        offset = (page - 1) * size

        data = list(
            get_db().anomaly_banking.aggregate(
                [
                    {
                        "$match": {
                            "$and": [
                                {
                                    "deleted": False,
                                    "banking_id": ObjectId(banking_id),
                                    "$or": [
                                        {"booking_ref": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                        {"status": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                        {"anomaly_type": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                    ],
                                },
                            ]
                        }
                    },
                    {
                        "$group": {
                            "_id": {
                                "anomaly_type": "$anomaly_type",
                                "booking_ref": "$booking_ref",
                                "client_id": "$client_id",
                            },
                            "anomaly_ids": {"$push": {"$toString": "$_id"}},
                            "modified_by": {"$first": "$modified_by"},
                            "status": {"$addToSet": "$status"},
                            "count": {"$count": {}},
                            "created_at": {"$min": "$created_at"},
                            "updated_at": {"$min": "$updated_at"},
                        }
                    },
                    {
                        "$lookup": {
                            "from": "trust_fund_v2",
                            "localField": "_id.booking_ref",
                            "foreignField": "booking_ref",
                            "pipeline": [
                                {
                                    "$match": {"client_id": banking_metadata["client_id"]},
                                }
                            ],
                            "as": "trust",
                        },
                    },
                    {"$unwind": "$trust"},
                    {
                        "$lookup": {
                            "from": "lookup_currency",
                            "localField": "bank.currency_code",
                            "foreignField": "code",
                            "as": "currency",
                        },
                    },
                    {"$sort": {sort_key: sort_order}},
                    {
                        "$project": {
                            "_id": 0,
                            "status": "$status",
                            "bookingRef": "$_id.booking_ref",
                            "anomalyIds": "$anomaly_ids",
                            "anomalyType": "$_id.anomaly_type",
                            "count": "$count",
                            "leadPassenger": "$trust.lead_pax",
                            "bookingDate": "$trust.booking_date",
                            "dateOfTravel": "$trust.departure_date",
                            "dateOfReturn": "$trust.return_date",
                            "currency_code": "$trust.currency_code",
                            "balanceInTrust": "$trust.balance",
                            "symbol": "$currency.symbol",
                            "clientId": "$_id.client_id",
                            "modifiedBy": "$modified_by",
                        }
                    },
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ],
                collation={"locale": "en", "strength": 1},
                allowDiskUse=True,
            )
        )
        anomaly_count = get_db().anomaly_banking.aggregate(
            [
                {
                    "$match": {
                        "$and": [
                            {
                                "deleted": False,
                                "status": "Unresolved",
                                "banking_id": ObjectId(banking_id),
                                "$or": [
                                    {"booking_ref": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                    {"status": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                    {"anomaly_type": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                ],
                            },
                        ]
                    }
                },
                {
                    "$group": {
                        "_id": {
                            "anomaly_type": "$anomaly_type",
                            "booking_ref": "$booking_ref",
                            "client_id": "$client_id",
                        },
                        "status": {"$addToSet": "$status"},
                    }
                },
                {"$count": "total_unresolved"},
            ],
        )
        anomaly_count = next(anomaly_count, {"total_unresolved": 0})["total_unresolved"]
        ptt_users = auth_service.ptt_users_list()
        data_list = []
        for item in data[0]["data"]:
            item["balanceInTrust"] = item["balanceInTrust"] if item.get("balanceInTrust") else 0
            if item["status"] == ["Resolved"]:
                item["status"] = "Resolved"
            else:
                item["status"] = "Unresolved"

            user = next(filter(lambda x: x["userId"] == item.get("modifiedBy"), ptt_users), {})
            item["modifiedBy"] = user.get("name") or user.get("email")
            item["friendlyName"] = client_basic_info["friendly_name"]
            item["clientId"] = client_basic_info["c_id"]
            data_list.append(item)

        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)
        response = {
            "content": data_list,
            "pageNumber": page,
            "numberOfElements": len(data_list),
            "totalElements": total_elements,
            "totalPages": total_pages,
            "anomalyCount": anomaly_count,
        }
        return response

    def update_banking_transaction(self, transactionId, data, user_id):
        banking_update = BankingFileDetailsSchema().load(data)
        try:
            existing_transaction = get_db().banking_file_details.find_one({"_id": ObjectId(transactionId)})
        except InvalidId:
            raise ServiceException("Invalid transaction id")
        if not existing_transaction:
            raise ServiceException("Transaction not found")
        banking_metadata = get_db().banking_metadata.find_one({"_id": existing_transaction["banking_id"]})
        if banking_metadata["status"] in ("Scanning", "Updating Status"):
            raise ServiceException("No action is allowed while the file is under modification by system.")
        if banking_metadata["status"] == "Authorised":
            user = get_db().user.find_one({"user_id": user_id}, projection={"role": 1, "_id": 0})
            if user["role"] == "ptt-user":
                raise ServiceException("No action is allowed since the file is authorised.")
        trust_type = None
        basic_info = get_db().client_basic_info.find_one({"_id": existing_transaction["client_id"]})
        if basic_info.get("type_of_trust_account"):
            type_of_trust = get_db().lookup_trust_type.find_one({"_id": basic_info["type_of_trust_account"]})
            trust_type = type_of_trust["name"]
        if trust_type == "ATOL Escrow" or trust_type == "Escrow Trigger":
            if str(existing_transaction["client_id"]) in [
                current_app.config["MAJOR_TRAVEL"],
                current_app.config["WST_TRAVEL"],
                current_app.config["ANGLIA_TOURS"],
            ]:
                if (
                    banking_update.get("booking_date")
                    and existing_transaction.get("booking_date")
                    and (
                        (
                            datetime.strptime(existing_transaction.get("booking_date"), "%Y-%m-%d")
                            < datetime.strptime("2022-04-01", "%Y-%m-%d")
                            <= datetime.strptime(banking_update.get("booking_date"), "%Y-%m-%d")
                        )
                        or (
                            datetime.strptime(existing_transaction.get("booking_date"), "%Y-%m-%d")
                            >= (datetime.strptime("2022-04-01", "%Y-%m-%d"))
                            > datetime.strptime(banking_update.get("booking_date"), "%Y-%m-%d")
                        )
                    )
                ):
                    raise ServiceException("Transaction is not editable for the given booking date")
            if data.get("amount"):
                escrow_multiplier = (
                    existing_transaction["escrow_multiplier"]
                    if existing_transaction.get("escrow_multiplier")
                    else current_app.config["ESCROW_MULTIPLIER"]
                )
                banking_update["original_amount"] = round((data["amount"] / escrow_multiplier), 2)
        with get_db().client.start_session() as session:
            with session.start_transaction():
                now = datetime.utcnow()
                updated_transaction = get_db().banking_file_details.find_one_and_update(
                    {"_id": ObjectId(transactionId)},
                    {"$set": {**banking_update, "updated_at": now}},
                    return_document=ReturnDocument.AFTER,
                )
                file_date = banking_metadata["banking_files"][-1]["file_date"]
                file_id = banking_metadata["banking_files"][-1]["file_id"]

                if data.get("amount") or data.get("status"):
                    self.__handle_banking_transaction_amount_and_status_change(
                        existing_transaction,
                        updated_transaction,
                        file_date,
                        file_id,
                        session,
                    )
                self.__handle_banking_anomaly_recalculation(updated_transaction, existing_transaction, session)

    def banking_get_transaction(self, transaction_id, user_id):
        try:
            transaction = get_db().banking_file_details.find_one(
                {"_id": ObjectId(transaction_id)},
                projection={
                    "_id": 0,
                },
            )
            user = get_db().user.find_one({"user_id": user_id}, projection={"clients": 1, "_id": 0})
            if transaction.get("client_id") not in user["clients"]:
                abort(403)
        except InvalidId:
            raise ServiceException("invalid transactionId")
        banking_files_details = BankingFileDetailsSchema().dump(transaction)
        return banking_files_details

    def get_uploaded_banking_transaction(self, supplier_name, client_id):
        transaction = get_db().banking_file_details.find_one(
            {"deleted": False, "supplier_names": supplier_name, "client_id": ObjectId(client_id)},
            projection={
                "_id": 1,
            },
        )

        if not transaction:
            raise ServiceException("Transaction not found")
        return {"_id": str(transaction["_id"])}

    def get_trust_nontrust_bookings(self, client_id, non_trust_flag):
        blue_style = Config.BLUESTYLE
        if client_id != blue_style:
            message = {"msg": "Do not have permission"}
            return message

        response = list(
            get_db().banking_file_details.find({"client_id": ObjectId(client_id), "non_trust_flag": non_trust_flag})
        )

        for item in response:
            item["_id"] = str(item["_id"])
            for key, value in item.items():
                if isinstance(value, ObjectId):
                    item[key] = str(value)

        return response


banking_service = BankingService()
