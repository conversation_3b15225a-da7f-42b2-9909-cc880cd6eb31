from datetime import datetime
from math import ceil

from flaskr.models import get_db
from flaskr.services.exceptions import ServiceException
from bson.errors import InvalidId
from bson import ObjectId


class AnomalyService:
    def banking_anomaly_search(self, data):
        query = data.get("query") or ""
        client_id = data.get("clientId")
        from_date = data.get("fromDate")
        to_date = data.get("toDate")
        date_match_condition = [
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        to_date if to_date else datetime.max.strftime("%Y-%m-%d"),
                    ]
                }
            },
        ]
        page = int(data["page"] or 1)
        size = int(data["size"] or 10)
        offset = (page - 1) * size

        client_match_condition = [{"client_id": ObjectId(client_id)}] if client_id else []
        data = list(
            get_db().banking_metadata.aggregate(
                [
                    {"$match": {"$and": [*client_match_condition, *date_match_condition]}},
                    {
                        "$lookup": {
                            "from": "client_basic_info",
                            "localField": "client_id",
                            "foreignField": "_id",
                            "as": "client",
                        }
                    },
                    {
                        "$match": {
                            "$or": [
                                {"client.full_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                {"client.friendly_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                {"client.c_id": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                            ]
                        }
                    },
                    {"$unwind": "$client"},
                    {
                        "$lookup": {
                            "from": "anomaly_banking",
                            "localField": "_id",
                            "foreignField": "banking_id",
                            "pipeline": [{"$match": {"status": "Unresolved", "deleted": False}}, {"$count": "count"}],
                            "as": "anomalies",
                        }
                    },
                    {"$unwind": "$anomalies"},
                    {"$sort": {"created_at": -1}},
                    {
                        "$project": {
                            "clientId": "$client.c_id",
                            "fileDate": {"$slice": ["$banking_files.file_date", -1]},
                            "fileName": {"$slice": ["$banking_files.file_name", -1]},
                            "clientName": "$client.full_name",
                            "friendlyName": "$client.friendly_name",
                            "count": "$anomalies.count",
                            "modifiedBy": "$assigned_to",
                            "anomalies": "$anomalies._id",
                            "file_id": {"$slice": ["$banking_files.file_id", -1]},
                        }
                    },
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ]
            )
        )
        content = []
        for row in data[0]["data"]:
            banking_dict = {
                "_id": str(row["_id"]),
                "clientId": row["clientId"],
                "clientName": row["clientName"],
                "friendlyName": row["friendlyName"],
                "fileDate": row["fileDate"][0],
                "status": "Unresolved Anomalies",
                "count": row["count"],
                "fileName": row["fileName"][0],
            }
            content.append(banking_dict)

        empty = True if not content else False
        first = True if page == 1 else False
        last = True if data[0]["metadata"] and page == ceil((data[0]["metadata"][0]["total"]) / size) else False
        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)
        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }

        return response

    def anomaly_update_status(self, file_catagory, data, user_id):
        if file_catagory not in ("banking", "claim"):
            raise ServiceException("Invalid file catagory")
        if type(data["status"]) != str:
            raise ServiceException("status should be a valid string")
        try:
            now = datetime.utcnow()
            anomaly_ids = [ObjectId(anomaly_id) for anomaly_id in data["anomalyIds"]]
            if file_catagory == "banking":
                get_db().anomaly_banking.update_many(
                    {"_id": {"$in": anomaly_ids}},
                    {"$set": {"status": data["status"], "updated_at": now, "modified_by": user_id}},
                )

            else:
                get_db().anomaly_claims.update_many(
                    {"_id": {"$in": anomaly_ids}},
                    {"$set": {"status": data["status"], "updated_at": now, "modified_by": user_id}},
                )
        except InvalidId:
            raise ServiceException("invalid AnomalyId")

    def update_batch(self, batch_id, data, user_id, file_category):
        if type(data["resolved"]) != str:
            raise ServiceException("status should be a valid string")
        if file_category not in ("banking", "claim"):
            raise ServiceException("Invalid file catagory")
        if data["resolved"] == "True":
            try:
                now = datetime.utcnow()
                anomaly_type = data["anomaly_type"]
                client_id = data["client_id"]
                status = "Resolved"
                if file_category == "claim":
                    get_db().anomaly_claims.update_many(
                        {
                            "$and": [
                                {"anomaly_type": anomaly_type},
                                {"claims_id": ObjectId(batch_id)},
                                {"client_id": ObjectId(client_id)},
                                {"deleted": False},
                            ]
                        },
                        {"$set": {"status": status, "updated_at": now, "modified_by": user_id, "deleted": True}},
                    )
                else:
                    get_db().anomaly_banking.update_many(
                        {
                            "$and": [
                                {"anomaly_type": anomaly_type},
                                {"banking_id": ObjectId(batch_id)},
                                {"client_id": ObjectId(client_id)},
                                {"deleted": False},
                            ]
                        },
                        {"$set": {"status": status, "updated_at": now, "modified_by": user_id, "deleted": True}},
                    )
            except InvalidId:
                raise ServiceException("invalid ClaimsId")

    def claims_anomaly_search(self, data):
        client_id = data.get("clientId")
        query = data.get("query") or ""
        from_date = data.get("fromDate") or ""
        to_date = data.get("toDate") or ""
        date_match_condition = [
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        to_date if to_date else datetime.max.strftime("%Y-%m-%d"),
                    ]
                }
            },
        ]

        page = int(data["page"] or 1)
        size = int(data["size"] or 10)
        offset = (page - 1) * size

        client_match_condition = [{"client_id": ObjectId(client_id)}] if client_id else []
        data = list(
            get_db().claims_metadata.aggregate(
                [
                    {"$match": {"$and": [*client_match_condition, *date_match_condition]}},
                    {
                        "$lookup": {
                            "from": "client_basic_info",
                            "localField": "client_id",
                            "foreignField": "_id",
                            "as": "client",
                        }
                    },
                    {
                        "$match": {
                            "$or": [
                                {"client.full_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                {"client.friendly_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                {"client.c_id": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                            ]
                        }
                    },
                    {"$unwind": "$client"},
                    {
                        "$lookup": {
                            "from": "anomaly_claims",
                            "localField": "_id",
                            "foreignField": "claims_id",
                            "pipeline": [{"$match": {"status": "Unresolved", "deleted": False}}, {"$count": "count"}],
                            "as": "anomalies",
                        }
                    },
                    {"$unwind": "$anomalies"},
                    {"$sort": {"created_at": -1}},
                    {
                        "$project": {
                            "clientId": "$client.c_id",
                            "fileDate": {"$slice": ["$claim_files.file_date", -1]},
                            "fileName": {"$slice": ["$claim_files.file_name", -1]},
                            "friendlyName": "$client.friendly_name",
                            "clientName": "$client.full_name",
                            "count": "$anomalies.count",
                            "modifiedBy": "$assigned_to",
                            "anomalies": "$anomalies._id",
                            "file_id": {"$slice": ["$claim_files.file_id", -1]},
                        }
                    },
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ]
            )
        )
        content = []
        for row in data[0]["data"]:
            claim_dict = {
                "_id": str(row["_id"]),
                "clientId": row["clientId"],
                "clientName": row["clientName"],
                "friendlyName": row["friendlyName"],
                "fileDate": row["fileDate"][0],
                "status": "Unresolved Anomalies",
                "count": row["count"],
                "fileName": row["fileName"][0],
            }
            content.append(claim_dict)

        empty = True if not content else False
        first = True if page == 1 else False
        last = True if data[0]["metadata"] and page == ceil((data[0]["metadata"][0]["total"]) / size) else False
        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)
        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }

        return response


anomaly_service = AnomalyService()
