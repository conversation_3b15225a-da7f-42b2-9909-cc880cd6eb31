from math import ceil
from bson import ObjectId
from flaskr.models import get_db
from flask import current_app
from datetime import datetime
from flaskr.services.exceptions import ServiceException
from flaskr.services.auth_service import auth_service
from flask import abort
import uuid
from flaskr.helpers.boto3_handler import validate_mime_type_xls_csv, upload_file
import os


class bankingAndClaimService:
    def get_banking_and_claim_summary(self, client_id, is_today, page, size):
        today = datetime.utcnow().strftime("%Y-%m-%d")
        match_condition = {"submittedDate": {"$eq": today}, "delete": False}
        if not is_today:
            match_condition = {"submittedDate": {"$lt": today}, "delete": False}
        if client_id:
            match_condition["client_id"] = ObjectId(client_id)

        page = int(page or 1)
        size = int(size or 10)
        offset = (page - 1) * size

        data = list(
            get_db().banking_and_claim_summary.aggregate(
                [
                    {
                        "$addFields": {
                            "submittedDate": {
                                "$dateToString": {
                                    "format": "%Y-%m-%d",
                                    "date": "$submitted_date",
                                }
                            }
                        }
                    },
                    {"$match": match_condition},
                    {
                        "$sort": {
                            "submittedDate": -1,
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_basic_info",
                            "localField": "client_id",
                            "foreignField": "_id",
                            "as": "client_name",
                        }
                    },
                    {"$unwind": "$client_name"},
                    {
                        "$group": {
                            "_id": {
                                "submittedDate": "$submittedDate",
                                "clientId": "$client_id",
                                "fileName": "$file_name",
                            },
                            "latest_record": {"$last": "$$ROOT"},
                        }
                    },
                    {
                        "$project": {
                            "_id": {"$toString": "$latest_record._id"},
                            "c_id": "$latest_record.client_name.c_id",
                            "client_id": {"$toString": "$latest_record.client_id"},
                            "client_name": "$latest_record.client_name.friendly_name",
                            "file_id": "$latest_record.file_id",
                            "file_name": "$latest_record.file_name",
                            "submitted_date": "$latest_record.submitted_date",
                            "created_at": "$latest_record.created_at",
                            "updated_at": "$latest_record.updated_at",
                            "file_size": "$latest_record.file_size",
                            "file_type": "$latest_record.file_type",
                            "uploaded_by": "$latest_record.uploaded_by",
                            "downloaded_by": "$latest_record.downloaded_by",
                            "downloaded_at": "$latest_record.downloaded_at",
                        }
                    },
                    {"$sort": {"client_name": 1}},
                    {
                        "$facet": {
                            "metadata": [
                                {"$count": "total"},
                                {"$addFields": {"page": page}},
                            ],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ]
            )
        )
        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)
        response = {
            "content": data[0]["data"],
            "pageNumber": page,
            "numberOfElements": len(data),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def banking_and_claim_summmary_create(self, access_token, client_id, file, user_id):
        user_details = auth_service.get_profile_details(access_token)
        uploaded_by = user_details["username"]
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if not client_basic_info:
            raise ServiceException("client not found")
        if client_basic_info.get("is_disabled") is True:
            abort(403)

        current_app.logger.info("Retrieval of file metadata started")
        now = datetime.utcnow()
        file_id = f"{str(uuid.uuid4())}_{now.isoformat()}"
        file_name = file.filename
        file_type = file.content_type
        file_path = f"{current_app.config['TEMP_DIR']}/{file_name}"
        file.save(file_path)
        file_size = os.path.getsize(file_path)

        latest_file = get_db().banking_and_claim_summary.find_one(
            {"client_id": ObjectId(client_id)},
            sort=[("updated_at", -1)],
            projection={"file_name": 1},
        )

        if latest_file and latest_file.get("file_name") != file_name:
            raise ServiceException(message=f"Invalid file name, file name should be {latest_file.get('file_name')}")

        current_app.logger.info("File content type validation started")
        if validate_mime_type_xls_csv(file_type) is False:
            raise ServiceException(message="File format not supported, use xls, xlsx or csv files")
        current_app.logger.info("File content type validation completed")

        current_app.logger.info("Banking and claim summary metadata updation started")
        bucket = current_app.config["BANKING_AND_CLAIM_SUMMARY_BUCKET"]
        banking_and_claim_summary_data = {
            "client_id": ObjectId(client_id),
            "file_id": file_id,
            "file_name": file_name,
            "submitted_date": datetime.utcnow(),
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "file_size": file_size,
            "file_type": file_type,
            "uploaded_by": uploaded_by,
            "delete": False,
        }
        previous_file = get_db().banking_and_claim_summary.find_one(
            {"client_id": ObjectId(client_id), "file_name": file_name, "delete": False},
            sort=[("updated_at", -1)],
            projection={"downloaded_at": 1, "downloaded_by": 1},
        )
        if previous_file:
            banking_and_claim_summary_data["downloaded_by"] = previous_file.get("downloaded_by")
            banking_and_claim_summary_data["downloaded_at"] = previous_file.get("downloaded_at")
        get_db().banking_and_claim_summary.insert_one(banking_and_claim_summary_data)
        current_app.logger.info("Banking and claim summary metadata updation completed")
        current_app.logger.info("Starting file upload to s3")
        upload_file(bucket, file_id, file_path)
        current_app.logger.info("Sucessfully uploaded file to s3")
        return {"file_id": file_id}


banking_and_claim_service = bankingAndClaimService()
