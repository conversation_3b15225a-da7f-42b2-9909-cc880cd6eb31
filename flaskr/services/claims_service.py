import re
from functools import reduce
import json
import random
from bson.errors import InvalidId
from math import ceil
import uuid
from datetime import date, datetime
from dateutil.relativedelta import relativedelta
from flask import current_app, abort, make_response, jsonify
from bson import ObjectId
from pymongo import ReturnDocument
from flaskr.helpers.auth.claims import (
    claim_details_access,
    claim_search_latest_access,
    claim_transaction_access,
)
from flaskr.helpers.kafka_handler import publish_to_kafka
from flaskr.helpers.boto3_handler import (
    delete_object,
    validate_mime_type_xls_csv,
    call_lambda,
    generate_presigned_url,
    head_object,
)
from flaskr.services.auth_service import auth_service
from flaskr.models import get_db
from flaskr.models.claims.claims_file_details import ClaimsFileDetailsSchema
from flaskr.models.claims.metadata import ClaimsMetadataSchema, ClaimTestingSchema
from flaskr.services.exceptions import ServiceException
from itertools import groupby, filterfalse
import os
from flaskr.helpers import track_opening_closing_balance_changes, round


class ClaimService:
    def __handle_anomaly_recalculation(self, updated_transaction, existing_transaction, session):
        if any(
            [
                updated_transaction.get("booking_date") != existing_transaction.get("booking_date"),
                updated_transaction.get("departure_date") != existing_transaction.get("departure_date"),
                updated_transaction.get("return_date") != existing_transaction.get("return_date"),
                updated_transaction.get("amount") != existing_transaction.get("amount"),
                updated_transaction.get("supplier_names") != existing_transaction.get("supplier_names"),
            ]
        ):
            get_db().anomaly_claims.update_many(
                {"transaction_id": existing_transaction["_id"]},
                {"$set": {"deleted": True, "updated_at": datetime.utcnow()}},
                session=session,
            )
            current_app.logger.info(
                f"Initiating recalculation of anomalies for file_id {existing_transaction['file_id']} and client_id {existing_transaction['client_id']}"
            )
            call_lambda(
                f"claim-anomaly-detector-lambda-{os.environ['ENVIRONMENT']}",
                json.dumps(
                    {
                        "fileId": existing_transaction["file_id"],
                        "clientId": str(existing_transaction["client_id"]),
                        "transactions": [str(existing_transaction["_id"])],
                    }
                ),
            )

    def __handle_claims_transaction_checks_update(self, transaction, old_check, new_check, file_id, session):
        if (old_check == "full-check" or old_check == "quick-check") and (
            new_check != "full-check" and new_check != "quick-check"
        ):
            count_difference = -1
            checked_amount_difference = -1 * transaction["amount"]
        elif (old_check != "full-check" and old_check != "quick-check") and (
            new_check == "full-check" or new_check == "quick-check"
        ):
            count_difference = 1
            checked_amount_difference = transaction["amount"]
        else:
            return

        get_db().claims_metadata.update_one(
            {"_id": transaction["claims_id"], "claim_files": {"$elemMatch": {"file_id": file_id}}},
            {
                "$inc": {
                    f'claim_files.$.checks.{transaction["currency_code"]}': count_difference,
                    f'claim_files.$.checked_amount.{transaction["currency_code"]}': checked_amount_difference,
                },
                "$set": {"updated_at": datetime.utcnow()},
            },
            session=session,
        )

    def __handle_claims_transaction_amount_and_status_change(
        self, existing_transaction, updated_transaction, file_date, file_id, session
    ):
        amount_difference = 0
        count_difference = 0
        if existing_transaction.get("status") != "Cancelled" and updated_transaction.get("status") == "Cancelled":
            get_db().claims_file_details.update_one(
                {"_id": updated_transaction["_id"], "deleted": False},
                {"$set": {"deleted": True}},
                session=session,
            )

            # Track removed booking reference (exclude Performance, Refund, Cancellation)
            self.__track_removed_booking_ref(existing_transaction, file_id, session)

            amount_difference = -1 * existing_transaction["amount"]
            count_difference = -1

        elif existing_transaction.get("status") == "Cancelled" and updated_transaction.get("status") != "Cancelled":
            get_db().claims_file_details.update_one(
                {"_id": updated_transaction["_id"], "deleted": True},
                {"$set": {"deleted": False}},
                session=session,
            )

            # Remove from removed booking references list when reinstated
            self.__remove_from_tracking(existing_transaction, session)

            amount_difference = updated_transaction["amount"]
            count_difference = 1

        else:
            amount_difference = updated_transaction["amount"] - existing_transaction["amount"]
        inc_items = {
            f'claim_files.$.claim_total.{updated_transaction["currency_code"]}': amount_difference,
            f'claim_files.$.item_count.{updated_transaction["currency_code"]}': count_difference,
        }
        if updated_transaction.get("check") == "full-check" or updated_transaction.get("check") == "quick-check":
            inc_items[f'claim_files.$.checks.{updated_transaction["currency_code"]}'] = count_difference
            inc_items[f'claim_files.$.checked_amount.{updated_transaction["currency_code"]}'] = amount_difference
        get_db().claims_metadata.update_one(
            {"_id": updated_transaction["claims_id"], "claim_files": {"$elemMatch": {"file_id": file_id}}},
            {
                "$inc": inc_items,
                "$set": {"updated_at": datetime.utcnow()},
            },
            session=session,
        )

        collection_name = "trust_fund" if updated_transaction["client_id"] == os.environ.get("NAS") else "trust_fund_v2"

        get_db()[collection_name].update_one(
            {"client_id": updated_transaction["client_id"], "booking_ref": updated_transaction["booking_ref"]},
            {"$inc": {"balance": round(-1 * amount_difference, 2), "total_claimed": amount_difference}},
            collation={"locale": "en", "strength": 1},
            session=session,
        )
        get_db().anomaly_claims.update_many(
            {"existing_transaction_id": existing_transaction["_id"]},
            {"$set": {"deleted": True, "updated_at": datetime.utcnow()}},
            session=session,
        )
        track_opening_closing_balance_changes(updated_transaction, amount_difference, file_date, session)

    def __track_removed_booking_ref(self, transaction, file_id, session):
        """Track removed booking reference for anomaly detection, excluding specific elements"""
        excluded_elements = ["performance", "refund", "cancellation"]
        element = transaction.get("element", "").lower().strip()
        
        # Skip tracking for excluded elements
        if element in excluded_elements:
            return
            
        removed_booking_data = {
            "client_id": transaction["client_id"],
            "booking_ref": transaction["booking_ref"],
            "file_id": file_id,
            "element": transaction.get("element"),
            "removal_date": datetime.utcnow(),
            "return_date": transaction.get("return_date"),
            "reason": "cancelled",
            "notes": f"Removed from claim file {file_id}",
            "created_at": datetime.utcnow(),
            "expired": False
        }
        
        # Insert or update the removed booking reference
        get_db().removed_booking_refs.update_one(
            {
                "client_id": transaction["client_id"],
                "booking_ref": transaction["booking_ref"],
                "file_id": file_id
            },
            {"$set": removed_booking_data},
            upsert=True,
            session=session
        )
        
        current_app.logger.info(
            f"Tracked removed booking reference: {transaction['booking_ref']} "
            f"for client {transaction['client_id']} in file {file_id}"
        )

    def __remove_from_tracking(self, transaction, session):
        """Remove booking reference from tracking when reinstated"""
        get_db().removed_booking_refs.delete_many(
            {
                "client_id": transaction["client_id"],
                "booking_ref": transaction["booking_ref"]
            },
            session=session
        )
        
        current_app.logger.info(
            f"Removed tracking for reinstated booking: {transaction['booking_ref']} "
            f"for client {transaction['client_id']}"
        )

    def __find_median_transactions(self, transaction_list: list) -> list:
        median_transactions = None
        length = len(transaction_list)
        sorted_transactions = sorted(transaction_list, key=lambda x: x["amount"])
        if length % 2 != 0:
            median_transactions = [sorted_transactions[length // 2]]
        else:
            middle_indexes = (length // 2, length // 2 - 1)
            median_transactions = [
                sorted_transactions[middle_indexes[0]],
                sorted_transactions[middle_indexes[1]],
            ]
        median_transactions_with_description = [
            {**transaction, "description": "Median Amount"} for transaction in median_transactions
        ]
        return median_transactions_with_description

    def claim_create_presigned_url(self, client_id, file_name, claim_from_tbr, user_id):
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        company_alias = [item.strip() for item in client_basic_info["company_alias"].split(",")]
        if not client_basic_info:
            raise ServiceException("client not found")
        if client_basic_info.get("is_disabled") is True:
            abort(403)

        now = datetime.utcnow()
        if claim_from_tbr:
            if client_basic_info["claim_from_tbr"] is False:
                response = make_response(jsonify({"message": "ClaimFromTBR is not enabled for this client"}))
                response.status_code = 409
                abort(response)
            anomaly_name = "Claim Too Early For Departure Date"
            lookup_anomaly = get_db().lookup_anomaly.find_one({"name": anomaly_name})
            client_anomaly = get_db().client_anomaly.find_one(
                {"client_id": ObjectId(client_id), "anomaly_id": lookup_anomaly["_id"]}
            )
            if not client_anomaly:
                response = make_response(
                    jsonify({"message": "Claim Too Early For Departure Date Anomaly is not enabled for this client"})
                )
                response.status_code = 409
                abort(response)
            if client_id == os.environ.get("BLUESTYLE"):
                if re.match(r"\d{8}[-]\d{{6}}[-]", file_name):
                    file_name = file_name
                else:
                    today = datetime.now().strftime("%Y%m%d-%H%M%S")
                    file_name = f"{today}-{file_name}"
            else:
                if re.match(r"\d{8}[-]", file_name):
                    file_name = file_name
                else:
                    today = date.today().strftime("%Y%m%d")
                    file_name = f"{today}-{file_name}"
        else:
            file_name = file_name
        try:
            # file_datetime = datetime.strptime(file_name.split("-")[0], "%Y%m%d")
            datetime.strptime(file_name.split("-")[0], "%Y%m%d")
        except ValueError:
            raise ServiceException("Invalid file name, failed to extract file date.")
        if client_id == os.environ.get("BLUESTYLE"):
            pattern = f"\\d{{8}}[-]\\d{{6}}[-]({'|'.join(company_alias)})[-]Claim"
        else:
            pattern = f"\\d{{8}}[-]({'|'.join(company_alias)})[-]*Claim"
        if re.match(pattern, file_name, re.IGNORECASE) is None and not claim_from_tbr:
            raise ServiceException("Invalid file name")
        # last_valid_file_date = now - relativedelta(months=current_app.config["FILE_DATE_AGE_IN_MONTHS"])
        # if file_datetime < last_valid_file_date:
        #     raise ServiceException(
        #         f"File date cannot be older than {current_app.config['FILE_DATE_AGE_IN_MONTHS']} month(s)."
        #     )
        existing_metadata = get_db().claims_metadata.find_one(
            {"client_id": ObjectId(client_id), "claim_files": {"$elemMatch": {"file_name": file_name}}}
        )

        if not existing_metadata:
            file_id = f"{str(uuid.uuid4())}_{now.isoformat()}"
        else:
            if existing_metadata["status"] == "Scanning":
                raise ServiceException("Previous scan for the item is not completed.")
            if existing_metadata["status"] == "Updating Status":
                raise ServiceException("The status of the file is being updated. No action allowed now.")
            if existing_metadata["status"] == "Authorised":
                user = get_db().user.find_one({"user_id": user_id}, projection={"role": 1, "_id": 0})
                if user["role"] in ["ptt-user", "ptt-client"]:
                    raise ServiceException("No action is allowed since the file is authorised.")
            file_id = f"{existing_metadata['claim_files'][0]['file_id'].split('_')[0]}_{now.isoformat()}"

        presigned_url = generate_presigned_url("put_object", current_app.config["CLAIM_FILE_BUCKET"], file_id, 300)
        return {"fileId": file_id, "presignedUrl": presigned_url}

    def claim_create(self, client_id, file_name, file_id, claim_from_tbr, sftp, user_id, sftp_key):
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if not client_basic_info:
            raise ServiceException("client not found")
        if client_basic_info.get("is_disabled") is True:
            abort(403)
        company_alias = [item.strip() for item in client_basic_info["company_alias"].split(",")]
        now = datetime.utcnow()
        if claim_from_tbr:
            if client_basic_info["claim_from_tbr"] is False:
                response = make_response(jsonify({"message": "ClaimFromTBR is not enabled for this client"}))
                response.status_code = 409
                abort(response)
            if client_id == os.environ.get("BLUESTYLE"):
                if re.match(r"\d{8}[-]\d{{6}}[-]", file_name):
                    file_name = file_name
                else:
                    today = datetime.now().strftime("%Y%m%d-%H%M%S")
                    file_name = f"{today}-{file_name}"
            else:
                if re.match(r"\d{8}[-]", file_name):
                    file_name = file_name
                else:
                    today = date.today().strftime("%Y%m%d")
                    file_name = f"{today}-{file_name}"
        else:
            file_name = file_name

        try:
            file_datetime = datetime.strptime(file_name.split("-")[0], "%Y%m%d")
        except ValueError:
            raise ServiceException("Invalid file name, failed to extract file date.")
        if client_id == os.environ.get("BLUESTYLE"):
            pattern = f"\\d{{8}}[-]\\d{{6}}[-]({'|'.join(company_alias)})[-]Claim"
        else:
            pattern = f"\\d{{8}}[-]({'|'.join(company_alias)})[-]*Claim"
        if re.match(pattern, file_name, re.IGNORECASE) is None and not claim_from_tbr:
            raise ServiceException("Invalid file name")
        file_date = file_datetime.strftime("%Y-%m-%d")
        last_valid_file_date = now - relativedelta(months=current_app.config["FILE_DATE_AGE_IN_MONTHS"])
        if file_datetime < last_valid_file_date:
            raise ServiceException(
                f"File date cannot be older than {current_app.config['FILE_DATE_AGE_IN_MONTHS']} month(s)."
            )
        current_app.logger.info("Retrieval of file metadata started")
        file_metadata = head_object(current_app.config["CLAIM_FILE_BUCKET"], file_id)
        current_app.logger.info("Retrieval of file metadata completed")
        mime_type = file_metadata["ContentType"]
        current_app.logger.info("File content type validation started")
        if validate_mime_type_xls_csv(mime_type) is False:
            delete_object(current_app.config["CLAIM_FILE_BUCKET"], file_id)
            raise ServiceException(message="File format not supported, use xls, xlsx or csv files")
        current_app.logger.info("File content type validation completed")

        current_app.logger.info("claims metadata and anomaly claims updation started")
        existing_metadata = get_db().claims_metadata.find_one(
            {"client_id": ObjectId(client_id), "claim_files": {"$elemMatch": {"file_name": file_name}}}
        )
        if not existing_metadata:
            metadata = ClaimsMetadataSchema().load(
                {
                    "clientId": ObjectId(client_id),
                    "createdAt": now.isoformat(),
                    "updatedAt": now.isoformat(),
                    "claimFiles": [
                        {
                            "fileId": file_id,
                            "fileName": file_name,
                            "fileDate": file_date,
                            "submittedDate": now.isoformat(),
                            "status": "Scanning",
                        }
                    ],
                    "status": "Scanning",
                    "frequency": "Weekly",
                }
            )
            get_db().claims_metadata.insert_one(metadata)
        else:
            if existing_metadata["status"] == "Scanning":
                raise ServiceException("Previous scan for the item is not completed.")
            if existing_metadata["status"] == "Authorised":
                user = get_db().user.find_one({"user_id": user_id}, projection={"role": 1, "_id": 0})
                if user["role"] in ["ptt-user", "ptt-client"]:
                    raise ServiceException("No action is allowed since the file is authorised.")
            if existing_metadata["status"] == "Updating Status":
                raise ServiceException("The status of the file is being updated. No action allowed now.")
            claim_files = existing_metadata["claim_files"]
            claim_files[-1]["status"] = "Cancelled by System"
            with get_db().client.start_session() as session:
                with session.start_transaction():
                    get_db().claims_metadata.update_one(
                        {
                            "client_id": ObjectId(client_id),
                            "claim_files": {"$elemMatch": {"file_name": file_name}},
                        },
                        {
                            "$set": {
                                "claim_files": [
                                    *claim_files,
                                    {
                                        "file_id": file_id,
                                        "file_name": file_name,
                                        "file_date": file_date,
                                        "submitted_date": now,
                                        "status": "Scanning",
                                    },
                                ],
                                "status": "Scanning",
                                "updated_at": now,
                            }
                        },
                        session=session,
                    )
                    get_db().anomaly_claims.update_many(
                        {"claims_id": existing_metadata["_id"]},
                        {
                            "$set": {
                                "deleted": True,
                                "updated_at": now,
                            }
                        },
                        session=session,
                    )
        current_app.logger.info("claims metadata and anomaly claims updation completed")
        payload = {
            "clientId": client_id,
            "fileId": file_id,
            "fileType": mime_type,
            "fileName": file_name,
            "claimFromTBR": claim_from_tbr,
            "sftp": sftp,
            "sftpKey": sftp_key,
        }
        payload["replace"] = True if existing_metadata else False
        if client_id in []:
            message = json.dumps(payload)
            kafka_topic = "claim-file-processor-topic-dev"
            try:
                publish_to_kafka(kafka_topic, message)
                return f"Message sent {message}"
            except Exception as e:
                print(f"Error sending message: {e}")
        else:
            call_lambda(f"claim-trigger-lambda-{current_app.config['ENVIRONMENT']}", json.dumps(payload))

    @claim_details_access()
    def get_claim_details(self, claims_id):
        try:
            claim_details = get_db().claims_metadata.find_one({"_id": ObjectId(claims_id)})
        except InvalidId:
            raise ServiceException("invalid claim id")
        client_id = claim_details["client_id"]
        client = get_db().client_basic_info.find_one({"_id": client_id})
        return {
            "clientName": client["full_name"],
            "friendlyName": client["friendly_name"],
            **ClaimsMetadataSchema().dump(claim_details),
        }

    @claim_transaction_access()
    def get_claim_transaction(self, claims_id, query, page, size, sort_key, sort_order):
        try:
            claims_metadata = get_db().claims_metadata.find_one({"_id": ObjectId(claims_id)})
        except InvalidId:
            raise ServiceException("invalid claimId")

        page = int(page or 1)
        size = int(size or 10)
        offset = (page - 1) * size

        transactions = list(
            get_db()
            .claims_file_details.find(
                {
                    "file_id": claims_metadata["claim_files"][-1]["file_id"],
                    "$or": [
                        {"booking_ref": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                        {"element": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                        {"currency_code": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                    ],
                },
                projection={
                    "booking_ref": 1,
                    "booking_date": 1,
                    "amount": 1,
                    "currency_code": 1,
                    "element": 1,
                    "client_id": 1,
                    "status": 1,
                    "original_amount": 1,
                    "escrow_multiplier": 1,
                },
            )
            .sort(sort_key, sort_order)
            .skip(offset)
            .limit(size)
        )
        total_elements = get_db().claims_file_details.count_documents(
            {
                "file_id": claims_metadata["claim_files"][-1]["file_id"],
                "$or": [
                    {"booking_ref": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                    {"element": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                    {"currency_code": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                ],
            },
        )

        claims_files_details = [ClaimsFileDetailsSchema().dump(transaction) for transaction in transactions]
        transaction_list = []
        for item in claims_files_details:
            amount = item["amount"] if item.get("amount") else 0
            item.pop("bookingDate")
            count = get_db().claims_file_details.count_documents(
                {
                    "client_id": ObjectId(item["clientId"]),
                    "booking_ref": item["bookingRef"],
                    "deleted": False,
                },
                collation={"locale": "en", "strength": 1},
            )
            duplicates = get_db().claims_file_details.count_documents(
                {
                    "client_id": ObjectId(item["clientId"]),
                    "booking_ref": item["bookingRef"],
                    "currency_code": item["currencyCode"],
                    "amount": amount,
                    "deleted": False,
                },
                collation={"locale": "en", "strength": 1},
            )
            basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(item["clientId"])})
            item["clientId"] = basic_info["c_id"]
            transaction_list.append({**item, "amount": amount, "count": count, "duplicates": duplicates})
        total_pages = ceil(total_elements / size)
        response = {
            "content": transaction_list,
            "pageNumber": page,
            "numberOfElements": len(transaction_list),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def get_check_type(self, check_type, claims_id, query):
        checks = list(
            get_db().claims_file_details.find(
                {
                    "check": check_type,
                    "claims_id": ObjectId(claims_id),
                    "deleted": False,
                    "$or": [
                        {"booking_ref": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                        {"element": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                        {"currency_code": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                    ],
                },
                projection={
                    "booking_ref": 1,
                    "amount": 1,
                    "currency_code": 1,
                    "element": 1,
                    "check": 1,
                    "client_id": 1,
                },
            )
        )
        check_list = [ClaimsFileDetailsSchema().dump(check) for check in checks]
        transaction_list = []
        for item in check_list:
            amount = item["amount"] if item.get("amount") else 0
            count = get_db().claims_file_details.count_documents(
                {
                    "client_id": ObjectId(item["clientId"]),
                    "booking_ref": item["bookingRef"],
                    "deleted": False,
                },
                collation={"locale": "en", "strength": 1},
            )
            duplicates = get_db().claims_file_details.count_documents(
                {
                    "client_id": ObjectId(item["clientId"]),
                    "booking_ref": item["bookingRef"],
                    "currency_code": item["currencyCode"],
                    "amount": amount,
                    "deleted": False,
                },
                collation={"locale": "en", "strength": 1},
            )
            basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(item["clientId"])})
            item["clientId"] = basic_info["c_id"]
            transaction_list.append({**item, "amount": amount, "count": count, "duplicates": duplicates})

        return transaction_list

    def claim_summary(self, claims_id, query, sort_key, sort_order, is_deleted=False):
        try:
            claims_metadata = get_db().claims_metadata.find_one({"_id": ObjectId(claims_id)})
        except InvalidId:
            raise ServiceException("invalid claimId")

        summary_list = list(
            get_db().claims_file_details.aggregate(
                [
                    {
                        "$match": {
                            "$and": [
                                {
                                    "file_id": claims_metadata["claim_files"][-1]["file_id"],
                                    "deleted": is_deleted,
                                    "$or": [
                                        {"element": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                        {"currency_code": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                    ],
                                },
                            ]
                        }
                    },
                    {
                        "$group": {
                            "_id": {"element": "$element", "currency": "$currency_code"},
                            "element": {"$first": "$element"},
                            "currency": {"$last": "$currency_code"},
                            "count": {"$sum": 1},
                            "minAmount": {"$min": "$amount"},
                            "maxAmount": {"$max": "$amount"},
                            "total": {"$sum": "$amount"},
                            "client_id": {"$first": "$client_id"},
                            "booking_date": {"$first": "$booking_date"},
                        }
                    },
                    {"$sort": {sort_key: sort_order}},
                    {
                        "$project": {
                            "_id": 0,
                            "element": 1,
                            "currency": 1,
                            "count": 1,
                            "minAmount": 1,
                            "maxAmount": 1,
                            "total": 1,
                            "claims_id": 1,
                        }
                    },
                ]
            )
        )

        if not summary_list:
            return []
        for item in summary_list:
            item["minAmount"] = item["minAmount"] if item.get("minAmount") else 0
            item["maxAmount"] = item["maxAmount"] if item.get("maxAmount") else 0
            item["total"] = item["total"] if item.get("total") else 0

        summary_list = sorted(summary_list, key=lambda x: x["currency"])
        response = []
        for key, value in groupby(summary_list, lambda x: x["currency"]):
            values = list(value)
            total_amount = reduce(lambda x, y: x + y["total"], values, 0)
            total_count = reduce(lambda x, y: x + y["count"], values, 0)

            for row in values:
                item_dict = {}
                symbols = list(
                    get_db().lookup_currency.find(
                        {},
                        projection={"_id": 0, "order": 0, "name": 0},
                    )
                )
                currency = list(filter(lambda x: x["code"] == row["currency"], symbols))[0]
                item_dict[row["currency"]] = {
                    "currency": row["currency"],
                    "totalCount": total_count,
                    "totalAmount": total_amount,
                    "symbol": currency["symbol"],
                    "content": values,
                }

            response.append(item_dict[row["currency"]])
        return response

    @claim_search_latest_access()
    def claim_search_latest(self, user_id, data):
        query = data.get("query") or ""
        client = data.get("client")
        status = data.get("status") or ""
        from_date = data.get("fromDate") or None
        to_date = data.get("toDate") or None
        assigned_to = data.get("assignedTo")
        date = data.get("date") or None
        from_date = datetime.fromisoformat(from_date) if from_date else datetime.min
        to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time()) if to_date else datetime.max
        page = data["page"] or 1
        size = data["size"] or 10
        offset = (page - 1) * size

        client_match_condition = [{"client_id": ObjectId(client)}] if client else []
        match_condition = [
            *client_match_condition,
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        date if date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        date if date else datetime.max.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$claim_files.submitted_date", -1]},
                        from_date,
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$claim_files.submitted_date", -1]},
                        to_date,
                    ]
                }
            },
            {"status": {"$regex": f"^.*{status}.*$", "$options": "i"}},
        ]
        if assigned_to:
            match_condition.append({"assigned_to": assigned_to})

        data = list(
            get_db().claims_metadata.aggregate(
                [
                    {"$match": {"$and": match_condition}},
                    {"$addFields": {"user_id": user_id}},
                    {
                        "$lookup": {
                            "from": "user",
                            "localField": "user_id",
                            "foreignField": "user_id",
                            "let": {"client_id": "$client_id"},
                            "pipeline": [{"$match": {"$expr": {"$in": ["$$client_id", "$clients"]}}}],
                            "as": "user",
                        }
                    },
                    {"$unwind": "$user"},
                    {
                        "$lookup": {
                            "from": "anomaly_claims",
                            "localField": "_id",
                            "foreignField": "claims_id",
                            "pipeline": [
                                {
                                    "$match": {
                                        "$and": [
                                            {
                                                "deleted": False,
                                                "status": "Unresolved",
                                            }
                                        ]
                                    }
                                },
                                {"$group": {"_id": None, "anomaly_count": {"$sum": 1}}},
                                {"$project": {"_id": 0}},
                            ],
                            "as": "anomalies",
                        }
                    },
                    {"$unwind": {"path": "$anomalies", "preserveNullAndEmptyArrays": True}},
                    {
                        "$lookup": {
                            "from": "client_basic_info",
                            "localField": "client_id",
                            "foreignField": "_id",
                            "as": "client",
                        }
                    },
                    {
                        "$match": {
                            "$or": [
                                {"client.full_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                {"client.friendly_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                {"client.c_id": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                            ]
                        }
                    },
                    {"$unwind": "$client"},
                    {
                        "$lookup": {
                            "from": "lookup_trust_type",
                            "localField": "client.type_of_trust_account",
                            "foreignField": "_id",
                            "as": "trust",
                        }
                    },
                    {"$unwind": {"path": "$trust", "preserveNullAndEmptyArrays": True}},
                    {
                        "$project": {
                            "_id": "$_id",
                            "clientId": "$client.c_id",
                            "clientName": "$client.full_name",
                            "friendlyName": "$client.friendly_name",
                            "submittedDate": {"$slice": ["$claim_files.submitted_date", -1]},
                            "claimDate": {"$slice": ["$claim_files.file_date", -1]},
                            "assignedTo": "$assigned_to",
                            "status": "$status",
                            "count": {"$slice": ["$claim_files.item_count", -1]},
                            "amount": {"$slice": ["$claim_files.claim_total", -1]},
                            "checks": {"$slice": ["$claim_files.checks", -1]},
                            "checkedAmount": {"$slice": ["$claim_files.checked_amount", -1]},
                            "notes": "$notes",
                            "anomalyCount": {"$ifNull": ["$anomalies.anomaly_count", 0]},
                            "trust": "$trust.name",
                        }
                    },
                    {"$sort": {"submittedDate": -1}},
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ],
                allowDiskUse=True,
            )
        )

        content = []
        symbols = list(
            get_db().lookup_currency.find(
                {},
                projection={"_id": 0, "order": 0, "name": 0},
            )
        )
        for row in data[0]["data"]:
            claim_dict = {
                "_id": str(row["_id"]),
                "clientId": row["clientId"],
                "clientName": row["clientName"],
                "friendlyName": row["friendlyName"],
                "submittedDate": row["submittedDate"][-1].isoformat(),
                "claimDate": row["claimDate"][-1],
                "assignedTo": row.get("assignedTo"),
                "status": row.get("status"),
                "notes": row["notes"] if row.get("notes") else "",
                "anomalies": row["anomalyCount"],
                "trustAccount": row.get("trust"),
            }
            items = []

            if row.get("amount"):
                for currency_code, amount in row["amount"][0].items():
                    currency = list(filter(lambda x: x["code"] == currency_code, symbols))[0]
                    amount = amount if amount else 0
                    item_dict = {}
                    percentageCheck = (
                        (row["checks"][0][currency_code] * 100 / row["count"][0][currency_code])
                        if row["count"][0][currency_code]
                        else 0
                    )
                    percentageTotal = (row["checkedAmount"][0][currency_code] * 100 / amount) if amount else 0
                    item_dict.update(
                        {
                            "currency": currency_code,
                            "count": row["count"][0][currency_code],
                            "amount": amount,
                            "symbol": currency["symbol"],
                            "checks": row["checks"][0][currency_code],
                            "checkedAmount": row["checkedAmount"][0][currency_code],
                            "percentageCheck": percentageCheck,
                            "percentageTotal": percentageTotal,
                        }
                    )
                    items.append(item_dict)
            claim_dict["items"] = items
            content.append(claim_dict)
        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)
        empty = True if not content else False
        first = True if page == 1 else False
        last = True if page == ceil((total_elements) / size) else False

        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def claim_search_summary(self, user_id, data):
        query = data.get("query")
        client = data.get("client")
        from_date = data.get("fromDate")
        to_date = data.get("toDate")
        date = data.get("date")
        from_date = datetime.fromisoformat(from_date) if from_date else datetime.min
        to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time()) if to_date else datetime.max
        page = data["page"] or 1
        size = data["size"] or 10
        offset = (page - 1) * size

        client_match_condition = [{"client_id": ObjectId(client)}] if client else []
        match_condition = [
            *client_match_condition,
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        date if date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        date if date else datetime.max.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$claim_files.submitted_date", -1]},
                        from_date,
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$claim_files.submitted_date", -1]},
                        to_date,
                    ]
                }
            },
        ]
        data = list(
            get_db().claims_metadata.aggregate(
                [
                    {"$match": {"status": {"$in": ["In Progress", "Information Required"]}}},
                    {"$addFields": {"user_id": user_id}},
                    {"$match": {"$and": match_condition}},
                    {
                        "$lookup": {
                            "from": "user",
                            "localField": "user_id",
                            "foreignField": "user_id",
                            "let": {"client_id": "$client_id"},
                            "pipeline": [{"$match": {"$expr": {"$in": ["$$client_id", "$clients"]}}}],
                            "as": "user",
                        }
                    },
                    {"$unwind": "$user"},
                    {
                        "$lookup": {
                            "from": "client_basic_info",
                            "localField": "client_id",
                            "foreignField": "_id",
                            "as": "client",
                        }
                    },
                    {
                        "$match": {
                            "$or": [
                                {"client.full_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                {"client.friendly_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                {"client.c_id": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                            ]
                        }
                    },
                    {"$unwind": "$client"},
                    {
                        "$lookup": {
                            "from": "lookup_trust_type",
                            "localField": "client.type_of_trust_account",
                            "foreignField": "_id",
                            "as": "trust",
                        }
                    },
                    {"$unwind": {"path": "$trust", "preserveNullAndEmptyArrays": True}},
                    {
                        "$lookup": {
                            "from": "claims_file_details",
                            "localField": "_id",
                            "let": {"trust": "$trust.name"},
                            "foreignField": "claims_id",
                            "pipeline": [
                                {"$addFields": {"trust": "$$trust"}},
                                {"$match": {"deleted": False}},
                                {
                                    "$addFields": {
                                        "element": {
                                            "$cond": [
                                                {
                                                    "$and": [
                                                        {"$eq": ["$$trust", "Tripartite Tour Op"]},
                                                        {"$eq": ["$element", "Refund"]},
                                                        {"$lt": ["$amount", 0]},
                                                    ]
                                                },
                                                "Refund From",
                                                {
                                                    "$cond": [
                                                        {
                                                            "$and": [
                                                                {"$eq": ["$$trust", "Tripartite Tour Op"]},
                                                                {"$eq": ["$element", "Refund"]},
                                                                {"$gt": ["$amount", 0]},
                                                            ]
                                                        },
                                                        "Refund To",
                                                        {
                                                            "$cond": [
                                                                {
                                                                    "$in": [
                                                                        "$element",
                                                                        ["Commission", "VAT on Commission"],
                                                                    ]
                                                                },
                                                                "Commission",
                                                                "$element",
                                                            ]
                                                        },
                                                    ]
                                                },
                                            ]
                                        }
                                    }
                                },
                                {
                                    "$group": {
                                        "_id": {
                                            "$cond": [
                                                {"$eq": ["$$trust", "Tripartite Tour Op"]},
                                                {"element": "$element", "currency": "$currency_code"},
                                                None,
                                            ]
                                        },
                                        "count": {"$count": {}},
                                        "amount": {
                                            "$sum": {
                                                "$cond": [{"$eq": ["$$trust", "Tripartite Tour Op"]}, "$amount", 0]
                                            }
                                        },
                                        "elementList": {"$addToSet": "$element"},
                                    }
                                },
                                {
                                    "$group": {
                                        "_id": "$_id.element",
                                        "items": {
                                            "$push": {
                                                "currency": "$_id.currency",
                                                "amount": "$amount",
                                                "count": "$count",
                                            }
                                        },
                                        "elementList": {"$first": "$elementList"},
                                    }
                                },
                            ],
                            "as": "claims",
                        }
                    },
                    {"$unwind": {"path": "$claims", "preserveNullAndEmptyArrays": True}},
                    {
                        "$project": {
                            "_id": "$_id",
                            "clientId": "$_id",
                            "cId": "$client.c_id",
                            "claims": "$claims",
                            "clientName": "$client.full_name",
                            "friendlyName": "$client.friendly_name",
                            "submittedDate": {"$slice": ["$claim_files.submitted_date", -1]},
                            "fileDate": {"$slice": ["$claim_files.file_date", -1]},
                            "status": "$status",
                            "frequency": "$frequency",
                            "items": "$claims",
                            "count": {"$slice": ["$claim_files.item_count", -1]},
                            "amount": {"$slice": ["$claim_files.claim_total", -1]},
                            "notes": "$notes",
                            "trust": "$trust.name",
                        }
                    },
                    {"$sort": {"submittedDate": 1}},
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ],
                allowDiskUse=True,
            )
        )
        content = []
        symbols = list(
            get_db().lookup_currency.find(
                {},
                projection={"_id": 0, "order": 0, "name": 0},
            )
        )
        for row in data[0]["data"]:
            item_dict = {}
            claim_dict = {
                "_id": str(row["_id"]),
                "clientId": str(row["clientId"]),
                "cId": row["cId"],
                "clientName": row["clientName"],
                "friendlyName": row["friendlyName"],
                "submittedDate": row["submittedDate"][-1].isoformat(),
                "fileDate": row["fileDate"][0],
                "status": row.get("status"),
                "frequency": row["frequency"],
                "notes": row["notes"] if row.get("notes") else "",
                "trustAccount": row.get("trust"),
            }
            items = []
            if row["claims"]["_id"]:
                items = row["claims"]["items"]
            else:
                if row.get("amount"):
                    for currency_code, amount in row["amount"][0].items():
                        item_dict = {}
                        currency = list(filter(lambda x: x["code"] == currency_code, symbols))[0]
                        amount = amount if amount else 0
                        item_dict.update(
                            {
                                "currency": currency_code,
                                "count": row["count"][0][currency_code],
                                "amount": amount,
                                "symbol": currency["symbol"],
                            }
                        )
                        items.append(item_dict)
            claim_dict["items"] = items
            element_list = row["claims"]["elementList"] if row["claims"] else []
            element_list = sorted(element_list, key=str.casefold)
            claim_dict["element"] = ", ".join(element_list)
            content.append(claim_dict)

        for item in content:
            if item.get("trustAccount") == "Tripartite Tour Op":
                if item.get("element") == "Deposit":
                    item["element"] = "Deposit to Airline"
                if item.get("element") == "Balance":
                    item["element"] = "Balance to Airline"
                if item.get("element") == "Commission":
                    item["element"] = f"Commission to {item.get('clientName')}"
                if item.get("element") == "Refund To":
                    item["element"] = f"Refund to {item.get('clientName')}"
                if item.get("element") == "Refund From":
                    item["element"] = "Refund from Airline"
                if item.get("element") == "Cancellation":
                    if item.get("items")[0]["amount"] > 0:
                        item["element"] = f"Cancellation to {item.get('clientName')}"
                    if item.get("items")[0]["amount"] < 0:
                        item["element"] = "Cancellation from Airline"

        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)
        empty = True if not content else False
        first = True if page == 1 else False
        last = True if page == ceil((total_elements) / size) else False

        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def update_claims_metadata(self, data, user_id):
        claim_details = ClaimsMetadataSchema().load(data)
        try:
            existing_metadata = get_db().claims_metadata.find_one({"_id": ObjectId(data["claimId"])})
        except InvalidId:
            raise ServiceException("invalid claimId")
        if not existing_metadata:
            raise ServiceException("claim file not found")

        previous_status = existing_metadata["status"]
        new_status = data.get("status")
        is_status_handle_required = False
        is_deleted = False
        if new_status:
            if previous_status in ("Scanning", "Updating Status"):
                raise ServiceException("No action is allowed while the file is under modification by system.")
            if previous_status == "Authorised":
                user = get_db().user.find_one({"user_id": user_id}, projection={"role": 1, "_id": 0})
                if user["role"] == "ptt-user":
                    raise ServiceException("No action is allowed since the file is authorised.")
            if previous_status != "Cancelled" and new_status == "Cancelled":
                is_status_handle_required = True
                claim_details["status"] = "Updating Status"
            if previous_status == "Cancelled" and new_status != "Cancelled":
                is_status_handle_required = True
                is_deleted = True
                claim_details["status"] = "Updating Status"
            claim_details["claim_files.$.status"] = claim_details["status"]

        file_id = existing_metadata["claim_files"][-1]["file_id"]
        file_date = existing_metadata["claim_files"][-1]["file_date"]
        with get_db().client.start_session() as session:
            with session.start_transaction():
                get_db().claims_metadata.update_one(
                    {
                        "_id": ObjectId(data["claimId"]),
                        "claim_files.file_id": file_id,
                    },
                    {
                        "$set": {
                            **claim_details,
                            "updated_at": datetime.utcnow(),
                        }
                    },
                )

                if is_status_handle_required:
                    call_lambda(
                        f"claim-status-change-trigger-lambda-{os.environ['ENVIRONMENT']}",
                        json.dumps(
                            {
                                "claimId": data["claimId"],
                                "fileId": file_id,
                                "clientId": str(existing_metadata["client_id"]),
                                "previousStatus": previous_status,
                                "newStatus": new_status,
                                "createdAt": existing_metadata["created_at"].isoformat(),
                                "fileDate": file_date,
                                "isDeleted": is_deleted,
                            }
                        ),
                    )

    def get_claim_automated_transaction(self, claims_id, query):
        grouped_transactions = list(
            get_db().claims_file_details.aggregate(
                [
                    {
                        "$match": {
                            "$and": [
                                {
                                    "claims_id": ObjectId(claims_id),
                                    "deleted": False,
                                    "$or": [
                                        {"booking_ref": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                        {"element": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                        {"currency_code": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                    ],
                                },
                            ]
                        }
                    },
                    {
                        "$project": {
                            "booking_ref": 1,
                            "element": 1,
                            "status": 1,
                            "currency_code": 1,
                            "amount": 1,
                            "description": 1,
                            "client_id": 1,
                        }
                    },
                    {
                        "$group": {
                            "_id": "$currency_code",
                            "data": {"$push": "$$ROOT"},
                        },
                    },
                ]
            )
        )
        automated_transactions = []
        for group in grouped_transactions:
            max_transaction = {**max(group["data"], key=lambda x: x["amount"]), "description": "Highest Amount"}
            min_transaction = {**min(group["data"], key=lambda x: x["amount"]), "description": "Lowest Amount"}
            median_transactions = self.__find_median_transactions(group["data"])
            automated_transactions.extend([max_transaction, min_transaction, *median_transactions])
        claims_file_details = list(
            get_db().claims_file_details.find(
                {
                    "claims_id": ObjectId(claims_id),
                    "deleted": False,
                    "$or": [
                        {"booking_ref": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                        {"element": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                        {"currency_code": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                    ],
                },
                projection={
                    "booking_ref": 1,
                    "element": 1,
                    "status": 1,
                    "currency_code": 1,
                    "amount": 1,
                    "description": 1,
                    "client_id": 1,
                },
            )
        )
        total_transactions = len(claims_file_details)
        remaining_transactions = list(
            filterfalse(
                lambda x: x
                in [
                    {k: v for k, v in transaction.items() if k != "description"}
                    for transaction in automated_transactions
                ],
                claims_file_details,
            )
        )

        if len(remaining_transactions) > 0:
            random_transactions = random.sample(remaining_transactions, min(3, len(remaining_transactions)))
            random_transactions_with_description = [
                {**transaction, "description": "Random Amount"} for transaction in random_transactions
            ]
            automated_transactions.extend(random_transactions_with_description)
        automated_transactions.sort(key=lambda x: x["_id"])
        unique_transactions = []
        for key, value in groupby(automated_transactions, key=lambda x: x["_id"]):
            value = list(value)
            description = map(lambda x: x["description"], value)
            transaction = value[0]
            transaction.update({"description": ", ".join(description)})
            unique_transactions.append(transaction)
        modified_unique_transactions = [
            {**ClaimsFileDetailsSchema().dump(transaction), "description": transaction["description"]}
            for transaction in unique_transactions
        ]
        final_transaction_list = []
        for item in modified_unique_transactions:
            count = get_db().claims_file_details.count_documents(
                {
                    "client_id": ObjectId(item["clientId"]),
                    "booking_ref": item["bookingRef"],
                    "deleted": False,
                },
                collation={"locale": "en", "strength": 1},
            )
            duplicates = get_db().claims_file_details.count_documents(
                {
                    "client_id": ObjectId(item["clientId"]),
                    "booking_ref": item["bookingRef"],
                    "currency_code": item["currencyCode"],
                    "amount": item["amount"],
                    "deleted": False,
                },
                collation={"locale": "en", "strength": 1},
            )
            basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(item["clientId"])})
            item["clientId"] = basic_info["c_id"]
            final_transaction_list.append({**item, "count": count, "duplicates": duplicates})
        return {"transactions": final_transaction_list, "totalTransactions": total_transactions}

    def update_claim_transaction(self, transactionId, data, user_id):
        claim_update = ClaimsFileDetailsSchema().load(data)
        try:
            existing_transaction = get_db().claims_file_details.find_one({"_id": ObjectId(transactionId)})
        except InvalidId:
            raise ServiceException("Invalid transaction id")
        if not existing_transaction:
            raise ServiceException("Transaction not found")
        claims_metadata = get_db().claims_metadata.find_one({"_id": existing_transaction["claims_id"]})
        if claims_metadata["status"] in ("Scanning", "Updating Status"):
            raise ServiceException("No action is allowed while the file is under modification by system.")
        if claims_metadata["status"] == "Authorised":
            user = get_db().user.find_one({"user_id": user_id}, projection={"role": 1, "_id": 0})
            if user["role"] == "ptt-user":
                raise ServiceException("No action is allowed since the file is authorised.")
        trust_type = None
        basic_info = get_db().client_basic_info.find_one({"_id": existing_transaction["client_id"]})
        if basic_info.get("type_of_trust_account"):
            type_of_trust = get_db().lookup_trust_type.find_one({"_id": basic_info["type_of_trust_account"]})
            trust_type = type_of_trust["name"]
        if trust_type == "ATOL Escrow" or trust_type == "Escrow Trigger":
            if str(existing_transaction["client_id"]) in [
                current_app.config["MAJOR_TRAVEL"],
                current_app.config["WST_TRAVEL"],
                current_app.config["ANGLIA_TOURS"],
            ]:
                if (
                    claim_update.get("booking_date")
                    and existing_transaction.get("booking_date")
                    and (
                        (
                            datetime.strptime(existing_transaction.get("booking_date"), "%Y-%m-%d")
                            < datetime.strptime("2022-04-01", "%Y-%m-%d")
                            <= datetime.strptime(claim_update.get("booking_date"), "%Y-%m-%d")
                        )
                        or (
                            datetime.strptime(existing_transaction.get("booking_date"), "%Y-%m-%d")
                            >= (datetime.strptime("2022-04-01", "%Y-%m-%d"))
                            > datetime.strptime(claim_update.get("booking_date"), "%Y-%m-%d")
                        )
                    )
                ):
                    raise ServiceException("Transaction is not editable for the given booking date")
            if data.get("amount"):
                escrow_multiplier = (
                    existing_transaction["escrow_multiplier"]
                    if existing_transaction.get("escrow_multiplier")
                    else current_app.config["ESCROW_MULTIPLIER"]
                )
                claim_update["original_amount"] = round((data["amount"] / escrow_multiplier), 2)
        with get_db().client.start_session() as session:
            with session.start_transaction():
                now = datetime.utcnow()
                updated_transaction = get_db().claims_file_details.find_one_and_update(
                    {"_id": ObjectId(transactionId)},
                    {"$set": {**claim_update, "updated_at": now}},
                    return_document=ReturnDocument.AFTER,
                )
                file_date = claims_metadata["claim_files"][-1]["file_date"]
                file_id = claims_metadata["claim_files"][-1]["file_id"]

                if data.get("amount") or data.get("status"):
                    self.__handle_claims_transaction_amount_and_status_change(
                        existing_transaction,
                        updated_transaction,
                        file_date,
                        file_id,
                        session,
                    )
                elif "check" in data:
                    self.__handle_claims_transaction_checks_update(
                        updated_transaction, existing_transaction.get("check"), data["check"], file_id, session
                    )
                self.__handle_anomaly_recalculation(updated_transaction, existing_transaction, session)

    def get_claim_anomalies(self, claims_id, query, page, size, sort_key, sort_order):
        try:
            claims_metadata = get_db().claims_metadata.find_one({"_id": ObjectId(claims_id)})
        except InvalidId:
            raise ServiceException("invalid claimId")

        page = int(page or 1)
        size = int(size or 10)
        offset = (page - 1) * size

        data = list(
            get_db().anomaly_claims.aggregate(
                [
                    {
                        "$match": {
                            "$and": [
                                {
                                    "deleted": False,
                                    "claims_id": ObjectId(claims_id),
                                    "$or": [
                                        {"booking_ref": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                        {"status": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                        {"anomaly_type": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                    ],
                                },
                            ]
                        }
                    },
                    {
                        "$lookup": {
                            "from": "claims_file_details",
                            "localField": "transaction_id",
                            "foreignField": "_id",
                            "as": "claim_transaction",
                        },
                    },
                    {"$unwind": "$claim_transaction"},
                    {
                        "$group": {
                            "_id": {
                                "anomaly_type": "$anomaly_type",
                                "booking_ref": "$booking_ref",
                                "client_id": "$client_id",
                            },
                            "amount": {"$sum": "$claim_transaction.amount"},
                            "anomaly_ids": {"$push": {"$toString": "$_id"}},
                            "modified_by": {"$first": "$modified_by"},
                            "status": {"$addToSet": "$status"},
                            "count": {"$count": {}},
                            "created_at": {"$min": "$created_at"},
                            "updated_at": {"$min": "$updated_at"},
                        }
                    },
                    {
                        "$lookup": {
                            "from": "trust_fund_v2",
                            "localField": "_id.booking_ref",
                            "foreignField": "booking_ref",
                            "pipeline": [
                                {
                                    "$match": {"client_id": claims_metadata["client_id"]},
                                }
                            ],
                            "as": "trust",
                        },
                    },
                    {"$unwind": "$trust"},
                    {
                        "$lookup": {
                            "from": "lookup_currency",
                            "localField": "trust.currency_code",
                            "foreignField": "code",
                            "as": "currency",
                        },
                    },
                    {"$sort": {sort_key: sort_order}},
                    {
                        "$project": {
                            "_id": 0,
                            "status": "$status",
                            "bookingRef": "$_id.booking_ref",
                            "anomalyIds": "$anomaly_ids",
                            "count": "$count",
                            "anomalyType": "$_id.anomaly_type",
                            "leadPassenger": "$trust.lead_pax",
                            "bookingDate": "$trust.booking_date",
                            "dateOfTravel": "$trust.departure_date",
                            "dateOfReturn": "$trust.return_date",
                            "balanceInTrust": "$trust.balance",
                            "currency_code": "$currency.code",
                            "symbol": "$currency.symbol",
                            "clientId": "$_id.client_id",
                            "modifiedBy": "$modified_by",
                            "totalClaim": "$amount",
                        }
                    },
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ],
                collation={"locale": "en", "strength": 1},
                allowDiskUse=True,
            )
        )
        anomaly_count = get_db().anomaly_claims.aggregate(
            [
                {
                    "$match": {
                        "$and": [
                            {
                                "deleted": False,
                                "status": "Unresolved",
                                "claims_id": ObjectId(claims_id),
                                "$or": [
                                    {"booking_ref": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                    {"status": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                    {"anomaly_type": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                ],
                            },
                        ]
                    }
                },
                {
                    "$group": {
                        "_id": {
                            "anomaly_type": "$anomaly_type",
                            "booking_ref": "$booking_ref",
                            "client_id": "$client_id",
                        },
                        "status": {"$addToSet": "$status"},
                    }
                },
                {"$count": "total_unresolved"},
            ],
        )
        anomaly_count = next(anomaly_count, {"total_unresolved": 0})["total_unresolved"]
        ptt_users = auth_service.ptt_users_list()
        data_list = []
        for item in data[0]["data"]:
            item["totalClaim"] = round(item["totalClaim"], 2)
            item["balanceInTrust"] = item["balanceInTrust"] if item.get("balanceInTrust") else 0
            if item["status"] == ["Resolved"]:
                item["status"] = "Resolved"
            else:
                item["status"] = "Unresolved"

            user = next(filter(lambda x: x["userId"] == item.get("modifiedBy"), ptt_users), {})
            item["modifiedBy"] = user.get("name") or user.get("email")
            basic_info = get_db().client_basic_info.find_one({"_id": item["clientId"]})
            item["clientId"] = basic_info["c_id"]

            data_list.append(item)

        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)
        response = {
            "content": data_list,
            "pageNumber": page,
            "numberOfElements": len(data_list),
            "totalElements": total_elements,
            "totalPages": total_pages,
            "anomalyCount": anomaly_count,
        }
        return response

    def claim_get_transaction(self, transaction_id, user_id):
        try:
            transaction = get_db().claims_file_details.find_one(
                {"_id": ObjectId(transaction_id)},
                projection={
                    "_id": 0,
                },
            )
            user = get_db().user.find_one({"user_id": user_id}, projection={"clients": 1, "_id": 0})
            if transaction.get("client_id") not in user["clients"]:
                abort(403)
        except InvalidId:
            raise ServiceException("invalid transactionId")
        if not transaction:
            raise ServiceException("Transaction not found")

        claims__transaction = ClaimsFileDetailsSchema().dump(transaction)

        return claims__transaction

    def claim_testing(self, user_id, data):
        query = data.get("query")
        client = data.get("client")
        from_date = data.get("fromDate")
        to_date = data.get("toDate")
        date = data.get("date")
        from_date = datetime.fromisoformat(from_date) if from_date else datetime.min
        to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time()) if to_date else datetime.max
        page = data["page"] or 1
        size = data["size"] or 10
        offset = (page - 1) * size

        client_match_condition = [{"client_id": ObjectId(client)}] if client else []
        match_condition = [
            *client_match_condition,
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        date if date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        date if date else datetime.max.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$claim_files.submitted_date", -1]},
                        from_date,
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$claim_files.submitted_date", -1]},
                        to_date,
                    ]
                }
            },
        ]
        data = list(
            get_db().claims_metadata.aggregate(
                [
                    {"$match": {"status": {"$nin": ["Cancelled", "Cancelled by System"]}}},
                    {"$addFields": {"user_id": user_id}},
                    {"$match": {"$and": match_condition}},
                    {
                        "$lookup": {
                            "from": "user",
                            "localField": "user_id",
                            "foreignField": "user_id",
                            "let": {"client_id": "$client_id"},
                            "pipeline": [{"$match": {"$expr": {"$in": ["$$client_id", "$clients"]}}}],
                            "as": "user",
                        }
                    },
                    {"$unwind": "$user"},
                    {
                        "$lookup": {
                            "from": "client_basic_info",
                            "localField": "client_id",
                            "foreignField": "_id",
                            "as": "client",
                        }
                    },
                    {"$unwind": "$client"},
                    {
                        "$match": {
                            "$or": [
                                {"client.full_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                {"client.friendly_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                {"client.c_id": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                            ]
                        }
                    },
                    {
                        "$lookup": {
                            "from": "claim_testing",
                            "localField": "_id",
                            "foreignField": "claims_id",
                            "as": "claim_test",
                        }
                    },
                    {"$unwind": {"path": "$claim_test", "preserveNullAndEmptyArrays": True}},
                    {
                        "$project": {
                            "_id": "$_id",
                            "clientId": "$client.c_id",
                            "clientName": "$client.full_name",
                            "submittedDate": {"$slice": ["$claim_files.submitted_date", -1]},
                            "fileDate": {"$slice": ["$claim_files.file_date", -1]},
                            "status": "$status",
                            "count": {"$slice": ["$claim_files.item_count", -1]},
                            "items": {"$slice": ["$claim_files.claim_total", -1]},
                            "originalClaim": "$claim_test.original_claim",
                            "reasons": "$claim_test.reasons",
                        }
                    },
                    {"$sort": {"submittedDate": 1}},
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ],
                collation={"locale": "en", "strength": 1},
                allowDiskUse=True,
            )
        )
        content = []
        for row in data[0]["data"]:
            items = [
                [
                    {
                        "currency": currency,
                        "revisedClaim": amount,
                        "originalClaim": row["originalClaim"][currency] if row.get("originalClaim") else None,
                    }
                    for currency, amount in item.items()
                    if row.get("originalClaim") and round(row.get("originalClaim")[currency]) != round(amount)
                ]
                for item in row.get("items")
                if row.get("items")
            ]

            if len(items[0]) != 0:
                claim_dict = {
                    "_id": str(row["_id"]),
                    "clientId": row["clientId"],
                    "clientName": row["clientName"],
                    "submittedDate": row["submittedDate"][0].isoformat(),
                    "fileDate": row["fileDate"][0],
                    "status": row.get("status"),
                    "reasons": row.get("reasons"),
                    "items": items[0],
                }

                content.append(claim_dict)

        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)
        empty = True if not content else False
        first = True if page == 1 else False
        last = True if page == ceil((total_elements) / size) else False

        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def update_claim_testing(self, claims_id, data):
        now = datetime.utcnow()
        data = ClaimTestingSchema().load({**data, "claimsId": ObjectId(claims_id)})
        get_db().claim_testing.update_one(
            {"claims_id": ObjectId(claims_id)},
            {"$setOnInsert": {"created_at": now}, "$set": {**data, "updated_at": now}},
            upsert=True,
        )

    def claim_escrow_search_summary(self, data):
        from_date = data.get("fromDate")
        to_date = data.get("toDate")
        page = data["page"] or 1
        size = data["size"] or 10
        offset = (page - 1) * size

        Iglu_Escrow = current_app.config.get("IGLU_ESCROW")

        data_list = list(
            get_db().client_basic_info.aggregate(
                [
                    {
                        "$lookup": {
                            "from": "lookup_trust_type",
                            "localField": "type_of_trust_account",
                            "foreignField": "_id",
                            "as": "trust_type",
                        }
                    },
                    {
                        "$match": {
                            "is_disabled": False,
                            "trust_type.name": "ATOL Escrow",
                            "_id": {"$ne": ObjectId(Iglu_Escrow)},
                        }
                    },
                    {
                        "$lookup": {
                            "from": "banking_metadata",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "pipeline": [
                                {"$project": {"banking_file": {"$arrayElemAt": ["$banking_files", -1]}}},
                                {
                                    "$match": {
                                        "$and": [
                                            {
                                                "$expr": {
                                                    "$gte": [
                                                        "$banking_file.file_date",
                                                        from_date,
                                                    ]
                                                },
                                            },
                                            {"$expr": {"$lte": ["$banking_file.file_date", to_date]}},
                                            {"banking_file.status": {"$ne": "Authorised"}},
                                        ]
                                    },
                                },
                                {"$project": {"deposit": {"$objectToArray": "$banking_file.deposit"}}},
                                {"$unwind": "$deposit"},
                                {"$group": {"_id": "$deposit.k", "total_banking": {"$sum": "$deposit.v"}}},
                            ],
                            "as": "banking",
                        }
                    },
                    {"$unwind": {"path": "$banking", "preserveNullAndEmptyArrays": True}},
                    {
                        "$lookup": {
                            "from": "claims_metadata",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "let": {"currency": "$banking._id"},
                            "pipeline": [
                                {
                                    "$project": {
                                        "claim_file": {"$arrayElemAt": ["$claim_files", -1]},
                                        "client_id": "$client_id",
                                        "frequency": 1,
                                    }
                                },
                                {
                                    "$match": {
                                        "$and": [
                                            {
                                                "$expr": {
                                                    "$gte": [
                                                        "$claim_file.file_date",
                                                        from_date,
                                                    ]
                                                },
                                            },
                                            {"$expr": {"$lte": ["$claim_file.file_date", to_date]}},
                                            {"claim_file.status": {"$ne": "Authorised"}},
                                        ]
                                    },
                                },
                                {
                                    "$project": {
                                        "claim_id": "$_id",
                                        "file_id": "$claim_file.file_id",
                                        "claim_total": {"$objectToArray": "$claim_file.claim_total"},
                                        "status": "$claim_file.status",
                                        "notes": "$claim_file.notes",
                                        "client_id": "$client_id",
                                        "submitted_date": "$claim_file.submitted_date",
                                        "frequency": "$frequency",
                                    }
                                },
                                {"$unwind": "$claim_total"},
                                {
                                    "$group": {
                                        "_id": "$claim_total.k",
                                        "claims_id": {"$first": "$_id"},
                                        "file_id": {"$first": "$file_id"},
                                        "client_id": {"$first": "$client_id"},
                                        "total_claim": {"$sum": "$claim_total.v"},
                                        "status": {"$last": "$status"},
                                        "notes": {"$first": "$notes"},
                                        "submitted_date": {"$last": "$submitted_date"},
                                        "frequency": {"$first": "$frequency"},
                                    }
                                },
                            ],
                            "as": "claims",
                        }
                    },
                    {"$unwind": {"path": "$claims", "preserveNullAndEmptyArrays": True}},
                    {
                        "$group": {
                            "_id": {"client_id": "$claims.client_id", "currency": "$claims._id"},
                            "client_id": {"$first": "$claims.client_id"},
                            "claims_id": {"$first": "$claims.claims_id"},
                            "file_id": {"$first": "$claims.file_id"},
                            "c_id": {"$first": "$c_id"},
                            "full_name": {"$first": "$full_name"},
                            "status": {"$last": "$claims.status"},
                            "notes": {"$last": "$claims.notes"},
                            "submitted_date": {"$last": "$claims.submitted_date"},
                            "frequency": {"$last": "$claims.frequency"},
                            "claim_amount": {"$first": "$claims.total_claim"},
                            "banking_amount": {
                                "$sum": {
                                    "$cond": [
                                        {"$eq": ["$claims._id", "$banking._id"]},
                                        "$banking.total_banking",
                                        0,
                                    ]
                                },
                            },
                        }
                    },
                    {"$addFields": {"amount": {"$subtract": ["$banking_amount", "$claim_amount"]}}},
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$lt": ["$amount", 0]},
                                ]
                            }
                        },
                    },
                    {
                        "$group": {
                            "_id": "$_id",
                            "client_id": {"$first": "$_id.client_id"},
                            "claims_id": {"$first": "$claims_id"},
                            "file_id": {"$first": "$file_id"},
                            "c_id": {"$first": "$c_id"},
                            "full_name": {"$first": "$full_name"},
                            "currency": {"$addToSet": "$_id.currency"},
                            "amount": {"$addToSet": "$amount"},
                            "status": {"$last": "$status"},
                            "notes": {"$last": "$notes"},
                            "submitted_date": {"$last": "$submitted_date"},
                            "frequency": {"$last": "$frequency"},
                        }
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "clientId": "$client_id",
                            "claimsId": "$claims_id",
                            "fileId": "$file_id",
                            "cId": "$c_id",
                            "clientName": "$full_name",
                            "status": "$status",
                            "notes": "$notes",
                            "currency": "$currency",
                            "amount": {"$map": {"input": "$amount", "as": "amt", "in": {"$abs": "$$amt"}}},
                            "submitted_date": "$submitted_date",
                            "frequency": "$frequency",
                        },
                    },
                    {"$unwind": "$currency"},
                    {"$unwind": "$amount"},
                    {
                        "$group": {
                            "_id": "$clientId",
                            "clientId": {"$first": "$clientId"},
                            "claimsId": {"$first": "$claimsId"},
                            "fileId": {"$first": "$fileId"},
                            "cId": {"$first": "$cId"},
                            "clientName": {"$first": "$clientName"},
                            "currency": {"$addToSet": "$currency"},
                            "amount": {"$addToSet": "$amount"},
                            "status": {"$last": "$status"},
                            "notes": {"$last": "$notes"},
                            "submitted_date": {"$last": "$submitted_date"},
                            "frequency": {"$last": "$frequency"},
                        }
                    },
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ]
            )
        )
        content_dict = {}
        for row in data_list[0]["data"]:
            client_id = row["clientId"]
            client_name = row["clientName"]
            currencies = row.get("currency")
            amounts = row.get("amount")
            items = []
            for currency, amount in zip(currencies, amounts):
                item = {"currency": currency, "amount": amount}
                items.append(item)
            if (client_id, client_name) not in content_dict:
                content_dict[(client_id, client_name)] = {
                    "clientId": str(row.get("clientId")),
                    "claimsId": str(row.get("claimsId")),
                    "fileId": row.get("fileId"),
                    "clientName": client_name,
                    "cId": row.get("cId"),
                    "frequency": row.get("frequency"),
                    "submittedDate": row.get("submitted_date"),
                    "element": "Excess of Funds",
                    "items": items,
                    "notes": row.get("notes"),
                    "status": row.get("status"),
                }
            else:
                content_dict[(client_id, client_name)]["items"].extend(items)
        content = sorted(list(content_dict.values()), key=lambda x: (x["clientId"], x["clientName"]))
        for item in content:
            item["items"] = sorted(item["items"], key=lambda x: x["currency"])
            item["items"] = [{"amount": x["amount"], "currency": x["currency"]} for x in item["items"]]

        total_elements = 0
        total_pages = 0
        if data_list[0]["metadata"]:
            total_elements = data_list[0]["metadata"][0]["total"]
            total_pages = ceil((data_list[0]["metadata"][0]["total"]) / size)
        empty = True if not content else False
        first = True if page == 1 else False
        last = True if page == ceil((total_elements) / size) else False

        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def update_notes(self, claims_id, file_id, new_notes):
        filter = {"_id": ObjectId(claims_id), "claim_files.file_id": file_id}
        update = {"$set": {"claim_files.$.notes": new_notes}}
        result = get_db().claims_metadata.update_one(filter, update)

        if result.modified_count == 1:
            return {"message": "Notes updated successfully"}
        else:
            return {"message": "No updates applied"}, 200


claim_service = ClaimService()
