import os
from flask import json
from flaskr.helpers.boto3_handler import call_lambda
from flaskr.models import get_db
from datetime import datetime, timedelta
from flaskr.helpers.date_util import monthly_dates
from flask import current_app, send_file
import xlsxwriter
from flaskr.helpers.boto3_handler import download_file, upload_file
import uuid
from bson import ObjectId
from flaskr.models.dashboard.risk_exposure import DashboardRiskExposureSchema
from flaskr.helpers import round
from flask import abort
from config import Config


class DashboardService:
    def __ytd_clients_report(self, year, period):

        client_list = list(get_db().client_basic_info.find())

        dates = monthly_dates(year)
        counts = []
        for start, end in dates:
            filtered_res = list(
                filter(
                    lambda x: x["created_at"] >= datetime.strptime(start, "%d-%m-%Y")
                    and x["created_at"] <= datetime.strptime(end, "%d-%m-%Y"),
                    client_list,
                )
            )

            start = datetime.strptime(start, "%d-%m-%Y")

            data = {"month": start.strftime("%B"), "count": len(filtered_res), "period": period}
            counts.append(data)

        return counts

    def __ytd_bookings_report(self, year, client_id, currency_code, period):
        dates = monthly_dates(year)
        counts = []
        for start, end in dates:
            start = datetime.strptime(start, "%d-%m-%Y")

            end = datetime.strptime(end, "%d-%m-%Y")
            bookings = list(
                get_db().banking_metadata.aggregate(
                    [
                        {"$addFields": {"recent_file": {"$arrayElemAt": ["$banking_files", -1]}}},
                        {
                            "$addFields": {
                                "file_datetime": {"$dateFromString": {"dateString": "$recent_file.file_date"}}
                            }
                        },
                        {
                            "$lookup": {
                                "from": "banking_file_details",
                                "localField": "recent_file.file_id",
                                "foreignField": "file_id",
                                "as": "banks",
                            }
                        },
                        {"$unwind": "$banks"},
                        {
                            "$match": {
                                "$and": [
                                    {"client_id": client_id},
                                    {"banks.currency_code": currency_code},
                                    {"banks.deleted": False},
                                    {"file_datetime": {"$gte": start, "$lte": end}},
                                ]
                            }
                        },
                        {"$project": {"booking_ref": "$banks.booking_ref", "client_id": "$banks.client_id", "_id": 0}},
                        {
                            "$unionWith": {
                                "coll": "claims_metadata",
                                "pipeline": [
                                    {"$addFields": {"recent_file": {"$arrayElemAt": ["$claim_files", -1]}}},
                                    {
                                        "$addFields": {
                                            "file_datetime": {
                                                "$dateFromString": {"dateString": "$recent_file.file_date"}
                                            }
                                        }
                                    },
                                    {
                                        "$lookup": {
                                            "from": "claims_file_details",
                                            "localField": "recent_file.file_id",
                                            "foreignField": "file_id",
                                            "as": "claims",
                                        }
                                    },
                                    {"$unwind": "$claims"},
                                    {
                                        "$match": {
                                            "$and": [
                                                {"client_id": client_id},
                                                {"claims.currency_code": currency_code},
                                                {"claims.deleted": False},
                                                {"file_datetime": {"$gte": start, "$lte": end}},
                                            ]
                                        }
                                    },
                                    {
                                        "$project": {
                                            "booking_ref": "$claims.booking_ref",
                                            "client_id": "$claims.client_id",
                                            "_id": 0,
                                        }
                                    },
                                ],
                            }
                        },
                        {"$group": {"_id": "$booking_ref", "count": {"$count": {}}}},
                        {"$count": "count"},
                    ]
                )
            )
            if bookings:
                result = bookings[0]["count"]
            else:
                result = 0

            data = {"month": start.strftime("%B"), "count": result, "period": period}
            counts.append(data)
        return counts

    def __ytd_payments(self, year, period, client_id, currency):

        dates = monthly_dates(year)
        amount = []
        for start, end in dates:
            start = datetime.strptime(start, "%d-%m-%Y")
            end = datetime.strptime(end, "%d-%m-%Y")
            banking = list(
                get_db().banking_metadata.aggregate(
                    [
                        {"$addFields": {"recent_file": {"$arrayElemAt": ["$banking_files", -1]}}},
                        {
                            "$addFields": {
                                "file_datetime": {"$dateFromString": {"dateString": "$recent_file.file_date"}}
                            }
                        },
                        {
                            "$lookup": {
                                "from": "banking_file_details",
                                "localField": "recent_file.file_id",
                                "foreignField": "file_id",
                                "as": "bank",
                            }
                        },
                        {"$unwind": "$bank"},
                        {
                            "$match": {
                                "$and": [
                                    {"client_id": client_id},
                                    {"bank.currency_code": currency},
                                    {"file_datetime": {"$gte": start, "$lte": end}},
                                    {"bank.deleted": False},
                                ]
                            }
                        },
                        {"$project": {"payments_amount": "$bank.amount", "_id": 0}},
                        {
                            "$group": {
                                "_id": None,
                                "paymentsAmount": {"$sum": "$payments_amount"},
                            }
                        },
                    ]
                )
            )
            data = {
                "month": start.strftime("%B"),
                "payments": banking[0]["paymentsAmount"] if banking else 0,
                "period": period,
            }
            amount.append(data)
        return amount

    def __ytd_claims(self, year, period, client_id, currency):

        dates = monthly_dates(year)
        amount = []
        for start, end in dates:
            start = datetime.strptime(start, "%d-%m-%Y")
            end = datetime.strptime(end, "%d-%m-%Y")
            claim = list(
                get_db().claims_metadata.aggregate(
                    [
                        {"$addFields": {"recent_file": {"$arrayElemAt": ["$claim_files", -1]}}},
                        {
                            "$addFields": {
                                "file_datetime": {"$dateFromString": {"dateString": "$recent_file.file_date"}}
                            }
                        },
                        {
                            "$lookup": {
                                "from": "claims_file_details",
                                "localField": "recent_file.file_id",
                                "foreignField": "file_id",
                                "as": "claims",
                            }
                        },
                        {"$unwind": "$claims"},
                        {
                            "$match": {
                                "$and": [
                                    {"client_id": client_id},
                                    {"claims.currency_code": currency},
                                    {"file_datetime": {"$gte": start, "$lte": end}},
                                    {"claims.deleted": False},
                                ]
                            }
                        },
                        {"$project": {"claims_amount": "$claims.amount", "_id": 0}},
                        {
                            "$group": {
                                "_id": None,
                                "claimsAmount": {"$sum": "$claims_amount"},
                            }
                        },
                    ]
                )
            )
            data = {
                "month": start.strftime("%B"),
                "claims": claim[0]["claimsAmount"] if claim else 0,
                "period": period,
            }
            amount.append(data)
        return amount

    def ytd_clients(self, current_year, previous_year):
        current_data = self.__ytd_clients_report(current_year, "Current")
        previous_data = self.__ytd_clients_report(previous_year, "Previous")
        response = []
        for current, previous in zip(current_data, previous_data):
            data = {
                "month": current["month"],
                "currentYearCount": current["count"],
                "previousYearCount": previous["count"],
            }
            response.append(data)
        return response

    def dashboard_details(self, currency_code, from_date, to_date):

        start_date = datetime.fromisoformat(from_date) if from_date else datetime.min
        end_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time()) if to_date else datetime.max
        total_clients = get_db().client_basic_info.count_documents({
            "created_at": {"$gte": start_date, "$lte": end_date}, "is_disabled": False
        })
        filterd_clients = get_db().client_basic_info.count_documents(
            {"created_at": {"$gte": start_date, "$lte": end_date}, "is_disabled": False}
        )
        active_client_ids = list(
            get_db().client_basic_info.distinct("_id", {"created_at": {"$gte": start_date, "$lte": end_date}, "is_disabled": False})
        )
        banking = get_db().anomaly_banking.count_documents({"status": "Unresolved", "deleted": False})

        claims = get_db().anomaly_claims.count_documents({"status": "Unresolved", "deleted": False})
        unhandled_anomalies = banking + claims

        banking_match_condition = [
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        to_date if to_date else datetime.today().strftime("%Y-%m-%d"),
                    ]
                }
            },
            {"client_id": {"$in": active_client_ids}},
        ]
        claim_match_condition = [
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        to_date if to_date else datetime.today().strftime("%Y-%m-%d"),
                    ]
                }
            },
            {"client_id": {"$in": active_client_ids}},
        ]
        total_payments = list(
            get_db().banking_metadata.aggregate(
                [
                    {
                        "$match": {
                            "$and": banking_match_condition,
                            "status": {"$nin": ["Cancelled", "Cancelled by System"]},
                        }
                    },
                    {
                        "$project": {
                            "amount": {"$last": f"$banking_files.deposit.{currency_code}"},
                            "_id": 0,
                        }
                    },
                    {"$group": {"_id": None, "totalAmount": {"$sum": "$amount"}}},
                ]
            )
        )
        total_payments = total_payments[0]["totalAmount"] if total_payments else 0
        total_claims = list(
            get_db().claims_metadata.aggregate(
                [
                    {
                        "$match": {
                            "$and": claim_match_condition,
                            "status": {"$nin": ["Cancelled", "Cancelled by System"]},
                        }
                    },
                    {
                        "$project": {
                            "amount": {"$last": f"$claim_files.claim_total.{currency_code}"},
                            "_id": 0,
                        }
                    },
                    {"$group": {"_id": None, "totalAmount": {"$sum": "$amount"}}},
                ]
            )
        )
        total_claims = total_claims[0]["totalAmount"] if total_claims else 0

        response = {
            "totalClients": total_clients,
            "unhandledAnomalies": unhandled_anomalies,
            "totalPayments": total_payments,
            "totalClaims": total_claims,
            "clientsCount": filterd_clients,
        }
        return response

    def ytd_bookings(self, current_year, previous_year, client_id, currency_code, user_id):
        user = get_db().user.find_one({"user_id": user_id}, projection={"clients": 1, "_id": 0})
        if ObjectId(client_id) in user["clients"]:
            current_data = self.__ytd_bookings_report(current_year, ObjectId(client_id), currency_code, "Current")
            previous_data = self.__ytd_bookings_report(previous_year, ObjectId(client_id), currency_code, "Previous")
            response = []
            for current, previous in zip(current_data, previous_data):
                data = {
                    "month": current["month"],
                    "currentYearCount": current["count"],
                    "previousYearCount": previous["count"],
                }
                response.append(data)
            return response
        else:
            abort(403)

    def ytd_payments(self, current_year, previous_year, client_id, currency, user_id):
        user = get_db().user.find_one({"user_id": user_id}, projection={"clients": 1, "_id": 0})
        if ObjectId(client_id) in user["clients"]:
            current_data = self.__ytd_payments(current_year, "Current", ObjectId(client_id), currency)
            previous_data = self.__ytd_payments(previous_year, "Previous", ObjectId(client_id), currency)
            response = []
            for current, previous in zip(current_data, previous_data):
                data = {
                    "month": current["month"],
                    "currentYearCount": current["payments"],
                    "previousYearCount": previous["payments"],
                }
                response.append(data)
            return response
        else:
            abort(403)

    def ytd_claims(self, current_year, previous_year, client_id, currency, user_id):
        user = get_db().user.find_one({"user_id": user_id}, projection={"clients": 1, "_id": 0})
        if ObjectId(client_id) in user["clients"]:
            current_data = self.__ytd_claims(current_year, "Current", ObjectId(client_id), currency)
            previous_data = self.__ytd_claims(previous_year, "Previous", ObjectId(client_id), currency)
            response = []
            for current, previous in zip(current_data, previous_data):
                data = {
                    "month": current["month"],
                    "currentYearCount": current["claims"],
                    "previousYearCount": previous["claims"],
                }
                response.append(data)
            return response
        else:
            abort(403)

    def ytd_clients_category(self):
        total_clients = list(
            get_db().client_basic_info.aggregate(
                [
                    {"$match": {"is_disabled": False}},
                    {
                        "$group": {
                            "_id": "$type_of_trust_account",
                            "count": {"$sum": 1},
                        }
                    },
                    {
                        "$lookup": {
                            "from": "lookup_trust_type",
                            "localField": "_id",
                            "foreignField": "_id",
                            "as": "trust",
                        }
                    },
                    {"$unwind": {"path": "$trust", "preserveNullAndEmptyArrays": True}},
                    {
                        "$project": {
                            "clients": {"$ifNull": ["$count", 0]},
                            "typeOfTrust": {"$ifNull": ["$trust.name", "Unassigned"]},
                            "_id": 0,
                        }
                    },
                ]
            )
        )
        return total_clients

    def risk_exposure_refresh(self, user_id):
        call_lambda(
            f"risk-exposure-insight-lambda-{os.environ['ENVIRONMENT']}",
            json.dumps({"userId": user_id}),
        )

    def risk_exposure(self, user_id, data, resolution):
        from_date = datetime.combine(datetime.today() - timedelta(days=resolution - 1), datetime.min.time())
        to_date = datetime.combine(datetime.today(), datetime.min.time())
        user = get_db().user.find_one({"user_id": user_id}, projection={"_id": 0, "clients": 1})
        clients = user["clients"]
        if data.get("client"):
            clients = [ObjectId(data.get("client"))]
        if data.get("fromDate"):
            from_date = datetime.fromisoformat(data["fromDate"])
        if data.get("toDate"):
            to_date = datetime.combine(datetime.fromisoformat(data.get("toDate")), datetime.min.time())
        date_difference = (to_date - from_date).days
        date_lis = []
        if date_difference == 0:
            date_lis = [to_date]
        if date_difference < resolution:
            date_lis = [from_date + timedelta(days=i) for i in range(0, date_difference + 1)]
        else:
            days = date_difference / resolution
            date_dif = 0
            for i in range(resolution - 2):
                date_dif += days
                date = from_date + timedelta(days=date_dif)
                date_lis.append(date)
            date_lis.insert(0, from_date)
            date_lis.insert(-1, to_date)
            date_lis = [datetime.combine(date, datetime.min.time()) for date in date_lis]
            date_lis = list(set(date_lis))
        date_lis.sort()
        match_condition = {
            "client_id": {"$in": clients},
            "date": {"$in": date_lis},
            "currency_code": data.get("currency"),
        }

        data = list(
            get_db().dashboard_risk_exposure.aggregate(
                [
                    {"$match": match_condition},
                    {"$sort": {"date": 1}},
                    {"$group": {"_id": {"date": "$date"}, "claim_amount": {"$sum": "$claim_amount"}}},
                    {"$sort": {"_id.date": 1}},
                    {
                        "$project": {
                            "_id": 0,
                            "date": "$_id.date",
                            "claim_amount": "$claim_amount",
                        }
                    },
                ]
            )
        )
        for item in data:
            item["claim_amount"] = round(item["claim_amount"], 2)
        zero_amount_data = [{"date": x, "claim_amount": 0} for x in date_lis if x not in [y["date"] for y in data]]
        data = data + zero_amount_data
        data.sort(key=lambda x: x["date"])

        return [DashboardRiskExposureSchema().dump(item) for item in data]

    def risk_exposure_status(self, user_id):
        user = get_db().user.find_one({"user_id": user_id}, projection={"_id": 0, "clients": 1})
        clients = user["clients"]
        risk_data = list(
            get_db().dashboard_risk_exposure.find({"client_id": {"$in": clients}}).sort("updated_at", -1).limit(1)
        )
        updated_at = risk_data[0]["updated_at"]
        return {"updatedAt": updated_at}

    def movement_of_funds(self, data, user_id):
        currency = data.get("currency")
        clients = get_db().user.find_one({"user_id": user_id}, projection={"clients": 1})["clients"]
        if data.get("clientId"):
            clients = [ObjectId(data["clientId"])]
        from_date = data.get("fromDate")
        to_date = data.get("toDate")
        banking_match_condition = [
            {"client_id": {"$in": clients}, "status": {"$nin": ["Cancelled", "Cancelled by System"]}},
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        to_date if to_date else datetime.today().strftime("%Y-%m-%d"),
                    ]
                }
            },
        ]
        claim_match_condition = [
            {"client_id": {"$in": clients}, "status": {"$nin": ["Cancelled", "Cancelled by System"]}},
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        to_date if to_date else datetime.today().strftime("%Y-%m-%d"),
                    ]
                }
            },
        ]
        total_payments = list(
            get_db().banking_metadata.aggregate(
                [
                    {"$match": {"$and": banking_match_condition}},
                    {
                        "$project": {
                            "amount": {"$last": f"$banking_files.deposit.{currency}"},
                            "_id": 0,
                        }
                    },
                    {"$group": {"_id": None, "totalAmount": {"$sum": "$amount"}}},
                ]
            )
        )
        total_payments = round(total_payments[0]["totalAmount"] if total_payments else 0, 2)
        total_claims = list(
            get_db().claims_metadata.aggregate(
                [
                    {"$match": {"$and": claim_match_condition}},
                    {
                        "$project": {
                            "amount": {"$last": f"$claim_files.claim_total.{currency}"},
                            "_id": 0,
                        }
                    },
                    {"$group": {"_id": None, "totalAmount": {"$sum": "$amount"}}},
                ]
            )
        )
        total_claims = round(total_claims[0]["totalAmount"] if total_claims else 0, 2)
        balance = round(total_payments - total_claims, 2)

        response = {"totalPayments": total_payments, "totalClaims": total_claims, "balance": balance}
        return response

    def exposure_status_trigger(self, user_id):
        call_lambda(
            f"exposure-status-lambda-{os.environ['ENVIRONMENT']}",
            json.dumps({"userId": user_id}),
        )

    def exposure_status(self, user_id, data):
        user = get_db().user.find_one({"user_id": user_id}, projection={"_id": 0, "clients": 1})
        clients = user["clients"]
        if data.get("client"):
            clients = [ObjectId(data.get("client"))]
        match_condition = {
            "client_id": {"$in": clients},
            "currency_code": data.get("currency"),
        }
        response = []
        status_dict = list(
            get_db().dashboard_exposure_status.aggregate(
                [
                    {"$match": match_condition},
                    {
                        "$group": {
                            "_id": None,
                            "open_amount": {"$sum": "$exposure_status.open.amount"},
                            "open_count": {"$sum": "$exposure_status.open.count"},
                            "flown_amount": {"$sum": "$exposure_status.flown.amount"},
                            "flown_count": {"$sum": "$exposure_status.flown.count"},
                            "vouchered_amount": {"$sum": "$exposure_status.vouchered.amount"},
                            "vouchered_count": {"$sum": "$exposure_status.vouchered.count"},
                            "rebooked_amount": {"$sum": "$exposure_status.rebooked.amount"},
                            "rebooked_count": {"$sum": "$exposure_status.rebooked.count"},
                            "refunded_amount": {"$sum": "$exposure_status.refunded.amount"},
                            "refunded_count": {"$sum": "$exposure_status.refunded.count"},
                            "visa_amount": {"$sum": "$exposure_status.expired.visa.amount"},
                            "visa_count": {"$sum": "$exposure_status.expired.visa.count"},
                            "master_card_amount": {"$sum": "$exposure_status.expired.master_card.amount"},
                            "master_card_count": {"$sum": "$exposure_status.expired.master_card.count"},
                            "charge_back_amount": {"$sum": "$exposure_status.charge_back.amount"},
                            "charge_back_count": {"$sum": "$exposure_status.charge_back.count"},
                        }
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "open": {"amount": "$open_amount", "count": "$open_count"},
                            "flown": {"amount": "$flown_amount", "count": "$flown_count"},
                            "vouchered": {"amount": "$vouchered_amount", "count": "$vouchered_count"},
                            "rebooked": {"amount": "$rebooked_amount", "count": "$rebooked_count"},
                            "refunded": {"amount": "$refunded_amount", "count": "$refunded_count"},
                            "expired": {
                                "visa": {"amount": "$visa_amount", "count": "$visa_count"},
                                "masterCard": {"amount": "$master_card_amount", "count": "$master_card_count"},
                            },
                            "chargeBack": {"amount": "$charge_back_amount", "count": "$charge_back_count"},
                        }
                    },
                ]
            )
        )
        if status_dict:
            expired_count = sum(value["count"] for key, value in status_dict[0]["expired"].items())
            expired_amount = sum(round(value["amount"], 2) for key, value in status_dict[0]["expired"].items())
            for key, value in status_dict[0].items():
                new_status_dict = {
                    "name": key,
                    "count": value["count"] if key not in ["expired"] else expired_count,
                    "amount": round(value["amount"], 2) if key not in ["expired"] else expired_amount,
                }
                response.append(new_status_dict)
        return response

    def exposure_status_status(self, user_id):
        user = get_db().user.find_one({"user_id": user_id}, projection={"_id": 0, "clients": 1})
        clients = user["clients"]
        exposure_data = list(
            get_db().dashboard_exposure_status.find({"client_id": {"$in": clients}}).sort("updated_at", -1).limit(1)
        )
        updated_at = exposure_data[0]["updated_at"]
        return {"updatedAt": updated_at}

    def uploaded_transactions(self, user_id, data):
        from_date = data.get("fromDate") or None
        to_date = data.get("toDate") or None
        user = get_db().user.find_one({"user_id": user_id}, projection={"_id": 0, "clients": 1})
        clients = user["clients"]
        if data.get("client"):
            clients = [ObjectId(data.get("client"))]
        if from_date:
            from_date = datetime.fromisoformat(from_date)
        else:
            from_date = datetime.today() - timedelta(days=30)
            from_date = datetime.combine(from_date, datetime.min.time())
        if to_date:
            to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time())
        else:
            to_date = datetime.combine(datetime.today(), datetime.max.time())
        date_difference = (to_date - from_date).days
        date_lis = []
        if date_difference == 0:
            date_lis = [to_date.strftime("%Y-%m-%d")]
        if date_difference > 0:
            date_lis = [(from_date + timedelta(days=i)).strftime("%Y-%m-%d") for i in range(0, date_difference + 1)]
        date_lis.sort()

        response = list(
            get_db().banking_file_details.aggregate(
                [
                    {
                        "$match": {
                            "client_id": {"$in": clients},
                            "deleted": False,
                            "created_at": {
                                "$gte": from_date,
                                "$lte": to_date,
                            },
                        }
                    },
                    {"$addFields": {"created_At": {"$dateToString": {"format": "%Y-%m-%d", "date": "$created_at"}}}},
                    {
                        "$group": {
                            "_id": "$created_At",
                            "count": {"$count": {}},
                        }
                    },
                    {"$sort": {"_id": 1}},
                    {
                        "$project": {
                            "_id": 0,
                            "date": "$_id",
                            "count": "$count",
                        }
                    },
                ]
            )
        )

        zero_amount_data = [{"date": x, "count": 0} for x in date_lis if x not in [y["date"] for y in response]]
        response = response + zero_amount_data
        response.sort(key=lambda x: x["date"])

        return response

    def expiry_details(self, user_id, data):
        user = get_db().user.find_one({"user_id": user_id}, projection={"_id": 0, "clients": 1})
        clients = user["clients"]
        from_date = datetime.fromisoformat(data["fromDate"])
        to_date = datetime.fromisoformat(data["toDate"])
        if data.get("client"):
            clients = [ObjectId(data.get("client"))]

        match_condition = {
            "client_id": {"$in": clients},
            "$expr": {"$eq": [{"$arrayElemAt": ["$currency_code", -1]}, data.get("currency")]},
            "payment_type": {"$in": ["MC", "VI"]},
        }

        date_difference = (to_date - from_date).days
        date_lis = []
        if date_difference == 0:
            date_lis = [to_date.strftime("%Y-%m-%d")]
        if date_difference > 0:
            date_lis = [(from_date + timedelta(days=i)).strftime("%Y-%m-%d") for i in range(0, date_difference + 1)]
        date_lis.sort()

        x_days_from_departure_from_date = (from_date - timedelta(days=120)).strftime("%Y-%m-%d")
        x_days_from_booking_from_date = (from_date - timedelta(days=540)).strftime("%Y-%m-%d")

        x_days_from_departure_to_date = (to_date - timedelta(days=120)).strftime("%Y-%m-%d")
        x_days_from_booking_to_date = (to_date - timedelta(days=540)).strftime("%Y-%m-%d")

        trust_funds = list(
            get_db().trust_fund_v2.aggregate(
                [
                    {"$match": match_condition},
                    {
                        "$addFields": {
                            "expiry_date120": {
                                "$dateAdd": {
                                    "startDate": {"$dateFromString": {"dateString": "$departure_date"}},
                                    "unit": "day",
                                    "amount": 120,
                                }
                            }
                        }
                    },
                    {
                        "$addFields": {
                            "expiry_date540": {
                                "$dateAdd": {
                                    "startDate": {"$dateFromString": {"dateString": "$booking_date"}},
                                    "unit": "day",
                                    "amount": 540,
                                }
                            }
                        }
                    },
                    {
                        "$match": {
                            "$or": [
                                {
                                    "expiry_date120": {
                                        "$gte": from_date,
                                        "$lte": to_date,
                                    },
                                },
                                {
                                    "expiry_date540": {
                                        "$gte": from_date,
                                        "$lte": to_date,
                                    }
                                },
                            ]
                        }
                    },
                    {
                        "$project": {
                            "client_id": "$client_id",
                            "currency_code": "$currency_code",
                            "expiry_date": {
                                "$cond": [
                                    {
                                        "$and": [
                                            {"$gte": ["$expiry_date120", from_date]},
                                            {"$lte": ["$expiry_date120", to_date]},
                                        ]
                                    },
                                    "$expiry_date120",
                                    "$expiry_date540",
                                ]
                            },
                            "Visa120": {
                                "$cond": [
                                    {
                                        "$and": [
                                            {"$eq": ["$payment_type", "VI"]},
                                            {"$gte": ["$departure_date", x_days_from_departure_from_date]},
                                            {"$lte": ["$departure_date", x_days_from_departure_to_date]},
                                        ]
                                    },
                                    "$balance",
                                    0,
                                ]
                            },
                            "Visa540": {
                                "$cond": [
                                    {
                                        "$and": [
                                            {"$eq": ["$payment_type", "VI"]},
                                            {"$gte": ["$booking_date", x_days_from_booking_from_date]},
                                            {"$lte": ["$booking_date", x_days_from_booking_to_date]},
                                        ]
                                    },
                                    "$balance",
                                    0,
                                ]
                            },
                            "MasterCard120": {
                                "$cond": [
                                    {
                                        "$and": [
                                            {"$eq": ["$payment_type", "MC"]},
                                            {"$gte": ["$departure_date", x_days_from_departure_from_date]},
                                            {"$lte": ["$departure_date", x_days_from_departure_to_date]},
                                        ]
                                    },
                                    "$balance",
                                    0,
                                ]
                            },
                            "MasterCard540": {
                                "$cond": [
                                    {
                                        "$and": [
                                            {"$eq": ["$payment_type", "MC"]},
                                            {"$gte": ["$booking_date", x_days_from_booking_from_date]},
                                            {"$lte": ["$booking_date", x_days_from_booking_to_date]},
                                        ]
                                    },
                                    "$balance",
                                    0,
                                ]
                            },
                        }
                    },
                    {
                        "$group": {
                            "_id": {
                                "client_id": "$client_id",
                                "currency_code": "$currency_code",
                                "expiry_date": "$expiry_date",
                            },
                            "visa120": {"$sum": "$Visa120"},
                            "visa540": {"$sum": "$Visa540"},
                            "masterCard120": {"$sum": "$MasterCard120"},
                            "masterCard540": {"$sum": "$MasterCard540"},
                        }
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "date": {"$dateToString": {"format": "%Y-%m-%d", "date": "$_id.expiry_date"}},
                            "visa120": 1,
                            "visa540": 1,
                            "masterCard120": 1,
                            "masterCard540": 1,
                        }
                    },
                ],
                collation={"locale": "en", "strength": 1},
            )
        )

        zero_amount_data = [
            {"date": x, "visa120": 0, "visa540": 0, "masterCard120": 0, "masterCard540": 0}
            for x in date_lis
            if x not in [y["date"] for y in trust_funds]
        ]
        response = trust_funds + zero_amount_data
        response.sort(key=lambda x: x["date"])

        return response

    def save_closing_balance(self, data):
        client_id = data.get("client_id")
        blue_style = Config.BLUESTYLE
        if client_id != blue_style:
            return {"msg": "Do not have permission"}

        # Extract fields from the incoming data
        closing_balance = data.get("closing_balance")
        date = data.get("date")
        from_date = data.get("from_date")
        to_date = data.get("to_date")
        opening_balance_ucb = data.get("opening_balance_ucb")
        opening_balance_rb = data.get("opening_balance_rb")
        receipts_ucb = data.get("receipts_ucb")
        receipts_rb = data.get("receipts_rb")
        payments_ucb = data.get("payments_ucb")
        payments_rb = data.get("payments_rb")
        bank_charges_ucb = data.get("bank_charges_ucb")
        bank_charges_rb = data.get("bank_charges_rb")
        imperium_total_opening_balance = data.get("imperium_total_opening_balance")
        closing_balance_statement_rb = data.get("closing_balance_statement_rb")
        closing_balance_statement_ucb = data.get("closing_balance_statement_ucb")
        deductibles = data.get("deductibles")
        statements = data.get("statements")

        db = get_db()
        query = {"client_id": client_id, "from_date": from_date, "to_date": to_date}

        # Check if a document with the same from_date and to_date exists
        existing_document = db.client_financial_reports.find_one(query)

        # Prepare the document
        document = {
            "client_id": client_id,
            "closing_balance": closing_balance,
            "date": date,
            "from_date": from_date,
            "to_date": to_date,
            "opening_balance_ucb": opening_balance_ucb,
            "opening_balance_rb": opening_balance_rb,
            "receipts_ucb": receipts_ucb,
            "receipts_rb": receipts_rb,
            "payments_ucb": payments_ucb,
            "payments_rb": payments_rb,
            "bank_charges_ucb": bank_charges_ucb,
            "bank_charges_rb": bank_charges_rb,
            "imperium_total_opening_balance": imperium_total_opening_balance,
            "closing_balance_statement_rb": closing_balance_statement_rb,
            "closing_balance_statement_ucb": closing_balance_statement_ucb,
            "deductibles": deductibles,
            "statements": statements,
            "created_at": datetime.utcnow(),
        }

        if existing_document:
            # Update the existing document
            # Remove created_at to prevent overwriting
            document.pop("created_at", None)
            db.client_financial_reports.update_one(query, {"$set": document})
            return {"msg": "Data updated successfully!"}
        else:
            # Insert a new document
            db.client_financial_reports.insert_one(document)
            return {"msg": "Data saved successfully!"}

    def get_closing_balance(self, client_id, from_date=None, to_date=None):
        blue_style = Config.BLUESTYLE
        if client_id != blue_style:
            return {"msg": "Do not have permission"}

        db = get_db()

        # Query for exact match on from_date and to_date
        query = {"client_id": client_id, "from_date": from_date, "to_date": to_date}

        # Find the document matching the exact from_date and to_date
        details = db.client_financial_reports.find_one(query)

        # If no document is found, fetch the latest document
        if not details:
            details = db.client_financial_reports.find_one({"client_id": client_id}, sort=[("date", -1)])
            if details:
                return {"closing_balance": details.get("closing_balance")}
            else:
                return {"msg": "No data found"}

        # Return the full matching document
        return {
            "date": details.get("date"),
            "closing_balance": details.get("closing_balance"),
            "opening_balance_ucb": details.get("opening_balance_ucb"),
            "opening_balance_rb": details.get("opening_balance_rb"),
            "receipts_ucb": details.get("receipts_ucb"),
            "receipts_rb": details.get("receipts_rb"),
            "payments_ucb": details.get("payments_ucb"),
            "payments_rb": details.get("payments_rb"),
            "bank_charges_ucb": details.get("bank_charges_ucb"),
            "bank_charges_rb": details.get("bank_charges_rb"),
            "deductibles": details.get("deductibles"),
            "statements": details.get("statements"),
            "closing_balance_statement_rb": details.get("closing_balance_statement_rb"),
            "closing_balance_statement_ucb": details.get("closing_balance_statement_ucb"),
        }

    def image_upload_snapshot(self, file):
        now = datetime.utcnow()
        file_id = f"{str(uuid.uuid4())}_{now.isoformat()}"
        file_name = file.filename
        temp_dir = current_app.config["TEMP_DIR"]

        os.makedirs(temp_dir, exist_ok=True)
        file_path = os.path.join(temp_dir, file_name)
        file.save(file_path)

        current_app.logger.info(f"File saved locally at {file_path}")
        bucket = current_app.config["ERV_DASHBOARD_GRAPHS_SNAPSHOT_BUCKET"]
        current_app.logger.info("Starting file upload to S3")
        upload_file(bucket, file_id, file_path)
        current_app.logger.info("Successfully uploaded file to S3")

        return file_id

    def download_snapshot_image(self, file_id, image_name):
        bucket = current_app.config["ERV_DASHBOARD_GRAPHS_SNAPSHOT_BUCKET"]
        temp_file_path = f"{current_app.config['TEMP_DIR']}/{image_name}"

        current_app.logger.info(f"Starting download for file_id: {file_id} from bucket: {bucket}")

        try:
            download_file(bucket, file_id, temp_file_path)
            current_app.logger.info(f"File {file_id} downloaded successfully to {temp_file_path}")
        except Exception as e:
            current_app.logger.error(f"Error downloading file {file_id}: {e}", exc_info=True)
            return {"error": "Failed to download file"}, 500

        try:
            response = send_file(temp_file_path)
            current_app.logger.info(f"Successfully sending file {file_id} to client")
            return temp_file_path
        except Exception as e:
            current_app.logger.error(f"Error sending file {file_id}: {e}", exc_info=True)
            return {"error": "Failed to send file"}, 500

    def non_trust_bookings_excel_file(self, client_id, currency, from_date, to_date, file_name, image_path):
        client_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        client_name = client_info.get("full_name", None) if client_info else None
        if from_date:
            from_date = datetime.fromisoformat(from_date)
        else:
            from_date = datetime.min
        if to_date:
            to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time())
        else:
            to_date = datetime.max

        pipeline = [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "status": {"$nin": ["Cancelled", "Cancelled by System"]},
                    "banking_files.file_date": {
                        "$gte": from_date.strftime("%Y-%m-%d"),
                        "$lte": to_date.strftime("%Y-%m-%d"),
                    },
                }
            },
            {"$unwind": "$banking_files"},
            {
                "$lookup": {
                    "from": "banking_file_details",
                    "localField": "banking_files.file_id",
                    "foreignField": "file_id",
                    "as": "file_details",
                }
            },
            {"$unwind": "$file_details"},
            {"$match": {"file_details.non_trust_flag": True}},
            {
                "$group": {
                    "_id": "$banking_files.file_date",
                    "total_amount": {"$sum": "$file_details.bank_file_amount"},
                    "no_of_bookings": {"$sum": 1},
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "fileDate": "$_id",
                    "amount": {"$ifNull": ["$total_amount", 0]},
                    "noOfBookings": {"$ifNull": ["$no_of_bookings", 0]},
                }
            },
            {"$sort": {"fileDate": 1}},
        ]

        response = list(get_db().banking_metadata.aggregate(pipeline))

        total_amount = response[0].get("amount")
        no_of_bookings = response[0].get("noOfBookings")

        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{file_name}")

        worksheet = workbook.add_worksheet()
        bold_format = workbook.add_format({"bold": True})
        border_format = workbook.add_format({"border": 1})

        worksheet.merge_range("A1:B1", "Non Trust Bookings", bold_format)

        data = [
            ["Client name", client_name],
            ["Currency code", currency],
            ["From Date", "To Date"],
            [from_date.strftime("%Y-%m-%d"), to_date.strftime("%Y-%m-%d")],
            ["Total no of Bookings", no_of_bookings],
            ["Total Amount ", total_amount],
        ]

        row, col = 1, 0
        for item in data:
            worksheet.write(row, col, item[0], border_format)
            worksheet.write(row, col + 1, item[1], border_format)
            row += 1

        worksheet.set_column(0, 1, 25)
        worksheet.insert_image("D2", image_path, {"x_scale": 0.5, "y_scale": 0.5})

        workbook.close()

    def movement_of_funds_excel_file(self, client_id, currency, from_date, to_date, user_id, file_name, image_path):
        client_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        client_name = client_info.get("full_name", None) if client_info else None

        clients = get_db().user.find_one({"user_id": user_id}, projection={"clients": 1})["clients"]
        if client_id:
            clients = [ObjectId(client_id)]
        banking_match_condition = [
            {"client_id": {"$in": clients}, "status": {"$nin": ["Cancelled", "Cancelled by System"]}},
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        to_date if to_date else datetime.today().strftime("%Y-%m-%d"),
                    ]
                }
            },
        ]
        claim_match_condition = [
            {"client_id": {"$in": clients}, "status": {"$nin": ["Cancelled", "Cancelled by System"]}},
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        to_date if to_date else datetime.today().strftime("%Y-%m-%d"),
                    ]
                }
            },
        ]
        total_payments = list(
            get_db().banking_metadata.aggregate(
                [
                    {"$match": {"$and": banking_match_condition}},
                    {
                        "$project": {
                            "amount": {"$last": f"$banking_files.deposit.{currency}"},
                            "_id": 0,
                        }
                    },
                    {"$group": {"_id": None, "totalAmount": {"$sum": "$amount"}}},
                ]
            )
        )
        total_payments = round(total_payments[0]["totalAmount"] if total_payments else 0, 2)
        total_claims = list(
            get_db().claims_metadata.aggregate(
                [
                    {"$match": {"$and": claim_match_condition}},
                    {
                        "$project": {
                            "amount": {"$last": f"$claim_files.claim_total.{currency}"},
                            "_id": 0,
                        }
                    },
                    {"$group": {"_id": None, "totalAmount": {"$sum": "$amount"}}},
                ]
            )
        )
        total_claims = round(total_claims[0]["totalAmount"] if total_claims else 0, 2)
        balance = round(total_payments - total_claims, 2)
        if isinstance(from_date, str):
            from_date = datetime.strptime(from_date, "%Y-%m-%d")
        if isinstance(to_date, str):
            to_date = datetime.strptime(to_date, "%Y-%m-%d")

        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{file_name}")

        worksheet = workbook.add_worksheet()
        bold_format = workbook.add_format({"bold": True})
        border_format = workbook.add_format({"border": 1})
        bold_border_format = workbook.add_format({"bold": True, "border": 1})

        worksheet.merge_range("A1:B1", "Movement Of Funds", bold_format)
        formatted_total_claims = format(total_claims, ",")
        formatted_balance = format(balance, ",")
        formatted_total_payments = format(total_payments, ",")

        data = [
            ["Client Name", client_name],
            ["Currency code", currency],
            ["From Date", "To Date"],
            [from_date.strftime("%Y-%m-%d"), to_date.strftime("%Y-%m-%d")],
            ["Total Payments", formatted_total_payments],
            ["Total Claims", formatted_total_claims],
            ["Balance", formatted_balance],
        ]

        row, col = 1, 0
        for item in data:
            worksheet.write(row, col, item[0], border_format)
            worksheet.write(row, col + 1, item[1], border_format)
            row += 1

        worksheet.set_column(0, 1, 25)
        worksheet.insert_image("D2", image_path, {"x_scale": 0.5, "y_scale": 0.5})

        workbook.close()

    def exposure_breakdown_excel_file(self, client_id, currency, from_date, to_date, file_name, image_path):
        client_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        client_name = client_info.get("full_name", None) if client_info else None
        from_date = datetime.strptime(from_date, "%Y-%m-%d")
        to_date = datetime.strptime(to_date, "%Y-%m-%d")

        banking_metadata = list(
            get_db().banking_metadata.find(
                {
                    "client_id": ObjectId(client_id),
                    "banking_files.file_date": {
                        "$gte": from_date.strftime("%Y-%m-%d"),
                        "$lte": to_date.strftime("%Y-%m-%d"),
                    },
                }
            )
        )
        total_non_bonded_amount = 0
        query = {"client_id": ObjectId(client_id), "booking_date": {"$gte": from_date, "$lte": to_date}}
        non_bond_info = get_db().banking_file_details.find(query)
        for document in non_bond_info:
            total_non_bonded_amount += document.get("non_bonded_amount", 0)

        sent_to_trust = 0
        file_ids = set()

        for metadata in banking_metadata:
            banking_files = metadata.get("banking_files", [])
            if not banking_files:
                continue
            # Sort files by file_date in descending order
            banking_files.sort(key=lambda x: x.get("file_date"), reverse=True)
            latest_file = banking_files[0]  # Take the latest file
            sent_to_trust += latest_file.get("deposit", {}).get(currency, 0)
            file_ids.add(latest_file.get("file_id"))

        # Step 2: Fetch banking_file_details and sum original amounts
        total_value = 0
        banking_file_details = list(
            get_db().banking_file_details.find(
                {"file_id": {"$in": list(file_ids)}, "deleted": False, "currency_code": currency}
            )
        )

        for file_detail in banking_file_details:
            total_value += file_detail.get("original_amount", 0)

        not_sent_to_trust = total_value - sent_to_trust

        bond_info = get_db().client_bond_info.find_one({"client_id": ObjectId(client_id)})
        bond_amount = bond_info.get("bond_amount", 0) if bond_info else 0

        sentToTrust = round(sent_to_trust, 2)
        totalValue = round(total_value, 2)
        notSentToTrust = round(not_sent_to_trust, 2)
        deductableAmount = round(bond_amount, 2)
        nonBondedAmount = total_non_bonded_amount

        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{file_name}")

        worksheet = workbook.add_worksheet()
        bold_format = workbook.add_format({"bold": True})
        border_format = workbook.add_format({"border": 1})
        bold_border_format = workbook.add_format({"bold": True, "border": 1})

        worksheet.merge_range("A1:B1", "Risk Exposure Breakdown", bold_format)
        
        formatted_total_value = format(total_value, ",")
        formatted_sentToTrust = format(sentToTrust, ",")
        formatted_notSentToTrust = format(notSentToTrust, ",")
        
        
        data = [
            ["Client name", client_name],
            ["Currency code", currency],
            ["From Date", "To Date"],
            [from_date.strftime("%Y-%m-%d"), to_date.strftime("%Y-%m-%d")],
            ["Total Value(100%)", formatted_total_value],
            ["Amount sent to Trust(40%)", formatted_sentToTrust],
            ["Amount not sent to Trust(60%)", formatted_notSentToTrust],
        ]

        row, col = 1, 0
        for item in data:
            worksheet.write(row, col, item[0], border_format)
            worksheet.write(row, col + 1, item[1], border_format)
            row += 1

        worksheet.set_column(0, 1, 25)
        worksheet.insert_image("D2", image_path, {"x_scale": 0.5, "y_scale": 0.5})

        workbook.close()

    def delayed_trust_funds_excel_file(self, currency, from_date, to_date, client_id, file_name, image_path):
        client_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        client_name = client_info.get("full_name", None) if client_info else None
        if from_date:
            from_date = datetime.strptime(from_date, "%Y-%m-%d")
        else:
            from_date = datetime.min

        if to_date:
            to_date = datetime.strptime(to_date, "%Y-%m-%d")
        else:
            to_date = datetime.today()

        aggregation_pipeline = [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "banking_files.file_date": {
                        "$gte": from_date.strftime("%Y-%m-%d"),
                        "$lte": to_date.strftime("%Y-%m-%d"),
                    },
                    "banking_files.status": {
                        "$nin": ["authorized", "Cancelled by System", "Cancelled", "Authorised", "authorised"]
                    },
                }
            },
            {"$unwind": "$banking_files"},
            {
                "$project": {
                    "file_id": "$banking_files.file_id",
                    "file_date": "$banking_files.file_date",
                    "deposit": "$banking_files.deposit",
                }
            },
            {
                "$group": {
                    "_id": None,
                    "file_info": {"$push": {"file_id": "$file_id", "file_date": "$file_date", "deposit": "$deposit"}},
                }
            },
        ]

        file_ids_result = list(get_db().banking_metadata.aggregate(aggregation_pipeline))

        file_info = file_ids_result[0].get("file_info", []) if file_ids_result else []

        response = []
        if file_info:
            for info in file_info:
                file_date = datetime.strptime(info["file_date"], "%Y-%m-%d")
                deposit = info.get("deposit", {})
                escrow_amount = deposit.get(currency, 0)
                formatted_escrow_amount = format(escrow_amount, ",")

                days_diff = (datetime.today() - file_date).days

                if days_diff > 4:
                    risk_level = "High"
                elif days_diff > 3:
                    risk_level = "Medium"
                elif days_diff > 2:
                    risk_level = "Low"
                else:
                    risk_level = "None"

                if escrow_amount != 0:

                    response.append(
                        {"amount": formatted_escrow_amount, "fileDate": file_date.strftime("%Y-%m-%d"), "riskLevel": risk_level}
                    )

        response = sorted(response, key=lambda x: x["fileDate"])
        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{file_name}")

        worksheet = workbook.add_worksheet()
        bold_format = workbook.add_format({"bold": True})
        border_format = workbook.add_format({"border": 1})
        bold_border_format = workbook.add_format({"bold": True, "border": 1})

        worksheet.merge_range("A1:B1", "Delayed Trust Funds", bold_format)

        data = [
            ["Client name", client_name],
            ["Currency code", currency],
            ["From Date", "To Date"],
            [from_date.strftime("%Y-%m-%d"), to_date.strftime("%Y-%m-%d")],
        ]

        row, col = 1, 0
        for item in data:
            worksheet.write(row, col, item[0], border_format)
            worksheet.write(row, col + 1, item[1], border_format)
            row += 1

        row += 1

        worksheet.write(row, 0, "Amount", border_format)
        worksheet.write(row, 1, "File Date", border_format)
        worksheet.write(row, 2, "Risk", border_format)
        row += 1

        for record in response:
            worksheet.write(row, 0, record["amount"], border_format)

            # If fileDate is already a string, no need to call strftime
            file_date = record["fileDate"]
            if isinstance(file_date, str):
                worksheet.write(row, 1, file_date, border_format)
            else:
                worksheet.write(row, 1, file_date.strftime("%Y-%m-%d"), border_format)
            worksheet.write(row, 2, record["riskLevel"], border_format)
            row += 1

        worksheet.set_column(0, 1, 25)
        worksheet.insert_image("F2", image_path, {"x_scale": 0.5, "y_scale": 0.5})

        workbook.close()

    def non_trust_bookings_erv(self, data):
        client_id = data.get("clientId")
        from_date = data.get("fromDate")
        to_date = data.get("toDate")
        currency = data.get("currency")

        if from_date:
            from_date = datetime.fromisoformat(from_date)
        else:
            from_date = datetime.min
        if to_date:
            to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time())
        else:
            to_date = datetime.max

        pipeline = [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "status": {"$nin": ["Cancelled", "Cancelled by System"]},
                    "banking_files.file_date": {
                        "$gte": from_date.strftime("%Y-%m-%d"),
                        "$lte": to_date.strftime("%Y-%m-%d"),
                    },
                }
            },
            {"$unwind": "$banking_files"},
            {
                "$lookup": {
                    "from": "banking_file_details",
                    "localField": "banking_files.file_id",
                    "foreignField": "file_id",
                    "as": "file_details",
                }
            },
            {"$unwind": "$file_details"},
            {"$match": {"file_details.non_trust_flag": True}},
            {
                "$group": {
                    "_id": "$banking_files.file_date",
                    "total_amount": {"$sum": "$file_details.bank_file_amount"},
                    "no_of_bookings": {"$sum": 1},
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "fileDate": "$_id",
                    "amount": {"$ifNull": ["$total_amount", 0]},
                    "noOfBookings": {"$ifNull": ["$no_of_bookings", 0]},
                }
            },
            {"$sort": {"fileDate": 1}},
        ]

        response = list(get_db().banking_metadata.aggregate(pipeline))
        return response

    def get_banking_summary(self, data):

        client_id = data.get("clientId")
        from_date = data.get("fromDate")
        to_date = data.get("toDate")
        currency = data.get("currency")  # In this case, currency may be used if needed in projections

        # Step 1: Aggregate banking file details to get the total amount and non-bonded amount
        banking_pipeline = [
            {"$match": {
                "client_id": ObjectId(client_id),
                "date_banking_file": {"$gte": from_date, "$lte": to_date}
            }},
            {"$group": {
                "_id": None,
                "banking_value": {"$sum": "$amount"},
                "total_non_bonded_amount": {"$sum": "$non_bonded_amount"}
            }}
        ]
        banking_result = list(get_db().banking_file_details.aggregate(banking_pipeline))
        if banking_result:
            banking_value = banking_result[0].get("banking_value", 0)
            total_non_bonded_amount = banking_result[0].get("total_non_bonded_amount", 0)
        else:
            banking_value = 0
            total_non_bonded_amount = 0

        # Step 2: Aggregate claims file details to get the total claims amount
        claims_pipeline = [
            {"$match": {
                "client_id": ObjectId(client_id),
                "date_claim_file": {"$gte": from_date, "$lte": to_date}
            }},
            {"$group": {
                "_id": None,
                "claims_value": {"$sum": "$amount"}
            }}
        ]
        claims_result = list(get_db().claims_file_details.aggregate(claims_pipeline))
        claims_value = claims_result[0].get("claims_value", 0) if claims_result else 0

        # Step 3: Calculate sent_to_trust and total_value using the aggregated values
        sent_to_trust = banking_value - claims_value

        # Total Value (100%): 100/40* amount (1) example : 40% - *********.13 , 100/40* *********.13 = 12,411,788,450.33
        total_value = (2.5 * sent_to_trust) if sent_to_trust else 0
        not_sent_to_trust = total_value - sent_to_trust

        # Step 4: Fetch bond information if needed
        bond_info = get_db().client_bond_info.find_one({"client_id": ObjectId(client_id)})
        bond_amount = bond_info.get("bond_amount", 0) if bond_info else 0

        # Prepare and return the final response
        response = {
            "sentToTrust": round(sent_to_trust, 2),
            "totalValue": round(total_value, 2),
            "notSentToTrust": round(not_sent_to_trust, 2),
            "deductableAmount": round(bond_amount, 2),
            "nonBondedAmount": total_non_bonded_amount,
        }
        return response

    def risk_exposure_graph(self, data):
        client_id = data.get("clientId")
        from_date = data.get("fromDate")
        to_date = data.get("toDate")
        currency = data.get("currency")

        if from_date:
            from_date = datetime.strptime(from_date, "%Y-%m-%d")
        else:
            from_date = datetime.min

        if to_date:
            to_date = datetime.strptime(to_date, "%Y-%m-%d")
        else:
            to_date = datetime.today()

        aggregation_pipeline = [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "banking_files.file_date": {
                        "$gte": from_date.strftime("%Y-%m-%d"),
                        "$lte": to_date.strftime("%Y-%m-%d"),
                    },
                    "banking_files.status": {
                        "$nin": ["authorized", "Cancelled by System", "Cancelled", "Authorised", "authorised"]
                    },
                }
            },
            {"$unwind": "$banking_files"},
            {
                "$project": {
                    "file_id": "$banking_files.file_id",
                    "file_date": "$banking_files.file_date",
                    "deposit": "$banking_files.deposit",
                }
            },
            {
                "$group": {
                    "_id": None,
                    "file_info": {"$push": {"file_id": "$file_id", "file_date": "$file_date", "deposit": "$deposit"}},
                }
            },
        ]

        file_ids_result = list(get_db().banking_metadata.aggregate(aggregation_pipeline))

        file_info = file_ids_result[0].get("file_info", []) if file_ids_result else []

        response = []
        if file_info:
            for info in file_info:
                file_date = info["file_date"]
                deposit = info.get("deposit", {})
                escrow_amount = deposit.get(currency, 0)

                if escrow_amount != 0:
                    response.append({"amount": escrow_amount, "fileDate": file_date})

        response = sorted(response, key=lambda x: x["fileDate"])
        return response


dashboard_service = DashboardService()
