from datetime import datetime
from math import ceil
from bson import ObjectId
from flaskr.models import get_db
from flaskr.models.issue_log import IssueLogSchema


class IssueLogService:
    def create_issue_log(self, data):
        now = datetime.utcnow()
        if "opened" in data and data["opened"] == "":
            data["opened"] = None
        data = IssueLogSchema().load(data)
        issue_log = get_db().issue_log.insert_one({**data, "deleted": False, "created_at": now})
        return {"_id": str(issue_log.inserted_id)}

    def list_issue_log(self, from_date, to_date, query, client, status, priority, sort_key, sort_order, page, size):
        page = int(page or 1)
        size = int(size or 10)
        offset = (page - 1) * size
        match_condition = {"deleted": False}
        query_match_condition = []
        if client:
            match_condition.update({"client_id": ObjectId(client)})
        if status:
            match_condition.update({"status": status})
        if priority:
            match_condition.update({"priority": priority})
        from_date_obj = None
        to_date_obj = None
        if from_date and to_date and from_date and to_date:
            from_date_obj = datetime.strptime(from_date, "%Y-%m-%d")
            to_date_obj = datetime.strptime(to_date, "%Y-%m-%d")
            match_condition.update({"opened": {"$gte": from_date_obj, "$lte": to_date_obj}})

        if query:
            query_match_condition = [
                {
                    "$match": {
                        "$or": [
                            {"status": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                            {"resolution_notes": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                            {"priority": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                            {"short_description": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                            {"client.full_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                            {"client.friendly_name": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                        ]
                    }
                },
            ]
        data = list(
            get_db().issue_log.aggregate(
                [
                    {"$match": match_condition},
                    {
                        "$lookup": {
                            "from": "client_basic_info",
                            "localField": "client_id",
                            "foreignField": "_id",
                            "as": "client",
                        }
                    },
                    *query_match_condition,
                    {"$unwind": "$client"},
                    {"$sort": {sort_key: sort_order}},
                    {
                        "$project": {
                            "_id": 1,
                            "client_id": "$client.c_id",
                            "status": 1,
                            "resolution_notes": 1,
                            "priority": 1,
                            "short_description": 1,
                            "opened": 1,
                            "date_resolved": 1,
                            "full_name": "$client.full_name",
                        }
                    },
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ],
                allowDiskUse=True,
            )
        )
        content = []
        for row in data[0]["data"]:
            content.append({**IssueLogSchema().dump(row), "clientName": row["full_name"]})
        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)
        empty = True if not content else False
        first = True if page == 1 else False
        last = True if page == ceil((total_elements) / size) else False

        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def update_issue_log(self, data, issue_id):
        now = datetime.utcnow()
        data = IssueLogSchema().load(data)
        get_db().issue_log.update_one({"_id": ObjectId(issue_id)}, {"$set": {**data, "updated_at": now}})


issue_log_service = IssueLogService()
