from flask import abort, jsonify
import uuid
from flask_pymongo import pymongo
from flaskr.helpers.auth.banking import banking_details_access
from flaskr.helpers.auth.claims import claim_details_access
from flaskr.helpers.auth.client import client_details_access
from flaskr.models import get_db
from datetime import datetime, timedelta
from flaskr.helpers.date_util import format_date_to_iso
from flask import current_app, send_file
from pymongo import ReadPreference
from sheet2dict import Worksheet
from flaskr.models.client.address import ClientAddressSchema
from flaskr.models.client.anomaly import ClientAnomalySchema
from flaskr.models.client.atol_info import ClientAtolInfoSchema
from flaskr.models.client.authorized_signatories import ClientAuthorizedSignatoriesInfoSchema
from flaskr.models.client.bank_acct_details import ClientBankAcctDetailsSchema
from flaskr.models.client.deductable_amount import ClientDeductableAmountSchema
from flaskr.models.client.banking_column import BankingColumnSchema
from flaskr.models.client.basic_info import ClientBasicInfoSchema
from flaskr.models.client.check_info import ClientCheckInfoSchema
from flaskr.models.client.claim_frequency import ClientClaimFrequencySchema
from flaskr.models.client.claims_columns import ClientClaimColumnSchema
from flaskr.models.client.escrow_multiplier import ClientEscrowMultiplierSchema
from flaskr.models.client.insurance_info import ClientInsuranceInfoSchema
from flaskr.models.client.bond_info import ClientBondInfoSchema
from flaskr.models.client.limit import ClientLimitSchema
from flaskr.models.client.client_files import ClientFilesSchema
from flaskr.models.client.manuals import ClientManualsInfoSchema
from flaskr.models.client.manuals_logs import ClientManualsLogsInfoSchema
from flaskr.services.auth_service import auth_service
from flaskr.services.exceptions import ServiceException
from bson import ObjectId
from flaskr.helpers.boto3_handler import (
    upload_file,
    validate_mime_type_pdf,
    validate_mime_type_file,
    validate_mime_type_pptx,
    validate_mime_type_xls_csv,
    download_file,
    generate_presigned_url,
    validate_mime_type_png_jpeg,
)


class ClientService:
    def __total_annual_revenue_and_claim_limit_calculation(
        self, client_id, currency, from_date, to_date, total_annual_revenue, max_no_of_claims
    ):
        response = {
            "isAnnualRevenueLimitExceeded": False,
            "isClaimsExceeded": False,
            "TotalAnnualRevenue": 0,
            "totalNoOfClaims": 0,
        }
        if total_annual_revenue:
            banking = list(
                get_db().banking_metadata.aggregate(
                    [
                        {
                            "$match": {
                                "$and": [
                                    {
                                        "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
                                        "client_id": ObjectId(client_id),
                                        f"banking_files.deposit.{currency}": {"$exists": True},
                                    },
                                    {
                                        "$expr": {
                                            "$gte": [{"$arrayElemAt": ["$banking_files.file_date", -1]}, from_date]
                                        },
                                    },
                                    {"$expr": {"$lte": [{"$arrayElemAt": ["$banking_files.file_date", -1]}, to_date]}},
                                ]
                            },
                        },
                        {
                            "$group": {
                                "_id": None,
                                "TotalAnnualRevenue": {
                                    "$sum": {"$arrayElemAt": [f"$banking_files.deposit.{currency}", -1]}
                                },
                            }
                        },
                        {
                            "$project": {
                                "TotalAnnualRevenue": "$TotalAnnualRevenue",
                            },
                        },
                    ]
                )
            )
            if banking:
                response["isAnnualRevenueLimitExceeded"] = (
                    True if banking[0]["TotalAnnualRevenue"] > total_annual_revenue else False
                )
                response["TotalAnnualRevenue"] = banking[0]["TotalAnnualRevenue"]

        if max_no_of_claims:
            claim = list(
                get_db().claims_metadata.aggregate(
                    [
                        {
                            "$match": {
                                "$and": [
                                    {
                                        "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
                                        "client_id": ObjectId(client_id),
                                        f"claim_files.item_count.{currency}": {"$exists": True},
                                    },
                                    {
                                        "$expr": {
                                            "$gte": [{"$arrayElemAt": ["$claim_files.file_date", -1]}, from_date]
                                        },
                                    },
                                    {"$expr": {"$lte": [{"$arrayElemAt": ["$claim_files.file_date", -1]}, to_date]}},
                                ]
                            },
                        },
                        {
                            "$group": {
                                "_id": None,
                                "totalNoOfClaims": {"$sum": 1},
                            }
                        },
                        {
                            "$project": {
                                "totalNoOfClaims": "$totalNoOfClaims",
                            },
                        },
                    ]
                )
            )
            if claim:
                response["totalNoOfClaims"] = claim[0]["totalNoOfClaims"]
                response["isClaimsExceeded"] = True if claim[0]["totalNoOfClaims"] > max_no_of_claims else False

        return response

    def upsert_basic_info(self, data):
        with get_db().client.start_session() as session:
            with session.start_transaction():
                if data.get("cId"):
                    data["cId"] = "".join(data.get("cId").split())
                if data.get("create"):
                    if not data.get("cId"):
                        raise ServiceException("ClientId Missing")
                    now = datetime.utcnow()
                    basic_info = get_db().client_basic_info.insert_one(
                        ClientBasicInfoSchema().load(
                            {
                                **data,
                                "status": "draft",
                                "isEditable": True,
                                "isDisabled": False,
                                "createdAt": now.isoformat(),
                            }
                        ),
                        session=session,
                    )
                    client_id = basic_info.inserted_id
                    get_db().user.update_many(
                        {"role": {"$in": ["ptt-admin", "ptt-user"]}},
                        {
                            "$addToSet": {"clients": client_id},
                        },
                        session=session,
                    )
                else:
                    basic_info = get_db().client_basic_info.find_one_and_update(
                        {"_id": ObjectId(data["clientId"])},
                        {"$set": ClientBasicInfoSchema().load(data, partial=True)},
                        session=session,
                    )
                    if not basic_info:
                        raise ServiceException("Client not found")
                    client_id = basic_info["_id"]
                get_db().client_check_info.delete_many({"client_id": client_id}, session=session)
                get_db().client_claim_frequency.delete_many({"client_id": client_id}, session=session)
                get_db().client_limit.delete_many({"client_id": client_id}, session=session)
                get_db().client_authorized_signatories_info.delete_many({"client_id": client_id}, session=session)
                additional_check = data.get("additionChecks")
                address = data.get("address")
                limit = data.get("limits")
                claim_frequency = data.get("frequency")
                authorized_signatories = data.get("authorizedSignatories")
                if additional_check:
                    for item in additional_check:
                        check_info = ClientCheckInfoSchema().load({**item, "clientId": client_id})
                        get_db().client_check_info.insert_one(check_info, session=session)
                if claim_frequency:
                    for frequency_id in claim_frequency:
                        client_claim_frequency = ClientClaimFrequencySchema().load(
                            {"frequencyId": frequency_id, "clientId": client_id}
                        )
                        get_db().client_claim_frequency.insert_one(client_claim_frequency, session=session)
                if address:
                    address = get_db().client_address.find_one_and_update(
                        {"client_id": client_id},
                        {"$set": ClientAddressSchema().load({**address, "clientId": client_id})},
                        upsert=True,
                        session=session,
                    )
                if limit:
                    for item in limit:
                        limit = get_db().client_limit.insert_one(
                            ClientLimitSchema().load({**item, "clientId": client_id}), session=session
                        )
                if authorized_signatories:
                    for item in authorized_signatories:
                        document = ClientAuthorizedSignatoriesInfoSchema().load({**item, "clientId": client_id})
                        get_db().client_authorized_signatories_info.insert_one(document, session=session)
                return str(client_id)

    def upsert_bank_info(self, data):
        with get_db().client.start_session() as session:
            with session.start_transaction():
                get_db().client_bank_acct_details.delete_many(
                    {"client_id": ObjectId(data["clientId"])}, session=session
                )

                for item in data["data"]:
                    bank_info = ClientBankAcctDetailsSchema().load({**item, "clientId": ObjectId(data["clientId"])})
                    get_db().client_bank_acct_details.insert_one(bank_info, session=session)

    def upsert_insurance_atol(self, data):
        with get_db().client.start_session() as session:
            with session.start_transaction(read_preference=ReadPreference.PRIMARY):
                get_db().client_insurance_info.delete_many({"client_id": ObjectId(data["clientId"])}, session=session)
                get_db().client_atol_info.delete_many({"client_id": ObjectId(data["clientId"])}, session=session)
                get_db().client_bond_info.delete_many({"client_id": ObjectId(data["clientId"])}, session=session)
                if data.get("insurance"):
                    for item in data["insurance"]:
                        item["files"] = [{"file_id": file_id} for file_id in item["files"]]
                        if item.get("supplierListFile"):
                            item["supplierListFile"] = {"file_id": item["supplierListFile"]}
                        insurance = ClientInsuranceInfoSchema().load({**item, "clientId": ObjectId(data["clientId"])})
                        get_db().client_insurance_info.insert_one(insurance, session=session)

                if data.get("bond"):
                    for item in data["bond"]:
                        item["files"] = [{"file_id": file_id} for file_id in item["files"]]
                        bond = ClientBondInfoSchema().load({**item, "clientId": ObjectId(data["clientId"])})
                        get_db().client_bond_info.insert_one(bond, session=session)

                if data.get("atol"):
                    for item in data["atol"]:
                        item["files"] = [{"file_id": file_id} for file_id in item["files"]]
                        atol = ClientAtolInfoSchema().load({**item, "clientId": ObjectId(data["clientId"])})
                        get_db().client_atol_info.insert_one(atol, session=session)

    def upsert_anomalies(self, data):
        with get_db().client.start_session() as session:
            with session.start_transaction():
                get_db().client_anomaly.delete_many({"client_id": ObjectId(data["clientId"])})
                for item in data["anomalies"]:
                    anomaly_dict = {"clientId": ObjectId(data["clientId"]), "anomalyId": item["anomalyId"]}
                    if item.get("customFieldValue") is not None:
                        anomaly_dict["customFieldValue"] = item["customFieldValue"]
                    if item.get("elements"):
                        anomaly_dict["elements"] = item["elements"]
                    if item.get("bankGuaranteeValue"):
                        anomaly_dict["bankGuaranteeValue"] = item["bankGuaranteeValue"]
                    if item.get("fromDate"):
                        date_obj = datetime.strptime(item["fromDate"], "%Y-%m-%dT%H:%M:%S.%fZ")
                        formatted_date = date_obj.strftime("%Y-%m-%d")
                        anomaly_dict["fromDate"] = formatted_date
                    if item.get("toDate"):
                        date_obj = datetime.strptime(item["toDate"], "%Y-%m-%dT%H:%M:%S.%fZ")
                        formatted_date = date_obj.strftime("%Y-%m-%d")
                        anomaly_dict["toDate"] = formatted_date
                    if item.get("ticketMasterLimitValue"):
                        anomaly_dict["ticketMasterLimitValue"] = item["ticketMasterLimitValue"]
                    if item.get("directLimitValue"):
                        anomaly_dict["directLimitValue"] = item["directLimitValue"]
                    if item.get("considerBookingType"):
                        anomaly_dict["considerBookingType"] = item["considerBookingType"]
                    anomaly = ClientAnomalySchema().load(anomaly_dict)
                    get_db().client_anomaly.insert_one(anomaly)

    def upsert_banking_columns(self, data):
        with get_db().client.start_session() as session:
            with session.start_transaction():
                get_db().client_banking_column.delete_many({"client_id": ObjectId(data["clientId"])}, session=session)
                for object_id in data["columns"]:
                    banking_column = BankingColumnSchema().load(
                        {"clientId": ObjectId(data["clientId"]), "column": object_id}
                    )
                    get_db().client_banking_column.insert_one(banking_column, session=session)

    def upsert_claim_columns(self, data):
        if data["status"] == "published" and not get_db().client_banking_column.find_one(
            {"client_id": ObjectId(data["clientId"])}
        ):
            raise ServiceException("Mandatory Banking Columns needs to be selected")
        with get_db().client.start_session() as session:
            with session.start_transaction():
                basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(data["clientId"])})
                if not basic_info:
                    raise ServiceException(message="Client not found")
                get_db().client_claim_column.delete_many({"client_id": ObjectId(data["clientId"])}, session=session)
                for object_id in data["columns"]:
                    claim_column = ClientClaimColumnSchema().load(
                        {"clientId": ObjectId(data["clientId"]), "column": object_id}
                    )
                    get_db().client_claim_column.insert_one(claim_column, session=session)
                basic_info["status"] = data["status"]
                get_db().client_basic_info.update_one({"_id": basic_info["_id"]}, {"$set": basic_info}, session=session)

    @client_details_access()
    def get_client(self, client_id):
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if not client_basic_info:
            raise ServiceException("Client Not Found")
        else:
            if client_basic_info.get("type_of_trust_account") is not None:
                trust_type_name = get_db().lookup_trust_type.find_one(
                    {"_id": client_basic_info["type_of_trust_account"]}
                )
            else:
                trust_type_name = None

        client_check_info_list = list(get_db().client_check_info.find({"client_id": ObjectId(client_id)}))
        additional_checks = [
            ClientCheckInfoSchema().dump(client_check_info) for client_check_info in client_check_info_list
        ]

        client_bank_list = list(get_db().client_bank_acct_details.find({"client_id": ObjectId(client_id)}))

        client_insurance_info_list = list(get_db().client_insurance_info.find({"client_id": ObjectId(client_id)}))
        client_insurance_list = []
        for item in client_insurance_info_list:
            res = []

            for file in item["files"]:
                client_files = get_db().client_files.find_one(
                    {"file_id": file["file_id"]}, projection={"file_id": 1, "file_name": 1, "_id": 0}
                )
                res.append(ClientFilesSchema().dump(client_files))

            supplier_list = None
            if item.get("supplier_list_file"):
                supplier_list = get_db().client_files.find_one(
                    {"file_id": item["supplier_list_file"]["file_id"]},
                    projection={"file_id": 1, "file_name": 1, "_id": 0},
                )
            item.update({"files": res, "supplier_list_file": ClientFilesSchema().dump(supplier_list)})
            client_insurance_list.append(item)

        client_insurance = [
            ClientInsuranceInfoSchema().dump(client_insurance_info) for client_insurance_info in client_insurance_list
        ]
        client_bond_info_list = list(get_db().client_bond_info.find({"client_id": ObjectId(client_id)}))
        client_bond_list = []
        for item in client_bond_info_list:
            res = []
            for file in item["files"]:
                client_files = get_db().client_files.find_one(
                    {"file_id": file["file_id"]}, projection={"file_id": 1, "file_name": 1, "_id": 0}
                )
                res.append(ClientFilesSchema().dump(client_files))
            item.update({"files": res})
            client_bond_list.append(item)

        client_bond = [ClientBondInfoSchema().dump(client_bond_info) for client_bond_info in client_bond_list]
        client_atol_info_list = list(get_db().client_atol_info.find({"client_id": ObjectId(client_id)}))
        client_atol_list = []
        for item in client_atol_info_list:
            res = []
            for file in item["files"]:
                client_files = get_db().client_files.find_one(
                    {"file_id": file["file_id"]}, projection={"file_id": 1, "file_name": 1, "_id": 0}
                )
                res.append(ClientFilesSchema().dump(client_files))
            item.update({"files": res})
            client_atol_list.append(item)
        client_atol = [ClientAtolInfoSchema().dump(client_atol_info) for client_atol_info in client_atol_list]

        anomaly_list = list(
            get_db().client_anomaly.find(
                {"client_id": ObjectId(client_id)},
                projection={
                    "_id": 0,
                    "anomaly_id": 1,
                    "custom_field_value": 1,
                    "elements": 1,
                    "bank_guarantee_value": 1,
                    "from_date": 1,
                    "consider_booking_type": 1,
                    "to_date": 1,
                    "ticket_master_limit_value": 1,
                    "direct_limit_value": 1,
                },
            )
        )
        anomalies = [
            ClientAnomalySchema().dump(
                {
                    **client_anomaly_info,
                    "from_date": format_date_to_iso(client_anomaly_info.get("from_date")),
                    "to_date": format_date_to_iso(client_anomaly_info.get("to_date")),
                }
            )
            for client_anomaly_info in anomaly_list
        ]
        client_bank = [ClientBankAcctDetailsSchema().dump(client_bank_info) for client_bank_info in client_bank_list]

        banking_list = list(get_db().client_banking_column.find({"client_id": ObjectId(client_id)}))
        banking_columns = [
            BankingColumnSchema().dump(banking_column_info)["column"] for banking_column_info in banking_list
        ]

        claims_list = list(get_db().client_claim_column.find({"client_id": ObjectId(client_id)}))
        claim_columns = [
            ClientClaimColumnSchema().dump(claim_column_info)["column"] for claim_column_info in claims_list
        ]

        frequency_list = list(get_db().client_claim_frequency.find({"client_id": ObjectId(client_id)}))
        claim_frequencies = [
            ClientClaimFrequencySchema().dump(claim_frequency)["frequencyId"] for claim_frequency in frequency_list
        ]

        client_limit_list = list(get_db().client_limit.find({"client_id": ObjectId(client_id)}))
        client_limit = [ClientLimitSchema().dump(client_limits) for client_limits in client_limit_list]
        authorized_signatories_list = list(
            get_db().client_authorized_signatories_info.find({"client_id": ObjectId(client_id)})
        )
        authorized_signatories = [
            ClientAuthorizedSignatoriesInfoSchema().dump(authorized_signatories)
            for authorized_signatories in authorized_signatories_list
        ]
        address_info = get_db().client_address.find_one({"client_id": ObjectId(client_id)})
        for item in client_limit:
            if client_limit and item.get("currency") and item.get("fromDate") and item.get("toDate"):
                flag = self.__total_annual_revenue_and_claim_limit_calculation(
                    client_id,
                    item["currency"],
                    item["fromDate"],
                    item["toDate"],
                    item.get("totalAnnualRevenue"),
                    item.get("maximumNoOfClaims"),
                )
                item["isClaimsExceeded"] = flag["isClaimsExceeded"]
                item["isAnnualRevenueLimitExceeded"] = flag["isAnnualRevenueLimitExceeded"]

        return {
            "bankDetails": client_bank,
            "insurance": client_insurance,
            "bond": client_bond,
            "atol": client_atol,
            "additionChecks": additional_checks,
            "anomalies": anomalies,
            "bankingColumns": banking_columns,
            "claimColumns": claim_columns,
            "frequency": claim_frequencies,
            "limits": client_limit,
            "address": ClientAddressSchema().dump(address_info),
            "trustAccountName": trust_type_name["name"] if trust_type_name is not None else None,
            **ClientBasicInfoSchema().dump(client_basic_info),
            "authorizedSignatories": authorized_signatories,
        }

    def clients_details_list(self, user_id, search_query):
        data = list(
            get_db().client_basic_info.aggregate(
                [
                    {"$addFields": {"user_id": user_id}},
                    {
                        "$lookup": {
                            "from": "user",
                            "localField": "user_id",
                            "foreignField": "user_id",
                            "let": {"client_id": "$_id"},
                            "pipeline": [{"$match": {"$expr": {"$in": ["$$client_id", "$clients"]}}}],
                            "as": "user",
                        }
                    },
                    {"$unwind": "$user"},
                    {
                        "$match": {
                            "$or": [
                                {"full_name": {"$regex": f"^.*{search_query}.*$", "$options": "i"}},
                                {"friendly_name": {"$regex": f"^.*{search_query}.*$", "$options": "i"}},
                                {"c_id": {"$regex": f"^.*{search_query}.*$", "$options": "i"}},
                            ]
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_bank_acct_details",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "as": "bank",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_insurance_info",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "as": "insurance",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_atol_info",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "as": "atol",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_bond_info",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "as": "bond",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_address",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "as": "address",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "authorized_signatories",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "as": "authorized_signatories",
                        }
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "clientId": {"$toString": "$_id"},
                            "cId": "$c_id",
                            "status": "$status",
                            "friendlyName": "$friendly_name",
                            "currency": "$bank.currency",
                            "typeOfTrustAccount": "$type_of_trust_account",
                            "insuranceExpiryDate": "$insurance.expiry_date",
                            "bondExpiryDate": "$bond.expiry_date",
                            "atolExpiryDate": "$atol.expiry_date",
                            "goLiveDate": "$go_live_date",
                            "country": "$address.country",
                            "line1": "$address.line1",
                            "line2": "$address.line2",
                            "line3": "$address.line3",
                            "postCode": "$address.postcode",
                            "town": "$address.town",
                            "pocName": "$point_of_contact",
                            "pocEmail": "$email",
                            "isDisabled": "$is_disabled",
                        }
                    },
                ],
                collation={"locale": "en", "strength": 1},
            )
        )
        client_list = []
        trust_type_list = list(get_db().lookup_trust_type.find())
        one_month_from_now = datetime.now() + timedelta(days=30)
        for client in data:
            trust_type = list(
                filter(
                    lambda x: x["_id"] == client["typeOfTrustAccount"],
                    trust_type_list,
                )
            )

            insurance_expiry_date_list = []
            insurance_list = client.get("insuranceExpiryDate")
            if insurance_list:
                for i_date in insurance_list:
                    if i_date <= one_month_from_now:
                        insurance_expiry_date_list.append(i_date.strftime("%Y-%m-%d"))
                insurance_expiry_date_list.sort()
                client["insuranceExpiryDate"] = insurance_expiry_date_list
            client["showInsuranceExpiry"] = True if client["insuranceExpiryDate"] else False

            bond_expiry_date_list = []
            bond_list = client.get("bondExpiryDate")
            if bond_list:
                for i_date in bond_list:
                    if i_date <= one_month_from_now:
                        bond_expiry_date_list.append(i_date.strftime("%Y-%m-%d"))
                bond_expiry_date_list.sort()
                client["bondExpiryDate"] = bond_expiry_date_list
            client["showBondExpiry"] = True if client["bondExpiryDate"] else False

            atol_expiry_date_list = []
            atol_list = client.get("atolExpiryDate")
            if atol_list:
                for i_date in atol_list:
                    if i_date <= one_month_from_now:
                        atol_expiry_date_list.append(i_date.strftime("%Y-%m-%d"))
                atol_expiry_date_list.sort()
                client["atolExpiryDate"] = atol_expiry_date_list
            client["showAtolExpiry"] = True if client.get("atolExpiryDate") else False
            client["typeOfTrustAccount"] = str(client["typeOfTrustAccount"])
            if trust_type:
                client["typeOfTrustAccountName"] = trust_type[0]["name"]

            client_list.append(client)
        return sorted(
            client_list,
            key=lambda x: (
                -x["showInsuranceExpiry"],
                -x["showAtolExpiry"],
                x["showBondExpiry"],
                x["friendlyName"].lower(),
            ),
        )

    def list_clients(self, user_id, claim_from_tbr=None, is_disabled=None):
        user = get_db().user.find_one({"user_id": user_id})
        match_condition = {"status": "published", "_id": {"$in": user["clients"]}}
        if claim_from_tbr is not None:
            match_condition["claim_from_tbr"] = claim_from_tbr
        if is_disabled is not None:
            match_condition["is_disabled"] = is_disabled

        clients = list(
            get_db().client_basic_info.find(
                match_condition,
                projection={
                    "_id": 0,
                    "clientId": {"$toString": "$_id"},
                    "clientName": "$full_name",
                    "clientFriendlyName": "$friendly_name",
                },
                collation={"locale": "en", "strength": 1},
                sort=[("full_name", 1)],
            )
        )
        return clients

    def client_file_upload(self, client_id, file_type, file):
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if not client_basic_info:
            raise ServiceException(message="client not found")

        supplier_list = None
        actual_file_name = file.filename
        file_path = f"{current_app.config['TEMP_DIR']}/{actual_file_name}"
        file.save(file_path)

        if file_type == "insurance":
            validate_mime_type_pdf(file.content_type)
            bucket = current_app.config["INSURANCE_FILE_BUCKET"]
        elif file_type == "atol":
            validate_mime_type_pdf(file.content_type)
            bucket = current_app.config["ATOL_FILE_BUCKET"]
        elif file_type == "bond":
            validate_mime_type_file(file.content_type)
            bucket = current_app.config["BOND_FILE_BUCKET"]
        elif file_type == "supplierList":
            if validate_mime_type_xls_csv(file.content_type) is False:
                raise ServiceException(message="File format not supported, use xls, xlsx or csv files")
            else:
                bucket = current_app.config["SUPPLIER_LIST_FILE_BUCKET"]
                ws = Worksheet()
                if file.content_type == "text/csv":
                    ws.csv_to_dict(file)
                else:
                    ws.xlsx_to_dict(path=file_path)
                data_items = ws.sheet_items

                supplier_list = map(
                    lambda x: {
                        "supplierName": x.get("Supplier Name") if x.get("Supplier Name") != "None" else None,
                        "capAmount": float(x.get("Cap Amount"))
                        if x.get("Cap Amount") not in ["None", "", None]
                        else None,
                    },
                    data_items,
                )
                filtered_supplier_list = list(filter(lambda x: x and x["supplierName"] is not None, supplier_list))
                if not filtered_supplier_list:
                    raise ServiceException(message="uploaded file cannot be processed")
        elif file_type == "authorizedSignatories":
            validate_mime_type_png_jpeg(file.content_type)
            file_path = f"{current_app.config['TEMP_DIR']}/{actual_file_name}"
            bucket = current_app.config["AUTHORIZED_SIGNATORIES_BUCKET"]
        else:
            raise ServiceException(message="file_type error")

        file_id = str(uuid.uuid4())
        upload_file(bucket, file_id, file_path)

        if file_type == "supplierList":
            client_files = ClientFilesSchema().load(
                {
                    "clientId": ObjectId(client_id),
                    "fileName": file.filename,
                    "fileId": file_id,
                    "supplierList": filtered_supplier_list,
                }
            )

        else:
            client_files = ClientFilesSchema().load(
                {
                    "clientId": ObjectId(client_id),
                    "fileName": file.filename,
                    "fileId": file_id,
                }
            )
        get_db().client_files.insert_one(client_files)
        return file_id

    def client_file_download(self, access_token, file_type, file_id, user_id):
        presigned_url = False
        if file_type == "insurance":
            bucket = current_app.config["INSURANCE_FILE_BUCKET"]
            client_id = get_db().client_insurance_info.find_one(
                {"files.file_id": file_id}, projection={"_id": 0, "client_id": 1}
            )

        elif file_type == "atol":
            bucket = current_app.config["ATOL_FILE_BUCKET"]
            client_id = get_db().client_atol_info.find_one(
                {"files.file_id": file_id}, projection={"_id": 0, "client_id": 1}
            )
        elif file_type == "bond":
            bucket = current_app.config["BOND_FILE_BUCKET"]
            client_id = get_db().client_bond_info.find_one(
                {"files.file_id": file_id}, projection={"_id": 0, "client_id": 1}
            )

        elif file_type == "supplierList":
            bucket = current_app.config["SUPPLIER_LIST_FILE_BUCKET"]
            client_id = get_db().client_files.find_one({"file_id": file_id}, projection={"_id": 0, "client_id": 1})

        elif file_type == "template":
            bucket = current_app.config["FILE_TEMPLATE_BUCKET"]

        elif file_type == "banking":
            bucket = current_app.config["BANKING_FILE_BUCKET"]
            presigned_url = True
            client_id = get_db().banking_file_details.find_one(
                {"file_id": file_id}, projection={"_id": 0, "client_id": 1}
            )

        elif file_type == "claim":
            bucket = current_app.config["CLAIM_FILE_BUCKET"]
            presigned_url = True
            client_id = get_db().claims_file_details.find_one(
                {"file_id": file_id}, projection={"_id": 0, "client_id": 1}
            )

        elif file_type == "report-files":
            bucket = current_app.config["REPORTS_BUCKET"]
            presigned_url = True
            client_id = get_db().report_files.find_one({"file_id": file_id}, projection={"_id": 0, "client_id": 1})

        elif file_type == "banking-and-claim-summary":
            user_details = auth_service.get_profile_details(access_token)
            downloaded_by = user_details["username"]
            bucket = current_app.config["BANKING_AND_CLAIM_SUMMARY_BUCKET"]
            presigned_url = True
            data = get_db().banking_and_claim_summary.find_one(
                {"file_id": file_id}, projection={"_id": 0, "client_id": 1}
            )
            client_id = data
            get_db().banking_and_claim_summary.find_one_and_update(
                {"file_id": file_id},
                {
                    "$set": {
                        "updated_at": datetime.utcnow(),
                        "downloaded_by": downloaded_by,
                        "downloaded_at": datetime.utcnow(),
                    }
                },
            )
        elif file_type == "authorizedSignatories":
            bucket = current_app.config["AUTHORIZED_SIGNATORIES_BUCKET"]
            # presigned_url = True
            client_id = get_db().client_files.find_one({"file_id": file_id}, projection={"_id": 0, "client_id": 1})

        else:
            raise ServiceException(message="file_type error")

        if file_type == "template":
            download_file(bucket, file_id, f"{current_app.config['TEMP_DIR']}/{file_id}")
            return send_file(f"{current_app.config['TEMP_DIR']}/{file_id}")
        else:
            if client_id is not None:
                user = get_db().user.find_one({"user_id": user_id}, projection={"clients": 1, "_id": 0})
                if client_id["client_id"] not in user["clients"]:
                    abort(403)
                else:
                    if presigned_url is True:
                        return generate_presigned_url("get_object", bucket, file_id, 300)
                    download_file(bucket, file_id, f"{current_app.config['TEMP_DIR']}/{file_id}")
                    return send_file(f"{current_app.config['TEMP_DIR']}/{file_id}")
            else:
                raise ServiceException(message="access denied")

    def get_client_users(self, client_id, access_token):
        ptt_clients = auth_service.get_list_clients()
        users = get_db().user.find({"role": "ptt-client", "clients": ObjectId(client_id)})
        user_ids = [user["user_id"] for user in users]
        client_users = list(filter(lambda x: x["userId"] in user_ids, ptt_clients))

        return client_users

    def update_client_users(self, client_id, data):
        client_users = list(get_db().user.find({"role": "ptt-client"}, projection={"user_id": 1}))
        user_ids = []
        for user in client_users:
            user_ids.append(user["user_id"])
        common_user = [value for value in data["users"] if value in user_ids]
        excluded_user = [value for value in user_ids if value not in data["users"]]
        with get_db().client.start_session() as session:
            with session.start_transaction():
                get_db().user.update_many(
                    {"user_id": {"$in": common_user}}, {"$addToSet": {"clients": ObjectId(client_id)}}, session=session
                )
                get_db().user.update_many(
                    {"user_id": {"$in": excluded_user}}, {"$pull": {"clients": ObjectId(client_id)}}, session=session
                )

    @banking_details_access()
    def get_escrow_amount_banking(self, id, client_id, file_type):
        banking_metadata = get_db().banking_metadata.find_one({"_id": ObjectId(id)})
        response = {}
        item_count = banking_metadata["banking_files"][-1]["item_count"]
        deposit = banking_metadata["banking_files"][-1]["deposit"]
        if client_id == current_app.config.get("WLH_NEW"):
            total_gross_amount = banking_metadata["banking_files"][-1]["gross_amount"]
            final_net_amount = banking_metadata["banking_files"][-1]["net_amount"]

            currency = "GBP"
            response[currency] = {
                "currency": currency,
                "grossAmount": total_gross_amount[currency],
                "agreedNetAmount": final_net_amount[currency],
            }

        else:
            client_specific_data = get_db().banking_file_details.aggregate(
                [
                    {"$match": {"banking_id": ObjectId(id), "deleted": False}},
                    {
                        "$group": {
                            "_id": "$currency_code",
                            "amount": {"$sum": "$original_amount"},
                            "count": {"$count": {}},
                        }
                    },
                    {
                        "$project": {
                            "amount": 1,
                            "count": 1,
                        }
                    },
                ],
                allowDiskUse=True,
            )
            for item in client_specific_data:
                currency = item["_id"]
                response[currency] = {
                    "currency": currency,
                    "count": item_count[currency],
                    "escrowAmount": deposit[currency],
                    "actualAmount": item["amount"],
                }
        return list(response.values())

    @claim_details_access()
    def get_escrow_amount_claims(self, id, client_id, file_type):
        claims_metadata = get_db().claims_metadata.find_one({"_id": ObjectId(id)})
        response = {}
        item_count = claims_metadata["claim_files"][-1]["item_count"]
        claim_total = claims_metadata["claim_files"][-1]["claim_total"]
        client_specific_data = get_db().claims_file_details.aggregate(
            [
                {"$match": {"claims_id": ObjectId(id), "deleted": False}},
                {
                    "$group": {
                        "_id": "$currency_code",
                        "amount": {"$sum": "$original_amount"},
                        "count": {"$count": {}},
                    }
                },
                {
                    "$project": {
                        "amount": 1,
                        "count": 1,
                    }
                },
            ],
            allowDiskUse=True,
        )
        for item in client_specific_data:
            currency = item["_id"]
            response[currency] = {
                "currency": currency,
                "count": item_count[currency],
                "escrowAmount": claim_total[currency],
                "actualAmount": item["amount"],
            }
        return list(response.values())

    def update_escrow_multiplier(self, client_id, user_id, data):
        request_date = data.get("date")
        get_db().client_escrow_multiplier.update_one(
            {"client_id": ObjectId(client_id), "date": request_date},
            {
                "$set": ClientEscrowMultiplierSchema().load(
                    {**data, "clientId": ObjectId(client_id), "modifiedBy": user_id}
                )
            },
            upsert=True,
        )

    def get_escrow_multiplier(self, client_id, date):
        ptt_users = auth_service.ptt_users_list()
        escrow_multiplier = get_db().client_escrow_multiplier.find_one(
            {"client_id": ObjectId(client_id), "date": date},
            projection={"_id": 0, "multiplier": 1, "modified_by": 1, "debit_multiplier": 1, "credit_multiplier": 1},
        )
        if escrow_multiplier:
            user = next(filter(lambda x: x["userId"] == escrow_multiplier["modified_by"], ptt_users), {})
            escrow_multiplier["modified_by"] = user.get("name") or user.get("email")
        if not escrow_multiplier:
            abort(404)
        return escrow_multiplier

    def clients_details_list_export(self, user_id, search_query):
        data = list(
            get_db().client_basic_info.aggregate(
                [
                    {"$addFields": {"user_id": user_id}},
                    {
                        "$lookup": {
                            "from": "user",
                            "localField": "user_id",
                            "foreignField": "user_id",
                            "let": {"client_id": "$_id"},
                            "pipeline": [{"$match": {"$expr": {"$in": ["$$client_id", "$clients"]}}}],
                            "as": "user",
                        }
                    },
                    {"$unwind": "$user"},
                    {
                        "$match": {
                            "$or": [
                                {"full_name": {"$regex": f"^.*{search_query}.*$", "$options": "i"}},
                                {"friendly_name": {"$regex": f"^.*{search_query}.*$", "$options": "i"}},
                                {"c_id": {"$regex": f"^.*{search_query}.*$", "$options": "i"}},
                            ]
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_bank_acct_details",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "as": "bank",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "lookup_trust_type",
                            "localField": "type_of_trust_account",
                            "foreignField": "_id",
                            "as": "trust_type",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_insurance_info",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "as": "insurance",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_bond_info",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "as": "bond",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_address",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "as": "address",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_limit",
                            "localField": "_id",
                            "foreignField": "client_id",
                            "pipeline": [
                                {
                                    "$match": {
                                        "$expr": {
                                            "$and": [
                                                {"$eq": ["$preferred", True]},
                                            ]
                                        }
                                    },
                                },
                            ],
                            "as": "limit",
                        }
                    },
                    {"$unwind": {"path": "$limit", "preserveNullAndEmptyArrays": True}},
                    {
                        "$project": {
                            "_id": "banking._id",
                            "from_Date": "$From_date",
                            "to_date": "$To_date",
                            "total_in_trust": "$banking.totalInTrust",
                            "clientId": {"$toString": "$_id"},
                            "cId": "$c_id",
                            "status": "$status",
                            "friendlyName": "$friendly_name",
                            "preferred": "$limit.preferred",
                            "currency": "$limit.currency",
                            "fromDate": "$limit.from_date",
                            "toDate": "$limit.to_date",
                            "balanceAT": "$limit.total_annual_revenue",
                            "actualAT": "$banking.totalInTrust",
                            "budgetedNOC": "$limit.max_no_of_claims",
                            "actualNOC": "$claims.no_of_claims",
                            "typeOfTrustAccount": "$trust_type.name",
                            "insuranceExpiryDate": "$insurance.expiry_date",
                            "bondExpiryDate": "$bond.expiry_date",
                            "goLiveDate": "$go_live_date",
                            "country": "$address.country",
                            "line1": "$address.line1",
                            "line2": "$address.line2",
                            "line3": "$address.line3",
                            "postCode": "$address.postcode",
                            "town": "$address.town",
                            "pocName": "$point_of_contact",
                            "pocEmail": "$email",
                            "isDisabled": "$is_disabled",
                        }
                    },
                ],
                collation={"locale": "en", "strength": 1},
            )
        )
        for item in data:
            if item.get("preferred") is True:
                if item.get("currency") and item.get("fromDate") and item.get("toDate"):
                    item["actualAT"] = self.__total_annual_revenue_and_claim_limit_calculation(
                        item["clientId"],
                        item.get("currency"),
                        item["fromDate"].strftime(
                            "%Y-%m-%d",
                        ),
                        item["toDate"].strftime(
                            "%Y-%m-%d",
                        ),
                        item.get("balanceAT"),
                        item.get("budgetedNOC"),
                    )["TotalAnnualRevenue"]
                    item["actualNOC"] = self.__total_annual_revenue_and_claim_limit_calculation(
                        item["clientId"],
                        item.get("currency"),
                        item["fromDate"].strftime(
                            "%Y-%m-%d",
                        ),
                        item["toDate"].strftime(
                            "%Y-%m-%d",
                        ),
                        item.get("balanceAT"),
                        item.get("budgetedNOC"),
                    )["totalNoOfClaims"]
        return data

    def get_client_friendly_name(self, client_id):
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if not client_basic_info:
            raise ServiceException("Client Not Found")
        else:
            client_name = client_basic_info["friendly_name"]
        return client_name

    def list_atol_standard_clients(self, user_id):
        user = get_db().user.find_one({"user_id": user_id})
        trust_type = get_db().lookup_trust_type.find_one({"name": "ATOL Standard"})
        match_condition = {
            "status": "published",
            "_id": {"$in": user["clients"]},
            "type_of_trust_account": trust_type["_id"],
        }

        clients = list(
            get_db().client_basic_info.find(
                match_condition,
                projection={
                    "_id": 0,
                    "clientId": {"$toString": "$_id"},
                    "clientName": "$full_name",
                    "clientFriendlyName": "$friendly_name",
                },
                collation={"locale": "en", "strength": 1},
                sort=[("full_name", 1)],
            )
        )
        return clients

    def update_client_max_cap_count(self, client_id):
        try:
            client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
            if not client_basic_info:
                raise ServiceException(message="Client not found")

            max_cap_count = client_basic_info.get("max_cap_anomaly_count", 0)

            increment = 2 if max_cap_count == 0 else (1 if max_cap_count < 6 else 0)

            if increment > 0:
                update_result = get_db().client_basic_info.update_one(
                    {"_id": ObjectId(client_id)}, {"$inc": {"max_cap_anomaly_count": increment}}
                )
                if update_result.matched_count != 1:
                    raise ServiceException(message="Failed to update client max cap count")
                return jsonify({"message": "Client max cap count updated successfully"})
            else:
                raise ServiceException(message="Client max cap count is already 6 or higher")

        except pymongo.errors.BulkWriteError as e:
            raise ServiceException(message="Error updating client max cap count: {}".format(str(e))) from e

    def update_client_max_cap_rollover_count(self, client_id):
        try:
            client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
            if not client_basic_info:
                raise ServiceException(message="Client not found")

            max_cap_count = client_basic_info.get("max_cap_rollover_anomaly_count", 0)

            increment = 2 if max_cap_count == 0 else (1 if max_cap_count < 6 else 0)

            if increment > 0:
                update_result = get_db().client_basic_info.update_one(
                    {"_id": ObjectId(client_id)}, {"$inc": {"max_cap_rollover_anomaly_count": increment}}
                )
                if update_result.matched_count != 1:
                    raise ServiceException(message="Failed to update client max cap rollover count")
                return jsonify({"message": "Client max cap rollover count updated successfully"})
            else:
                raise ServiceException(message="Client max cap rollover count is already 6 or higher")

        except pymongo.errors.BulkWriteError as e:
            raise ServiceException(message="Error updating client max cap rollover count: {}".format(str(e))) from e

    def manuals_upload(self, client_id, file):
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if not client_basic_info:
            raise ServiceException(message="client not found")
        actual_file_name = file.filename
        file_path = f"{current_app.config['TEMP_DIR']}/{actual_file_name}"
        file.save(file_path)

        validate_mime_type_pdf(file.content_type)
        file_path = f"{current_app.config['TEMP_DIR']}/{actual_file_name}"
        bucket = current_app.config["MANUALS_BUCKET"]
        file_id = str(uuid.uuid4())
        upload_file(bucket, file_id, file_path)
        
        client_files = ClientFilesSchema().load(
                {
                    "clientId": ObjectId(client_id),
                    "fileName": file.filename,
                    "fileId": file_id,
                }
            )
        manuals_file = ClientManualsInfoSchema().load(
            {
                "clientId": ObjectId(client_id),
                "manuals" : {
                    "fileName": file.filename,
                    "fileId": file_id,
                }
            }
        )
        filter = {"client_id": ObjectId(client_id)}
        get_db().client_files.insert_one(client_files)
        get_db().client_manuals_info.update_one(filter,{"$set": manuals_file},upsert=True)
        return file_id

    def manuals_download(self,access_token, file_id, user_id):
        presigned_url = False
        bucket = current_app.config["MANUALS_BUCKET"]
        # presigned_url = True
        client_id = get_db().client_files.find_one({"file_id": file_id}, projection={"_id": 0, "client_id": 1})

        if client_id is not None:
            user = get_db().user.find_one({"user_id": user_id}, projection={"clients": 1, "_id": 0})
            if client_id["client_id"] not in user["clients"]:
                abort(403)
            else:
                if presigned_url is True:
                    return generate_presigned_url("get_object", bucket, file_id, 300)
                download_file(bucket, file_id, f"{current_app.config['TEMP_DIR']}/{file_id}")
                return send_file(f"{current_app.config['TEMP_DIR']}/{file_id}")
        else:
            raise ServiceException(message="access denied")

    def get_manuals(self, client_id):
        manuals_info_list = list(get_db().client_manuals_info.find({"client_id": ObjectId(client_id)}))
        client_manuals = [ClientManualsInfoSchema().dump(manuals) for manuals in manuals_info_list]
        return client_manuals
    

    def create_manuals_log(self, data):
        data = ClientManualsLogsInfoSchema().load(data)
        manuals_log = get_db().client_manuals_log.insert_one({**data})
        return {"_id": str(manuals_log.inserted_id)}
    
    def update_deductable_amount(self, client_id, user_id, data):
        date=data.get("date")
        get_db().client_deductable_amount.update_one(
            {"client_id": ObjectId(client_id), "date": date},
            {
                "$set": ClientDeductableAmountSchema().load(
                    {**data, "clientId": ObjectId(client_id), "modifiedBy": user_id}
                )
            },
            upsert=True,
        )


    def get_deductable_amount(self, client_id, date):
        ptt_users = auth_service.ptt_users_list()
        deductable_info = get_db().client_deductable_amount.find_one({"client_id":ObjectId(client_id),"date":date})
       
        if not deductable_info:
            abort(404)
        if deductable_info:
            deductableAmount = deductable_info.get("deductable_amount")

        if deductable_info:
            user = next(filter(lambda x: x["userId"] == deductable_info["modified_by"], ptt_users), {})
            deductable_info["modified_by"] = user.get("name") or user.get("email")
        modified_by = deductable_info.get("modified_by")


        return { "deductableAmount": deductableAmount,"modified_by": modified_by}
    
    


    
client_service = ClientService()
