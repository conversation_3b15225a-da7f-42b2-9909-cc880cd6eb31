import itertools
import re
import uuid
import boto3
import botocore
from botocore.exceptions import ClientError, ParamValidationError
from flask import request, current_app, jsonify, send_file
from flaskr.helpers.auth import get_token, auth_error_handler
from flaskr.models import get_db
from flaskr.services.exceptions import ServiceException
from flaskr.helpers.boto3_handler import download_file, upload_file, validate_mime_type_png_jpeg
from bson import ObjectId
from flaskr.models.user import UserSchema
import os

client = boto3.client("cognito-idp")


class AuthService:
    def __init__(self):
        self.client = boto3.client("cognito-idp")

    def __list_user_groups(self, username):
        return self.client.admin_list_groups_for_user(
            Limit=5,
            UserPoolId=current_app.config["USER_POOL_ID"],
            Username=username,
        )

    def list_users_in_groups(self, groupname):
        return self.client.list_users_in_group(
            UserPoolId=current_app.config["USER_POOL_ID"],
            GroupName=groupname,
        )

    def list_clients(self):
        users = self.client.list_users(UserPoolId=current_app.config["USER_POOL_ID"])["Users"]
        ptt_admins = self.client.list_users_in_group(
            UserPoolId=current_app.config["USER_POOL_ID"], GroupName="ptt-admin"
        )["Users"]
        ptt_users = self.client.list_users_in_group(
            UserPoolId=current_app.config["USER_POOL_ID"], GroupName="ptt-user"
        )["Users"]
        ptt_clients = list(itertools.filterfalse(lambda x: x in ptt_users + ptt_admins, users))
        return ptt_clients

    def ptt_users_list(self):
        admins = auth_service.list_users_in_groups("ptt-admin")
        ptt_users = auth_service.list_users_in_groups("ptt-user")
        response = []
        for user in admins["Users"] + ptt_users["Users"]:
            user_data = {"status": user["UserStatus"]}
            user_attributes = {}
            for attribute in user["Attributes"]:
                user_attributes[attribute["Name"]] = attribute["Value"]
            user_data.update(
                {
                    "userId": user_attributes.get("sub"),
                    "email": user_attributes.get("email"),
                    "name": user_attributes.get("name"),
                }
            )
            response.append(user_data)
        return response

    def get_list_clients(self):
        clients = auth_service.list_clients()
        response = []
        for user in clients:
            user_data = {"status": user["UserStatus"], "username": user["Username"]}
            user_attributes = {}
            for attribute in user["Attributes"]:
                user_attributes[attribute["Name"]] = attribute["Value"]
            user_data.update(
                {
                    "userId": user_attributes.get("sub"),
                    "email": user_attributes.get("email"),
                    "name": user_attributes.get("name"),
                }
            )
            response.append(user_data)
        return response

    def login(self, username, password):
        try:
            auth_response = self.client.initiate_auth(
                ClientId=current_app.config["APP_CLIENT_ID"],
                AuthFlow="USER_PASSWORD_AUTH",
                AuthParameters={"USERNAME": username, "PASSWORD": password},
            )
            user_groups = self.__list_user_groups(username)
            if "AuthenticationResult" in auth_response:
                if "Groups" in user_groups:
                    return {
                        "authenticationResult": auth_response["AuthenticationResult"],
                        "userGroups": user_groups["Groups"],
                    }
                else:
                    return {"authenticationResult": auth_response["AuthenticationResult"], "userGroups": []}
            elif "ChallengeName" in auth_response and auth_response["ChallengeName"] == "SOFTWARE_TOKEN_MFA":
                return auth_response
            elif "ChallengeName" in auth_response and auth_response["ChallengeName"] == "SMS_MFA":
                return auth_response
            elif "ChallengeName" in auth_response and auth_response["ChallengeName"] == "NEW_PASSWORD_REQUIRED":
                return auth_response
            else:
                current_app.logger.error(f"Failed to authenticate user {username}, response received {auth_response}")
        except ClientError as e:
            current_app.logger.error("Failed to login", exc_info=e)
            return auth_error_handler(e)

    def change_password(self, current_password, new_password):
        try:
            return self.client.change_password(
                PreviousPassword=current_password,
                ProposedPassword=new_password,
                AccessToken=get_token(request.headers.get("Authorization")),
            )

        except ClientError as e:
            current_app.logger.error("Failed to change the password", exc_info=e)
            return auth_error_handler(e)
        except ParamValidationError as e:
            current_app.logger.error("Failed to change the password", exc_info=e)
            res = re.findall("valid min length: 6", str(e))
            if res:
                return jsonify({"message": "Password must contain atleast 8 characters"}), 400
            else:
                return str(e), 400

    def forgot_password(self, username):
        try:
            return self.client.forgot_password(ClientId=current_app.config["APP_CLIENT_ID"], Username=username)
        except ClientError as e:
            current_app.logger.error("Forgot password failed", exc_info=e)
            return auth_error_handler(e)

    def confirm_forgot_password(self, username, password, confirmation_code):
        try:
            return self.client.confirm_forgot_password(
                ClientId=current_app.config["APP_CLIENT_ID"],
                Username=username,
                ConfirmationCode=confirmation_code,
                Password=password,
            )
        except ClientError as e:
            current_app.logger.error("Failed to change the password", exc_info=e)
            return auth_error_handler(e)

        except ParamValidationError as e:
            current_app.logger.error("Failed to change the password", exc_info=e)
            res = re.findall("valid min length: 6", str(e))
            if res:
                return jsonify({"message": "Password must contain atleast 8 characters"}), 400
            else:
                return str(e), 400

    def create_client(self, user_id: str, username: str, email: str, client_id: str, name: str) -> dict:
        if user_id:
            user = get_db().user.find_one_and_update(
                {"user_id": user_id},
                {
                    "$addToSet": {"clients": ObjectId(client_id)},
                },
            )
            if not user:
                raise ServiceException("user not found")
            current_app.logger.info(f"Added client with client_id {client_id} to exisiting user with user_id {user_id}")
            return {"msg": "Client added for existing user successfully"}

        try:
            response = self.client.admin_create_user(
                UserPoolId=current_app.config["USER_POOL_ID"],
                Username=username,
                UserAttributes=[
                    {"Name": "email", "Value": email},
                    {"Name": "email_verified", "Value": "true"},
                    {"Name": "name", "Value": name},
                    {"Name": "picture", "Value": "profile"},
                ],
                DesiredDeliveryMediums=["EMAIL"],
            )
            user_attributes = response["User"]["Attributes"]
            user_id = next(filter(lambda x: x["Name"] == "sub", user_attributes))["Value"]
            get_db().user.insert_one(
                {
                    "user_id": user_id,
                    "role": "ptt-client",
                    "confirmation_status": "Force change password",
                    "clients": [ObjectId(client_id)],
                }
            )
            # Check if client_id matches the environment variable and add to group
            if client_id == os.environ.get("BLUESTYLE"):
                self.client.admin_add_user_to_group(
                    UserPoolId=current_app.config["USER_POOL_ID"],
                    Username=username,
                    GroupName="ptt-erv-dashboard",
                )
                current_app.logger.info(f"Added user {username} to group 'ptt-erv-dashboard'")

            current_app.logger.info(f"Created new ptt-client user with username {response['User']['Username']}")
            return response
        except ClientError as e:
            current_app.logger.error("Failed to create client", exc_info=e)
            if e.response["Error"]["Code"] == "UsernameExistsException":
                raise ServiceException(e.response["Error"]["Message"])
            raise ServiceException("An unexpected error occurred.")

    def respond_to_auth_challenge(self, username, password, session):
        try:
            auth_response = self.client.admin_respond_to_auth_challenge(
                UserPoolId=current_app.config["USER_POOL_ID"],
                ClientId=current_app.config["APP_CLIENT_ID"],
                ChallengeName="NEW_PASSWORD_REQUIRED",
                ChallengeResponses={"USERNAME": username, "NEW_PASSWORD": password},
                Session=session,
            )
            user_groups = self.__list_user_groups(username)
            # Get user ID from the authentication result
            user = self.client.get_user(AccessToken=auth_response["AuthenticationResult"]["AccessToken"])
            user_attributes = user["UserAttributes"]
            user_id = next(filter(lambda x: x["Name"] == "sub", user_attributes))["Value"]
            # Check if user already exists in database
            existing_user = get_db().user.find_one({"user_id": user_id})
            if existing_user:
                # Keep existing role if user exists
                role_specific_details = {"role": existing_user.get("role", "ptt-client")}
            else:
                # Default to ptt-user for new users created in Cognito
                role_specific_details = {"role": "ptt-user"}
            if "AuthenticationResult" in auth_response:
                if "Groups" in user_groups:
                    admin_group = next(filter(lambda x: x["GroupName"] == "ptt-admin", user_groups["Groups"]), None)
                    ptt_user_group = next(filter(lambda x: x["GroupName"] == "ptt-user", user_groups["Groups"]), None)
                    client_basic_info = get_db().client_basic_info.find({}, {"_id": 1})
                    clients = [basic_info["_id"] for basic_info in client_basic_info]

                    if ptt_user_group:
                        role_specific_details.update({"role": "ptt-user", "clients": clients})
                    if admin_group:
                        role_specific_details.update({"role": "ptt-admin", "clients": clients})
                    response = {
                        "authenticationResult": auth_response["AuthenticationResult"],
                        "userGroups": user_groups["Groups"],
                    }
                else:
                    response = {"authenticationResult": auth_response["AuthenticationResult"], "userGroups": []}
            else:
                current_app.logger.error(f"Failed to authenticate user {username}, response received {auth_response}")
            user = self.client.get_user(AccessToken=auth_response["AuthenticationResult"]["AccessToken"])
            user_attributes = user["UserAttributes"]
            user_id = next(filter(lambda x: x["Name"] == "sub", user_attributes))["Value"]
            get_db().user.update_one(
                {"user_id": user_id},
                {"$set": {"user_id": user_id, "confirmation_status": "Confirmed", **role_specific_details}},
                upsert=True,
            )
            return response

        except ClientError as e:
            current_app.logger.error("Failed to change the password", exc_info=e)
            return auth_error_handler(e)

    def user_profile_update(self, access_token, name, email, profile_pic):
        user = self.client.get_user(AccessToken=access_token)
        user_attributes = {}
        for item in user["UserAttributes"]:
            username = user_attributes["name"] if user_attributes.get("name") else " "
            user_attributes[item["Name"]] = item["Value"]
            user_attributes = {
                "name": username,
                "email": user_attributes.get("email"),
                "picture": user_attributes["picture"] if user_attributes.get("picture") else " ",
            }
        if profile_pic:
            validate_mime_type_png_jpeg(profile_pic.content_type)
            actual_file_name = profile_pic.filename
            file_path = f"{current_app.config['TEMP_DIR']}/{actual_file_name}"
            profile_pic.save(file_path)
            file_id = str(uuid.uuid4())
            upload_file(
                current_app.config["USER_PROFILE_BUCKET"],
                file_id,
                file_path,
            )
        else:
            file_id = user_attributes.get("picture")
        client.update_user_attributes(
            AccessToken=access_token,
            UserAttributes=[
                {"Name": "name", "Value": name if name else username},
                {"Name": "email", "Value": email if email else user_attributes.get("email")},
                {"Name": "picture", "Value": file_id},
            ],
        )

    def user_profile_download(self, access_token):
        user = self.client.get_user(AccessToken=access_token)
        file_ext = "jpeg"
        user_attributes = {}
        for item in user["UserAttributes"]:
            user_attributes[item["Name"]] = item["Value"]
        user_attributes = {
            "pictureFileId": user_attributes.get("picture"),
        }
        file_id = user_attributes.get("pictureFileId")
        bucket = current_app.config["USER_PROFILE_BUCKET"]
        download_file(bucket, f"{file_id}", f"{current_app.config['TEMP_DIR']}/{file_id}.{file_ext}")
        return send_file(f"{current_app.config['TEMP_DIR']}/{file_id}.{file_ext}")

    def get_profile_details(self, access_token):
        user = self.client.get_user(AccessToken=access_token)
        user_attributes = {}
        for item in user["UserAttributes"]:
            user_attributes[item["Name"]] = item["Value"]
        user_attributes = {
            "userId": user_attributes.get("sub"),
            "name": user_attributes.get("name"),
            "username": user.get("Username"),
            "email": user_attributes.get("email"),
            "pictureFileId": user_attributes.get("picture"),
        }
        return user_attributes

    def logout(self, access_token):
        response = self.client.global_sign_out(AccessToken=access_token)
        return response

    def refresh_token(self, refresh_token):
        try:
            response = self.client.initiate_auth(
                ClientId=current_app.config["APP_CLIENT_ID"],
                AuthFlow="REFRESH_TOKEN_AUTH",
                AuthParameters={
                    "REFRESH_TOKEN": refresh_token,
                },
            )
            return response

        except botocore.exceptions.ClientError as e:
            return e.response

    def client_set_software_token_mfa(self, access_token):
        user = self.client.get_user(AccessToken=access_token)
        user_name = user["Username"]
        try:
            response = self.client.associate_software_token(AccessToken=access_token)
            self.client.admin_set_user_mfa_preference(
                Username=user_name,
                UserPoolId=current_app.config["USER_POOL_ID"],
                SoftwareTokenMfaSettings={"Enabled": True, "PreferredMfa": True},
            )
            qrcode_url = f'otpauth://totp/PTT-APP:{user["Username"]}?secret={response["SecretCode"]}&issuer=PTT-APP'

            return {"qrcode": qrcode_url, "secretCode": response["SecretCode"]}
        except botocore.exceptions.ClientError as e:
            return e.response

    def verify_software_token_mfa(self, user_code, access_token):
        user = self.client.get_user(AccessToken=access_token)
        user_name = user["Username"]
        try:
            response = self.client.verify_software_token(
                AccessToken=access_token, UserCode=user_code, FriendlyDeviceName=user_name
            )
            return response

        except botocore.exceptions.ClientError as e:
            return jsonify({"message": e.response["message"]}), e.response["ResponseMetadata"]["HTTPStatusCode"]

    def respond_to_software_token_mfa_challenge(self, mfa_code, user_name, session):
        try:

            auth_response = self.client.respond_to_auth_challenge(
                ClientId=current_app.config["APP_CLIENT_ID"],
                ChallengeName="SOFTWARE_TOKEN_MFA",
                ChallengeResponses={"USERNAME": user_name, "SOFTWARE_TOKEN_MFA_CODE": mfa_code},
                Session=session,
            )
            user_groups = self.__list_user_groups(user_name)
            if "AuthenticationResult" in auth_response:
                if "Groups" in user_groups:
                    return {
                        "authenticationResult": auth_response["AuthenticationResult"],
                        "userGroups": user_groups["Groups"],
                    }
                else:
                    return {"authenticationResult": auth_response["AuthenticationResult"], "userGroups": []}
            else:
                current_app.logger.error(f"Failed to authenticate user {user_name}, response received {auth_response}")
        except botocore.exceptions.ClientError as e:
            current_app.logger.error("Failed to login", exc_info=e)
            return auth_error_handler(e)

    def client_set_sms_mfa(self, access_token, phone_number):
        user = self.client.get_user(AccessToken=access_token)
        user_name = user["Username"]
        try:
            self.client.admin_update_user_attributes(
                UserPoolId=current_app.config["USER_POOL_ID"],
                Username=user_name,
                UserAttributes=[
                    {"Name": "phone_number", "Value": phone_number},
                    {"Name": "phone_number_verified", "Value": "true"},
                ],
            )
            response = self.client.set_user_settings(
                AccessToken=access_token,
                MFAOptions=[
                    {"DeliveryMedium": "SMS", "AttributeName": "phone_number"},
                ],
            )
            return response
        except botocore.exceptions.ClientError as e:
            return e.response

    def respond_to_sms_mfa_challenge(self, sms_mfa_code, user_name, session):
        try:

            auth_response = self.client.respond_to_auth_challenge(
                ClientId=current_app.config["APP_CLIENT_ID"],
                ChallengeName="SMS_MFA",
                ChallengeResponses={"USERNAME": user_name, "SMS_MFA_CODE": sms_mfa_code},
                Session=session,
            )
            user_groups = self.__list_user_groups(user_name)
            if "AuthenticationResult" in auth_response:
                if "Groups" in user_groups:
                    return {
                        "authenticationResult": auth_response["AuthenticationResult"],
                        "userGroups": user_groups["Groups"],
                    }
                else:
                    return {"authenticationResult": auth_response["AuthenticationResult"], "userGroups": []}
            else:
                current_app.logger.error(f"Failed to authenticate user {user_name}, response received {auth_response}")
        except botocore.exceptions.ClientError as e:
            current_app.logger.error("Failed to login", exc_info=e)
            return auth_error_handler(e)

    def get_user_details(self, user_id):
        user = get_db().user.find_one({"user_id": user_id})
        user_details = UserSchema().dump(user)
        return user_details

    def update_user_details(self, data, user_id):
        data = UserSchema().load(data)
        get_db().user.update_one({"user_id": user_id}, {"$set": {**data}})

    def list_user_details(self):
        clients = auth_service.get_list_clients()
        users = get_db().user.find(
            {"role": "ptt-client"},
            projection={
                "_id": 0,
            },
        )
        response = [UserSchema().dump(user) for user in users]
        for user in response:
            user["userDetails"] = next(filter(lambda x: x["userId"] == user["userId"], clients), None)
        return response


auth_service = AuthService()
