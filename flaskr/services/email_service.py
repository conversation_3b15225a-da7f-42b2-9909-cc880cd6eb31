import boto3
import datetime
from bson import ObjectId

from flask import current_app as app
from flask import jsonify
from jinja2 import Environment, FileSystemLoader
from flaskr.helpers.date_util import (
    format_date_for_email_template,
    format_date_with_day_ordinal,
    format_ordinal_date,
    format_ordinal_numbers,
)

from flaskr.models import get_db
from flaskr.services.exceptions import ServiceException

from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from botocore.exceptions import ClientError


ses_client = boto3.client("ses", region_name="eu-west-2")


class EmailService:
    def __init__(self):
        self.env = Environment(loader=FileSystemLoader("templates"))

    def _load_template(self, template_name):
        return self.env.get_template(template_name)

    def _get_client_info(self, client_id):
        return get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})

    def _get_template_for_trust_type(self, client_id):
        client_basic_info = self._get_client_info(client_id)
        type_of_trust_account_id = client_basic_info.get("type_of_trust_account")

        if type_of_trust_account_id:
            trust_type_info = get_db().lookup_trust_type.find_one({"_id": type_of_trust_account_id})
            if trust_type_info and trust_type_info.get("name") == "ATOL Escrow":
                return self._load_template("monthly_escrow_report_email_template.html")
            else:
                return self._load_template("monthly_atol_report_email_template.html")

    def _get_day_and_date(self):
        today = datetime.date.today()
        day_str = today.strftime("%d/%m/%Y")
        date_format = format_date_with_day_ordinal(day_str)
        formatted_date = today.strftime("%Y/%m/%d")
        return date_format, formatted_date

    def _format_date(self, input_date):
        return format_date_for_email_template(input_date)

    def _extract_first_name(self, full_name):
        if not full_name:
            return ""
        name_parts = full_name.split()
        first_name = name_parts[0]
        return first_name

    def create_template(self, data):
        template_name = data.get("templateName")
        subject = data.get("subject")
        text = data.get("text") or ""
        html = data.get("html")
        response = ses_client.create_template(
            Template={
                "TemplateName": template_name,
                "SubjectPart": subject,
                "TextPart": text,
                "HtmlPart": html,
            }
        )
        return response

    def update_client_emails(self, client_id, to, cc):
        client = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if not client:
            return jsonify({"message": "Client not found"}), 404

        result = get_db().client_basic_info.update_one({"_id": ObjectId(client_id)}, {"$set": {"to": to, "cc": cc}})

        if result.modified_count > 0:
            return jsonify({"message": "Client emails updated successfully"}), 200
        else:
            return jsonify({"message": "Update failed"}), 500

    def fetch_weekly_email_template(
        self,
        client_id,
        fund_in,
        fund_out,
        name,
        designation,
        phone_number,
    ):
        try:
            env = Environment(loader=FileSystemLoader("templates"))
            template = env.get_template("weekly_report_email_template.html")
            client_basic_info = self._get_client_info(client_id)
            full_name = client_basic_info["full_name"]
            today = datetime.date.today()
            today_ordinal_format = format_ordinal_date(today)
            day_month_year_format_today = today.strftime("%d/%m/%Y")
            sender_first_name = self._extract_first_name(name)

            email_content = template.render(
                full_name=full_name,
                today_ordinal_format=today_ordinal_format,
                day_month_year_format_today=day_month_year_format_today,
                fund_in=fund_in,
                fund_out=fund_out,
                sender_name=name,
                designation=designation,
                phone_number=phone_number,
                sender_first_name=sender_first_name,
            )
            return email_content
        except Exception as e:
            return None, str(e)

    def get_recipients_monthly(self, client_id):
        client = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if not client:
            return jsonify({"message": "Client not found"}), 404
        cc = client["cc"]
        to = client["to"]
        return jsonify({"to": to, "cc": cc}), 200

    def get_recipients_weekly(self, client_id):
        client = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if not client:
            return jsonify({"message": "Client not found"}), 404
        cc = client["cc"]
        to = client["to"]

        cc = [email.lower() for email in cc]
        email_to_remove = "<EMAIL>"
        if email_to_remove in cc:
            cc.remove(email_to_remove)
        return jsonify({"to": to, "cc": cc}), 200

    def fetch_monthly_email_template(
        self,
        client_id,
        from_date,
        to_date,
        opening_balance,
        closing_balance,
        banking_amount,
        claim_amount,
        name,
        designation,
        phone_number,
        bank_charges,
        bank_fees,
        interest_received,
        bank_charges_reversal,
    ):
        try:
            template = self._get_template_for_trust_type(client_id)
            client_basic_info = self._get_client_info(client_id)
            client_name = client_basic_info["friendly_name"]
            client_full_name = client_basic_info["full_name"]
            date_format, formatted_date = self._get_day_and_date()
            new_to_date = format_ordinal_numbers(to_date)
            new_from_date = format_ordinal_numbers(from_date)
            sender_first_name = self._extract_first_name(name)
            if client_id in [
                app.config.get("TS"),
                app.config.get("INTE"),
                app.config.get("TURQ"),
                app.config.get("HOBE"),
            ]:
                notes = ""
            else:
                notes = "Please note that the opening and closing balances above include both balances held in the current and deposit accounts."
            email_content = template.render(
                client_name=client_name,
                day=date_format,
                formatted_date=formatted_date,
                from_date=new_from_date,
                to_date=new_to_date,
                full_name=client_full_name,
                opening_balance=opening_balance,
                closing_balance=closing_balance,
                banking_amount=banking_amount,
                claim_amount=claim_amount,
                sender_name=name,
                sender_first_name=sender_first_name,
                designation=designation,
                phone_number=phone_number,
                notes=notes,
                bank_charges=bank_charges,
                bank_fees=bank_fees,
                interest_received=interest_received,
                bank_charges_reversal=bank_charges_reversal,
            )
            return email_content
        except Exception as e:
            return None, str(e)

    def compose_email(self, client_id, email_content, sender, is_monthly, to_date):
        client_basic_info = self._get_client_info(client_id)
        if not client_basic_info:
            raise ServiceException("Client not found")

        if not client_basic_info.get("to"):
            raise ServiceException("Recipient list cannot be empty")
        to_list = client_basic_info.get("to")
        date_obj = datetime.datetime.strptime(to_date, "%Y-%m-%d")
        month_name = date_obj.strftime("%B")
        year = date_obj.strftime("%Y")

        cc_list = client_basic_info.get("cc")
        full_name = client_basic_info["full_name"]
        trust_type_id = client_basic_info.get("type_of_trust_account")
        trust_type_info = get_db().lookup_trust_type.find_one({"_id": trust_type_id})

        CHARSET = "utf-8"
        msg = MIMEMultipart("mixed")
        msg["From"] = sender
        msg["To"] = ", ".join(to_list) if isinstance(to_list, list) else to_list

        if is_monthly:
            msg["cc"] = ", ".join(cc_list) if isinstance(cc_list, list) else cc_list
            if trust_type_info and trust_type_info.get("name") == "ATOL Escrow":
                subject = f"{full_name} Month End report {month_name} {year} "
            else:
                subject = f"{full_name} Trust Reconciliation"
        else:
            email_to_remove = "<EMAIL>"
            cc_list = [email.lower() for email in cc_list]
            if email_to_remove in cc_list:
                cc_list.remove(email_to_remove)
            msg["cc"] = ", ".join(cc_list) if isinstance(cc_list, list) else cc_list
            subject = f"{full_name} - Statement of Trust on Compliance Reporting Day"

        msg["Subject"] = subject
        msg_body = MIMEMultipart("alternative")
        htmlpart = MIMEText(email_content.encode(CHARSET), "html", CHARSET)
        msg_body.attach(htmlpart)
        msg.attach(msg_body)
        return msg.as_string()

    def send_email(self, email_content):
        try:
            response = ses_client.send_raw_email(
                RawMessage={
                    "Data": email_content,
                },
            )
        except ClientError as e:
            print(e.response["Error"]["Message"])
            raise ServiceException(e.response["Error"]["Message"])
        else:
            return {"Email sent! Message ID:": response["MessageId"]}


email_service = EmailService()
