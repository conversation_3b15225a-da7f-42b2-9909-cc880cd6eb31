from flask import current_app
import msal
import requests

# from datetime import datetime, timedelta
from flaskr.models import get_db
import logging
import json
from datetime import datetime

logger = logging.getLogger(__name__)


class PowerBIService:
    def __init__(self):
        self._client_id = None
        self._client_secret = None
        self._tenant_id = None
        self._authority = None
        self._workspace_id = None
        self.scope = ["https://analysis.windows.net/powerbi/api/.default"]
        self.powerbi_api_url = "https://api.powerbi.com/v1.0/myorg"

    @property
    def client_id(self):
        if self._client_id is None:
            self._client_id = current_app.config.get("POWERBI_CLIENT_ID")
        return self._client_id

    @property
    def client_secret(self):
        if self._client_secret is None:
            self._client_secret = current_app.config.get("POWERBI_CLIENT_SECRET")
        return self._client_secret

    @property
    def tenant_id(self):
        if self._tenant_id is None:
            self._tenant_id = current_app.config.get("POWERBI_TENANT_ID")
        return self._tenant_id

    @property
    def authority(self):
        if self._authority is None:
            self._authority = f"https://login.microsoftonline.com/{self.tenant_id}"
        return self._authority

    @property
    def workspace_id(self):
        if self._workspace_id is None:
            self._workspace_id = current_app.config.get("POWERBI_WORKSPACE_ID")
        return self._workspace_id

    def get_access_token(self):
        """Get PowerBI access token using client credentials flow"""

        app = msal.ConfidentialClientApplication(
            self.client_id, authority=self.authority, client_credential=self.client_secret
        )

        result = app.acquire_token_for_client(scopes=self.scope)
        if "access_token" in result:
            current_app.logger.info("Successfully obtained access token")
            return result["access_token"]
        else:
            error_msg = f"Failed to get access token. Error: {result.get('error')}, Description: {result.get('error_description')}"
            current_app.logger.error(error_msg)
            raise Exception(error_msg)

    def get_report_embed_info(self, report_id, workspace_id, user_id):
        current_app.logger.info(
            f"Getting report embed info for report_id: {report_id}, workspace_id: {workspace_id}, user_id: {user_id}"
        )
        access_token = self.get_access_token()
        headers = {"Authorization": f"Bearer {access_token}"}
        # Get report details to get embed URL
        details_url = f"{self.powerbi_api_url}/groups/{workspace_id}/reports/{report_id}"
        details_response = requests.get(details_url, headers=headers)

        if details_response.status_code != 200:
            try:
                error_details = details_response.json()
                error_msg = f"Failed to get report details. Status: {details_response.status_code}, Error: {json.dumps(error_details)}"
            except json.JSONDecodeError:
                error_msg = f"Failed to get report details. Status: {details_response.status_code}, Response: {details_response.text}"
            current_app.logger.error(error_msg)
            raise Exception(error_msg)

        report_details = details_response.json()
        current_app.logger.info("Successfully obtained report details")

        # Get embed token
        token_url = f"{self.powerbi_api_url}/groups/{workspace_id}/reports/{report_id}/GenerateToken"
        embed_token_request = {"accessLevel": "View", "allowSaveAs": False}
        token_headers = {**headers, "Content-Type": "application/json"}
        token_response = requests.post(token_url, headers=token_headers, json=embed_token_request)

        if token_response.status_code != 200:
            current_app.logger.error(f"Failed to get embed token. Status: {token_response.status_code}")
            raise Exception(
                f"Failed to get embed token. Status: {token_response.status_code}, Response: {token_response.text}"
            )

        embed_token = token_response.json()
        current_app.logger.info("Successfully obtained embed token")
        return {
            "embedUrl": report_details.get("embedUrl"),
            "token": embed_token.get("token"),
            "reportId": report_id,
            "workspaceId": workspace_id,
        }

    def get_workspace_reports(self, workspace_id=None):
        workspace_id = workspace_id or self.workspace_id
        current_app.logger.info(f"Getting reports for workspace: {workspace_id}")
        access_token = self.get_access_token()
        headers = {"Authorization": f"Bearer {access_token}"}

        # Get all reports in the workspace
        reports_url = f"{self.powerbi_api_url}/groups/{workspace_id}/reports"
        response = requests.get(reports_url, headers=headers)
        if response.status_code != 200:
            try:
                error_details = response.json()
                error_msg = f"Failed to get workspace reports. Status: {response.status_code}, Error: {json.dumps(error_details)}"
            except:
                error_msg = (
                    f"Failed to get workspace reports. Status: {response.status_code}, Response: {response.text}"
                )
            current_app.logger.error(error_msg)
            raise Exception(error_msg)
        reports_data = response.json()
        current_app.logger.info(f"Successfully retrieved {len(reports_data.get('value', []))} reports")

        reports = [{"id": report["id"], "name": report["name"]} for report in reports_data.get("value", [])]
        return reports
    
    def update_user_powerbi_reports(self, user_id, pbi_reports):
        """
        Update PowerBI reports assigned to a user
        
        Args:
            user_id (str): The user ID
            pbi_reports (list): List of PowerBI report objects with id and name
            
        Returns:
            bool: True if successful
        """
        current_app.logger.info(f"Updating PowerBI reports for user: {user_id}")
    
        # Update user document in database with PowerBI reports
        result = get_db().user.update_one(
            {"user_id": user_id},
            {"$set": {"powerbi_reports": pbi_reports}}
        )
        
        if result.matched_count == 0:
            current_app.logger.error(f"User with ID {user_id} not found")
            raise Exception(f"User with ID {user_id} not found")
        
        current_app.logger.info(f"Successfully updated PowerBI reports for user: {user_id}")
        return True

    def get_user_powerbi_reports(self, user_id):
        """
        Get PowerBI reports assigned to a user
        
        Args:
            user_id (str): The user ID
            
        Returns:
            list: List of PowerBI report objects with id and name
        """
        current_app.logger.info(f"Getting PowerBI reports for user: {user_id}")
        
        # Get user document from database
        user = get_db().user.find_one({"user_id": user_id}, {"powerbi_reports": 1})
        
        if not user:
            current_app.logger.error(f"User with ID {user_id} not found")
            raise Exception(f"User with ID {user_id} not found")
        
        # Return empty list if no reports are assigned
        reports = user.get("powerbi_reports", [])
        current_app.logger.info(f"Found {len(reports)} PowerBI reports for user: {user_id}")
        return reports

    def get_erv_powerbi_dashboard_config(self):
        """
        Get ERV dashboard configuration from database
        """
        config = get_db().erv_powerbi_config.find_one()
        
        if not config:
            # Return default configuration from environment if not found in database
            default_report_id = current_app.config.get("POWERBI_ERV_REPORT_ID")
            if default_report_id:
                return {
                    "report_id": default_report_id,
                    "name": "ERV Dashboard"
                }
            else:
                current_app.logger.warning("No ERV dashboard configuration found")
                return None
        
        current_app.logger.info(f"Found ERV dashboard configuration: {config.get('report_id')}")
        return {
            "report_id": config.get("report_id"),
            "name": config.get("name", "ERV Dashboard")
        }

    def update_erv_dashboard_config(self, report_id, report_name):
        """
        Update ERV dashboard configuration in database
        """
        current_app.logger.info(f"Updating ERV dashboard configuration: {report_id}")
        
        # Update or insert ERV dashboard configuration
        get_db().erv_powerbi_config.update_one(
            {},
            {
                "$set": {
                    "report_id": report_id,
                    "name": report_name,
                    "updated_at": datetime.utcnow()
                }
            },
            upsert=True
        )
        current_app.logger.info(f"Successfully updated ERV Powerbi dashboard configuration: {report_id}")
        return True


powerbi_service = PowerBIService()
