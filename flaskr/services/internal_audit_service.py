from datetime import datetime, timed<PERSON>ta
from bson import ObjectId
from flaskr.models import get_db
from flask import current_app, send_file
from flaskr.helpers.boto3_handler import download_file
from flaskr.services.exceptions import ServiceException
from flaskr.helpers.date_util import monthly_dates
from dateutil.relativedelta import relativedelta
from flaskr.models.internal_audit import InternalBankingSchema
import os

class InternalAuditService:
    def __client_id_element_match(self, client_id):
        element_list = []
        name = None
        if current_app.config.get("BARRHEAD") == client_id:
            element_list = ["Cruise", "BSP", "Flights"]
            name = "SAF"
        if current_app.config.get("BROADWAY") == client_id:
            element_list = ["Charter", "Flight", "Cruise"]
            name = "SAF"
        if current_app.config.get("HAYS") == client_id:
            element_list = ["BSP", "LCF", "Atol"]
            name = "SAFI"
        if current_app.config.get("SUNSHINE") == client_id:
            element_list = ["Cruise", "Flight", "BSP", "Flights", "LCF"]
            name = "SAII"
        if current_app.config.get("TRAVEL_REPUBLIC") == client_id:
            element_list = ["BSP"]
            name = "SAII"

        if element_list:
            element_list = [element.lower() for element in element_list]
        return (element_list, name)

    def banking_report(self, client_id, from_date, to_date):
        date_match_condition = [
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        to_date if to_date else datetime.max.strftime("%Y-%m-%d"),
                    ]
                }
            },
        ]
        client_match_condition = [{"client_id": ObjectId(client_id)}] if client_id else []
        data = list(
            get_db().banking_metadata.aggregate(
                [
                    {
                        "$match": {
                            "$and": [
                                *client_match_condition,
                                *date_match_condition,
                                {"status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]}},
                            ]
                        }
                    },
                    {
                        "$lookup": {
                            "from": "anomaly_banking",
                            "localField": "_id",
                            "foreignField": "banking_id",
                            "pipeline": [{"$match": {"status": "Unresolved", "deleted": False}}],
                            "as": "anomalies",
                        }
                    },
                    {
                        "$project": {
                            "FileName": {"$slice": ["$banking_files.file_name", -1]},
                            "BankingReportAmount": {"$slice": ["$banking_files.deposit", -1]},
                            "BusinessRules": "$anomalies.anomaly_type",
                        }
                    },
                ]
            )
        )

        content = []
        for row in data:
            banking_dict = {
                "_id": str(row["_id"]),
                "fileName": row["FileName"][0],
                "BusinessRules": ", ".join(list(set(row.get("BusinessRules")))),
                "Samples": "",
                "Notes": "",
                "ResolutionNotes": "",
                "Risk": "",
                "AmountasperBank": "",
                "TestingStatus": "",
                "Status": "",
            }
            items = []
            if row.get("BankingReportAmount"):
                for currency_code, amount in row["BankingReportAmount"][0].items():
                    item_dict = {}
                    item_dict.update(
                        {
                            "currency": currency_code,
                            "amount": amount,
                        }
                    )
                    items.append(item_dict)
            banking_dict["BankingReportAmount"] = items
            content.append(banking_dict)

        response = {"content": content}
        return response

    def claim_analysis(self, data):
        from_date = data.get("fromDate")
        to_date = data.get("toDate")
        client = ObjectId(data.get("client"))
        currency = data.get("currency")
        element_response = None

        match_condition = {
            "client_id": client,
            "deleted": False,
            "file_date": {
                "$gte": from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                "$lte": to_date if to_date else datetime.max.strftime("%Y-%m-%d"),
            },
        }

        total_by_currency = list(
            get_db().claims_file_details.aggregate(
                [
                    {"$match": {**match_condition, "currency_code": currency}},
                    {
                        "$group": {
                            "_id": None,
                            "amount": {"$sum": "$amount"},
                            "bookingRefs": {"$count": {}},
                            "checkAmount": {
                                "$sum": {
                                    "$cond": {
                                        "if": {"$in": ["$check", ["full-check", "quick-check"]]},
                                        "then": "$amount",
                                        "else": 0,
                                    }
                                }
                            },
                            "checkCount": {
                                "$sum": {
                                    "$cond": {
                                        "if": {"$in": ["$check", ["full-check", "quick-check"]]},
                                        "then": 1,
                                        "else": 0,
                                    }
                                }
                            },
                        }
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "amount": 1,
                            "totalBookings": "$bookingRefs",
                            "percentageCheck": {
                                "$round": [
                                    {"$multiply": [{"$divide": ["$checkCount", "$bookingRefs"]}, 100]},
                                    2,
                                ]
                            },
                            "checkCount": "$checkCount",
                            "checkAmount": "$checkAmount",
                            "percentageAmount": {
                                "$round": [
                                    {"$multiply": [{"$divide": ["$checkAmount", "$amount"]}, 100]},
                                    2,
                                ]
                            },
                            "claims_id": 1,
                        }
                    },
                ],
                allowDiskUse=True,
            )
        )

        element_by_currency = list(
            get_db().claims_file_details.aggregate(
                [
                    {"$match": {**match_condition, "currency_code": currency}},
                    {
                        "$group": {
                            "_id": {"$toLower": "$element"},
                            "element": {"$first": "$element"},
                            "bookings": {"$count": {}},
                            "checkAmount": {
                                "$sum": {
                                    "$cond": {
                                        "if": {"$in": ["$check", ["full-check"]]},
                                        "then": "$amount",
                                        "else": 0,
                                    }
                                }
                            },
                            "checkCount": {
                                "$sum": {
                                    "$cond": {
                                        "if": {"$in": ["$check", ["full-check"]]},
                                        "then": 1,
                                        "else": 0,
                                    }
                                }
                            },
                            "elementAmount": {"$sum": "$amount"},
                        }
                    },
                ],
                allowDiskUse=True,
            )
        )
        if element_by_currency:
            element_response = element_by_currency

        claims_id = list(
            get_db().claims_file_details.aggregate(
                [
                    {"$match": match_condition},
                    {"$group": {"_id": None, "claims_id": {"$addToSet": "$claims_id"}}},
                    {"$project": {"_id": 0, "claims_id": "$claims_id"}},
                ]
            )
        )

        claims_id = claims_id[0]["claims_id"] if claims_id else []
        claim_testing = list(
            get_db().claim_testing.aggregate(
                [
                    {"$match": {"claims_id": {"$in": claims_id}}},
                    {"$addFields": {"original_claim": {"$objectToArray": "$original_claim"}}},
                    {"$unwind": "$original_claim"},
                    {"$match": {"original_claim.k": currency}},
                    {"$group": {"_id": "$original_claim.k", "total_original_claim": {"$sum": "$original_claim.v"}}},
                    {"$project": {"currency": "$_id", "amount": "$total_original_claim"}},
                ]
            )
        )

        if element_by_currency and total_by_currency:
            for item in element_by_currency:
                item["percentageCheck"] = (
                    round((item["checkCount"] / item["bookings"]) * 100, 2) if item["bookings"] != 0 else 0
                )
                item["percentageAmount"] = (
                    round((item["checkAmount"] / item["elementAmount"]) * 100, 2) if item["elementAmount"] != 0 else 0
                )
                item["elementPercentage"] = (
                    round((item["elementAmount"] / total_by_currency[0]["amount"]) * 100, 2)
                    if total_by_currency[0]["amount"] != 0
                    else 0
                )
                item.pop("_id")
        if claim_testing:
            for item in claim_testing:
                amount_claim_testing = item["amount"]
                variance = (
                    amount_claim_testing - total_by_currency[0]["amount"] if total_by_currency[0]["amount"] != 0 else 0
                )
                if total_by_currency:
                    total_by_currency[0]["variance"] = round(variance, 2)
        if total_by_currency:
            currency_response = total_by_currency[0]
        else:
            currency_response = None
        if element_by_currency:
            element_response = element_by_currency
        total_bookings = currency_response["totalBookings"] if total_by_currency else 0
        return {
            "totalByCurrency": currency_response,
            "elementByCurrency": element_response,
            "totalBookings": total_bookings,
        }

    def claim_report(self, client_id, from_date, to_date):
        trust_type_name = None
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if client_basic_info["type_of_trust_account"] is not None:
            trust_type = get_db().lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})
            trust_type_name = trust_type.get("name")

        date_match_condition = [
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        to_date if to_date else datetime.max.strftime("%Y-%m-%d"),
                    ]
                }
            },
        ]
        client_match_condition = [{"client_id": ObjectId(client_id)}] if client_id else []
        data = list(
            get_db().claims_metadata.aggregate(
                [
                    {
                        "$match": {
                            "$and": [
                                *client_match_condition,
                                *date_match_condition,
                                {"status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]}},
                            ]
                        }
                    },
                    {
                        "$lookup": {
                            "from": "anomaly_claims",
                            "localField": "_id",
                            "foreignField": "claims_id",
                            "pipeline": [
                                {
                                    "$match": {
                                        "status": "Unresolved",
                                        "deleted": False,
                                        "anomaly_type": {"$ne": "Funds in Trust Exceed/less than Total Booking Value"},
                                    }
                                }
                            ],
                            "as": "anomalies",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "claim_testing",
                            "localField": "_id",
                            "foreignField": "claims_id",
                            "as": "claim_test",
                        }
                    },
                    {"$unwind": {"path": "$claim_test", "preserveNullAndEmptyArrays": True}},
                    {
                        "$lookup": {
                            "from": "claims_file_details",
                            "localField": "_id",
                            "foreignField": "claims_id",
                            "pipeline": [
                                {
                                    "$match": {
                                        "check": "full-check",
                                        "deleted": False,
                                    },
                                }
                            ],
                            "as": "full_check",
                        }
                    },
                    {"$unwind": {"path": "$full_check"}},
                    {
                        "$project": {
                            "FileName": {"$slice": ["$claim_files.file_name", -1]},
                            "BusinessRules": "$anomalies.anomaly_type",
                            "amount": {"$slice": ["$claim_files.claim_total", -1]},
                            "count": {"$slice": ["$claim_files.item_count", -1]},
                            "originalClaim": "$claim_test.original_claim",
                            "checks": {"$slice": ["$claim_files.checks", -1]},
                            "checkedAmount": {"$slice": ["$claim_files.checked_amount", -1]},
                            "BookingRef": "$full_check.booking_ref",
                            "element": "$full_check.element",
                            "checkCurrency": "$full_check.currency_code",
                            "transactionId": "$full_check._id",
                        }
                    },
                ]
            )
        )

        content = []
        for row in data:
            mandatory_checks_list = []
            if row.get("transactionId"):
                booking_claim_checks_list = list(
                    get_db().booking_claim_checks.find(
                        {
                            "client_id": ObjectId(client_id),
                            "transaction_id": row.get("transactionId"),
                            "selected": False,
                        },
                        projection={"_id": 0, "name": 1},
                    )
                )
                mandatorty_checks = get_db().claim_mandatory_checks.find_one(
                    {"applicable_trust": trust_type_name, "element": row.get("element").lower()},
                    projection={"_id": 0, "mandatory_checks": 1},
                )
                if booking_claim_checks_list is not None and mandatorty_checks is not None:
                    for item in booking_claim_checks_list:
                        if item["name"] in mandatorty_checks["mandatory_checks"]:
                            mandatory_checks_list.append(item["name"])

            claim_dict = {
                "_id": str(row["_id"]),
                "fileName": row["FileName"][0],
                "BusinessRules": ", ".join(list(set(row.get("BusinessRules")))),
                "SubsetofFullChecks": "",
                "Notes": "",
                "ResolutionNotes": "",
                "Risk": "",
                "AmountasperBank": "",
                "Status": "",
            }
            items = []

            if row.get("amount"):
                for currency_code, amount in row["amount"][0].items():
                    amount = amount if amount else 0
                    item_dict = {}
                    percentageCheck = (
                        (row["checks"][0][currency_code] * 100 / row["count"][0][currency_code])
                        if row["count"][0][currency_code]
                        else 0
                    )
                    percentageTotal = (row["checkedAmount"][0][currency_code] * 100 / amount) if amount else 0

                    item_dict.update(
                        {
                            "currency": currency_code,
                            "revisedClaim": amount,
                            "originalClaim": row["originalClaim"][currency_code] if row.get("originalClaim") else None,
                            "checks": row["checks"][0][currency_code],
                            "checkedAmount": row["checkedAmount"][0][currency_code],
                            "percentageCheck": round(percentageCheck, 2),
                            "percentageTotal": round(percentageTotal, 2),
                            "bookingRef": row.get("BookingRef") if row.get("checkCurrency") == currency_code else None,
                            "element": row.get("element") if row.get("checkCurrency") == currency_code else None,
                            "MandatoryChecks": ", ".join(list(set(mandatory_checks_list)))
                            if row.get("checkCurrency") == currency_code
                            else None,
                        }
                    )
                    items.append(item_dict)
            claim_dict["items"] = items
            content.append(claim_dict)
        response = {"content": content}
        return response

    def update_banking_report(self, data, banking_id):
        now = datetime.utcnow()
        data = InternalBankingSchema().load(data)
        get_db().internal_audit_logs.update_one(
            {"_id": ObjectId(banking_id)}, {"$set": {**data, "updated_at": now}}, upsert=True
        )

    def update_claims_report(self, data, claim_id):
        now = datetime.utcnow()
        data = InternalBankingSchema().load(data)
        get_db().internal_audit_logs.update_one(
            {"_id": ObjectId(claim_id)}, {"$set": {**data, "updated_at": now}}, upsert=True
        )

    def risk_exposure_and_insurance_cover_graph(self, data, resolution):
        from_date = datetime.combine(datetime.today() - timedelta(days=resolution - 1), datetime.min.time())
        to_date = datetime.combine(datetime.today(), datetime.min.time())
        current_date = datetime.now().strftime("%Y-%m-%d")
        client = ObjectId(data.get("client"))
        if data.get("fromDate"):
            from_date = datetime.fromisoformat(data["fromDate"])
        if data.get("toDate"):
            to_date = datetime.combine(datetime.fromisoformat(data.get("toDate")), datetime.min.time())
        date_difference = (to_date - from_date).days
        date_lis = []
        if date_difference == 0:
            date_lis = [to_date]
        if date_difference < resolution:
            date_lis = [from_date + timedelta(days=i) for i in range(0, date_difference + 1)]
        else:
            days = date_difference / resolution
            date_dif = 0
            for i in range(resolution - 2):
                date_dif += days
                date = from_date + timedelta(days=date_dif)
                date_lis.append(date)
            date_lis.insert(0, from_date)
            date_lis.insert(-1, to_date)
            date_lis = [datetime.combine(date, datetime.min.time()) for date in date_lis]
            date_lis = list(set(date_lis))
        date_lis.sort()
        date_lis = [date.strftime("%Y-%m-%d") for date in date_lis]
        claim_details = []
        insurance_details = []
        cap_amount = 0
        element_list = []

        if str(client) in [
            current_app.config.get("BARRHEAD"),
            current_app.config.get("BROADWAY"),
            current_app.config.get("SUNSHINE"),
            current_app.config.get("HAYS"),
            current_app.config.get("TRAVEL_REPUBLIC"),
            current_app.config.get("TCT"),
            current_app.config.get("TDC"),
            current_app.config.get("EST"),
            current_app.config.get("NST"),
            current_app.config.get("PGL"),
        ]:
            max_cap_anomaly = get_db().lookup_anomaly.find_one({"name": "Max Cap Limit Anomaly"})
            anomaly_id = max_cap_anomaly.get("_id")
            insurance_match = {
                "client_id": client,
                "anomaly_id": anomaly_id,
                "$or": [
                    {"name": "LCF by corp card"},
                    {
                        "$and": [
                            {"from_date": {"$gte": data.get("fromDate")}},
                            {"to_date": {"$lte": data.get("toDate")}},
                        ]
                    },
                ],
            }

            insurance = list(
                get_db().client_anomaly.aggregate(
                    [
                        {"$match": insurance_match},
                        {
                            "$group": {
                                "_id": None,
                                "insuranceAmount": {"$sum": "$custom_field_value"},
                                "details": {
                                    "$push": {
                                        "elements": "$elements",
                                        "fromDate": "$from_date",
                                        "toDate": {"$ifNull": ["$to_date", ""]},
                                        "capAmount": {"$ifNull": ["$custom_field_value", 0]},
                                        "name": {"$ifNull": ["$name", ""]},
                                    }
                                },
                            }
                        },
                    ],
                    collation={"locale": "en", "strength": 2},
                )
            )

            element_list = []
            if insurance:
                cap_amount = round(insurance[0]["insuranceAmount"], 2)
                for details in insurance[0]["details"]:
                    if details["name"] != "LCF by corp card":
                        element_list += details["elements"]
                    insurance_amount = details["capAmount"]

                    match_condition = {
                        "client_id": client,
                        "deleted": False,
                        "currency_code": data.get("currency"),
                        "element": {"$in": details["elements"]},
                        "$or": [
                            {"departure_date": {"$gte": current_date}},
                            {
                                "$and": [
                                    {"return_date": {"$gt": current_date}},
                                    {"departure_date": {"$lte": current_date}},
                                ]
                            },
                        ],
                    }
                    if str(client) == current_app.config.get("SUNSHINE"):
                        match_condition = {
                            "client_id": client,
                            "deleted": False,
                            "currency_code": data.get("currency"),
                            "file_date": {"$gte": data.get("fromDate")},
                            "element": {"$in": details["elements"]},
                            "$or": [
                                {"departure_date": {"$gte": current_date}},
                                {
                                    "$and": [
                                        {"return_date": {"$gt": current_date}},
                                        {"departure_date": {"$lte": current_date}},
                                    ]
                                },
                            ],
                        }

                    claim = list(
                        get_db().claims_file_details.aggregate(
                            [
                                {"$match": match_condition},
                                {"$sort": {"departure_date": 1}},
                                {
                                    "$group": {
                                        "_id": None,
                                        "claimAmount": {"$sum": "$amount"},
                                        "noOfBookings": {"$sum": 1},
                                    }
                                },
                                {
                                    "$project": {
                                        "_id": 0,
                                        "claimAmount": "$claimAmount",
                                        "noOfBookings": "$noOfBookings",
                                    }
                                },
                            ],
                            collation={"locale": "en", "strength": 2},
                        )
                    )
                    buffer = insurance_amount - claim[0]["claimAmount"] if claim else 0
                    if insurance_amount == 0:
                        buffer = 0
                    insurance_data = {
                        "elements": details["elements"],
                        "noOfBookings": claim[0]["noOfBookings"] if claim else 0,
                        "riskAmount": round(claim[0]["claimAmount"], 2) if claim else 0,
                        "capAmount": round(insurance_amount, 2),
                        "buffer": round(buffer, 2),
                        "fromDate": details["fromDate"],
                        "toDate": details["toDate"],
                        "insuranceName": details["name"],
                    }
                    insurance_details.append(insurance_data)
        for date in date_lis:

            claim_data = {}
            claim_match_condition = {
                "client_id": client,
                "deleted": False,
                "currency_code": data.get("currency"),
                "$or": [
                    {"departure_date": {"$gt": date}},
                    {"$and": [{"return_date": {"$gt": date}}, {"departure_date": {"$lte": date}}]},
                ],
            }

            if str(client) == current_app.config.get("SUNSHINE"):
                claim_match_condition = {
                    "client_id": client,
                    "deleted": False,
                    "currency_code": data.get("currency"),
                    "file_date": {"$gte": data.get("fromDate")},
                    "$or": [
                        {"departure_date": {"$gt": date}},
                        {"$and": [{"return_date": {"$gt": date}}, {"departure_date": {"$lte": date}}]},
                    ],
                }

            if element_list:
                claim_match_condition["element"] = {"$in": element_list}

            claims = list(
                get_db().claims_file_details.aggregate(
                    [
                        {"$match": claim_match_condition},
                        {"$sort": {"departure_date": 1}},
                        {
                            "$group": {
                                "_id": None,
                                "claimAmount": {"$sum": "$amount"},
                                "noOfBookings": {"$sum": 1},
                            }
                        },
                        {
                            "$project": {
                                "_id": 0,
                                "claimAmount": "$claimAmount",
                                "date": date,
                                "noOfBookings": "$noOfBookings",
                            }
                        },
                    ],
                    collation={"locale": "en", "strength": 2},
                )
            )

            no_of_bookings = claims[0]["noOfBookings"] if claims else 0
            claim_amount = claims[0]["claimAmount"] if claims else 0
            claim_data["date"] = date
            claim_data["noOfBookings"] = no_of_bookings
            claim_data["claimAmount"] = round(claim_amount, 2)
            claim_data["capAmount"] = round(cap_amount, 2)
            buffer = cap_amount - claim_amount
            if cap_amount == 0:
                buffer = 0
            claim_data["buffer"] = round(buffer, 2)
            claim_details.append(claim_data)
            claim_details.sort(key=lambda x: x["date"])

        return {"claimDetails": claim_details, "insuranceDetails": insurance_details}

    def banking_report_download(self, client_id, from_date, to_date):
        if client_id == current_app.config.get("NAS"):
            bucket = current_app.config["IA_BUCKET"]

            if from_date == "2022-01-01" and to_date == "2022-03-31":
                key = "banking_report/Banking_Internal_Audit_Report for Jan-mar 2022.xlsx"
                file_name = "Banking_Internal_Audit_Report for Jan-mar 2022.xlsx"

            elif from_date == "2022-04-01" and to_date == "2022-04-30":
                key = "banking_report/Banking_Internal_Audit_Report April 2022.xlsx"
                file_name = "Banking_Internal_Audit_Report April 2022.xlsx"

            elif from_date == "2022-05-01" and to_date == "2022-06-30":
                key = "banking_report/Banking_Internal_Audit_Report for May-June 2022.xlsx"
                file_name = "Banking_Internal_Audit_Report for May-June 2022.xlsx"

            elif from_date == "2022-07-01" and to_date == "2022-09-30":
                key = "banking_report/Banking_Internal_Audit_Report for July-Sep 2022.xlsx"
                file_name = "Banking_Internal_Audit_Report for July-Sep 2022.xlsx"

            elif from_date == "2022-10-01" and to_date == "2022-12-31":
                key = "banking_report/Banking_Internal_Audit_Report for Oct-Dec 2022.xlsx"
                file_name = "Banking_Internal_Audit_Report for Oct-Dec 2022.xlsx"

            elif from_date == "2023-01-01" and to_date == "2023-06-30":
                key = "banking_report/Banking_Internal_Audit_Report for Jan-June 2023.xlsx"
                file_name = "Banking_Internal_Audit_Report for Jan-June 2023.xlsx"

            else:
                raise ServiceException(message="selected date range is not correct")

            download_file(bucket, key, f"{current_app.config['TEMP_DIR']}/{file_name}")
            return send_file(f"{current_app.config['TEMP_DIR']}/{file_name}")
        else:
            raise ServiceException(message="selected client is not correct")

    def claim_report_download(self, client_id, from_date, to_date):
        if client_id == current_app.config.get("NAS"):
            bucket = current_app.config["IA_BUCKET"]

            if from_date == "2022-01-01" and to_date == "2022-03-31":
                key = "claim_report/Claim_Internal_Audit_Report Jan-Mar 2022.xlsx"
                file_name = "Claim_Internal_Audit_Report Jan-Mar 2022.xlsx"

            elif from_date == "2022-04-01" and to_date == "2022-04-30":
                key = "claim_report/Claim_Internal_Audit_Report April 2022.xlsx"
                file_name = "Claim_Internal_Audit_Report April 2022.xlsx"

            elif from_date == "2022-05-01" and to_date == "2022-06-30":
                key = "claim_report/Claim_Internal_Audit_Report May-June 2022.xlsx"
                file_name = "Claim_Internal_Audit_Report May-June 2022.xlsx"

            elif from_date == "2022-07-01" and to_date == "2022-09-30":
                key = "claim_report/Claim_Internal_Audit_Report July-Sep 2022.xlsx"
                file_name = "Claim_Internal_Audit_Report July-Sep 2022.xlsx"

            elif from_date == "2022-10-01" and to_date == "2022-12-31":
                key = "claim_report/Claim_Internal_Audit_Report Oct-Dec 2022.xlsx"
                file_name = "Claim_Internal_Audit_Report Oct-Dec 2022.xlsx"

            elif from_date == "2023-01-01" and to_date == "2023-06-30":
                key = "claim_report/Claim_Internal_Audit_Report Jan-June 2023.xlsx"
                file_name = "Claim_Internal_Audit_Report Jan-June 2023.xlsx"

            else:
                raise ServiceException(message="selected date range is not correct")

            download_file(bucket, key, f"{current_app.config['TEMP_DIR']}/{file_name}")
            return send_file(f"{current_app.config['TEMP_DIR']}/{file_name}")
        else:
            raise ServiceException(message="selected client is not correct")

    def client_cash_flow_forecasts(self, year, client_id, currency_code, return_date):
        dates = monthly_dates(year)
        response = {"claimsData": []}
        total_claim_sum = 0
        client_match_condition = [{"client_id": ObjectId(client_id)}] if client_id else []
        unique_booking_ref = set()
        collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"

        for start, end in dates:
            start_x = datetime.strptime(start, "%d-%m-%Y")
            end_x = datetime.strptime(end, "%d-%m-%Y")
            month_name = start_x.strftime("%b")

            from_date = start_x.strftime("%Y-%m-%d")
            to_date = end_x.strftime("%Y-%m-%d")

            forecasts = list(
                get_db()[collection_name].aggregate(
                    [
                        {
                            "$match": {
                                "$and": [
                                    *client_match_condition,
                                    {"currency_code": currency_code},
                                    {"return_date": {"$lt": return_date}},
                                    {
                                        "return_date": {
                                            "$gte": from_date,
                                            "$lte": to_date,
                                        },
                                    },
                                ]
                            }
                        },
                        {
                            "$addFields": {
                                "roundedBalance": {
                                    "$round": ["$balance", 2],
                                }
                            },
                        },
                        {"$match": {"roundedBalance": {"$gt": 0}}},
                        {"$project": {"_id": 0, "booking_ref": 1, "amount": "$balance", "currency_code": 1}},
                    ]
                )
            )

            if forecasts:
                unique_booking_ref = set(forecast["booking_ref"] for forecast in forecasts)
                amount = sum(forecast["amount"] for forecast in forecasts)
                total_claim_sum += amount
            else:
                unique_booking_ref = []
                amount = 0

            data = {
                "month": month_name,
                "amount": amount,
                "bookings": list(unique_booking_ref),
                "bookings_count": len(unique_booking_ref),
            }
            response["claimsData"].append(data)
        response["totalClaimSum"] = total_claim_sum
        return response

    def client_performance(self, client_id, from_date, to_date):
        data = list(
            get_db().claims_metadata.aggregate(
                [
                    {
                        "$match": {
                            "$and": [
                                {
                                    "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
                                    "client_id": ObjectId(client_id),
                                },
                                {
                                    "$expr": {"$gte": [{"$arrayElemAt": ["$claim_files.file_date", -1]}, from_date]},
                                },
                                {
                                    "$expr": {"$lte": [{"$arrayElemAt": ["$claim_files.file_date", -1]}, to_date]},
                                },
                            ]
                        },
                    },
                    {
                        "$group": {
                            "_id": None,
                            "totalNoOfClaims": {"$sum": 1},
                        }
                    },
                    {
                        "$project": {
                            "totalNoOfClaims": "$totalNoOfClaims",
                        },
                    },
                ]
            )
        )
        total_no_of_claims = 0
        for no_of_claims in data:
            total_no_of_claims += no_of_claims["totalNoOfClaims"]
        second_data = list(
            get_db().banking_metadata.aggregate(
                [
                    {
                        "$match": {
                            "$and": [
                                {
                                    "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
                                    "client_id": ObjectId(client_id),
                                },
                            ]
                        },
                    },
                    {"$addFields": {"latest_banking_file": {"$arrayElemAt": ["$banking_files", -1]}}},
                    {"$match": {"latest_banking_file.file_date": {"$gte": from_date, "$lte": to_date}}},
                    {
                        "$lookup": {
                            "from": "banking_file_details",
                            "localField": "latest_banking_file.file_id",
                            "foreignField": "file_id",
                            "as": "file_details",
                        }
                    },
                    {"$unwind": "$file_details"},
                    {
                        "$group": {
                            "_id": {"date": "$latest_banking_file.file_date"},
                            "amount": {"$sum": "$file_details.amount"},
                        }
                    },
                ]
            )
        )
        from_date = datetime.strptime(from_date, "%Y-%m-%d")
        to_date = datetime.strptime(to_date, "%Y-%m-%d")
        delta = relativedelta(to_date, from_date)
        months_diff = delta.years * 12 + delta.months + 1

        month_names = [from_date.strftime("%B")]
        for i in range(1, months_diff):
            next_month = from_date + relativedelta(months=i)
            month_names.append(next_month.strftime("%B"))

        results_by_month = {
            month: {
                "month": month,
                "no_of_claims": 0,
                "amount": 0,
                "total_amount": 0,
                "budgeted_amount": 0,
                "max_no_of_claims": 0,
            }
            for month in month_names
        }
        for claims in month_names:
            results_by_month[claims]["no_of_claims"] = total_no_of_claims
        total_amount = 0
        total_annual_actual_revenue = 0

        for record in second_data:
            record_date = datetime.strptime(record["_id"]["date"], "%Y-%m-%d")
            if from_date <= record_date <= to_date:
                month_name = record_date.strftime("%B")
                amount = record["amount"]
                total_amount += amount
                results_by_month[month_name]["amount"] += amount
                results_by_month[month_name]["total_amount"] = total_amount
                total_annual_actual_revenue = total_amount

        results_list = list(results_by_month.values())

        total_claims = 0
        total_revenue = 0

        client_limit = get_db().client_limit.find_one(
            {
                "client_id": ObjectId(client_id),
                "$or": [
                    {"from_date": from_date, "to_date": to_date},
                    {"from_date": None, "to_date": None},
                ],
            },
            projection={"_id": 0, "max_no_of_claims": 1, "total_annual_revenue": 1},
        )

        if client_limit:
            total_claims = client_limit.get("max_no_of_claims", 0)
            total_revenue = client_limit.get("total_annual_revenue", 0)
        if total_revenue is None:
            total_revenue = 0

        budgeted_amount = round(total_revenue / 12, 2)

        item_dict = {
            "totalClaims": total_claims,
            "totalAnnualRevenue": total_revenue,
            "totalAnnualActualRevenue": total_annual_actual_revenue,
            "actualClaims": total_no_of_claims,
        }
        for result in results_list:
            result["budgeted_amount"] = budgeted_amount
            result["total_claims"] = total_claims
        response_data = {
            "monthly": results_list,
            "yearly": item_dict,
        }
        return response_data

    def list_enabled_clients(self):
        clients = list(
            get_db().client_basic_info.find(
                {"is_disabled": False},
                projection={
                    "_id": 0,
                    "clientId": {"$toString": "$_id"},
                    "clientName": "$full_name",
                    "clientFriendlyName": "$friendly_name",
                },
                collation={"locale": "en", "strength": 1},
                sort=[("full_name", 1)],
            )
        )

        return clients


internal_audit_service = InternalAuditService()
