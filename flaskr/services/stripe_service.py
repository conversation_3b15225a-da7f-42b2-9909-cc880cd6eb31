import json
import stripe
import boto3
from botocore.exceptions import Client<PERSON>rror

from flaskr.helpers.date_util import convert_and_timestamp, format_date_for_email_template
import xlsxwriter
from flask import current_app
from flaskr.models import get_db
from bson import ObjectId


class StripeService:
    def __get_stripe_api_key(self):
        secret_name = "ptt/pennywood/stripe/secret"
        session = boto3.session.Session()
        client = session.client(service_name="secretsmanager", region_name="eu-west-2")
        try:
            get_secret_value_response = client.get_secret_value(SecretId=secret_name)
        except ClientError as e:
            if e.response["Error"]["Code"] == "DecryptionFailureException":
                # Secrets Manager can't decrypt the protected secret text using the provided KMS key.
                # Deal with the exception here, and/or rethrow at your discretion.
                raise e
            elif e.response["Error"]["Code"] == "InternalServiceErrorException":
                # An error occurred on the server side.
                # Deal with the exception here, and/or rethrow at your discretion.
                raise e
            elif e.response["Error"]["Code"] == "InvalidParameterException":
                # You provided an invalid value for a parameter.
                # Deal with the exception here, and/or rethrow at your discretion.
                raise e
            elif e.response["Error"]["Code"] == "InvalidRequestException":
                # You provided a parameter value that is not valid for the current state of the resource.
                # Deal with the exception here, and/or rethrow at your discretion.
                raise e
            elif e.response["Error"]["Code"] == "ResourceNotFoundException":
                # We can't find the resource that you asked for.
                # Deal with the exception here, and/or rethrow at your discretion.
                raise e
        else:
            # Decrypts secret using the associated KMS key.
            if "SecretString" in get_secret_value_response:
                secret = get_secret_value_response["SecretString"]
                stripe_api_key = json.loads(secret).get("stripe_api_key")
                return stripe_api_key

    def get_stripe_transactions(self, from_date, to_date):
        stripe.api_key = self.__get_stripe_api_key()
        from_date_timestamp_format = convert_and_timestamp(from_date, max_time=False)
        to_date_timestamp_format = convert_and_timestamp(to_date, max_time=True)
        filter_params = {"created": {"gte": from_date_timestamp_format, "lte": to_date_timestamp_format}}
        charges = stripe.Charge.list(
            **filter_params,
            expand=["data.balance_transaction", "data.payment_intent", "data.refunds", "data.customer", "data.invoice"],
            limit=100,
        )
        return charges

    def generate_stripe_reconciliation_report(self, final_amount, final_fees, client, name, to_date):
        to_date_timestamp_format = format_date_for_email_template(to_date)
        stripe_settlement = final_amount - final_fees
        basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{name}")
        worksheet = workbook.add_worksheet()
        worksheet.set_column("A:A", 60)
        worksheet.set_column("B:B", 20)

        merge_left = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
        merge_center = workbook.add_format({"bold": 1, "size": 11, "align": "center", "valign": "vcenter"})
        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right", "valign": "vcenter"})
        closing_balance_at_format = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "bold": 1, "valign": "vcenter", "bottom": 6, "top": 1}
        )
        cell_format = workbook.add_format()
        cell_format.set_text_wrap()
        worksheet.write("A1", f"{basic_info['full_name']} Travel Group", merge_left)
        worksheet.write("A2", "Stripe Statement of Account", merge_left)
        worksheet.write("A3", f"For the Month Ending {to_date_timestamp_format}", merge_left)
        worksheet.write("B4", "£", merge_center)
        if final_amount:
            worksheet.write("B6", final_amount, amount_format)
            worksheet.write(
                "A6",
                "Total Funds collected for Trust and Non-Trust\n Payments",
                cell_format,
            )
            worksheet.write("B8", final_fees, amount_format)
            worksheet.write(
                "A8",
                "Stripe Fees ",
                cell_format,
            )
            worksheet.write("B10", stripe_settlement, closing_balance_at_format)
            worksheet.write(
                "A10",
                "Stripe Settlement to HSBC Account ",
                cell_format,
            )
        workbook.close()

    def stripe_reconciliation_summary(self, total_funds_collected, stripe_fees, client_name):
        stripe_settlement_amount = total_funds_collected - stripe_fees

        summary = {
            "totalFundsCollected": total_funds_collected,
            "stripeFees": stripe_fees,
            "stripeSettlementAmount": stripe_settlement_amount,
            "clientName": client_name.capitalize(),
        }

        return summary


stripe_service = StripeService()
