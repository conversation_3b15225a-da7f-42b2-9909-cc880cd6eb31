from logging.config import dictConfig

from flask import Flask
from celery import Celery

from celery_app.celery_instance import configure_celery
from flask_cors import CORS
from marshmallow.exceptions import ValidationError
from pymongo.errors import DuplicateKeyError

from config import LocalConfig, PreProdConfig, TestConfig, DevelopmentConfig, StageConfig, ProductionConfig
from flaskr.controllers.email import email_api
from flaskr.controllers.anomalies import anomalies_api
from flaskr.controllers.auth import auth_api
from flaskr.controllers.banking import banking_api
from flaskr.controllers.booking import booking_api
from flaskr.controllers.client import client_api
from flaskr.controllers.claims import claims_api
from flaskr.controllers.exceptions import ControllerException
from flaskr.controllers.lookup import lookup_api
from flaskr.controllers.reports import reports_api
from flaskr.controllers.dashboard import dashboard_api
from flaskr.controllers.issue_log import issue_log_api
from flaskr.controllers.open_banking import open_banking_api
from flaskr.controllers.banking_and_claim_summary import banking_and_claims_api
from flaskr.controllers.internal_audit import internal_audit_api
from flaskr.controllers.stripe import stripe_api
from flaskr.controllers.powerbi import powerbi_api

from flaskr.models import mongo
from flaskr.services.exceptions import AccessDeniedException, ServiceException


def create_app(config=None):
    dictConfig(
        {
            "version": 1,
            "formatters": {
                "default": {
                    "format": "%(asctime)s %(levelname)s in %(module)s: %(message)s",
                }
            },
            "handlers": {
                "wsgi": {
                    "class": "logging.StreamHandler",
                    "stream": "ext://flask.logging.wsgi_errors_stream",
                    "formatter": "default",
                }
            },
            "root": {"level": "INFO", "handlers": ["wsgi"]},
        }
    )

    app: Flask = Flask(__name__)
    CORS(app)

    if config == "test":
        app.config.from_object(TestConfig())
    elif config == "local":
        app.config.from_object(LocalConfig())
    elif config == "dev":
        app.config.from_object(DevelopmentConfig())
    elif config == "stage":
        app.config.from_object(StageConfig())
    elif config == "pre-prod":
        app.config.from_object(PreProdConfig())
    elif config == "prod":
        app.config.from_object(ProductionConfig())

    elif config is None:
        # load the instance config, if it exists, when not testing
        app.config.from_object(DevelopmentConfig())
    else:
        # load the config if passed in
        app.config.from_object(config)

    app.logger.info(f"Loaded config {config}")

    mongo.init_app(app)
    cel_app: Celery = configure_celery(app)

    app.logger.info(f"Initialized mongo")

    app.register_blueprint(auth_api, url_prefix="/api")
    app.register_blueprint(client_api, url_prefix="/api/client")
    app.register_blueprint(lookup_api, url_prefix="/api/lookup")
    app.register_blueprint(banking_api, url_prefix="/api/banking")
    app.register_blueprint(booking_api, url_prefix="/api/booking")
    app.register_blueprint(claims_api, url_prefix="/api/claim")
    app.register_blueprint(anomalies_api, url_prefix="/api/anomaly")
    app.register_blueprint(reports_api, url_prefix="/api/reports")
    app.register_blueprint(dashboard_api, url_prefix="/api/dashboard")
    app.register_blueprint(issue_log_api, url_prefix="/api/issue-log")
    app.register_blueprint(open_banking_api, url_prefix="/api/open-banking")
    app.register_blueprint(banking_and_claims_api, url_prefix="/api/banking-and-claims")
    app.register_blueprint(email_api, url_prefix="/api/email")
    app.register_blueprint(internal_audit_api, url_prefix="/api/internal-audit/reports")
    app.register_blueprint(stripe_api, url_prefix="/api/stripe")
    app.register_blueprint(powerbi_api, url_prefix="/api/powerbi")

    app.logger.info(f"Registered the api routes")

    @app.errorhandler(ValidationError)
    def handle_validation_error(e):
        app.logger.error(e.messages)
        return {"errors": e.messages}, 400

    @app.errorhandler(ControllerException)
    def handle_controller_exception(e):
        app.logger.error(e)
        return {"errors": e.messages}, 400

    @app.errorhandler(ServiceException)
    def handle_service_exception(e):
        app.logger.error(e)
        return {"errors": e.messages}, 400

    @app.errorhandler(DuplicateKeyError)
    def handle_duplicate_key_error(e):
        app.logger.error(e)
        return {"errors": "Duplicate Entity"}, 422

    @app.errorhandler(AccessDeniedException)
    def handle_access_denied(e):
        app.logger.error(e)
        return {"errors": "Access Denied"}, 403

    @app.route("/")
    def hello():
        return "Hello"

    return app
