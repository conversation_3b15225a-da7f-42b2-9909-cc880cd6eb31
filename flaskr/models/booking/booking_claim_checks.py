from flaskr.models import ObjectId
from marshmallow import Schema, fields, EXCLUDE


class BookingClaimChecksSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(required=True, data_key="clientId")
    transaction_id = ObjectId(data_key="transactionId")
    name = fields.String(data_key="name")
    description = fields.String(data_key="description")
    notes = fields.String(data_key="notes")
    selected = fields.Boolean(required=True, data_key="selected")
    modified_by = fields.String(data_key="modifiedBy")
    updated_at = fields.DateTime(data_key="updatedAt")
