from marshmallow import Schema, fields, RAISE
from flaskr.models import ObjectId


class TrustFundSchema(Schema):
    class Meta:
        unknown = RAISE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(required=True, data_key="clientId")
    booking_ref = fields.String(required=True, data_key="bookingRef")
    currency_code = fields.List(fields.String, data_key="currencyCode", dump_only=True)
    balance = fields.Number(data_key="balance", dump_only=True)
    remaining_cap_amount = fields.Number(data_key="remainingCapAmount", dump_only=True)
    total_in_trust = fields.Number(data_key="totalInTrust", dump_only=True)
    total_claimed = fields.Number(data_key="totalClaimed", dump_only=True)
    lead_pax = fields.String(data_key="leadPax", allow_none=True)
    pax_count = fields.Number(data_key="paxCount", dump_only=True)
    booking_date = fields.String(data_key="bookingDate", allow_none=True)
    departure_date = fields.String(data_key="departureDate", allow_none=True)
    return_date = fields.String(data_key="returnDate", allow_none=True)
    total_booking_value = fields.Number(data_key="totalBookingValue", allow_none=True)
    bonding = fields.String(data_key="bonding", dump_only=True)
    nights = fields.Number(data_key="nights", dump_only=True)
    created_at = fields.DateTime(data_key="createdAt", dump_only=True)
    updated_at = fields.DateTime(data_key="updatedAt", dump_only=True)
    total_due_to_supplier = fields.Number(data_key="totalDueToSupplier", allow_none=True)
    total_paid_by_customer = fields.Number(data_key="totalPaidByCustomer", allow_none=True)
    date_customer_paid = fields.String(data_key="dateCustomerPaid", allow_none=True)
    supplier = fields.String(data_key="supplier")
    booking_status = fields.String(data_key="bookingStatus")
    type = fields.String(data_key="type")
    payment_type = fields.String(data_key="paymentType")
