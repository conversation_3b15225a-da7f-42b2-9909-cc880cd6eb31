import bson
from flask_pymongo import PyMongo
from marshmallow import ValidationError, fields, missing

mongo = PyMongo()


def get_db():
    return mongo.db


def is_not_blank(val):
    return val is not None and len(val.strip()) != 0


class ObjectId(fields.Field):
    def _deserialize(self, value, attr, data, **kwargs):
        try:
            return bson.ObjectId(value)
        except Exception:
            raise ValidationError("invalid ObjectId")

    def _serialize(self, value, attr, obj):
        if value is None:
            return missing
        return str(value)
