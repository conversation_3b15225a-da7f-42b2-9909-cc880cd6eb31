from flaskr.models import ObjectId
from marshmallow import Schema, fields, EXCLUDE


class RemovedBookingRefsSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(required=True, data_key="clientId")
    booking_ref = fields.String(required=True, data_key="bookingRef")
    file_id = fields.String(required=True, data_key="fileId")
    element = fields.String(data_key="element")
    removal_date = fields.DateTime(required=True, data_key="removalDate")
    return_date = fields.Date(data_key="returnDate")
    reason = fields.String(data_key="reason")
    notes = fields.String(data_key="notes")
    created_at = fields.DateTime(data_key="createdAt")
    expired = fields.Boolean(default=False, data_key="expired") 