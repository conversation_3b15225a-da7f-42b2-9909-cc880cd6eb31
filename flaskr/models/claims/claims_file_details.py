from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId
from marshmallow.validate import OneOf


class ClaimsFileDetailsSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    claims_id = ObjectId(data_key="claimsId")
    file_id = fields.String(data_key="fileId")
    client_id = ObjectId(data_key="clientId")
    booking_ref = fields.String(data_key="bookingRef")
    funds_in_trust = fields.Boolean(data_key="fundsInTrust")
    local_value = fields.Number(data_key="localValue")
    charter_flight = fields.Number(data_key="charterFlight")
    scheduled_flight = fields.Number(data_key="scheduledFlight")
    accom = fields.Number(data_key="accom")
    transfer = fields.Number(data_key="transfer")
    parking = fields.Number(data_key="parking")
    car_hire = fields.Number(data_key="carHire")
    tour = fields.String(data_key="tour")
    add_on_tour = fields.Number(data_key="addOnTour")
    generic = fields.Number(data_key="generic")
    cruise = fields.Number(data_key="cruise")
    trvl_ins = fields.Number(data_key="trvlIns")
    output_vat = fields.Number(data_key="outputVat")
    fees = fields.Number(data_key="fees")
    td_profit = fields.Number(data_key="tdProfit")
    total_paid = fields.Number(data_key="totalPaid", allow_none=True)
    currency_code = fields.String(data_key="currencyCode")
    element = fields.String(data_key="element")
    claim_suggested_value = fields.Number(data_key="claimSuggestedValue")
    claimed_in_mth = fields.Number(data_key="claimedInMth")
    cuml_claimed_value = fields.Number(data_key="cumlClaimedValue")
    cumulative_fit = fields.Number(data_key="cumulativeFit")
    validity_date = fields.String(data_key="validityDate")
    check = fields.String(data_key="check", validate=OneOf(["", "full-check", "quick-check"]))
    id = fields.Number(data_key="id")
    receipt_or_paid = fields.String(data_key="receiptOrPaid")
    amount = fields.Number(data_key="amount")
    booking_date = fields.String(data_key="bookingDate", allow_none=True)
    departure_date = fields.String(data_key="departureDate", allow_none=True)
    nights = fields.Number(data_key="nights")
    atol = fields.Boolean(data_key="atol")
    post_or_pre = fields.String(data_key="postOrPre")
    payment_method = fields.String(data_key="paymentMethod")
    method_of_payment_summary = fields.String(data_key="methodOfPaymentSummary")
    receipt_or_paid_date = fields.String(data_key="receiptOrPaidDate")
    operator_or_pax = fields.String(data_key="operatorOrPax")
    company = fields.String(data_key="company")
    note = fields.String(data_key="note")
    date_paid_or_withdrawn_from_trust = fields.String(data_key="datePaidOrWithdrawnFromTrust")
    lead_pax = fields.String(data_key="leadPax", allow_none=True)
    pax_count = fields.Number(data_key="paxCount", allow_none=True)
    supplier_ref = fields.String(data_key="supplierRef", allow_none=True)
    supplier_names = fields.String(data_key="supplierNames", allow_none=True)
    type = fields.String(data_key="type")
    bonding = fields.String(data_key="bonding")
    dept_bal_lead_time = fields.String(data_key="deptBalLeadTime")
    total_booking_value = fields.Number(data_key="totalBookingValue", allow_none=True)
    element_ref = fields.String(data_key="elementRef")
    atol_amount_or_refund_amount = fields.Number(data_key="atolAmountOrRefundAmount")
    return_date = fields.String(data_key="returnDate", allow_none=True)
    confirmed_date = fields.String(data_key="confirmedDate")
    optioned_date = fields.String(data_key="optionedDate")
    total_amount = fields.Number(data_key="totalAmount")
    deposit_amount = fields.Number(data_key="depositAmount")
    payment_type = fields.String(data_key="paymentType")
    ticket_no = fields.Number(data_key="ticketNo")
    t_date_rule = fields.Number(data_key="tDateRule")
    d_date_rule = fields.Number(data_key="dDateRule")
    payment_reference = fields.String(data_key="paymentReference")
    reference = fields.String(data_key="reference")
    customer = fields.String(data_key="customer")
    customer_type = fields.String(data_key="customerType")
    status = fields.String(data_key="status")
    status_reason = fields.String(data_key="statusReason")
    total_due_to_supplier = fields.Number(data_key="totalDueToSupplier", allow_none=True)
    cancellation = fields.String(data_key="cancellation")
    commission = fields.String(data_key="commission")
    vat_on_commission = fields.Number(data_key="vatOnCommission")
    ejh_deposit = fields.Number(data_key="ejhDeposit")
    ejh_balance = fields.Number(data_key="ejhBalance")
    head_office = fields.String(data_key="headOffice")
    agent_comm = fields.Number(data_key="agentComm")
    amount_paid = fields.Number(data_key="amountPaid")
    cancellation_date = fields.String(data_key="cancellationDate")
    non_trust = fields.Number(data_key="nonTrust")
    difference = fields.Number(data_key="difference")
    exhibit_b_type = fields.String(data_key="exhibitBType")
    days_to_process = fields.Number(data_key="daysToProcess")
    payment_date = fields.String(data_key="paymentDate")
    booking_status = fields.String(data_key="bookingStatus")
    claimed_before = fields.String(data_key="claimedBefore")
    bsp_flag = fields.String(data_key="bspFlag")
    date_paid = fields.String(data_key="datePaid")
    date_returned = fields.String(data_key="dateReturned")
    abta_no = fields.String(data_key="abtaNumber")
    booking_reference = fields.String(data_key="bookingReference")
    deposit_or_balance = fields.Number(data_key="depositOrBalance")
    claim_date = fields.String(data_key="claimDate")
    original_amount = fields.Number(data_key="originalAmount")
    escrow_multiplier = fields.Number(data_key="escrowMultiplier")
    entry_no = fields.Number(data_key="entryNumber")
    entry_date = fields.String(data_key="entryDate")
    non_bonded_amount = fields.Number(data_key="nonBondedAmount")
    total_bonded_value = fields.Number(data_key="totalBondedValue")
    current_res_status = fields.String(data_key="currentResStatus")
    res_payment_status = fields.String(data_key="resPaymentStatus")
    cancel_date = fields.String(data_key="cancelDate")
    trust_status = fields.String(data_key="trustStatus")
    date_claim_file = fields.String(data_key="dateClaimFile")
    ref_claim_file = fields.String(data_key="refClaimFile")
