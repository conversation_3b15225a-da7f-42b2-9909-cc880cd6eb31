from marshmallow import <PERSON><PERSON><PERSON><PERSON><PERSON>, Schema, fields
from flaskr.models import ObjectId


class FileDetailSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    file_id = fields.String(required=True, data_key="fileId")
    file_name = fields.String(required=True, data_key="fileName")
    file_date = fields.String(data_key="fileDate")
    submitted_date = fields.DateTime(data_key="submittedDate")
    status = fields.String(data_key="status")
    item_count = fields.Dict(data_key="items")
    claim_total = fields.Dict(data_key="claimTotal")
    notes = fields.String(data_key="notes")
    checks = fields.Dict(data_key="checks")
    checked_amount = fields.Dict(data_key="checkedAmount")


class ClaimTestingSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    claims_id = ObjectId(required=True, data_key="claimsId")
    original_claim = fields.Dict(data_key="originalClaim")
    reasons = fields.String(data_key="reasons", allow_none=True)
    created_at = fields.DateTime(data_key="createdAt")
    updated_at = fields.DateTime(data_key="updatedAt")


class ClaimsMetadataSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(data_key="clientId")
    claim_files = fields.List(
        fields.Nested(FileDetailSchema),
        data_key="claimFiles",
    )
    assigned_to = fields.String(data_key="assignedTo", allow_none=True)
    notes = fields.String(data_key="notes", default="", allow_none=True)
    status = fields.String(data_key="status", allow_none=True)
    frequency = fields.String(data_key="frequency", allow_none=True)
    created_at = fields.DateTime(data_key="createdAt")
    updated_at = fields.DateTime(data_key="updatedAt")
