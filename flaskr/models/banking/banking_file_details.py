from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId


class BankingFileDetailsSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    banking_id = ObjectId(data_key="bankingId")
    file_id = fields.String(data_key="fileId")
    client_id = ObjectId(data_key="clientId")
    booking_ref = fields.String(data_key="bookingRef")
    lead_pax = fields.String(data_key="leadPax", allow_none=True)
    pax_count = fields.Number(data_key="paxCount")
    booking_date = fields.String(data_key="bookingDate", allow_none=True)
    departure_date = fields.String(data_key="departureDate", allow_none=True)
    return_date = fields.String(data_key="returnDate", allow_none=True)
    currency_code = fields.String(data_key="currencyCode")
    payment_type = fields.String(data_key="paymentType")
    total_booking_value = fields.Number(data_key="totalBookingValue")
    supplier_ref = fields.String(data_key="supplierRef", allow_none=True)
    payment_date = fields.String(data_key="paymentDate")
    amount = fields.Number(data_key="amount")
    cumulative_fit = fields.Number(data_key="cumulativeFit")
    total_fit = fields.Number(data_key="totalFit")
    exhibit_b_type = fields.String(data_key="exhibitBType")
    optioned_date = fields.String(data_key="optionedDate")
    ticket_no = fields.Number(data_key="ticketNo")
    id = fields.Number(data_key="id")
    receipt_or_paid = fields.String(data_key="receiptOrPaid")
    payment_method = fields.String(data_key="paymentMethod")
    receipt_or_paid_date = fields.String(data_key="receiptOrPaidDate")
    operator = fields.String(data_key="operator")
    note = fields.String(data_key="note")
    created_at = fields.DateTime(data_key="createdAt")
    updated_at = fields.DateTime(data_key="updatedAt")
    operator_or_pax = fields.String(data_key="operatorOrPax")
    card_no = fields.String(data_key="cardNo")
    txn_date = fields.String(data_key="txnDate")
    in_trust = fields.Boolean(data_key="inTrust")
    supplier_element_gross = fields.Number(data_key="supplierElementGross")
    non_supplier_element_gross = fields.Number(data_key="nonSupplierElementGross")
    total_deposit = fields.Number(data_key="totalDeposit")
    trust_balance = fields.Number(data_key="trustBalance")
    payment_ref = fields.String(data_key="paymentRef")
    balance_due_date = fields.String(data_key="balanceDueDate")
    report_date = fields.String(data_key="reportDate")
    payment_received = fields.String(data_key="paymentReceived")
    due = fields.Number(data_key="due")
    date_received = fields.String(data_key="dateReceived")
    ref = fields.String(data_key="ref")
    date_paid_into_trust = fields.String(data_key="datePaidIntoTrust")
    month_paid_into_trust = fields.String(data_key="monthPaidIntoTrust")
    total_paid_by_customer = fields.Number(data_key="totalPaidByCustomer")
    confirmed_date = fields.Number(data_key="confirmedDate")
    agent_id = fields.String(data_key="agentId")
    total_bkg_revenue = fields.String(data_key="totalBkgRevenue")
    customer_type = fields.String(data_key="customerType")
    bonding = fields.String(data_key="bonding")
    nights = fields.Number(data_key="nights")
    transfer_of_funds = fields.String(data_key="transferOfFunds")
    booking_status = fields.String(data_key="bookingStatus")
    type = fields.String(data_key="type")
    days_to_process = fields.Number(data_key="daysToProcess")
    supplier_names = fields.String(data_key="supplierNames")
    status = fields.String(data_key="status")
    status_reason = fields.String(data_key="statusReason")
    original_amount = fields.Number(data_key="originalAmount")
    escrow_multiplier = fields.Number(data_key="escrowMultiplier")
    trust_type = fields.String(data_key="trustType")
    element = fields.String(data_key="element")
    funds_collected = fields.Number(data_key="fundsCollected")
    escrow_deposit = fields.Number(data_key="escrowDeposit")
    entry_no = fields.Number(data_key="entryNumber")
    entry_date = fields.String(data_key="entryDate")
    entry_type = fields.String(data_key="entryType")
    current_reservation = fields.String(data_key="currentReservation")
    last_status_update = fields.String(data_key="lastStatusUpdate")
    cancel_date = fields.String(data_key="cancelDate")
    customer_no = fields.Number(data_key="customerNumber")
    journal_id = fields.Number(data_key="journalId")
    payment_relation_id = fields.Number(data_key="paymentRelationId")
    alloc_date = fields.String(data_key="allocDate")
    allocated_amount = fields.Number(data_key="allocatedAmount")
    remaining_amount = fields.Number(data_key="remainingAmount")
    bond_proportion = fields.Number(data_key="bondProportion")
    non_bonded_amount = fields.Number(data_key="nonBondedAmount")
    total_bonded_value = fields.Number(data_key="totalBondedValue")
    date_banking_file = fields.String(data_key="dateBankingFile")
    ref_banking_file = fields.String(data_key="refBankingFile")
    bank_file_amount = fields.Number(data_key="bankFileAmount")
    non_trust_flag = fields.Boolean(missing=False)
