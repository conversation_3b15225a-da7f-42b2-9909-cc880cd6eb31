from marshmallow import <PERSON><PERSON><PERSON><PERSON><PERSON>, Schema, fields
from flaskr.models import ObjectId


class FileDetailSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    file_id = fields.String(required=True, data_key="fileId")
    file_name = fields.String(required=True, data_key="fileName")
    file_date = fields.String(data_key="fileDate")
    submitted_date = fields.DateTime(data_key="submittedDate")
    status = fields.String(data_key="status")
    item_count = fields.Dict(data_key="items")
    deposit = fields.Dict(data_key="deposit")
    notes = fields.String(data_key="notes")


class BankingMetadataSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(data_key="clientId")
    banking_files = fields.List(
        fields.Nested(FileDetailSchema),
        data_key="bankingFiles",
    )
    assigned_to = fields.String(data_key="assignedTo", allow_none=True)
    notes = fields.String(data_key="notes", default="", allow_none=True)
    status = fields.String(data_key="status", allow_none=True)
    created_at = fields.DateTime(data_key="createdAt")
    updated_at = fields.DateTime(data_key="updatedAt")
