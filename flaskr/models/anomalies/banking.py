from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId


class AnomalyBankingSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(required=True, data_key="clientId")
    banking_id = ObjectId(required=True, data_key="bankingId")
    booking_ref = fields.String(data_key="bookingRef")
    transaction_id = ObjectId(required=True, data_key="transactionId")
    file_id = fields.String(required=True, data_key="fileId")
    anomaly_type = fields.String(data_type="anomalyType")
    status = fields.String(data_type="status")
    anomaly_id = ObjectId(data_key="AnomalyId")
    created_at = fields.DateTime(data_key="createdAt")
    updated_at = fields.DateTime(data_key="updatedAt")
    modified_by = fields.String(data_key="modifiedBy")
