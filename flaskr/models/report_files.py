from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId


class ReportFilesSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    file_id = fields.String(data_key="fileId")
    file_type = fields.String(data_key="fileType")
    name = fields.String(data_key="name")
    client_id = ObjectId(data_key="clientId")
    currency = fields.String(data_key="currency", allow_none=True)
    status = fields.String(data_key="status")
    generated_at = fields.DateTime(data_key="generatedAt")
    created_at = fields.DateTime(data_key="createdAt")
    updated_at = fields.DateTime(data_key="updatedAt")
