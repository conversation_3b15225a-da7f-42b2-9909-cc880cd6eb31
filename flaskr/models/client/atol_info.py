from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId


class ClientAtolInfoSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(required=True, data_key="clientId")
    license = fields.String(data_key="license")
    start_date = fields.DateTime(data_key="startDate", format="%Y-%m-%d")
    files = fields.List(
        fields.Dict,
        required=True,
        data_key="files",
    )
    expiry_date = fields.DateTime(data_key="expiryDate", format="%Y-%m-%d")
    document = fields.String(data_key="document")
