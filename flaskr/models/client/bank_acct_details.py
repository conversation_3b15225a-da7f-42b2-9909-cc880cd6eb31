from marshmallow import fields, EXCLUDE, Schema
from flaskr.models import ObjectId


class ClientBankAcctDetailsSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(required=True, data_key="clientId")
    bank_name = fields.String(data_key="bankName")
    account_no = fields.String(data_key="accountNumber")
    sort_code = fields.String(data_key="sortCode")
    currency = fields.String(data_key="currency")
    iban = fields.String(data_key="iban")
    account_type = fields.String(data_key="accountType")
    exclude_from_report = fields.Boolean(data_key="excludeFromReport")
