from flaskr.models import ObjectId, is_not_blank
from marshmallow import Schema, fields, EXCLUDE


class ClientCheckInfoSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(required=True, data_key="clientId")
    check_name = fields.String(required=True, validate=is_not_blank, data_key="checkName")
    short_name = fields.String(required=True, validate=is_not_blank, data_key="shortName")
    related_element = fields.String(required=True, validate=is_not_blank, data_key="relatedElement")
    description = fields.String(required=True, validate=is_not_blank, data_key="description")
