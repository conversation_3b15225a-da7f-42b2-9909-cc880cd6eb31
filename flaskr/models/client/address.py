from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId


class ClientAddressSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="id")
    line1 = fields.String(data_key="line1")
    line2 = fields.String(data_key="line2")
    line3 = fields.String(data_key="line3")
    town = fields.String(data_key="town")
    country = fields.String(data_key="country")
    postcode = fields.String(data_key="postcode")
    client_id = ObjectId(required=True, data_key="clientId")
