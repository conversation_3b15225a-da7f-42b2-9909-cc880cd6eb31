from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId


class SupplierListSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    supplier_name = fields.String(data_key="supplierName")
    cap_amount = fields.Number(data_key="capAmount", allow_none=True)


class ClientFilesSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(required=True, data_key="clientId")
    file_name = fields.String(data_key="fileName")
    file_id = fields.String(data_key="fileId")
    supplier_list = fields.List(
        fields.Nested(SupplierListSchema),
        data_key="supplierList",
    )
