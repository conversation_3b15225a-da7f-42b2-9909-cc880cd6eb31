from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import is_not_blank, ObjectId


class ClientBasicInfoSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="clientId")
    c_id = fields.String(required=True, data_key="cId")
    friendly_name = fields.String(required=True, validate=is_not_blank, data_key="friendlyName")
    full_name = fields.String(required=True, validate=is_not_blank, data_key="fullName")
    go_live_date = fields.DateTime(required=True, data_key="goLiveDate", format="%Y-%m-%d")
    type_of_trust_account = ObjectId(data_key="typeOfTrustAccount", allow_none=True)
    existing_client = fields.Boolean(data_key="existingClient")
    reuse_old_booking = fields.Boolean(data_key="reuseOldBooking")
    status = fields.String(data_key="status")
    email = fields.String(data_key="email")
    point_of_contact = fields.String(data_key="pointOfContact")
    is_editable = fields.Boolean(data_key="isEditable")
    claim_from_tbr = fields.Boolean(data_key="claimFromTBR", default=False)
    created_at = fields.DateTime(data_key="createdAt")
    is_disabled = fields.Boolean(data_key="isDisabled", default=False)
    sftp_location = fields.List(fields.String, data_key="sftpLocation", dump_default=[])
    sftp_location_reports = fields.String(data_key="sftpLocationReports")
    lead_time = fields.Number(data_key="leadTime", allow_none=True)
    escrow_multiplier = fields.Number(data_key="escrowMultiplier")
    new_workflow = fields.Boolean(data_key="newWorkflow")
    to = fields.List(fields.String, data_key="to", dump_default=[])
    cc = fields.List(fields.String, data_key="cc", dump_default=[])
    company_alias = fields.String(data_key="companyAlias")
    bluestyle_no_of_days = fields.Number(data_key="bluestyleNoOfDays", allow_none=True)
