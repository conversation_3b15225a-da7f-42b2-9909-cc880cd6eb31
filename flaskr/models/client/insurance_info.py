from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId


class ClientInsuranceInfoSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(required=True, data_key="clientId")
    policy_no = fields.String(data_key="policyNumber")
    files = fields.List(
        fields.Dict,
        required=True,
        data_key="files",
    )
    provider = fields.String(data_key="provider")
    expiry_date = fields.DateTime(data_key="expiryDate", format="%Y-%m-%d")
    document = fields.String(data_key="document")
    supplier_list_file = fields.Dict(data_key="supplierListFile")
