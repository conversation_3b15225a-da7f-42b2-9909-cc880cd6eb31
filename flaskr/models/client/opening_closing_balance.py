from flaskr.models import ObjectId
from marshmallow import Schema, fields, EXCLUDE


class OpeningClosingBalanceSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="id")
    client_id = ObjectId(required=True, data_key="clientId")
    currency = fields.String(data_key="currency")
    opening_balance = fields.Number(data_key="openingBalance")
    closing_balance = fields.Number(data_key="closingBalance")
    date = fields.DateTime(data_key="date", format="%Y-%m-%d")
    updated_at = fields.DateTime(data_key="updatedAt")
    created_at = fields.DateTime(data_key="createdAt")
