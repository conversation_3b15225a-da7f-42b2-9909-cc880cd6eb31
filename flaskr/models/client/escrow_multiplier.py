from flaskr.models import ObjectId
from marshmallow import Schema, fields, EXCLUDE


class ClientEscrowMultiplierSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    date = fields.String(data_key="date", format="%Y-%m-%d")
    multiplier = fields.Number(data_key="multiplier")
    client_id = ObjectId(required=True, data_key="clientId")
    modified_by = fields.String(data_key="modifiedBy")
    debit_multiplier = fields.Number(data_key="debitMultiplier")
    credit_multiplier = fields.Number(data_key="creditMultiplier")
    multiplier_difference = fields.Number(data_key="multiplierDifference")
