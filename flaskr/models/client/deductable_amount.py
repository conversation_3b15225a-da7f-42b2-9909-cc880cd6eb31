from flaskr.models import ObjectId
from marshmallow import Schema, fields, EXCLUDE


class ClientDeductableAmountSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    date = fields.String(data_key="date", format="%Y-%m-%d")
    deductable_amount = fields.Number(data_key="deductable_amount")
    client_id = ObjectId(required=True, data_key="clientId")
    modified_by = fields.String(data_key="modifiedBy")
    currency = fields.String(data_key="currency")