from flaskr.models import ObjectId
from marshmallow import Schema, fields, EXCLUDE


class ClientAuthorizedSignatoriesInfoSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(required=True, data_key="clientId")
    name = fields.String(required=True, data_key="name")
    status = fields.String(data_key="status")
    signature_file = fields.Dict(data_key="signatureFile")
