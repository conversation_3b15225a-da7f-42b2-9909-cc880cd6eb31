from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId


class ClientBondInfoSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(required=True, data_key="clientId")
    note = fields.String(data_key="note")
    bond_amount = fields.Number(data_key="bondAmount", allow_none=True)
    files = fields.List(
        fields.Dict,
        required=True,
        data_key="files",
    )
    expiry_date = fields.DateTime(data_key="expiryDate", format="%Y-%m-%d")
    start_date = fields.DateTime(data_key="startDate", format="%Y-%m-%d")
    bond_name = fields.String(data_key="bondName")
