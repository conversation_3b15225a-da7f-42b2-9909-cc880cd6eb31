from flaskr.models import ObjectId
from marshmallow import Schema, fields, EXCLUDE


class ClientLimitSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="id")
    max_no_of_claims = fields.Number(data_key="maximumNoOfClaims", allow_none=True)
    total_annual_revenue = fields.Number(data_key="totalAnnualRevenue", allow_none=True)
    currency = fields.String(data_key="currency")
    client_id = ObjectId(required=True, data_key="clientId")
    from_date = fields.DateTime(data_key="fromDate", format="%Y-%m-%d", allow_none=True)
    to_date = fields.DateTime(data_key="toDate", format="%Y-%m-%d", allow_none=True)
    preferred = fields.Boolean(data_key="preferred", allow_none=True)
