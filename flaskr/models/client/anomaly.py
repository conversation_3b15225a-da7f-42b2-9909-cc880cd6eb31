from marshmallow import <PERSON><PERSON><PERSON><PERSON><PERSON>, Schema, fields
from flaskr.models import ObjectId


class ClientAnomalySchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(required=True, data_key="clientId")
    anomaly_id = ObjectId(required=True, data_key="anomalyId")
    custom_field_value = fields.Number(data_key="customFieldValue")
    elements = fields.List(fields.String, data_key="elements")
    bank_guarantee_value = fields.Number(data_key="bankGuaranteeValue")
    from_date = fields.String(data_key="fromDate", format="%Y-%m-%d")
    to_date = fields.String(data_key="toDate", format="%Y-%m-%d")
    name = fields.String(data_key="name")
    ticket_master_limit_value = fields.Number(data_key="ticketMasterLimitValue")
    direct_limit_value = fields.Number(data_key="directLimitValue")
    consider_booking_type = fields.Boolean(data_key="considerBookingType")
