from flaskr.models import ObjectId
from marshmallow import Schema, fields, EXCLUDE


class ClientManualsLogsInfoSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    user_id  = fields.String(data_key="userId") 
    client_id = ObjectId(required=True, data_key="clientId")  
    name  =  fields.String(data_key="clientName")
    activity = fields.String(data_key="activity")
    created_at = fields.DateTime(data_key="createdAt")
    
