from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId


class InternalBankingSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(data_key="clientId")
    notes = fields.String(data_key="notes", allow_none=True)
    status = fields.String(data_key="status", allow_none=True)
    business_rules = fields.String(data_key="businessRules", allow_none=True)
    resolution_notes = fields.String(data_key="resolutionNotes", allow_none=True)
    risk = fields.String(data_key="risk", allow_none=True)
    amount_asper_bank = fields.String(data_key="amountasperBank", allow_none=True)
    testing_status = fields.Boolean(data_key="testingstatus", allow_none=True)
    deleted = fields.Boolean(data_key="deleted", allow_none=True)
    created_at = fields.DateTime(data_key="createdAt")
    updated_at = fields.DateTime(data_key="updatedAt")
