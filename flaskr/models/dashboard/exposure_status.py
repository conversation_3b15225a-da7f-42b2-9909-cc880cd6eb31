from marshmallow import <PERSON>X<PERSON><PERSON><PERSON>, Schema, fields
from flaskr.models import ObjectId


class StatusDetailsSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    count = fields.Number(data_key="count")
    amount = fields.Number(data_key="amount")


class ExpiredStatusSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    visa = fields.Nested(StatusDetailsSchema, data_key="visa")
    master_card = fields.Nested(StatusDetailsSchema, data_key="masterCard")


class StatusSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    open = fields.Nested(StatusDetailsSchema, data_key="open")
    flown = fields.Nested(StatusDetailsSchema, data_key="flown")
    vouchered = fields.Nested(StatusDetailsSchema, data_key="vouchered")
    rebooked = fields.Nested(StatusDetailsSchema, data_key="rebooked")
    refunded = fields.Nested(StatusDetailsSchema, data_key="refunded")
    expired = fields.Nested(ExpiredStatusSchema, data_key="expired")
    charge_back = fields.Nested(StatusDetailsSchema, data_key="chargeBack")


class DashboardExposureStatusSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(data_key="clientId")
    currency_code = fields.String(data_key="currencyCode")
    exposure_status = fields.Nested(StatusSchema, data_key="exposureStatus")
    updated_at = fields.DateTime(data_key="updatedAt")
