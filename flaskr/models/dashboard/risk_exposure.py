from marshmallow import Schema, fields, RAISE
from flaskr.models import ObjectId


class DashboardRiskExposureSchema(Schema):
    class Meta:
        unknown = RAISE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(data_key="clientId")
    currency_code = fields.String(data_key="currencyCode")
    claim_amount = fields.Number(data_key="claimAmount")
    date = fields.DateTime(data_key="date")
    created_at = fields.DateTime(data_key="createdAt")
    updated_at = fields.DateTime(data_key="updatedAt")
