from marshmallow import Schema, fields, RAISE
from flaskr.models import ObjectId


class OpeningClosingBalanceSchema(Schema):
    class Meta:
        unknown = RAISE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId()
    currency = fields.String()
    opening_balance = fields.Number()
    closing_balance = fields.Number()
    date = fields.DateTime()
    created_at = fields.DateTime()
    updated_at = fields.DateTime()
