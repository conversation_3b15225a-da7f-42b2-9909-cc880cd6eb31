from marshmallow import Schema, EXCLUDE, fields
from flaskr.models import is_not_blank, ObjectId


class LookupAnomalySchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    name = fields.String(required=True, data_key="name", validate=is_not_blank)
    description = fields.String(data_key="description")
    custom_field_name = fields.String(data_key="customFieldName")
    dependencies = fields.List(fields.String, data_key="dependencies")
    guarantee_value_name = fields.String(data_key="guaranteeValueName")
    from_date_name = fields.String(data_key="fromDateName")
    to_date_name = fields.String(data_key="toDateName")
    insurance_name = fields.String(data_key="insuranceName", allow_none=True)
    ticket_master_limit_name = fields.String(data_key="ticketMasterLimitName")
    direct_limit_name = fields.String(data_key="directLimitName")
    consider_booking_type_name = fields.String(data_key="considerBookingTypeName")
