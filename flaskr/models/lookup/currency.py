from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import is_not_blank, ObjectId


class LookupCurrencySchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    code = fields.String(required=True, data_key="code", validate=is_not_blank)
    name = fields.String(required=True, data_key="name", validate=is_not_blank)
    symbol = fields.String(data_key="symbol")
