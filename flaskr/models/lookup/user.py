from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import is_not_blank, ObjectId


class LookupUserSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = fields.String(data_key="_id")
    user_id = fields.String(required=True, data_key="userId", validate=is_not_blank)
    role = fields.String(required=True, data_key="role", validate=is_not_blank)
    client_id = ObjectId(data_key="clientId")
    confirmation_status = fields.String(data_key="confirmationStatus")
