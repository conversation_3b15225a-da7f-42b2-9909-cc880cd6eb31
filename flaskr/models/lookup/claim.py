from marshmallow import Schema, EXCLUDE, fields
from flaskr.models import is_not_blank, ObjectId


class LookupClaimSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    name = fields.String(required=True, data_key="name", validate=is_not_blank)
    column_name = fields.String(data_key="columnName")
    data_type = fields.String(data_key="dataType")
    preferred = fields.Boolean(data_key="preferred")
    required = fields.Boolean(data_key="required")
