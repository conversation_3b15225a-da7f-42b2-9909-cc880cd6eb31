from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId, is_not_blank


class LookupDefaultChecksSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    name = fields.String(required=True, data_key="name", validate=is_not_blank)
    short_name = fields.String(data_key="shortName")
    description = fields.String(data_key="description")
