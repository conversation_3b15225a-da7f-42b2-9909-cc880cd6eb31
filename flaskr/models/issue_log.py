from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId


class IssueLogSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(data_key="clientId")
    opened = fields.DateTime(data_key="opened", format="%Y-%m-%d", allow_none=True)
    short_description = fields.String(data_key="shortDescription", allow_none=True)
    priority = fields.String(data_key="priority", allow_none=True)
    resolution_notes = fields.String(data_key="resolutionNotes", allow_none=True)
    status = fields.String(data_key="status", allow_none=True)
    date_resolved = fields.DateTime(data_key="dateResolved", format="%Y-%m-%d", allow_none=True)
    deleted = fields.Boolean(data_key="deleted", allow_none=True)
    created_at = fields.DateTime(data_key="createdAt")
    updated_at = fields.DateTime(data_key="updatedAt")
