from marshmallow import <PERSON><PERSON><PERSON><PERSON><PERSON>, Schema, fields
from flaskr.models import ObjectId


class BankingAndClaimSummarySchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    client_id = ObjectId(data_key="clientId")
    client_name = fields.String(data_key="clientName")
    file_id = fields.String(required=True, data_key="fileId")
    file_name = fields.String(required=True, data_key="fileName")
    submitted_date = fields.DateTime(data_key="submittedDate")
    created_at = fields.DateTime(data_key="createdAt")
    updated_at = fields.DateTime(data_key="updatedAt")
    file_size = fields.Integer(data_key="fileSize")
    file_type = fields.String(data_key="fileType")
    uploaded_by = fields.String(data_key="uploadedBy")
    downloaded_by = fields.String(data_key="downloadedBy", allow_none=True)
    downloaded_at = fields.DateTime(data_key="downloadedAt", allow_none=True)
    delete = fields.Boolean(data_key="delete")
