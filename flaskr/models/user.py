from marshmallow import Schema, fields, EXCLUDE
from flaskr.models import ObjectId


class UserSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _id = ObjectId(data_key="_id")
    user_id = fields.String(data_key="userId")
    clients = fields.List(ObjectId, data_key="clients")
    confirmation_status = fields.String(data_key="confirmationStatus", dump_only=True)
    role = fields.String(data_key="role", dump_only=True)
    transaction = fields.Boolean(data_key="transaction", dump_default=False)
    booking = fields.Boolean(data_key="booking", dump_default=False)
    reports = fields.List(fields.String, data_key="reports", dump_default=[])
