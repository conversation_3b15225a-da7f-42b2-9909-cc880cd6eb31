from collections import OrderedDict
from flask import Blueprint, Response, current_app, make_response, request, jsonify, send_file
from jose import jwt
from flaskr.controllers.exceptions import ControllerException
from flaskr.helpers.date_util import change_date_format
from flaskr.models.booking.trust_fund import TrustFundSchema
from flaskr.services.booking_service import booking_service
from flaskr.helpers.auth import booking_access, client_access, ptt_user_required
from flaskr.services.reporting_service import reporting_service

booking_api = Blueprint("booking_api", __name__)


@booking_api.route("/search", methods=["POST"])
@booking_access(request)
def booking_search(access_token):
    if not request.json.get("clientId") or not request.json.get("bookingReference"):
        raise ControllerException(message="Missing required details")
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    client_id = request.json["clientId"]
    booking_reference = request.json["bookingReference"]
    client_access(user_id, client_id)
    booking_search = booking_service.booking_search(client_id, booking_reference)
    return jsonify(booking_search)


@booking_api.route("/payments", methods=["POST"])
@booking_access(request)
def booking_payments(access_token):
    if not request.json.get("clientId") or not request.json.get("bookingReference"):
        raise ControllerException(message="Missing required details")
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    client_id = request.json["clientId"]
    client_access(user_id, client_id)
    booking_referance = request.json["bookingReference"]
    query = request.json.get("query") or ""
    list_payments = booking_service.list_payments(client_id, booking_referance, query)
    return jsonify(list_payments)


@booking_api.route("/payments/export", methods=["POST"])
@booking_access(request)
def export_payments(access_token):
    if not request.json.get("clientId") or not request.json.get("bookingReference"):
        raise ControllerException(message="Missing required details")
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    client_id = request.json["clientId"]
    booking_referance = request.json["bookingReference"]
    client_access(user_id, client_id)
    query = request.json.get("query") or ""
    list_payments = booking_service.list_payments(client_id, booking_referance, query)
    data_list = []
    for data in list_payments["payments"]:
        data["paymentDate"] = change_date_format(data["paymentDate"])
        data_list.append(data)

    header_dict = OrderedDict(
        [
            ("paymentDate", "Payment Date"),
            ("customerType", "Customer Type"),
            ("currencyCode", "Currency"),
            ("amount", "Amount"),
            ("type", "Type"),
        ]
    )
    name = "BookingPayments"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@booking_api.route("/claims", methods=["POST"])
@booking_access(request)
def booking_claims(access_token):
    if not request.json.get("clientId") or not request.json.get("bookingReference"):
        raise ControllerException(message="Missing required details")
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    client_id = request.json["clientId"]
    client_access(user_id, client_id)
    booking_ref = request.json["bookingReference"]
    query = request.json.get("query") or ""
    list_payments = booking_service.booking_claims(client_id, booking_ref, query)
    return jsonify(list_payments)


@booking_api.route("/claims/export", methods=["POST"])
@booking_access(request)
def export_claims(access_token):
    if not request.json.get("clientId") or not request.json.get("bookingReference"):
        raise ControllerException(message="Missing required details")
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    client_id = request.json["clientId"]
    booking_ref = request.json["bookingReference"]
    client_access(user_id, client_id)
    query = request.json.get("query") or ""
    list_payments = booking_service.booking_claims(client_id, booking_ref, query)
    data_list = []
    for data in list_payments["claims"]:
        data["claimDate"] = change_date_format(data["claimDate"])
        data_list.append(data)

    header_dict = OrderedDict(
        [
            ("claimDate", "Claim Date"),
            ("element", "Element"),
            ("currencyCode", "Currency"),
            ("amount", "Amount"),
        ]
    )
    name = "BookingClaims"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@booking_api.route("/claim-checks", methods=["POST"])
@ptt_user_required()
def booking_claim_checks(access_token):
    if not request.json.get("clientId") or not request.json.get("transactionId"):
        raise ControllerException(message="Missing required details")
    client_id = request.json["clientId"]
    transaction_id = request.json["transactionId"]
    data = booking_service.booking_claim_checks(client_id, transaction_id)

    return jsonify({"checks": data})


@booking_api.route("/claim-checks", methods=["PUT"])
@ptt_user_required()
def update_booking_claim_checks(access_token):
    if not request.json.get("clientId") or not request.json.get("transactionId"):
        raise ControllerException(message="Missing required details")
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    checks_list = request.json["checks"]
    booking_service.update_booking_claim_checks(checks_list, user_id)
    return Response(status=200)


@booking_api.route("/anomalies", methods=["POST"])
@ptt_user_required()
def booking_anomalies_listing(access_token):
    if not request.json.get("clientId") or not request.json.get("bookingReference"):
        raise ControllerException(message="Missing required details")
    client_id = request.json["clientId"]
    booking_reference = request.json["bookingReference"]
    query = request.json.get("query") or ""
    list_anomalies = booking_service.list_anomalies(client_id, booking_reference, query)
    return jsonify(list_anomalies)


@booking_api.route("/anomalies/export", methods=["POST"])
@ptt_user_required()
def export_anomalies(access_token):
    if not request.json.get("clientId") or not request.json.get("bookingReference"):
        raise ControllerException(message="Missing required details")
    client_id = request.json["clientId"]
    booking_reference = request.json["bookingReference"]
    query = request.json.get("query") or ""
    list_anomalies = booking_service.list_anomalies(client_id, booking_reference, query)
    header_dict = OrderedDict(
        [
            ("anomalyCategory", "Category"),
            ("anomalyType", "Anomaly Type"),
            ("count", "Count"),
            ("status", "Status"),
        ]
    )
    name = "BookingAnomalies"
    reporting_service.generate_excel_report(header_dict, list_anomalies["anomalies"], name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@booking_api.route("/update", methods=["PUT"])
@ptt_user_required()
def booking_update(access_token):
    data = TrustFundSchema().load(request.json)
    booking_service.booking_update(data)
    return Response(status=200)
