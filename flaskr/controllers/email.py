import boto3
from flask import Blueprint, Response, request, jsonify
from flaskr.helpers.auth import user_required
from flaskr.services.email_service import email_service
from flaskr.controllers.exceptions import ControllerException

email_api = Blueprint("email_api", __name__)


@email_api.route("/verify", methods=["GET"])
@user_required()
def verify_email_identity(access_token):
    email_address = request.json.get("emailAddress")
    ses_client = boto3.client("ses", region_name="eu-west-2")
    response = ses_client.verify_email_identity(EmailAddress=email_address)
    return jsonify(response)


@email_api.route("/template/create", methods=["GET"])
@user_required()
def create_email_template(access_token):
    body = request.json
    response = email_service.create_template(body)
    return response


@email_api.route("/recipients/update/<client_id>", methods=["PUT"])
@user_required()
def update_client_email(access_token, client_id):
    new_to = request.json.get("to")
    new_cc = request.json.get("cc")
    response = email_service.update_client_emails(client_id, new_to, new_cc)
    return response


@email_api.route("/template/weekly", methods=["GET"])
@user_required()
def get_weekly_email_template(access_token):
    client_id = request.args.get("clientId")
    fund_in = request.args.get("fundIn")
    fund_out = request.args.get("fundOut")
    name, designation, phone_number = get_email_signature_details(access_token)
    template = email_service.fetch_weekly_email_template(
        client_id,
        fund_in,
        fund_out,
        name,
        designation,
        phone_number,
    )
    response = Response(template, content_type="text/html")
    return response


@email_api.route("/template/monthly", methods=["GET"])
@user_required()
def get_monthly_email_template(access_token):
    client_id = request.args.get("clientId")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    opening_balance = request.args.get("openingBalance")
    closing_balance = request.args.get("closingBalance")
    banking_amount = request.args.get("bankingAmount")
    claim_amount = request.args.get("claimAmount")
    bank_charges = request.args.get("bankCharges")
    bank_fees = request.args.get("bankFees")
    interest_received = request.args.get("interestReceived")
    bank_charges_reversal = request.args.get("bankChargesReversal")
    name, designation, phone_number = get_email_signature_details(access_token)
    template = email_service.fetch_monthly_email_template(
        client_id,
        from_date,
        to_date,
        opening_balance,
        closing_balance,
        banking_amount,
        claim_amount,
        name,
        designation,
        phone_number,
        bank_charges,
        bank_fees,
        interest_received,
        bank_charges_reversal,
    )
    response = Response(template, content_type="text/html")
    return response


@email_api.route("/send", methods=["POST"])
@user_required()
def send_email(access_token):
    data = request.json
    client_id = data["clientId"]
    email_content = data["emailContent"]
    is_monthly = data["isMonthly"]
    to_date = data["toDate"]

    current_user_email = get_logged_in_user_email(access_token)
    email_content = email_service.compose_email(client_id, email_content, current_user_email, is_monthly, to_date)
    response = email_service.send_email(email_content)
    return response


def get_logged_in_user_email(access_token):
    client = boto3.client("cognito-idp")
    user = client.get_user(AccessToken=access_token)
    for item in user["UserAttributes"]:
        if item["Name"] == "email":
            current_user_email = item["Value"]
    return current_user_email


def get_email_signature_details(access_token):
    client = boto3.client("cognito-idp")
    user = client.get_user(AccessToken=access_token)

    name = ""
    designation = ""
    phone_number = ""

    for item in user["UserAttributes"]:
        if item["Name"] == "name":
            name = item["Value"]
        elif item["Name"] == "custom:designation":
            designation = item["Value"]
        elif item["Name"] == "phone_number":
            phone_number = item["Value"]
    return name, designation, phone_number


@email_api.route("/recipients/monthly", methods=["GET"])
@user_required()
def get_recipients_monthly(access_token):
    client_id = request.args.get("clientId")
    if not all([client_id]):
        raise ControllerException(message="missing required fields")
    response = email_service.get_recipients_monthly(client_id)
    return response


@email_api.route("/recipients/weekly", methods=["GET"])
@user_required()
def get_recipients_weekly(access_token):
    client_id = request.args.get("clientId")
    if not all([client_id]):
        raise ControllerException(message="missing required fields")
    response = email_service.get_recipients_weekly(client_id)
    return response
