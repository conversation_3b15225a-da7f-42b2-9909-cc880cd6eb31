from collections import OrderedDict
from jose import jwt
from flask import Blueprint, current_app, request, jsonify, Response, make_response, send_file
from flaskr.controllers.exceptions import ControllerException
from flaskr.helpers.auth import admin_required, ptt_management_required, ptt_user_required, user_required
from flaskr.helpers.date_util import change_date_format
from flaskr.services.client_service import client_service
from flaskr.services.reporting_service import reporting_service
from config import Config

client_api = Blueprint("client_api", __name__)


@client_api.route("/basic-info", methods=["PUT"])
@admin_required()
def upsert_basic_info(access_token):
    payload = request.json
    data = client_service.upsert_basic_info(payload)
    return {"clientId": data}


@client_api.route("/bank-account", methods=["PUT"])
@admin_required()
def upsert_bank_info(access_token):
    client_bank_info = request.json
    client_service.upsert_bank_info(client_bank_info)
    return Response(status=200)


@client_api.route("/insurance-and-atol", methods=["PUT"])
@admin_required()
def upsert_insurance_atol(access_token):
    data = request.json
    if not data.get("clientId"):
        raise ControllerException(message="Missing required details")
    client_service.upsert_insurance_atol(data)
    return Response(status=200)


@client_api.route("/anomalies", methods=["PUT"])
@admin_required()
def upsert_anomalies(access_token):
    client_anomaly = request.json
    if not client_anomaly.get("anomalies"):
        raise ControllerException(message="anomalies cannot be empty")
    client_service.upsert_anomalies(client_anomaly)
    return Response(status=200)


@client_api.route("/banking-columns", methods=["PUT"])
@admin_required()
def upsert_banking_columns(access_token):
    banking_column_data = request.json
    if banking_column_data.get("columns"):
        client_service.upsert_banking_columns(banking_column_data)
        return Response(status=200)
    else:
        return Response(status=400)


@client_api.route("/claim-columns", methods=["PUT"])
@admin_required()
def upsert_claim_columns(access_token):
    claim_column_data = request.json
    if claim_column_data.get("columns"):
        client_service.upsert_claim_columns(claim_column_data)
        return Response(status=200)
    else:
        return Response(status=400)


@client_api.route("/<client_id>", methods=["GET"])
@user_required()
def get_client(access_token, client_id):
    client_list = client_service.get_client(client_id)
    return jsonify(client_list)


@client_api.route("max-cap-count/<client_id>", methods=["PUT"])
@user_required()
def update_client_max_cap_count(access_token, client_id):
    result = client_service.update_client_max_cap_count(client_id)
    return result


@client_api.route("max-cap-rollover-count/<client_id>", methods=["PUT"])
@user_required()
def update_client_max_cap_rollover_count(access_token, client_id):
    result = client_service.update_client_max_cap_rollover_count(client_id)
    return result


@client_api.route("/search", methods=["POST"])
@user_required()
def list_clients_search_details(access_token):
    claims = jwt.get_unverified_claims(access_token)
    search_query = request.json.get("query")
    clients_list = client_service.clients_details_list(claims["sub"], search_query or "")
    is_empty = False if clients_list else True
    return jsonify({"content": clients_list, "empty": is_empty})


@client_api.route("/search/export", methods=["POST"])
@ptt_user_required()
def export_clients_search_details(access_token):
    claims = jwt.get_unverified_claims(access_token)
    search_query = request.json.get("query")
    clients = client_service.clients_details_list_export(claims["sub"], search_query or "")
    data_list = []
    i = 1

    for data in clients:
        if data["isDisabled"] is True:
            data["status"] = "Disabled"
        elif data["status"] == "published":
            data["status"] = "Active"
        else:
            data["status"] = "Onboarding"
        data["#"] = i
        data["country"] = data["country"][0] if data["country"] else ""
        data["line1"] = data["line1"][0] if data["line1"] else ""
        data["line2"] = data["line2"][0] if data["line2"] else ""
        data["line3"] = data["line3"][0] if data["line3"] else ""
        data["postCode"] = data["postCode"][0] if data["postCode"] else ""
        data["town"] = data["town"][0] if data["town"] else ""
        data["goLiveDate"] = change_date_format(str(data["goLiveDate"])[:10]) if "goLiveDate" in data else ""
        data["balanceAT"] = data["balanceAT"] if data.get("balanceAT") else 0
        data["actualAT"] = data["actualAT"] if data.get("actualAT") else 0
        data["budgetedNOC"] = data["budgetedNOC"] if data.get("budgetedNOC") else 0
        data["actualNOC"] = data["actualNOC"] if data.get("actualNOC") else 0
        data["fromDate"] = change_date_format(data["fromDate"].strftime("%Y-%m-%d")) if data.get("fromDate") else None
        data["toDate"] = change_date_format(data["toDate"].strftime("%Y-%m-%d")) if data.get("toDate") else None
        data["typeOfTrustAccount"] = data["typeOfTrustAccount"][0] if data.get("typeOfTrustAccount") else ""
        i += 1
        data_list.append(data)
    header_dict = OrderedDict(
        [
            ("#", "#"),
            ("cId", "Client ID"),
            ("friendlyName", "Client Name"),
            ("typeOfTrustAccount", "Type of Trust"),
            ("status", "Status"),
            ("goLiveDate", "Go Live Date"),
            ("currency", "Currency"),
            ("fromDate", "From Date"),
            ("toDate", "To Date"),
            ("balanceAT", "Budgeted Annual Turnover (m)"),
            ("actualAT", "Actual Annual Turnover (m)"),
            ("budgetedNOC", "Budgeted Number of Claims"),
            ("actualNOC", "Actual Number of Claims"),
            ("pocName", "POC Name"),
            ("pocEmail", "POC Email address"),
            ("line1", "Address Line 1"),
            ("line2", "Address Line 2"),
            ("line3", "Address Line 3"),
            ("town", "Town/City"),
            ("country", "Country"),
            ("postCode", "Post Code"),
        ]
    )
    name = "ClientSearch"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@client_api.route("/list", methods=["GET"])
@user_required()
def list_clients(access_token):
    claims = jwt.get_unverified_claims(access_token)
    if request.args.get("claimFromTBR") == "true":
        claim_from_tbr = True
    elif request.args.get("claimFromTBR") == "false":
        claim_from_tbr = False
    else:
        claim_from_tbr = None
    if request.args.get("isDisabled") == "true":
        is_disabled = True
    elif request.args.get("isDisabled") == "false":
        is_disabled = False
    else:
        is_disabled = None
    list_clients = client_service.list_clients(claims["sub"], claim_from_tbr, is_disabled)
    return jsonify(list_clients)


@client_api.route("/file", methods=["POST"])
@admin_required()
def client_file_upload(access_token):
    if not request.form.get("clientId") or not request.form.get("type") or not request.files.get("file"):
        raise ControllerException(message="Missing required details")
    client_id = request.form["clientId"]
    file_type = request.form["type"]
    file = request.files["file"]
    file_id = client_service.client_file_upload(client_id, file_type, file)
    return jsonify({"fileId": file_id})


@client_api.route("/file/<file_type>/<file_id>", methods=["GET"])
@user_required()
def client_file_download(access_token, file_type, file_id):
    claims = jwt.get_unverified_claims(access_token)
    response = client_service.client_file_download(access_token, file_type, file_id, claims["sub"])
    return response


@client_api.route("/<client_id>/users", methods=["GET"])
@ptt_user_required()
def client_users(access_token, client_id):
    client_users = client_service.get_client_users(client_id, access_token)
    return jsonify(client_users)


@client_api.route("/<client_id>/users", methods=["PUT"])
@admin_required()
def update_client_users(access_token, client_id):
    data = request.json
    client_service.update_client_users(client_id, data)
    return Response(status=200)


@client_api.route("/<client_id>/escrow_amount", methods=["GET"])
@user_required()
def get_escrow_amount(access_token, client_id):
    file_type = request.args.get("fileType")
    id = request.args.get("id")
    if file_type == "banking":
        data = client_service.get_escrow_amount_banking(id, client_id, file_type)
    else:
        data = client_service.get_escrow_amount_claims(id, client_id, file_type)
    response = {
        "items": data,
    }
    return jsonify(response)


@client_api.route("/<client_id>/escrow_multiplier", methods=["PUT"])
@admin_required()
def update_escrow_multiplier(access_token, client_id):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.json
    if not data.get("date") or not data.get("multiplier"):
        raise ControllerException(message="Missing required details")
    client_service.update_escrow_multiplier(client_id, user_id, data)
    return Response(status=200)


@client_api.route("/<client_id>/escrow_multiplier", methods=["GET"])
@ptt_user_required()
def get_escrow_multiplier(access_token, client_id):
    date = request.args.get("date")
    response = client_service.get_escrow_multiplier(client_id, date)
    return jsonify(response)


@client_api.route("/atol-clients", methods=["GET"])
@user_required()
def list_clients_atol_standard(access_token):
    claims = jwt.get_unverified_claims(access_token)
    list_clients = client_service.list_atol_standard_clients(claims["sub"])
    return jsonify(list_clients)


@client_api.route("/upload/manuals", methods=["POST"])
@ptt_management_required()
def manuals_file_upload(access_token):
    if not request.form.get("clientId") or not request.files.get("file"):
        raise ControllerException(message="Missing required details")
    client_id = request.form["clientId"]
    file = request.files["file"]
    file_id = client_service.manuals_upload(client_id, file)
    return jsonify({"fileId": file_id})

@client_api.route("/download/manuals/<file_id>", methods=["GET"])
@admin_required()
def manuals_download(access_token, file_id):
    claims = jwt.get_unverified_claims(access_token)
    response = client_service.manuals_download(access_token, file_id, claims["sub"])
    return response

@client_api.route("/manuals/<client_id>", methods=["GET"])
@admin_required()
def get_manuals(access_token, client_id):
    manuals_list = client_service.get_manuals(client_id)
    return jsonify(manuals_list)

@client_api.route("/create/manuals-log", methods=["POST"])
@ptt_user_required()
def create_manuals_log(access_token):
    data = request.json
    if not data.get("clientId") or not data.get("userId"):
        raise ControllerException(message="Missing required details")
    response = client_service.create_manuals_log(data)
    return jsonify(response)

@client_api.route("/<client_id>/deductable_amount", methods=["PUT"])
@admin_required()
def update_deductable_amount(access_token, client_id):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    if client_id != Config.BLUESTYLE:
        raise ControllerException(message="Only for BLUESTYLE")
    data = request.json
    if not data.get("date") or not data.get("deductable_amount"):
        raise ControllerException(message="Missing required details")
    client_service.update_deductable_amount(client_id, user_id, data)
    return Response(status=200)


@client_api.route("/<client_id>/deductable_amount", methods=["GET"])
@admin_required()
def get_deductable_amount(access_token, client_id):
    if not request.args.get("date"):
        raise ControllerException(message="Missing required details")
    date = request.args.get("date")
    response = client_service.get_deductable_amount(client_id, date)
    return jsonify(response)
