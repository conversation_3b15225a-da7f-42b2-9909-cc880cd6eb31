from flask import Blueprint, request, abort, make_response, jsonify, Response
from flaskr.helpers.auth import ptt_user_required
from flask import current_app
import requests
from flaskr.controllers.exceptions import ControllerException
from flaskr.services.lookup_service import lookup_service
import json

open_banking_api = Blueprint("open_banking_api", __name__)


@open_banking_api.route("/auth-link", methods=["GET"])
@ptt_user_required()
def auth_link(access_token):
    bank_id = request.args.get("bankId")
    redirect_uri = request.args.get("redirectURI")
    if not bank_id or not redirect_uri:
        raise ControllerException("Missing mandatory fields")
    headers = {"Authorization": f"Bearer {access_token}"}
    params = {"bank_id": bank_id, "redirect_uri": redirect_uri}
    response = requests.get(f'{current_app.config["OPEN_BANKING_URI"]}/api/auth', params=params, headers=headers)
    response.raise_for_status()
    return response.json()


@open_banking_api.route("/exchange-authorization-code", methods=["POST"])
@ptt_user_required()
def exchange_authorization_code(access_token):
    bank_id = request.json.get("bankId")
    authorization_code = request.json.get("authorizationCode")
    redirect_uri = request.json.get("redirectURI")
    if not bank_id or not redirect_uri:
        raise ControllerException("Missing mandatory fields")
    headers = {"Authorization": f"Bearer {access_token}"}
    data = {"bank_id": bank_id, "authorization_code": authorization_code, "redirect_uri": redirect_uri}
    response = requests.post(
        f'{current_app.config["OPEN_BANKING_URI"]}/api/auth/exchange-authorization-code',
        json=data,
        headers=headers,
    )
    response.raise_for_status()
    return Response(status=200)


@open_banking_api.route("/transactions")
@ptt_user_required()
def get_transactions(access_token):
    client_id = request.args.get("clientId")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    bank_name = request.args.get("bankName")
    if not any([client_id, currency, from_date, to_date]):
        raise ControllerException("Missing mandatory fields")
    bank_details = lookup_service.get_bank_accounts(client_id, currency ,bank_name)[0]
    if not bank_details["bank_id"]:
        response = make_response(jsonify({"message": "Bank Id not set for client"}))
        response.status_code = 409
        abort(response)
    headers = {"Authorization": f"Bearer {access_token}"}
    params = {
        "bank_id": bank_details["bank_id"],
        "account_no": bank_details["account_no"],
        "from_date": from_date,
        "to_date": to_date,
    }
    response = requests.get(
        f'{current_app.config["OPEN_BANKING_URI"]}/api/data/transactions', params=params, headers=headers
    )
    if response.status_code != 200:
        error_resonse = make_response(json.loads(response.content))
        error_resonse.status_code = response.status_code
        abort(error_resonse)
    return response.json()
