from flask import Blueprint, abort, current_app, jsonify, request, Response
from flaskr.helpers.auth import admin_required, ptt_user_required, user_required, ptt_sftp_user_required
from flaskr.services.auth_service import auth_service
from jose import jwt

auth_api = Blueprint("auth_api", __name__)


@auth_api.route("/login", methods=["POST"])
def login():
    if not request.json or not request.json.get("username") or not request.json.get("password"):
        abort(400)
    data = request.json
    token = auth_service.login(data["username"], data["password"])
    if token is None:
        return "Login failed", 401
    current_app.logger.info(f"Logged in the user {data['username']}")
    return token


@auth_api.route("/change-password", methods=["POST"])
@user_required()
def change_password(access_token):
    if not request.json or not request.json.get("currentPassword") or not request.json.get("password"):
        abort(400)
    data = request.json
    response = auth_service.change_password(data["currentPassword"], data["password"])
    current_app.logger.info("Password changed for the user")
    return response


@auth_api.route("/forgot-password", methods=["POST"])
def forgot_password():
    if not request.json or not request.json.get("username"):
        abort(400)
    data = request.json
    response = auth_service.forgot_password(data["username"])
    current_app.logger.info(f"Initiated the forgot password for {data['username']}")
    return response


@auth_api.route("/confirm-forgot-password", methods=["POST"])
def confirm_forgot_password():
    if (
        not request.json
        or not request.json.get("username")
        or not request.json.get("password")
        or not request.json.get("confirmation_code")
    ):
        abort(400)
    data = request.json
    response = auth_service.confirm_forgot_password(data["username"], data["password"], data["confirmation_code"])
    current_app.logger.info(f"Reset the password for {data['username']}")
    return response


@auth_api.route("/create-client", methods=["POST"])
@admin_required()
def create_client(access_token):
    data = request.json
    name = request.json.get("name") or " "
    if not (
        all([data.get("userId"), data.get("clientId")])
        or all([data.get("username"), data.get("email"), data.get("clientId")])
    ):
        abort(400)
    response = auth_service.create_client(
        data.get("userId"), data.get("username"), data.get("email"), data.get("clientId"), name
    )
    return response


@auth_api.route("/respond-to-auth-challenge", methods=["POST"])
def respond_to_auth():
    data = request.json
    response = auth_service.respond_to_auth_challenge(data["username"], data["password"], data["session"])
    current_app.logger.info(f"Logged in the user {data['username']}")
    return response


@auth_api.route("/profile-update", methods=["POST"])
@user_required()
def user_profile_update(access_token):
    name = request.form.get("name")
    email = request.form.get("email")
    profile_pic = request.files.get("profilePic")
    auth_service.user_profile_update(access_token, name, email, profile_pic)
    return Response(status=200)


@auth_api.route("/profile-download", methods=["GET"])
@user_required()
def user_profile_download(access_token):
    file = auth_service.user_profile_download(access_token)
    return file


@auth_api.route("/profile-details", methods=["GET"])
@user_required()
def get_profile_details(access_token):
    profile_list = auth_service.get_profile_details(access_token)
    return jsonify(profile_list)


@auth_api.route("/logout", methods=["GET"])
@ptt_sftp_user_required()
def logout(access_token):
    response = auth_service.logout(access_token)
    if response is None:
        return "Logout failed", 401
    current_app.logger.info("Logged out the user")
    return response


@auth_api.route("/refresh", methods=["POST"])
def refresh_token():
    refresh_token = request.json.get("refreshToken")
    response = auth_service.refresh_token(refresh_token)
    return response


@auth_api.route("/set-software-token-mfa", methods=["POST"])
@user_required()
def client_set_software_token_mfa(access_token):
    response = auth_service.client_set_software_token_mfa(access_token)
    return response


@auth_api.route("/verify-software-token-mfa", methods=["POST"])
@user_required()
def verify_software_token_mfa(access_token):
    data = request.json
    if not data.get("userCode"):
        abort(400)
    response = auth_service.verify_software_token_mfa(data.get("userCode"), access_token)
    return response


@auth_api.route("/respond-to-software-token-mfa-challenge", methods=["POST"])
def respond_to_software_token_mfa_challenge():
    data = request.json
    if not (all([data.get("mfaCode"), data.get("userName"), data.get("session")])):
        abort(400)
    response = auth_service.respond_to_software_token_mfa_challenge(
        data.get("mfaCode"),
        data.get("userName"),
        data.get("session"),
    )
    return response


@auth_api.route("/set-sms-mfa", methods=["POST"])
@user_required()
def client_set_sms_mfa(access_token):
    data = request.json
    if not data.get("phoneNumber"):
        abort(400)
    response = auth_service.client_set_sms_mfa(access_token, data.get("phoneNumber"))
    return response


@auth_api.route("/respond-to-sms-mfa-challenge", methods=["POST"])
def respond_to_sms_mfa_challenge():
    data = request.json
    if not (all([data.get("smsMfaCode"), data.get("userName"), data.get("session")])):
        abort(400)
    response = auth_service.respond_to_sms_mfa_challenge(
        data.get("smsMfaCode"),
        data.get("userName"),
        data.get("session"),
    )
    return response


@auth_api.route("/user-details", methods=["GET"])
@user_required()
def get_user_details(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    user_details = auth_service.get_user_details(user_id)
    return jsonify(user_details)


@auth_api.route("/user-update", methods=["PUT"])
@admin_required()
def update_user(access_token):
    data = request.json
    user_id = data.get("userId")
    auth_service.update_user_details(data, user_id)
    return Response(status=200)


@auth_api.route("/list-user", methods=["GET"])
@ptt_user_required()
def list_user_details(access_token):
    list_users = auth_service.list_user_details()
    return jsonify(list_users)
