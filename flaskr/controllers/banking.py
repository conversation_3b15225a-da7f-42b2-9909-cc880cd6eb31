import sys

from flask import Blueprint, current_app, make_response, request, jsonify, Response, send_file
from jose import jwt

from flaskr.controllers.exceptions import ControllerException
from flaskr.helpers.date_util import change_date_format
from flaskr.services.banking_service import banking_service
from flaskr.helpers.auth import ptt_sftp_user_required, ptt_user_required, user_required, client_access
from flaskr.services.auth_service import auth_service
from flaskr.services.reporting_service import reporting_service
from collections import OrderedDict
from config import Config

banking_api = Blueprint("banking_api", __name__)


@banking_api.route("/upload/presigned-url", methods=["GET"])
@ptt_sftp_user_required()
def banking_create_presigned_url(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.args
    if not request.args.get("clientId") or not request.args.get("fileName"):
        raise ControllerException(message="Missing required details")
    if data.get("clientId"):
        client_access(user_id, data.get("clientId"))
    client_id = request.args["clientId"]
    file_name = request.args["fileName"]
    response = banking_service.banking_create_presigned_url(client_id, file_name, user_id)
    return response


@banking_api.route("/upload", methods=["POST"])
@ptt_sftp_user_required()
def banking_create(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.args
    if data.get("clientId"):
        client_access(user_id, data.get("client"))
    if not all(key in request.json for key in ["clientId", "fileName", "fileId"]):
        raise ControllerException(message="Missing required details")
    client_id = request.json["clientId"]
    file_name = request.json["fileName"]
    file_id = request.json["fileId"]
    sftp = request.json["sftp"] if request.json.get("sftp") else False
    sftp_key = request.json["sftpKey"] if request.json.get("sftpKey") else ""
    banking_service.banking_create(client_id, file_name, file_id, sftp, user_id, sftp_key)
    return Response(status=201)


@banking_api.route("/search", methods=["POST"])
@user_required()
def banking_search(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.args
    if data.get("client"):
        client_access(user_id, data.get("client"))
    body = request.json
    response = banking_service.banking_search(user_id, body)
    return jsonify(response)


@banking_api.route("/search/export", methods=["POST"])
@ptt_user_required()
def export_banking_search(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    body = request.json
    body["page"] = 1
    body["size"] = int(sys.maxsize)
    response = banking_service.banking_search(user_id, body)
    user_data = auth_service.ptt_users_list()
    data_list = []
    for data in response["content"]:
        data["fileDate"] = change_date_format(data["fileDate"])

        for i in data["items"]:
            data["currency"] = i["currency"]
            data["count"] = i["count"]
            data["amount"] = i["amount"]
            data_list.append(data.copy())

    for item in data_list:
        user = next(filter(lambda x: x["userId"] == item["assignedTo"], user_data), {"name": ""})
        item["assignedTo"] = user["name"]

    header_dict = OrderedDict(
        [
            ("clientId", "Client Id"),
            ("clientName", "Client Name"),
            ("friendlyName", "Friendly Name"),
            ("fileDate", "File Date"),
            ("assignedTo", "Assigned To"),
            ("currency", "Currency"),
            ("count", "Count"),
            ("amount", "Amount"),
            ("status", "Status"),
            ("notes", "Notes"),
        ]
    )
    name = "BankingSearch"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@banking_api.route("/payments/<banking_id>", methods=["GET"])
@user_required()
def get_payments(access_token, banking_id):
    query = request.args.get("query") or ""
    page = request.args.get("page")
    size = request.args.get("size")
    sort_key = request.args.get("sortKey") or "_id"
    sort_order = int(request.args.get("sortOrder") or 1)
    converted_sort_key_list = {
        "_id": "_id",
        "bookingRef": "booking_ref",
        "bookingDate": "booking_date",
        "returnDate": "return_date",
        "departureDate": "departure_date",
        "paymentDate": "payment_date",
        "customerType": "customer_type",
        "currencyCode": "currency_code",
        "amount": "amount",
        "status": "status",
    }
    if sort_key not in converted_sort_key_list.keys():
        raise ControllerException(message="invalid sort key")
    if sort_order not in [1, -1]:
        raise ControllerException(message="invalid sort order")
    converted_sort_key = converted_sort_key_list[sort_key]
    banking_list = banking_service.get_payments(banking_id, query, page, size, converted_sort_key, sort_order)
    return jsonify(banking_list)


@banking_api.route("/payments/<banking_id>/export", methods=["GET"])
@ptt_user_required()
def export_payments(access_token, banking_id):
    query = request.args.get("query") or ""
    page = 1
    size = sys.maxsize
    sort_key = request.args.get("sortKey") or "_id"
    sort_order = int(request.args.get("sortOrder") or 1)
    converted_sort_key_list = {
        "_id": "_id",
        "bookingRef": "booking_ref",
        "bookingDate": "booking_date",
        "returnDate": "return_date",
        "departureDate": "departure_date",
        "paymentDate": "payment_date",
        "customerType": "customer_type",
        "currencyCode": "currency_code",
        "amount": "amount",
    }
    if sort_key not in converted_sort_key_list.keys():
        raise ControllerException(message="invalid sort key")
    if sort_order not in [1, -1]:
        raise ControllerException(message="invalid sort order")

    converted_sort_key = converted_sort_key_list[sort_key]
    banking_payments = banking_service.get_payments(banking_id, query, page, size, converted_sort_key, sort_order)
    data_list = []
    for data in banking_payments["content"]:
        data["bookingDate"] = change_date_format(data["bookingDate"])
        data["returnDate"] = change_date_format(data["returnDate"])
        data["departureDate"] = change_date_format(data["departureDate"])
        data_list.append(data)
    header_dict = OrderedDict(
        [
            ("bookingRef", "Booking Ref"),
            ("bookingDate", "Booking Date"),
            ("returnDate", "Return Date"),
            ("departureDate", "Departure Date"),
            ("paymentDate", "Payment Date"),
            ("customerType", "Type of Booking"),
            ("currencyCode", "Currency"),
            ("amount", "Amount"),
        ]
    )
    name = "Payments"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@banking_api.route("/details/<banking_id>", methods=["GET"])
@user_required()
def banking_details(access_token, banking_id):
    details = banking_service.get_banking_details(banking_id)
    return jsonify(details), 200


@banking_api.route("/anomalies/<banking_id>", methods=["GET"])
@ptt_user_required()
def banking_anomalies(access_token, banking_id):
    query = request.args.get("query") or ""
    page = request.args.get("page")
    size = request.args.get("size")
    sort_key = request.args.get("sortKey") or "createdAt"
    sort_order = int(request.args.get("sortOrder") or -1)
    converted_sort_key_list = {
        "createdAt": "created_at",
        "bookingRef": "_id.booking_ref",
        "bookingDate": "trust.booking_date",
        "dateOfTravel": "trust.departure_date",
        "dateOfReturn": "trust.return_date",
        "count": "count",
        "balanceInTrust": "trust.balance",
        "anomalyType": "_id.anomaly_type",
        "status": "status",
        "leadPassenger": "trust.lead_pax",
    }
    if sort_key not in converted_sort_key_list.keys():
        raise ControllerException(message="invalid sort key")
    if sort_order not in [1, -1]:
        raise ControllerException(message="invalid sort order")
    converted_sort_key = converted_sort_key_list[sort_key]
    banking_anomaly = banking_service.get_banking_anomalies(
        banking_id, query, page, size, converted_sort_key, sort_order
    )
    return jsonify(banking_anomaly)


@banking_api.route("/anomalies/<banking_id>/export", methods=["GET"])
@ptt_user_required()
def export_banking_anomalies(access_token, banking_id):
    query = request.args.get("query") or ""
    page = 1
    size = sys.maxsize
    sort_key = request.args.get("sortKey") or "createdAt"
    sort_order = int(request.args.get("sortOrder") or -1)
    converted_sort_key_list = {
        "createdAt": "created_at",
        "bookingRef": "_id.booking_ref",
        "bookingDate": "trust.booking_date",
        "dateOfTravel": "trust.departure_date",
        "dateOfReturn": "trust.return_date",
        "count": "count",
        "balanceInTrust": "trust.balance",
        "anomalyType": "_id.anomaly_type",
        "status": "status",
        "leadPassenger": "trust.lead_pax",
    }
    if sort_key not in converted_sort_key_list.keys():
        raise ControllerException(message="invalid sort key")
    if sort_order not in [1, -1]:
        raise ControllerException(message="invalid sort order")
    converted_sort_key = converted_sort_key_list[sort_key]
    banking_anomaly = banking_service.get_banking_anomalies(
        banking_id, query, page, size, converted_sort_key, sort_order
    )
    data_list = []
    for data in banking_anomaly["content"]:
        data["bookingDate"] = change_date_format(data["bookingDate"])
        data["dateOfReturn"] = change_date_format(data["dateOfReturn"])
        data["dateOfTravel"] = change_date_format(data["dateOfTravel"])
        data["currency_code"] = ", ".join(data["currency_code"])
        data_list.append(data)

    header_dict = OrderedDict(
        [
            ("friendlyName", "Friendly Name"),
            ("bookingRef", "Booking Ref."),
            ("bookingDate", "Booking Date"),
            ("dateOfTravel", "Date of Travel"),
            ("leadPassenger", "Lead Passenger"),
            ("dateOfReturn", "Date of Return"),
            ("currency_code", "Currency"),
            ("balanceInTrust", "Balance in Trust"),
            ("modifiedBy", "Modified By"),
            ("anomalyType", "Anomaly Type"),
            ("status", "Status"),
            ("count", "Count"),
        ]
    )
    name = "BankingAnomalies"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@banking_api.route("/update", methods=["PUT"])
@ptt_user_required()
def update_banking_metadata(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    banking_id = request.json.get("bankingId")
    notes = request.json.get("notes")
    status = request.json.get("status")
    assigned_to = request.json.get("assignedTo")
    if banking_id and (notes or status or assigned_to):
        banking_service.update_banking_metadata(request.json, user_id)
    else:
        raise ControllerException(message="Missing required details")
    return Response(status=200)


@banking_api.route("/transaction/<transaction_id>", methods=["PUT"])
@ptt_user_required()
def update_banking_transaction(access_token, transaction_id):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    if "bookingRef" in request.json.keys():
        raise ControllerException(message="Booking Ref is not editable")
    banking_service.update_banking_transaction(transaction_id, request.json, user_id)
    return Response(status=200)


@banking_api.route("/get-transaction/<transaction_id>", methods=["GET"])
@user_required()
def banking_get_transaction(access_token, transaction_id):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    banking_get_transaction_list = banking_service.banking_get_transaction(transaction_id, user_id)
    return jsonify(banking_get_transaction_list)


@banking_api.route("/get-transaction", methods=["GET"])
@user_required()
def get_uploaded_banking_transaction(access_token):
    supplier_name = request.args.get("supplierName")
    client_id = request.args.get("clientId")
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    if not supplier_name or not client_id:
        raise ControllerException(message="Missing required details")
    client_access(user_id, client_id)
    banking_get_transaction = banking_service.get_uploaded_banking_transaction(supplier_name, client_id)
    return jsonify(banking_get_transaction)

@banking_api.route("/get-nontrust-bookings", methods=["GET"])
@user_required()
def get_nontrust_bookings(access_token):
    client_id = request.args.get("client_id")
    non_trust_flag=True
    non_trust_list=banking_service.get_trust_nontrust_bookings(client_id,non_trust_flag)
    return jsonify(non_trust_list)

@banking_api.route("/get-trust-bookings", methods=["GET"])
@user_required()
def get_trust_bookings(access_token):
    client_id = request.args.get("client_id")
    non_trust_flag = False
    trust_list=banking_service.get_trust_nontrust_bookings(client_id,non_trust_flag)
    return jsonify(trust_list)







