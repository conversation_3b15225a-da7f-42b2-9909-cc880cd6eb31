import sys
from jose import jwt
from flask import Blueprint, current_app, make_response, request, jsonify, Response, send_file
from flaskr.controllers.exceptions import ControllerException
from flaskr.helpers.date_util import change_date_format
from flaskr.services.anomalies_service import anomaly_service
from flaskr.services.reporting_service import reporting_service
from flaskr.helpers.auth import ptt_user_required
from collections import OrderedDict

anomalies_api = Blueprint("anomalies_api", __name__)


@anomalies_api.route("/banking/export", methods=["POST"])
@ptt_user_required()
def banking_anomaly_search_export(access_token):
    body = request.json
    body["page"] = 1
    body["size"] = sys.maxsize
    response = anomaly_service.banking_anomaly_search(body)
    data_list = []
    for data in response["content"]:
        data["fileDate"] = change_date_format(data["fileDate"])
        data_list.append(data)
    header_dict = OrderedDict(
        [
            ("clientId", "Client Id"),
            ("clientName", "Client Name"),
            ("friendlyName", "Friendly Name"),
            ("fileDate", "File Date"),
            ("status", "Status"),
            ("count", "Count"),
            ("fileName", "File Name"),
        ]
    )
    name = "BankingAnomalies"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@anomalies_api.route("/banking", methods=["POST"])
@ptt_user_required()
def banking_anomaly_search(access_token):
    body = request.json
    response = anomaly_service.banking_anomaly_search(body)
    return jsonify(response)


@anomalies_api.route("/<file_category>/update-status", methods=["PUT"])
@ptt_user_required()
def anomaly_update_status(access_token, file_category):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    anomaly_id = request.json.get("anomalyIds")
    status = request.json.get("status")
    if anomaly_id and status:
        anomaly_service.anomaly_update_status(file_category, request.json, user_id)
    else:
        raise ControllerException(message="Missing required details")
    return Response(status=200)


@anomalies_api.route("/<file_category>/<batch_id>/batch", methods=["POST"])
@ptt_user_required()
def batch_anomalies(access_token, batch_id, file_category):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    client_id = request.json.get("client_id")
    anomaly_type = request.json.get("anomalyType")
    resolved = request.json.get("resolved")
    data = {"anomaly_type": anomaly_type, "resolved": resolved, "client_id": client_id}

    if anomaly_type and resolved:
        anomaly_service.update_batch(batch_id, data, user_id, file_category)
    else:
        raise ControllerException(message="Missing required details")
    return Response(status=200)


@anomalies_api.route("/claims/export", methods=["POST"])
@ptt_user_required()
def claims_anomaly_search_export(access_token):
    body = request.json
    body["page"] = 1
    body["size"] = sys.maxsize
    response = anomaly_service.claims_anomaly_search(body)
    data_list = []
    for data in response["content"]:
        data["fileDate"] = change_date_format(data["fileDate"])
        data_list.append(data)
    header_dict = OrderedDict(
        [
            ("clientId", "Client Id"),
            ("clientName", "Client Name"),
            ("friendlyName", "Friendly Name"),
            ("fileDate", "File Date"),
            ("status", "Status"),
            ("count", "Count"),
            ("fileName", "File Name"),
        ]
    )
    name = "ClaimAnomalies"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@anomalies_api.route("/claims", methods=["POST"])
@ptt_user_required()
def claims_anomaly_search(access_token):
    body = request.json
    response = anomaly_service.claims_anomaly_search(body)
    return jsonify(response)
