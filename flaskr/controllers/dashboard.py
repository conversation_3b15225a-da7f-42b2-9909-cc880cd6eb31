from datetime import datetime
from dateutil.relativedelta import relativedelta
from flask import Blueprint, request, jsonify, Response
from jose import jwt
from flask import current_app, make_response, send_file
from flaskr.controllers.exceptions import ControllerException
from flaskr.services.dashboard_service import dashboard_service
from flaskr.helpers.auth import (
    ptt_user_required,
    user_required,
    client_access,
    admin_or_ptt_erv_dashboard_required,
    user_or_ptt_czech_client_required,
    admin_or_ptt_erv_dashboard_or_ptt_czech_client_required,
)

dashboard_api = Blueprint("dashboard_api", __name__)


@dashboard_api.route("/clients", methods=["GET"])
@ptt_user_required()
def ytd_clients(access_token):
    current_year = request.args.get("year")
    if current_year:
        previous_year = datetime.strptime(current_year, "%Y")
        previous_year = (previous_year - relativedelta(years=1)).strftime("%Y")
        response = dashboard_service.ytd_clients(current_year, previous_year)
    else:
        raise ControllerException(message="Missing required details")

    return jsonify(response)


@dashboard_api.route("/clients-category", methods=["GET"])
@ptt_user_required()
def ytd_clients_category(access_token):
    output_list = dashboard_service.ytd_clients_category()
    return jsonify(output_list)


@dashboard_api.route("/details", methods=["GET"])
@ptt_user_required()
def dashboard_details(access_token):
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    currency = request.args.get("currency")
    if currency:
        response = dashboard_service.dashboard_details(currency, from_date, to_date)
    else:
        raise ControllerException(message="Missing required details")
    return jsonify(response)


@dashboard_api.route("/bookings", methods=["GET"])
@user_required()
def ytd_bookings(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    client_id = request.args.get("clientId")
    currency_code = request.args.get("currency")
    current_year = request.args.get("year")
    if current_year and currency_code and client_id:
        previous_year = datetime.strptime(current_year, "%Y")
        previous_year = (previous_year - relativedelta(years=1)).strftime("%Y")
        response = dashboard_service.ytd_bookings(current_year, previous_year, client_id, currency_code, user_id)
    else:
        raise ControllerException(message="Missing required details")

    return jsonify(response)


@dashboard_api.route("/payments", methods=["GET"])
@user_required()
def ytd_payments(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    current_year = request.args.get("year")
    client_id = request.args.get("clientId") or None
    currency = request.args.get("currency") or None

    if current_year and client_id and currency:
        current_year_datetime = datetime.strptime(current_year, "%Y")
        previous_year = (current_year_datetime - relativedelta(years=1)).strftime("%Y")
        response = dashboard_service.ytd_payments(current_year, previous_year, client_id, currency, user_id)
    else:
        raise ControllerException(message="Missing required details")

    return jsonify(response)


@dashboard_api.route("/claims", methods=["GET"])
@user_required()
def ytd_claims(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    current_year = request.args.get("year")
    client_id = request.args.get("clientId") or None
    currency = request.args.get("currency") or None

    if current_year and client_id and currency:
        current_year_datetime = datetime.strptime(current_year, "%Y")
        previous_year = (current_year_datetime - relativedelta(years=1)).strftime("%Y")
        response = dashboard_service.ytd_claims(current_year, previous_year, client_id, currency, user_id)
    else:
        raise ControllerException(message="Missing required details")
    return jsonify(response)


@dashboard_api.route("/risk-exposure/refresh", methods=["GET"])
@user_required()
def risk_exposure_refresh(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    dashboard_service.risk_exposure_refresh(user_id)
    return Response(status=200)


@dashboard_api.route("/risk-exposure", methods=["GET"])
@user_required()
def risk_exposure(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.args
    if data.get("client"):
        client_access(user_id, data.get("client"))
    resolution = int(data["resolution"]) if data.get("resolution") else 10
    if not data.get("currency"):
        raise ControllerException(message="Missing required details")
    response = dashboard_service.risk_exposure(user_id, data, resolution)
    return jsonify(response)


@dashboard_api.route("/risk-exposure/status", methods=["GET"])
@user_required()
def risk_exposure_status(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    response = dashboard_service.risk_exposure_status(user_id)
    return jsonify(response)


@dashboard_api.route("/movement-of-funds", methods=["GET"])
@user_required()
def ytd_movement_of_funds(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.args
    if data.get("clientId"):
        client_access(user_id, data.get("clientId"))
    if not data.get("currency"):
        raise ControllerException(message="missing required fields")
    response = dashboard_service.movement_of_funds(data, user_id)
    return jsonify(response)


@dashboard_api.route("/exposure-status/refresh", methods=["GET"])
@user_required()
def exposure_status_trigger(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    dashboard_service.exposure_status_trigger(user_id)
    return Response(status=200)


@dashboard_api.route("/exposure-status", methods=["GET"])
@user_required()
def exposure_status(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.args
    if data.get("client"):
        client_access(user_id, data.get("client"))
    if not data.get("currency"):
        raise ControllerException(message="Missing required details")
    response = dashboard_service.exposure_status(user_id, data)
    return jsonify(response)


@dashboard_api.route("/exposure-status/status", methods=["GET"])
@user_required()
def exposure_status_status(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    response = dashboard_service.exposure_status_status(user_id)
    return jsonify(response)


@dashboard_api.route("/uploaded-transactions", methods=["GET"])
@user_required()
def uploaded_transactions(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.args
    if data.get("client"):
        client_access(user_id, data.get("client"))
    response = dashboard_service.uploaded_transactions(user_id, data)
    return jsonify(response)


@dashboard_api.route("/expiry", methods=["GET"])
@user_required()
def expiry_details(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.args
    if data.get("client"):
        client_access(user_id, data.get("client"))
    if not all([data.get("fromDate"), data.get("toDate"), data.get("currency")]):
        raise ControllerException(message="Missing required details")
    response = dashboard_service.expiry_details(user_id, data)
    return jsonify(response)


@dashboard_api.route("/save_closing_balance", methods=["POST"])
@user_required()
def save_closing_balance(access_token):
    body = request.json
    response = dashboard_service.save_closing_balance(body)
    return jsonify(response)


@dashboard_api.route("/get_closing_balance", methods=["GET"])
@user_required()
def get_closing_balance(access_token):
    client_id = request.args.get("client_id")
    from_date = request.args.get("from_date")
    to_date = request.args.get("to_date")

    response = dashboard_service.get_closing_balance(client_id, from_date, to_date)
    return jsonify(response)


@dashboard_api.route("/erv-dashboard/movement-of-funds", methods=["GET"])
@user_or_ptt_czech_client_required()
def ytd_movement_of_funds_erv(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.args
    if data.get("clientId"):
        client_access(user_id, data.get("clientId"))
    if not data.get("currency"):
        raise ControllerException(message="missing required fields")
    response = dashboard_service.movement_of_funds(data, user_id)
    return jsonify(response)


@dashboard_api.route("/erv-dashboard/movement-of-funds/export", methods=["POST"])
@user_or_ptt_czech_client_required()
def export_ytd_movement_of_funds_erv(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    client_id = request.form.get("clientId")
    currency = request.form.get("currency")
    from_date = request.form.get("fromDate")
    to_date = request.form.get("toDate")
    image_data = request.files.get("image_data")
    file_id = dashboard_service.image_upload_snapshot(image_data)
    if not request.form["currency"]:
        raise ControllerException(message="missing required fields")

    file_name = f"Movement of Funds {to_date}.xlsx"
    image_name = image_data.filename
    image_path = dashboard_service.download_snapshot_image(file_id, image_name)
    dashboard_service.movement_of_funds_excel_file(
        client_id, currency, from_date, to_date, user_id, file_name, image_path
    )
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{file_name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@dashboard_api.route("/erv-dashboard/non-trust-bookings", methods=["GET"])
@user_or_ptt_czech_client_required()
def non_trust_booking_erv(access_token):
    data = request.args
    if not data.get("clientId") or not data.get("currency"):
        raise ControllerException(message="missing required fields")
    response = dashboard_service.non_trust_bookings_erv(data)
    return jsonify(response)


@dashboard_api.route("/erv-dashboard/non-trust-bookings/export", methods=["POST"])
@user_or_ptt_czech_client_required()
def export_non_trust_booking_erv(access_token):
    client_id = request.form.get("clientId")
    currency = request.form.get("currency")
    from_date = request.form.get("fromDate")
    to_date = request.form.get("toDate")
    image_data = request.files.get("image_data")
    if not currency:
        raise ControllerException(message="missing required fields")
    file_id = dashboard_service.image_upload_snapshot(image_data)
    file_name = f"Non Trust Bookings {to_date}.xlsx"
    image_name = image_data.filename
    image_path = dashboard_service.download_snapshot_image(file_id, image_name)
    dashboard_service.non_trust_bookings_excel_file(client_id, currency, from_date, to_date, file_name, image_path)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{file_name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@dashboard_api.route("/erv-dashboard/risk-exposure-breakdown", methods=["GET"])
@admin_or_ptt_erv_dashboard_or_ptt_czech_client_required()
def risk_exposure_breakdown(access_token):
    data = request.args
    if not data.get("clientId") or not data.get("currency"):
        raise ControllerException(message="missing required fields")
    response = dashboard_service.get_banking_summary(data)
    return jsonify(response)


@dashboard_api.route("/erv-dashboard/risk-exposure-breakdown/export", methods=["POST"])
@admin_or_ptt_erv_dashboard_or_ptt_czech_client_required()
def export_risk_exposure_breakdown(access_token):
    client_id = request.form.get("clientId")
    currency = request.form.get("currency")
    from_date = request.form.get("fromDate")
    to_date = request.form.get("toDate")
    image_data = request.files.get("image_data")
    if not currency or not client_id:
        raise ControllerException(message="missing required fields")

    file_id = dashboard_service.image_upload_snapshot(image_data)
    file_name = f"Risk Exposure Breakdown {to_date}.xlsx"
    image_name = image_data.filename
    image_path = dashboard_service.download_snapshot_image(file_id, image_name)
    dashboard_service.exposure_breakdown_excel_file(client_id, currency, from_date, to_date, file_name, image_path)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{file_name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@dashboard_api.route("/erv-dashboard/risk-exposure-graph", methods=["GET"])
@admin_or_ptt_erv_dashboard_or_ptt_czech_client_required()
def risk_exposure_graph(access_token):
    data = request.args
    if not data.get("clientId") or not data.get("currency"):
        raise ControllerException(message="missing required fields")
    response = dashboard_service.risk_exposure_graph(data)
    return jsonify(response)


@dashboard_api.route("/erv-dashboard/risk-exposure-graph/export", methods=["POST"])
@admin_or_ptt_erv_dashboard_or_ptt_czech_client_required()
def export_risk_exposure_graph(access_token):
    client_id = request.form.get("clientId")
    currency = request.form.get("currency")
    from_date = request.form.get("fromDate")
    to_date = request.form.get("toDate")
    image_data = request.files.get("image_data")

    file_id = dashboard_service.image_upload_snapshot(image_data)
    file_name = f"Delayed Trust Funds {to_date}.xlsx"
    image_name = image_data.filename
    image_path = dashboard_service.download_snapshot_image(file_id, image_name)
    dashboard_service.delayed_trust_funds_excel_file(currency, from_date, to_date, client_id, file_name, image_path)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{file_name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response
