from collections import OrderedDict
from datetime import datetime
from flask import Blueprint, current_app, make_response, request, send_file
from flaskr.helpers.auth import user_required
from flaskr.helpers.currency_util import amount_decimal_conversion
from flaskr.helpers.date_util import convert_date_utc_format
from flaskr.services.client_service import client_service
from flaskr.services.reporting_service import reporting_service
from flaskr.services.stripe_service import stripe_service

stripe_api = Blueprint("stripe_api", __name__)


@stripe_api.route("/transactions", methods=["GET"])
@user_required()
def get_stripe_data(access_token):
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    result = stripe_service.get_stripe_transactions(from_date, to_date)
    return result


@stripe_api.route("/statement/export", methods=["GET"])
@user_required()
def export_stripe_statement_data(access_token):
    client = request.args.get("client")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    response = stripe_service.get_stripe_transactions(from_date, to_date)
    client_name = client_service.get_client_friendly_name(client)
    to_date_obj = datetime.strptime(to_date, "%Y-%m-%d")
    month_year = to_date_obj.strftime("%B %Y")
    data_list = []
    for data in response["data"]:
        data["amount"] = amount_decimal_conversion(data["amount"])
        data["amountRefunded"] = amount_decimal_conversion(data["amount_refunded"])
        data["created"] = convert_date_utc_format(data["created"])
        data["convertedAmount"] = (
            amount_decimal_conversion(data["balance_transaction"]["amount"])
            if data["balance_transaction"] is not None
            else 0
        )
        data["convertedCurrency"] = (
            data["balance_transaction"]["currency"] if data["balance_transaction"] is not None else 0
        )
        data["fee"] = (
            amount_decimal_conversion(data["balance_transaction"]["fee"])
            if data["balance_transaction"] is not None
            else 0
        )
        data["statementDescriptor"] = data["calculated_statement_descriptor"]
        data["status"] = "Paid" if data["paid"] is True else None
        data["sellerMessage"] = data["outcome"]["seller_message"] if data["outcome"] is not None else None
        data["cardId"] = data["payment_method"]
        data["customerID"] = data["customer"]["id"] if data["customer"] is not None else None
        data["customerDescription"] = data["customer"]["description"] if data["customer"] is not None else None
        data["customerEmail"] = data["customer"]["email"] if data["customer"] is not None else None
        data["invoiceId"] = data["invoice"]["id"] if data["invoice"] is not None else None
        data["tid"] = data["metadata"]["tid"] if data["metadata"] is not None else None
        data_list.append(data)
    header_dict = OrderedDict(
        [
            ("id", "Id"),
            ("created", "Created Date(UTC)"),
            ("amount", "Amount"),
            ("amountRefunded", "Amount Refunded"),
            ("currency", "Currency"),
            ("captured", "Captured"),
            ("convertedAmount", "Converted Amount"),
            # ("convertedAmountRefunded", "Converted Amount Refunded"),
            ("convertedCurrency", "Converted Currency"),
            ("description", "Description"),
            ("fee", "Fee"),
            # ("refundedDate", "Refunded Date (UTC)"),
            ("statementDescriptor", "Statement Descriptor"),
            ("status", "Status"),
            ("sellerMessage", "Seller Message"),
            # ("bankingFileTotal", "Taxes On Fee"),
            ("cardId", "Card ID"),
            ("customerID", "Customer ID"),
            ("customerDescription", "Customer Description"),
            ("customerEmail", "Customer Email"),
            ("invoiceID", "Invoice ID"),
            # ("transfer", "Transfer"),
            ("tid", "tid (metadata)"),
        ]
    )
    name = f"{client_name}- Stripe Statement {month_year}.xlsx"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


def generate_report_name(client_name):
    """Generate the Excel report name."""
    return f"{client_name} Reconciliation Statement.xlsx"


def create_excel_response(report_name):
    """Create a response to send an Excel file."""
    file_path = f"{current_app.config['TEMP_DIR']}/{report_name}"
    response = make_response(send_file(file_path))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


def calculate_total_funds_collected_and_stripe_fees(transactions):
    """Calculate the total funds collected and stripe fees from Stripe transactions."""
    total_funds_collected = 0
    stripe_fees = 0

    for transaction in transactions["data"]:
        if transaction.get("paid"):
            total_funds_collected += amount_decimal_conversion(transaction["amount"])
            stripe_fees += calculate_fees(transaction.get("balance_transaction", {}))

    return round(total_funds_collected, 2), round(stripe_fees, 2)


def calculate_fees(balance_transaction):
    """Calculate the total fees from a balance transaction."""
    fees = 0

    fee_details = balance_transaction.get("fee_details", [])
    for fee in fee_details:
        fee_amount = amount_decimal_conversion(fee.get("amount"))
        if fee_amount:
            fees += fee_amount

    return fees


@stripe_api.route("/reconciliation/export", methods=["GET"])
@user_required()
def stripe_reconciliation_export(access_token):
    client = request.args.get("client")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")

    client_name = client_service.get_client_friendly_name(client)
    transactions = stripe_service.get_stripe_transactions(from_date, to_date)

    total_funds_collected, stripe_fees = calculate_total_funds_collected_and_stripe_fees(transactions)
    report_name = generate_report_name(client_name)

    stripe_service.generate_stripe_reconciliation_report(
        total_funds_collected, stripe_fees, client, report_name, to_date
    )

    return create_excel_response(report_name)


@stripe_api.route("/reconciliation/summary", methods=["GET"])
@user_required()
def stripe_reconciliation_summary(access_token):
    client = request.args.get("client")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")

    client_name = client_service.get_client_friendly_name(client)
    transactions = stripe_service.get_stripe_transactions(from_date, to_date)

    total_amount, total_fees = calculate_total_funds_collected_and_stripe_fees(transactions)

    summary = stripe_service.stripe_reconciliation_summary(total_amount, total_fees, client_name)
    return summary
