from collections import OrderedDict
import sys
from flask import Blueprint, current_app, Response, jsonify, make_response, request, send_file
from flaskr import ControllerException
from flaskr.helpers.auth import ptt_user_required
from flaskr.helpers.date_util import change_date_format
from flaskr.services.issue_log_service import issue_log_service
from flaskr.services.reporting_service import reporting_service

issue_log_api = Blueprint("issue_log_api", __name__)


@issue_log_api.route("", methods=["POST"])
@ptt_user_required()
def create_issue_log(access_token):
    data = request.json
    if not data.get("clientId"):
        raise ControllerException(message="Missing ClientId")
    response = issue_log_service.create_issue_log(data)
    return jsonify(response)


@issue_log_api.route("", methods=["GET"])
@ptt_user_required()
def list_issue_log(access_token):
    from_date = request.args.get("from_date")
    to_date = request.args.get("to_date")
    query = request.args.get("query")
    client = request.args.get("client")
    page = request.args.get("page")
    size = request.args.get("size")
    sort_key = request.args.get("sortKey") or "createdAt"
    sort_order = int(request.args["sortOrder"]) if request.args.get("sortOrder") and request.args.get("sortOrder").strip() else -1
    status = request.args.get("status")
    priority = request.args.get("priority")
    converted_sort_key_list = {
        "createdAt": "created_at",
        "opened": "opened",
        "clientName": "client.full_name",
        "dateResolved": "date_resolved",
        "priority": "priority",
        "status": "status",
        "clientId": "client.c_id",
    }
    if sort_key not in converted_sort_key_list.keys():
        raise ControllerException(message="invalid sort key")
    if sort_order not in [1, -1]:
        raise ControllerException(message="invalid sort order")
    sort_key = converted_sort_key_list[sort_key]
    response = issue_log_service.list_issue_log(
        from_date, to_date, query, client, status, priority, sort_key, sort_order, page, size
    )
    return jsonify(response)


@issue_log_api.route("/<issue_id>", methods=["PUT"])
@ptt_user_required()
def update_issue_log(access_token, issue_id):
    data = request.json
    issue_log_service.update_issue_log(data, issue_id)
    return Response(status=200)


@issue_log_api.route("/export", methods=["GET"])
@ptt_user_required()
def export_list_issue_log(access_token):
    query = request.args.get("query")
    client = request.args.get("client")
    page = 1
    size = sys.maxsize
    sort_key = request.args.get("sortKey") or "createdAt"
    sort_order = int(request.args["sortOrder"]) if request.args.get("sortOrder") and request.args.get("sortOrder").strip() else -1
    status = request.args.get("status")
    priority = request.args.get("priority")
    converted_sort_key_list = {
        "createdAt": "created_at",
        "opened": "opened",
        "clientName": "client.full_name",
        "dateResolved": "date_resolved",
        "priority": "priority",
        "status": "status",
        "clientId": "client.c_id",
    }
    if sort_key not in converted_sort_key_list.keys():
        raise ControllerException(message="invalid sort key")
    if sort_order not in [1, -1]:
        raise ControllerException(message="invalid sort order")
    sort_key = converted_sort_key_list[sort_key]
    response = issue_log_service.list_issue_log(
        None, None, query, client, status, priority, sort_key, sort_order, page, size
    )
    data_list = []
    for data in response["content"]:
        if data.get("opened"):
            data["opened"] = change_date_format(data["opened"], use_hyphens=True)
        if data.get("dateResolved"):
            data["dateResolved"] = change_date_format(data["dateResolved"], use_hyphens=True)
        data_list.append(data)

    header_dict = OrderedDict(
        [
            ("clientId", "Client Id"),
            ("clientName", "Client Name"),
            ("opened", "Opened"),
            ("status", "Status"),
            ("priority", "Priority"),
            ("dateResolved", "Date Resolved"),
            ("resolutionNotes", "Resolution Notes"),
            ("shortDescription", "Short Description"),
        ]
    )
    name = "IssueLogs"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response
