from collections import OrderedDict
from flask import Blueprint, current_app, make_response, request, send_file, jsonify, Response

from flaskr.controllers.exceptions import ControllerException
from flaskr.helpers.auth import admin_required
from flaskr.helpers.boto3_handler import download_file
from flaskr.services.reporting_service import reporting_service
from flaskr.services.internal_audit_service import internal_audit_service

internal_audit_api = Blueprint("internal_audit_api", __name__)


@internal_audit_api.route("/banking", methods=["GET"])
@admin_required()
def banking_report(access_token):
    client_id = request.args.get("clientId")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")

    if not all([client_id, from_date, to_date]):
        raise ControllerException(message="Missing required details")

    response = internal_audit_service.banking_report(client_id, from_date, to_date)
    return response


@internal_audit_api.route("/banking/export", methods=["GET"])
@admin_required()
def export_banking_report(access_token):
    client_id = request.args.get("clientId")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")

    response = internal_audit_service.banking_report(client_id, from_date, to_date)
    data_list = []
    for data in response["content"]:
        for i in data["BankingReportAmount"]:
            data["currency"] = i["currency"]
            data["amount"] = i["amount"]
            data_list.append(data.copy())

    header_dict = OrderedDict(
        [
            ("fileName", "File Name"),
            ("currency", "Currency Code"),
            ("amount", "Banking Report Amount"),
            ("AmountasperBank", "Amount as per Bank"),
            ("BusinessRules", "Business Rules"),
            ("TestingStatus", "Testing Status"),
            ("Samples", "Samples"),
            ("Notes", "Add/Edit Note"),
            ("Risk", "Risk"),
            ("ResolutionNotes", "Resolution Notes"),
            ("Status", "Status"),
        ]
    )
    name = "Banking Internal Audit Report"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@internal_audit_api.route("/banking/<banking_id>", methods=["PUT"])
@admin_required()
def banking_edit(access_token, banking_id):
    data = request.json
    internal_audit_service.update_banking_report(data, banking_id)
    return Response(status=200)


@internal_audit_api.route("/claim-analysis", methods=["GET"])
@admin_required()
def claim_analysis(access_token):
    data = request.args
    client_id = data.get("client")
    currency = data.get("currency")
    if not all([client_id, currency]):
        raise ControllerException(message="Missing required details")
    response = internal_audit_service.claim_analysis(data)
    return jsonify(response)


@internal_audit_api.route("/claim", methods=["GET"])
@admin_required()
def claim_report(access_token):
    client_id = request.args.get("clientId")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")

    if not all([client_id, from_date, to_date]):
        raise ControllerException(message="Missing required details")

    response = internal_audit_service.claim_report(client_id, from_date, to_date)
    return response


@internal_audit_api.route("/claim/export", methods=["GET"])
@admin_required()
def export_claim_report(access_token):
    client_id = request.args.get("clientId")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")

    response = internal_audit_service.claim_report(client_id, from_date, to_date)
    data_list = []

    for data in response["content"]:
        for i in data["items"]:
            data["currency"] = i["currency"]
            data["revisedClaim"] = i["revisedClaim"]
            data["originalClaim"] = i["originalClaim"]
            data["checks"] = i["checks"]
            data["percentageCheck"] = i["percentageCheck"]
            data["percentageTotal"] = i["percentageTotal"]
            data["bookingRef"] = i["bookingRef"]
            data["element"] = i["element"]
            data["MandatoryChecks"] = i["MandatoryChecks"]

            data_list.append(data.copy())

    header_dict = OrderedDict(
        [
            ("fileName", "File Name"),
            ("currency", "Currency Code"),
            ("revisedClaim", "Revised Amount"),
            ("originalClaim", "Original Amount"),
            ("checks", "Checks"),
            ("percentageCheck", "%Checks"),
            ("percentageTotal", "%Tests"),
            ("AmountasperBank", "Amount as per Bank"),
            ("BusinessRules", "Business Rules"),
            ("bookingRef", "Full Checks"),
            ("element", "Element"),
            ("MandatoryChecks", "Missing Mandatory Checks"),
            ("SubsetofFullChecks", "Subset of Full Checks"),
            ("Notes", "Add/Edit Note"),
            ("Risk", "Risk"),
            ("ResolutionNotes", "Resolution Notes"),
            ("Status", "Status"),
        ]
    )
    name = "Claim Internal Audit Report"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@internal_audit_api.route("/claims/<claim_id>", methods=["PUT"])
@admin_required()
def claims_edit(access_token, claim_id):
    data = request.json
    internal_audit_service.update_claims_report(data, claim_id)
    return Response(status=200)


@internal_audit_api.route("/risk-exposure-and-insurance-cover", methods=["GET"])
@admin_required()
def risk_exposure_and_insurance_cover(access_token):
    data = request.args
    client_id = request.args.get("client")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    if not all([client_id, from_date, to_date]):
        raise ControllerException(message="Missing required details")

    resolution = int(data["resolution"]) if data.get("resolution") else 10
    response = internal_audit_service.risk_exposure_and_insurance_cover_graph(data, resolution)
    return jsonify(response)


@internal_audit_api.route("/log-download", methods=["GET"])
@admin_required()
def log_download(access_token):
    bucket = current_app.config["IA_BUCKET"]
    key = "audit_log/Internal Audit Log Escrows.xlsx"
    file_name = "Internal Audit Log Escrows.xlsx"
    download_file(bucket, key, f"{current_app.config['TEMP_DIR']}/{file_name}")
    return send_file(f"{current_app.config['TEMP_DIR']}/{file_name}")


@internal_audit_api.route("/banking-download", methods=["GET"])
@admin_required()
def banking_download(access_token):
    client_id = request.args.get("clientId")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")

    if not all([client_id, from_date, to_date]):
        raise ControllerException(message="Missing required details")

    response = internal_audit_service.banking_report_download(client_id, from_date, to_date)
    return response


@internal_audit_api.route("/claim-download", methods=["GET"])
@admin_required()
def claim_download(access_token):
    client_id = request.args.get("clientId")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")

    if not all([client_id, from_date, to_date]):
        raise ControllerException(message="Missing required details")

    response = internal_audit_service.claim_report_download(client_id, from_date, to_date)
    return response


@internal_audit_api.route("/client-cash-flow", methods=["GET"])
@admin_required()
def client_cash_flow_forecasts(access_token):
    client_id = request.args.get("clientId")
    currency_code = request.args.get("currency")
    year = request.args.get("year")
    return_date = request.args.get("returnDate")

    if year and currency_code and client_id and return_date:
        response = internal_audit_service.client_cash_flow_forecasts(year, client_id, currency_code, return_date)
    else:
        raise ControllerException(message="Missing required details")

    return jsonify(response)


@internal_audit_api.route("/client-cash-flow/export", methods=["GET"])
@admin_required()
def export_client_cash_flow_forecasts(access_token):
    client_id = request.args.get("clientId")
    currency_code = request.args.get("currency")
    year = request.args.get("year")
    return_date = request.args.get("returnDate")

    if year and currency_code and client_id:
        response = internal_audit_service.client_cash_flow_forecasts(year, client_id, currency_code, return_date)
    else:
        raise ControllerException(message="Missing required details")

    data_list = []
    for data in response["claimsData"]:
        month = data.get("month", "N/A")
        amount = data.get("amount", 0.0)
        data_list.append({"month": month, "amount": amount})

    total_claim_sum = response.get("totalClaimSum", 0.0)
    data_list.append({"month": "Total Claim Sum", "amount": total_claim_sum})
    header_dict = OrderedDict(
        [
            ("month", "Month"),
            ("amount", "Amount"),
        ]
    )
    name = "Client Cash Flow Forecasts Report"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@internal_audit_api.route("/client-performance", methods=["GET"])
@admin_required()
def get_client_performance(access_token):
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    client = request.args.get("client")
    if not all([from_date, to_date, client]):
        raise ControllerException(message="Missing required details")
    response = internal_audit_service.client_performance(client, from_date, to_date)
    return jsonify(response)


@internal_audit_api.route("/MA-log-download", methods=["GET"])
@admin_required()
def ma_log_download(access_token):
    bucket = current_app.config["IA_BUCKET"]
    key = "audit_log/Internal Audit Log MA.xlsx"
    file_name = "Internal Audit Log MA.xlsx"
    download_file(bucket, key, f"{current_app.config['TEMP_DIR']}/{file_name}")
    return send_file(f"{current_app.config['TEMP_DIR']}/{file_name}")


@internal_audit_api.route("/peer-report-download", methods=["GET"])
@admin_required()
def peer_report_download(access_token):
    bucket = current_app.config["IA_BUCKET"]
    key = "peer_report/Peer Review Report.docx"
    file_name = "Peer Review Report.docx"
    download_file(bucket, key, f"{current_app.config['TEMP_DIR']}/{file_name}")
    return send_file(f"{current_app.config['TEMP_DIR']}/{file_name}")


@internal_audit_api.route("/list-enabled-clients", methods=["GET"])
@admin_required()
def list_enabled_clients(access_token):
    list_clients = internal_audit_service.list_enabled_clients()
    return jsonify(list_clients)
