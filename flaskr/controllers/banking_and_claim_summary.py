from flask import Blueprint, request, jsonify
from flaskr.controllers.exceptions import ControllerException
from flaskr.helpers.auth import ptt_user_required
from jose import jwt

from flaskr.services.banking_and_claim_summary_service import banking_and_claim_service

banking_and_claims_api = Blueprint("banking_and_claims_api", __name__)


@banking_and_claims_api.route("/list", methods=["GET"])
@ptt_user_required()
def banking_and_claim_summary(access_token):
    is_today = False
    client_id = request.args.get("clientId")
    if request.args.get("isToday") == "true":
        is_today = True
    page = request.args.get("page")
    size = request.args.get("size")
    banking_and_claim_summary_files = banking_and_claim_service.get_banking_and_claim_summary(
        client_id, is_today, page, size
    )
    return banking_and_claim_summary_files


@banking_and_claims_api.route("/upload", methods=["POST"])
@ptt_user_required()
def banking_and_claim_summmary_create(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    if not request.form.get("clientId") or not request.files.get("file"):
        raise ControllerException(message="Missing required details")
    client_id = request.form["clientId"]
    file = request.files["file"]
    response = banking_and_claim_service.banking_and_claim_summmary_create(access_token, client_id, file, user_id)
    return jsonify(response)
