import json
import logging
from uuid import uuid4
import collections.abc

import requests
from flaskr.controllers.exceptions import ControllerException
import sys
from collections import OrderedDict
from flask import Blueprint, current_app, jsonify, request, make_response, send_file, Response
from flask import abort
from flaskr.helpers.date_util import change_date_format, date_format
from flaskr.services.client_service import client_service
from flaskr.helpers.reporting_util import movements_of_funds_open_banking_data
from flaskr.services.lookup_service import lookup_service
from flaskr.services.reporting_service import reporting_service
from flaskr.helpers.auth import ptt_user_required, reports_access
from flaskr.models import get_db
from datetime import datetime, timedelta
import dateutil.relativedelta
from bson import ObjectId
from jose import jwt
from flaskr.helpers.boto3_handler import call_lambda

from celery_app.tasks.reports import trust_balance_report_task
from celery_app.tasks.reports import trust_balance_report_nas

reports_api = Blueprint("reports_api", __name__)


@reports_api.route("/trust-balance", methods=["GET"])
@reports_access(request, report="TrustBalanceReport")
def trust_balance_report(access_token):
    query = request.args

    if query.get("client") == current_app.config.get("SWOOP") or query.get("client") == current_app.config.get(
        "SWOOP_TRAVEL"
    ):
        details = reporting_service.multi_currency_trust_balance_report(query)
    elif query.get("client") == current_app.config.get("GTL"):
        details = reporting_service.trust_balance_report_gtl(query)

        # Ensure content is properly handled
        if isinstance(details["content"], collections.abc.Generator):
            details["content"] = list(details["content"])  # Materialize generator safely

        if isinstance(details["content"], list):
            details["content"] = [dict(item) if isinstance(item, dict) else item for item in details["content"]]

        details["numberOfElements"] = len(details["content"])

    else:
        details = reporting_service.trust_balance_report(query)

        # Ensure content is properly handled
        if isinstance(details["content"], collections.abc.Generator):
            details["content"] = list(details["content"])  # Materialize generator safely

        if isinstance(details["content"], list):
            details["content"] = [dict(item) if isinstance(item, dict) else item for item in details["content"]]

        details["numberOfElements"] = len(details["content"])

    return Response(json.dumps(details, default=str), mimetype="application/json"), 200


@reports_api.route("/trust-balance/progress", methods=["GET"])
# @reports_access(request, report="TrustBalanceReport")
def trust_balance_report_progress():
    query = request.args
    details = reporting_service.trust_balance_report_progress(query)
    return Response(json.dumps(details, default=str), mimetype="application/json"), 200


@reports_api.route("/trust-balance/export", methods=["GET"])
@reports_access(request, report="TrustBalanceReport")
def export_trust_balance_report(access_token):
    query = request.args.to_dict()
    query["page"] = 1
    query["size"] = sys.maxsize
    client_id = query.get("client")
    currency = query.get("currency")
    name = "TrustBalance"
    existing_report_xlsx = get_db().report_files.find_one(
        {"name": name, "client_id": ObjectId(client_id), "currency": currency, "file_type": "xlsx"}
    )
    existing_report_csv = get_db().report_files.find_one(
        {"name": name, "client_id": ObjectId(client_id), "currency": currency, "file_type": "csv"}
    )
    file_id_xlsx = None
    file_id_csv = None
    now = datetime.utcnow()
    if existing_report_xlsx:
        if existing_report_xlsx["status"] == "Generating New Report" and existing_report_xlsx[
            "updated_at"
            # ] > now - timedelta(minutes=15):
        ] > now - timedelta(minutes=2):
            return (
                jsonify({"message": "TBR is already under generation for the selected filters"}),
                409,
            )
        get_db().report_files.update_one(
            {"_id": existing_report_xlsx["_id"]}, {"$set": {"status": "Generating New Report", "updated_at": now}}
        )
        file_id_xlsx = existing_report_xlsx["file_id"]
    else:
        file_id_xlsx = f"{uuid4()}.xlsx"
        get_db().report_files.insert_one(
            {
                "name": name,
                "client_id": ObjectId(client_id),
                "currency": currency,
                "file_type": "xlsx",
                "file_id": file_id_xlsx,
                "status": "Generating New Report",
                "created_at": now,
                "updated_at": now,
            }
        )
    if existing_report_csv:
        if existing_report_csv["status"] == "Generating New Report" and existing_report_csv[
            "updated_at"
            # ] > now - timedelta(minutes=15):
        ] > now - timedelta(minutes=2):
            return (
                jsonify({"message": "TBR is already under generation for the selected filters"}),
                409,
            )
        get_db().report_files.update_one(
            {"_id": existing_report_csv["_id"]}, {"$set": {"status": "Generating New Report", "updated_at": now}}
        )
        file_id_csv = existing_report_csv["file_id"]
    else:
        file_id_csv = f"{uuid4()}.csv"
        get_db().report_files.insert_one(
            {
                "name": name,
                "client_id": ObjectId(client_id),
                "currency": currency,
                "file_type": "csv",
                "file_id": file_id_csv,
                "status": "Generating New Report",
                "created_at": now,
                "updated_at": now,
            }
        )
    if client_id == current_app.config.get("WLH_NEW"):
        logging.info("Invoking celery %s for client %s", client_id)
        trust_balance_report_task.delay(
            data={**query, "name": name, "file_id_xlsx": file_id_xlsx, "file_id_csv": file_id_csv}
        )
    # elif client_id == current_app.config.get("NAS") or client_id == "62d914a7165ccb6ff35708e1": 
    #     logging.info("Invoking celery for NAS client %s", client_id)
    #     trust_balance_report_nas.delay(
    #         data={**query, "name": name, "file_id_xlsx": file_id_xlsx, "file_id_csv": file_id_csv}
    #     )

    else:
        payload = json.dumps(
            {"client": client_id, "currency": currency, "file_id_xlsx": file_id_xlsx, "file_id_csv": file_id_csv}
        ).encode("utf-8")
        func_name = f"data-transform-lambda-{current_app.config['ENVIRONMENT']}"
        logging.info("Invoking Lambda %s for client %s", func_name, client_id)
        call_lambda(func_name, payload)
        logging.info("Invoked Lambda %s for client %s", func_name, client_id)

    return jsonify({"message": "Generation of Trust Balance Reports initiated."}), 202

    # if client_id == current_app.config.get("NAS"):
    #     logging.info("Invoking celery %s for client %s", client_id)
    #     trust_balance_report_task.delay(
    #         data={**query, "name": name, "file_id_xlsx": file_id_xlsx, "file_id_csv": file_id_csv}
    #     )
    # elif client_id == current_app.config.get("NAS"):
    #       logging.info("Invoking celery %s for client %s", client_id)
    #     trust_balance_report_nas.delay(
    #         data={**query, "name": name, "file_id_xlsx": file_id_xlsx, "file_id_csv": file_id_csv}
    #     )
    # else:
    #     payload = json.dumps(
    #         {"client": client_id, "currency": currency, "file_id_xlsx": file_id_xlsx, "file_id_csv": file_id_csv}
    #     ).encode("utf-8")
    #     func_name = f"data-transform-lambda-{current_app.config['ENVIRONMENT']}"
    #     logging.info("Invoking Lambda %s for client %s", func_name, client_id)
    #     call_lambda(func_name, payload)
    #     logging.info("Invoked Lambda %s for client %s", func_name, client_id)
    # return jsonify({"message": "Generation of Trust Balance Reports initiated."}), 202


@reports_api.route("/client-files", methods=["GET"])
@reports_access(request, report="ClientFilesReport")
def client_files_report(access_token):
    query = request.args
    response = reporting_service.client_files_report(query)
    return jsonify(response), 200


@reports_api.route("/client-files/export", methods=["GET"])
@reports_access(request, report="ClientFilesReport")
def export_client_files_report(access_token):
    query = request.args.to_dict()
    query["page"] = 1
    query["size"] = sys.maxsize
    response = reporting_service.client_files_report(query)
    data_list = []
    for data in response["content"]:
        data["fileDate"] = change_date_format(data["fileDate"], use_hyphens=True)
        data["fileSubmitted"] = change_date_format(data["fileSubmitted"][:10], use_hyphens=True)
        data_list.append(data)
    header_dict = OrderedDict(
        [
            ("clientName", "Client Name"),
            ("friendlyName", "Friendly Name"),
            ("fileDate", "File Date"),
            ("fileStatus", "File Status"),
            ("fileType", "File Type"),
            ("fileName", "Original Filename"),
            ("fileSubmitted", "Submitted"),
            ("validClaimItems", "Valid Claim Items"),
            ("voidClaimItems", "Void Claim Items"),
            ("claimItems", "Total Claim Items"),
            ("validClaimAmount", "Valid Claim Amount"),
            ("voidClaimAmount", "Void Claim Amount"),
            ("claimFileTotal", "Claim File Total"),
            ("bankingItems", "Total Banking Items"),
            ("bankingFileTotal", "Banking File Total"),
            ("currency", "Currency"),
        ]
    )
    name = "ClientFiles"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/weekly-fortnightly", methods=["GET"])
@reports_access(request, report="WeeklyFortnightlyReport")
def weekly_fortnightly_report(access_token):
    client = request.args.get("client")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    weekly_fornightly = True
    bank_name = request.args.get("bankName")
    banking = request.args.get("banking")
    # account_no = request.args.get("accountNo")
    # sort_code = request.args.get("sortCode")

    if banking == "Barclays":
        client_bank_account_info = get_db().client_bank_acct_details.find_one({"client_id": ObjectId(client)})
        account_no = client_bank_account_info.get("account_no", None) if client_bank_account_info else None
        sort_code = client_bank_account_info.get("sort_code", None) if client_bank_account_info else None
        if not all([account_no, from_date]):
            raise ControllerException(message="missing required fields")
        client_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        client_name = client_info.get("full_name", None) if client_info else None

        from_date = date_format(from_date)
        params = {"accountNo": account_no, "fromDate": from_date, "sortCode": sort_code}
        open_banking_response = requests.get(
            f'{current_app.config["OPEN_BANKING_URI"]}/api/barclays/data/weekly-barclays-summary', params=params
        )

        if open_banking_response.status_code == 400:
            error_response = make_response(json.loads(open_banking_response.content))
            error_message = json.loads(open_banking_response.content)
            if error_message.get("error") == "invalid_grant":
                error_response.status_code = open_banking_response.status_code
                abort(error_response)
        if open_banking_response.status_code != 200:
            logging.info(json.loads(open_banking_response.content))
            return jsonify(open_banking_response), 200
        response = {}
        response["clientName"] = client_name
        open_banking_data = open_banking_response.json()
        open_banking_data["bankingAmount"] = open_banking_data.pop("funds_in")
        open_banking_data["claimAmount"] = open_banking_data.pop("funds_out")
        response["openBankingData"] = open_banking_data
        return jsonify(response), 200
    else:

        if not all([client, from_date, to_date, currency]):
            raise ControllerException(message="missing required fields")
        response = reporting_service.banking_and_claim_between_dates(
            client, currency, from_date, to_date, weekly_fornightly
        )
        headers = {"Authorization": f"Bearer {access_token}"}
        bank_accounts = lookup_service.get_bank_accounts(client, currency, bank_name)
        from_date = date_format(from_date)
        to_date = date_format(to_date)
        data = {"bank_accounts": bank_accounts, "from_date": from_date, "to_date": to_date}
        open_banking_response = requests.post(
            f'{current_app.config["OPEN_BANKING_URI"]}/api/data/weekly-fortnightly-summary', json=data, headers=headers
        )
        if open_banking_response.status_code == 400:
            error_response = make_response(json.loads(open_banking_response.content))
            error_message = json.loads(open_banking_response.content)
            if error_message.get("error") == "invalid_grant":
                error_response.status_code = open_banking_response.status_code
                abort(error_response)
        if open_banking_response.status_code != 200:
            logging.info(json.loads(open_banking_response.content))
            return jsonify(response), 200

        open_banking_data = open_banking_response.json()
        open_banking_data["bankingAmount"] = open_banking_data.pop("funds_in")
        open_banking_data["claimAmount"] = open_banking_data.pop("funds_out")

        return jsonify({**response, "openBankingData": open_banking_data}), 200


@reports_api.route("/weekly-fortnightly/export", methods=["GET"])
@reports_access(request, report="WeeklyFortnightlyReport")
def export_weekly_fortnightly_report(access_token):
    client = request.args.get("client")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    weekly_fornightly = True
    bank_name = request.args.get("bankName")
    banking = request.args.get("banking")
    # account_no = request.args.get("accountNo")
    # sort_code = request.args.get("sortCode")

    if banking == "Barclays":
        client_bank_account_info = get_db().client_bank_acct_details.find_one({"client_id": ObjectId(client)})
        account_no = client_bank_account_info.get("account_no", None) if client_bank_account_info else None
        sort_code = client_bank_account_info.get("sort_code", None) if client_bank_account_info else None

        if not all([account_no, from_date]):
            raise ControllerException(message="missing required fields")

        from_date = date_format(from_date)
        params = {"accountNo": account_no, "fromDate": from_date, "sortCode": sort_code}
        open_banking_response = requests.get(
            f'{current_app.config["OPEN_BANKING_URI"]}/api/barclays/data/weekly-barclays-summary', params=params
        )
        client_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        client_name = client_info.get("full_name", None) if client_info else None
        date = datetime.now().strftime("%d %B %Y")
        name = f"Weekly-Barclays {date} {client_name} "

        if open_banking_response.status_code == 400:
            error_response = make_response(json.loads(open_banking_response.content))
            error_message = json.loads(open_banking_response.content)
            if error_message.get("error") == "invalid_grant":
                error_response.status_code = open_banking_response.status_code
                abort(error_response)
        if open_banking_response.status_code != 200:
            logging.info(json.loads(open_banking_response.content))
            return jsonify(open_banking_response), 200

        open_banking_data = open_banking_response.json()
        reporting_service.export_weekly_barclays_report(open_banking_data, name, client_name)
        response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
        response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        return response

    if not all([client, from_date, to_date, currency]):
        raise ControllerException(message="missing required fields")
    weekly_data = reporting_service.banking_and_claim_between_dates(
        client, currency, from_date, to_date, weekly_fornightly
    )

    name = "Weekly-fortnightly"
    headers = {"Authorization": f"Bearer {access_token}"}
    bank_accounts = lookup_service.get_bank_accounts(client, currency, bank_name)
    data = {"bank_accounts": bank_accounts, "from_date": from_date, "to_date": to_date}
    open_banking_response = requests.post(
        f'{current_app.config["OPEN_BANKING_URI"]}/api/data/weekly-fortnightly-summary', json=data, headers=headers
    )

    if open_banking_response.status_code == 400:
        error_response = make_response(json.loads(open_banking_response.content))
        error_message = json.loads(open_banking_response.content)
        if error_message.get("error") == "invalid_grant":
            error_response.status_code = open_banking_response.status_code
            abort(error_response)
    if open_banking_response.status_code != 200:
        logging.info(json.loads(open_banking_response.content))
        open_banking_data = None
        reporting_service.export_weekly_fortnightly_report(
            weekly_data, open_banking_data, name, client, to_date, currency
        )
        response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
        response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        return response

    open_banking_data = open_banking_response.json()
    reporting_service.export_weekly_fortnightly_report(weekly_data, open_banking_data, name, client, to_date, currency)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/bank-reconciliation", methods=["GET"])
@reports_access(request, report="BankReconciliationStatement")
def bank_reconciliation_statement_report(access_token):
    client = request.args.get("client")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    accounting_statement_no = request.args.get("accountingStatement")
    bank_name = request.args.get("bankName")
    banking = request.args.get("banking")

    if not all([client, from_date, to_date, currency]):
        raise ControllerException(message="missing required fields")
    response = reporting_service.bank_reconciliation_statement_report(client, currency, from_date, to_date)

    if not (accounting_statement_no):
        accounting_statement_no = "1"
    accounting_statements = {
        "1": "Please note that the opening and closing balances above include balances held in the current, reserve and bonus accounts",
        "2": "Please note that the opening and closing balances above include balances held in the current and reserve accounts",
        "3": "Please note that the receipts include £   which relate to monies processed by  which were accounted on  and this amount is included in the Closing Balance. Also note that the receipts exclude £, which relate to monies processed by   which were received into the Trust bank account on    and are included in the opening balance as at ",
        "4": "",
    }

    response["accountingStatement"] = accounting_statements.get(accounting_statement_no)

    headers = {"Authorization": f"Bearer {access_token}"}
    bank_accounts = lookup_service.get_bank_accounts(client, currency, bank_name)
    client_name = client_service.get_client_friendly_name(client)
    from_date = date_format(from_date)
    to_date = date_format(to_date)

    if banking == "Barclays":
        client_bank_account_info = get_db().client_bank_acct_details.find_one({"client_id": ObjectId(client)})
        account_no = client_bank_account_info.get("account_no", None) if client_bank_account_info else None
        sort_code = client_bank_account_info.get("sort_code", None) if client_bank_account_info else None
        data = {
            "bank_accounts": bank_accounts,
            "from_date": from_date,
            "to_date": to_date,
            "client_name": client_name,
            "banking": banking,
            "account_no": account_no,
            "sort_code": sort_code,
        }
    else:
        data = {"bank_accounts": bank_accounts, "from_date": from_date, "to_date": to_date, "client_name": client_name}

    open_banking_response = requests.post(
        f'{current_app.config["OPEN_BANKING_URI"]}/api/data/banking-summary', json=data, headers=headers
    )
    if open_banking_response.status_code == 400:
        error_response = make_response(json.loads(open_banking_response.content))
        error_message = json.loads(open_banking_response.content)
        if error_message.get("error") == "invalid_grant":
            error_response.status_code = open_banking_response.status_code
            abort(error_response)
    if open_banking_response.status_code != 200:
        logging.info(json.loads(open_banking_response.content))
        return jsonify(response), 200

    open_banking_data = open_banking_response.json()
    if banking == "Barclays":
        open_banking_data["funds_out"] = -abs(open_banking_data["funds_out"])
    if client == current_app.config["PENNYWOOD"] and bank_accounts[0]["bank_name"] == "HSBC Business":
        open_banking_data["openingBalance"] = open_banking_data.pop("opening_balance")
        open_banking_data["closingBalance"] = open_banking_data.pop("closing_balance")
        open_banking_data["closingBalanceAsPerStatement"] = open_banking_data.pop("closing_balance_on_statement")
        open_banking_data["trustNonTrustPaymentReceived"] = open_banking_data.pop("trust_non_trust_payment_received")
        open_banking_data["paymentReleased"] = open_banking_data.pop("payment_released")
        open_banking_data["trustFundsSentToPTT"] = open_banking_data.pop("trust_funds_sent_to_ptt")
        open_banking_data["unreconciledTransactions"] = open_banking_data.pop("unreconciled_transactions")
        open_banking_data["difference"] = open_banking_data.pop("difference")
    else:
        open_banking_data["openingBalance"] = open_banking_data.pop("opening_balance")
        open_banking_data["closingBalance"] = open_banking_data.pop("closing_balance")
        open_banking_data["closingBalanceAsPerStatement"] = open_banking_data.pop("closing_balance_on_statement")
        open_banking_data["bankingAmount"] = open_banking_data.pop("funds_in")
        open_banking_data["claimAmount"] = open_banking_data.pop("funds_out")
        open_banking_data["bankCharges"] = open_banking_data.pop("bank_charges")
        open_banking_data["bankFees"] = open_banking_data.pop("bank_fees")
        open_banking_data["interestReceived"] = open_banking_data.pop("interest_received")
        open_banking_data["bankChargesReversal"] = open_banking_data.pop("bank_charges_reversal")
        open_banking_data["difference"] = open_banking_data.pop("difference")
        open_banking_data["transferToCLL"] = open_banking_data.pop("transfer_cll")
        open_banking_data["transferToCLLABTOT"] = open_banking_data.pop("transfer_abtot")
        open_banking_data["transferFromDeposit"] = open_banking_data.pop("transfer_from_deposit")

    return jsonify({**response, "openBankingData": open_banking_data}), 200


@reports_api.route("/bank-reconciliation/export", methods=["GET"])
@reports_access(request, report="BankReconciliationStatement")
def export_bank_reconciliation_statement_report(access_token):
    client = request.args.get("client")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    accounting_statement_no = request.args.get("accountingStatement")
    bank_name = request.args.get("bankName")
    banking = request.args.get("banking")

    hide_warnings = True if request.args.get("hideWarnings") == "true" else False
    bank_reconciliation_data = reporting_service.bank_reconciliation_statement_report(
        client, currency, from_date, to_date
    )

    if not (accounting_statement_no):
        accounting_statement_no = "1"
    accounting_statements = {
        "1": "Please note that the opening and closing balances above include balances held in the current, reserve and bonus accounts",
        "2": "Please note that the opening and closing balances above include balances held in the current and reserve accounts",
        "3": "Please note that the receipts include £   which relate to monies processed by  which were accounted on  and this amount is included in the Closing Balance. Also note that the receipts exclude £, which relate to monies processed by   which were received into the Trust bank account on    and are included in the opening balance as at ",
        "4": "",
    }
    bank_reconciliation_data["accountingStatement"] = accounting_statements.get(accounting_statement_no)
    client_name = client_service.get_client_friendly_name(client)
    from_date = date_format(from_date)
    to_date = date_format(to_date)

    if not all([client, from_date, to_date, currency]):
        raise ControllerException(message="missing required fields")
    name = "Bank Reconciliation Statement Of Trust Account"

    headers = {"Authorization": f"Bearer {access_token}"}
    bank_accounts = lookup_service.get_bank_accounts(client, currency, bank_name)
    if banking == "Barclays":
        client_bank_account_info = get_db().client_bank_acct_details.find_one({"client_id": ObjectId(client)})
        account_no = client_bank_account_info.get("account_no", None) if client_bank_account_info else None
        sort_code = client_bank_account_info.get("sort_code", None) if client_bank_account_info else None
        data = {
            "bank_accounts": bank_accounts,
            "from_date": from_date,
            "to_date": to_date,
            "client_name": client_name,
            "banking": banking,
            "account_no": account_no,
            "sort_code": sort_code,
        }
    else:
        data = {"bank_accounts": bank_accounts, "from_date": from_date, "to_date": to_date, "client_name": client_name}
    open_banking_response = requests.post(
        f'{current_app.config["OPEN_BANKING_URI"]}/api/data/banking-summary', json=data, headers=headers
    )
    if open_banking_response.status_code == 400:
        error_response = make_response(json.loads(open_banking_response.content))
        error_message = json.loads(open_banking_response.content)
        if error_message.get("error") == "invalid_grant":
            error_response.status_code = open_banking_response.status_code
            abort(error_response)
    from_date = change_date_format(from_date)
    to_date = change_date_format(to_date)
    if open_banking_response.status_code != 200:
        logging.info(json.loads(open_banking_response.content))
        open_banking_data = None
        reporting_service.export_bank_reconciliation_statement_report(
            bank_reconciliation_data, open_banking_data, hide_warnings, name, client, from_date, to_date, currency
        )
        response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
        response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        return response

    open_banking_data = open_banking_response.json()

    reporting_service.export_bank_reconciliation_statement_report(
        bank_reconciliation_data, open_banking_data, hide_warnings, name, client, from_date, to_date, currency
    )
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/bluestyle-report/export", methods=["POST"])
@reports_access(request, report="BlueStyleReport")
def export_bluestyle_report(access_token):
    request_data = request.json
    notes = request_data.get("notes")
    payload = request_data.get("payload")
    from_date = request_data.get("from_date")
    to_date = request_data.get("to_date")

    file_name = f"Blue Style a.s Trust Statement {to_date}.xlsx"
    reporting_service.bluestyle_create_excel_file(payload, notes, from_date, to_date, file_name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{file_name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/banking-summary/export", methods=["GET"])
@reports_access(request, report="BankingsummaryReport")
def export_banking_summary_report(access_token):
    client = request.args.get("client")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    bank_name = request.args.get("bankName")

    client_name = client_service.get_client_friendly_name(client)
    from_date = date_format(from_date)
    to_date = date_format(to_date)
    client_bank_account_info = get_db().client_bank_acct_details.find_one({"client_id": ObjectId(client)})
    account_no = client_bank_account_info.get("account_no", None) if client_bank_account_info else None
    sort_code = client_bank_account_info.get("sort_code", None) if client_bank_account_info else None

    if bank_name == "Barclays":

        if not all([account_no, from_date]):
            raise ControllerException(message="missing required fields")

        from_date = date_format(from_date)
        params = {"accountNo": account_no, "fromDate": from_date, "sortCode": sort_code, "toDate": to_date}
        open_banking_response = requests.get(
            f'{current_app.config["OPEN_BANKING_URI"]}/api/barclays/data/transactions', params=params
        )
        client_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        iban = client_info.get("iban", None) if client_info else None
        file_name = f"Barclays Statement  {client_name} {from_date} to {to_date}.xlsx"

        if open_banking_response.status_code == 400:
            error_response = make_response(json.loads(open_banking_response.content))
            error_message = json.loads(open_banking_response.content)
            if error_message.get("error") == "invalid_grant":
                error_response.status_code = open_banking_response.status_code
                abort(error_response)
        if open_banking_response.status_code != 200:
            logging.info(json.loads(open_banking_response.content))
            return jsonify(open_banking_response), 200

        open_banking_data = open_banking_response.json()
        reporting_service.export_barclays_statement_report(
            open_banking_data, client_name, from_date, to_date, account_no, file_name, currency, iban, sort_code
        )
        response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{file_name}"))
        response.headers["Content-Type"] = "application/vnd.openxmlformat=s-officedocument.spreadsheetml.sheet"
        return response
    else:

        name = f"Bank Statement {client_name} {from_date} to {to_date}.xlsx"
        if not all([client, from_date, to_date, currency]):
            raise ControllerException(message="missing required fields")

        headers = {"Authorization": f"Bearer {access_token}"}
        bank_accounts = lookup_service.get_bank_accounts(client, currency, bank_name)
        data = {"bank_accounts": bank_accounts, "from_date": from_date, "to_date": to_date, "client_name": client_name}
        open_banking_response = requests.get(
            f'{current_app.config["OPEN_BANKING_URI"]}/api/data/bank-transactions', json=data, headers=headers
        )
        if open_banking_response.status_code == 400:
            error_response = make_response(json.loads(open_banking_response.content))
            error_message = json.loads(open_banking_response.content)
            if error_message.get("error") == "invalid_grant":
                error_response.status_code = open_banking_response.status_code
                abort(error_response)
        from_date = change_date_format(from_date)
        to_date = change_date_format(to_date)
        if open_banking_response.status_code != 200:
            open_banking_data = None
            reporting_service.export_banking_summary_report(open_banking_data, name, client, from_date, to_date)
            response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
            response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            return response

        open_banking_data = open_banking_response.json()

        reporting_service.export_banking_summary_report(open_banking_data, name, client, from_date, to_date)
        response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
        response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        return response


@reports_api.route("/movement-of-funds", methods=["GET"])
@reports_access(request, report="MovementOfFunds")
def movements_of_funds(access_token):
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    currency = request.args.get("currency")
    client = request.args.get("client") or ""
    bank_name = request.args.get("bankName")

    if not all([from_date, to_date]):
        raise ControllerException(message="missing required fields")

    if from_date:
        from_date = datetime.fromisoformat(from_date)

    previous_from_date = from_date + dateutil.relativedelta.relativedelta(years=-1)

    if to_date:
        to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time())

    previous_to_date = to_date + dateutil.relativedelta.relativedelta(years=-1)

    date_format = "%Y-%m-%d"
    from_date_str = datetime.strftime(from_date, date_format)
    to_date_str = datetime.strftime(to_date, date_format)

    response = reporting_service.movements_of_funds(
        client, from_date_str, to_date_str, previous_from_date, previous_to_date
    )
    client_name = client_service.get_client_friendly_name(client)
    headers = {"Authorization": f"Bearer {access_token}"}
    bank_accounts = lookup_service.get_bank_accounts(client, currency, bank_name)
    data = {
        "bank_accounts": bank_accounts,
        "from_date": from_date_str,
        "to_date": to_date_str,
        "client_name": client_name,
    }

    open_banking_response = requests.post(
        f'{current_app.config["OPEN_BANKING_URI"]}/api/data/movement-of-funds', json=data, headers=headers
    )

    if open_banking_response.status_code == 400:
        error_response = make_response(json.loads(open_banking_response.content))
        error_message = json.loads(open_banking_response.content)
        if error_message.get("error") == "invalid_grant":
            error_response.status_code = open_banking_response.status_code
            abort(error_response)
    if open_banking_response.status_code != 200:
        logging.info(json.loads(open_banking_response.content))
        return jsonify(response), 200

    open_banking_data = open_banking_response.json()
    open_banking_datas = movements_of_funds_open_banking_data(open_banking_data)

    return jsonify({"items": response, "openBankingData": open_banking_datas}), 200


@reports_api.route("/movement-of-funds/export", methods=["GET"])
@reports_access(request, report="MovementOfFunds")
def export_movements_of_funds(access_token):
    client = request.args.get("client")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    bank_name = request.args.get("bankName")
    if from_date:
        from_date = datetime.fromisoformat(from_date)
    previous_from_date = from_date + dateutil.relativedelta.relativedelta(years=-1)
    if to_date:
        to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time())
    previous_to_date = to_date + dateutil.relativedelta.relativedelta(years=-1)
    date_format = "%Y-%m-%d"
    from_datetime = datetime.strftime(from_date, date_format)
    to_datetime = datetime.strftime(to_date, date_format)
    movement_of_funds_data = reporting_service.movements_of_funds(
        client, from_datetime, to_datetime, previous_from_date, previous_to_date
    )

    if not all([client, from_datetime, to_datetime]):
        raise ControllerException(message="missing required fields")
    name = "Summary Of Movement Of Funds"

    headers = {"Authorization": f"Bearer {access_token}"}
    bank_accounts = lookup_service.get_bank_accounts(client, currency, bank_name)
    data = {"bank_accounts": bank_accounts, "from_date": from_datetime, "to_date": to_datetime}
    open_banking_response = requests.post(
        f'{current_app.config["OPEN_BANKING_URI"]}/api/data/movement-of-funds', json=data, headers=headers
    )
    if open_banking_response.status_code == 400:
        error_response = make_response(json.loads(open_banking_response.content))
        error_message = json.loads(open_banking_response.content)
        if error_message.get("error") == "invalid_grant":
            error_response.status_code = open_banking_response.status_code
            abort(error_response)
    from_datetime = change_date_format(from_datetime)
    to_datetime = change_date_format(to_datetime)
    logging.info(json.loads(open_banking_response.content))
    if open_banking_response.status_code != 200:
        logging.info(json.loads(open_banking_response.content))
        open_banking_data = None
        reporting_service.export_movements_of_funds(
            movement_of_funds_data,
            open_banking_data,
            name,
            client,
            from_date,
            to_date,
            currency,
            previous_from_date,
            previous_to_date,
        )
        response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
        response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        return response

    open_banking_data = open_banking_response.json()
    open_banking_datas = movements_of_funds_open_banking_data(open_banking_data)

    reporting_service.export_movements_of_funds(
        movement_of_funds_data,
        open_banking_datas,
        name,
        client,
        from_date,
        to_date,
        currency,
        previous_from_date,
        previous_to_date,
    )
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/banking-claim-summary", methods=["GET"])
@reports_access(request, report="BankingClaimSummary")
def banking_claim_summary_report(access_token):
    client = request.args.get("client")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    page = request.args.get("page")
    size = request.args.get("size")

    if not all([client, from_date, to_date]):
        raise ControllerException(message="missing required fields")
    response = reporting_service.banking_claim_summary_report(client, from_date, to_date, page, size)
    return jsonify(response), 200


@reports_api.route("/banking-claim-summary/export", methods=["GET"])
@reports_access(request, report="BankingClaimSummary")
def export_banking_claim_summary_report(access_token):
    client = request.args.get("client")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    page = 1
    size = sys.maxsize
    details = reporting_service.banking_claim_summary_report(client, from_date, to_date, page, size)
    data_list = []
    for data in details["content"]:
        data["reportDate"] = change_date_format(data["reportDate"])
        if len(data["paymentDate"]) > 1:
            start_date = change_date_format(data["paymentDate"][0])
            end_date = change_date_format(data["paymentDate"][-1])
            data["paymentDate"] = f"{start_date}-{end_date}"
        elif len(data["paymentDate"]) == 0:
            data["paymentDate"] = ""
        else:
            data["paymentDate"] = change_date_format(data["paymentDate"][0])
        data["trustReceivedAmount"] = ""
        data["trustReceivedDate"] = ""
        data["t2"] = ""
        data["bankCharges"] = ""
        data_list.append(data)
    header_dict = OrderedDict(
        [
            ("clientId", "Client Id"),
            ("clientName", "Client Name"),
            ("friendlyName", "Friendly Name"),
            ("currency", "Currency"),
            ("reportDate", "Report Date"),
            ("paymentDate", "Payment Date"),
            ("openingBalance", "Opening Balance"),
            ("totalBanking", "Total Banking"),
            ("cheque", "Cheque"),
            ("bankTransfer", "Bank Transfer"),
            ("cash", "Cash"),
            ("paysafeAmount", "Paysafe Amount"),
            ("card", "Card"),
            ("otherPaymentAmount", "Others"),
            ("trustReceivedAmount", "Trust Received Amount"),
            ("trustReceivedDate", "Trust Received Date"),
            ("t2", "T+2"),
            ("bankCharges", "Bank Charges"),
            ("performance", "Performance"),
            ("commission", "Commission"),
            ("cancellation", "Cancellation"),
            ("refund", "Refund"),
            ("nonTrust", "Non-Trust"),
            ("lcf", "LCF"),
            ("charterFlightsOrScheduledFlights", "Charter Flights/Scheduled Flights"),
            ("bsp", "BSP"),
            ("atol", "ATOL"),
            ("cropCard", "Crop Card"),
            ("apcFee", "APC Fee"),
            ("balance", "Balance"),
            ("deposit", "Deposit"),
            ("cruise", "Cruise"),
            ("insurance", "Insurance"),
            ("otherElementAmount", "Others"),
            ("totalClaim", "Total Claim"),
            ("closingBalance", "Closing Balance"),
        ]
    )
    name = "Banking and Claim Summary Report"
    reporting_service.export_banking_claim_summary_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/compliance-computation", methods=["GET"])
@reports_access(request, report="ComplianceComputationReport")
def compliance_computation_report(access_token):
    client = request.args.get("client")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")

    if not all([client, currency, from_date, to_date]):
        raise ControllerException(message="missing required fields")
    client_functions = {
        current_app.config["WLH_NEW"]: reporting_service.compliance_computation_report_wlh,
        current_app.config["WE_LOVE_HOLIDAYS"]: reporting_service.compliance_computation_report_wlh,
    }
    report_function = client_functions.get(client, reporting_service.compliance_computation_report)
    response = report_function(client, currency, from_date, to_date)
    return jsonify(response), 200


@reports_api.route("/compliance-computation/export", methods=["GET"])
@reports_access(request, report="ComplianceComputationReport")
def export_compliance_computation_report(access_token):
    client = request.args.get("client")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")

    if not all([client, currency, from_date, to_date]):
        raise ControllerException(message="missing required fields")
    client_functions = {
        current_app.config["WLH_NEW"]: reporting_service.compliance_computation_report_wlh,
        current_app.config["WE_LOVE_HOLIDAYS"]: reporting_service.compliance_computation_report_wlh,
    }
    report_function = client_functions.get(client, reporting_service.compliance_computation_report)
    result = report_function(client, currency, from_date, to_date)

    name = "Compliance Computation Report"
    reporting_service.export_compliance_computation_report(result, name, client)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "text/plain"
    return response


@reports_api.route("/report-files", methods=["POST"])
@reports_access(request, report="TrustBalanceReport")
def report_files(access_token):
    response = reporting_service.report_files(request.json)
    if not response:
        abort(400)
    return response


@reports_api.route("/atol-renewal-tracker-list", methods=["GET"])
@reports_access(request, report="ATOLRenewalTrackerList")
def get_atol_renewal_tracker_list(access_token):
    client = request.args.get("client")
    if not (client):
        raise ControllerException(message="missing required fields")
    response = reporting_service.atol_renewal_tracker_list(client)
    return jsonify(response), 200


@reports_api.route("/atol-renewal-tracker-list/export", methods=["GET"])
@reports_access(request, report="ATOLRenewalTrackerList")
def atol_renewal_tracker_list_export(access_token):
    client = request.args.get("client")
    result = reporting_service.atol_renewal_tracker_list(client)
    data_list = []
    for data in result:
        data["startDate"] = change_date_format(data["startDate"])
        data["expiryDate"] = change_date_format(data["expiryDate"])
        data_list.append(data)
    header_dict = OrderedDict(
        [
            ("clientName", "Client Name"),
            ("license", "ATOL Licence Number"),
            ("startDate", "ATOL Licence Start Date"),
            ("expiryDate", "ATOL Licence Expiry date"),
        ]
    )
    name = "Atol Renewal Tracker"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/insurance-renewal-tracker-list", methods=["GET"])
@reports_access(request, report="InsuranceRenewalTrackerList")
def insurance_renewal_tracker_list(access_token):
    client = request.args.get("client")
    response = reporting_service.insurance_renewal_tracker_list(client)
    return jsonify(response), 200


@reports_api.route("/insurance-renewal-tracker-list/export", methods=["GET"])
@reports_access(request, report="InsuranceRenewalTrackerList")
def insurance_renewal_tracker_list_export(access_token):
    client = request.args.get("client")
    result = reporting_service.insurance_renewal_tracker_list(client)
    data_list = []
    for data in result:
        data["expiryDate"] = change_date_format(data["expiryDate"])
        data_list.append(data)
    header_dict = OrderedDict(
        [
            ("clientName", "Client Name"),
            ("policyNumber", "Policy Number"),
            ("provider", "Provider"),
            ("expiryDate", "Expiry Date"),
            ("supplierName", "List of suppliers"),
            ("capAmount", "Max. Cap per Supplier"),
            ("totalMaxCap", "Total Max Cap"),
        ]
    )
    name = "Insurance Renewal Tracker"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/daily-agent-exceptions", methods=["GET"])
@reports_access(request, report="DailyAgentException")
def daily_agent_exceptions_report(access_token):
    query = request.args

    details = reporting_service.daily_agent_exceptions_report(query)
    return jsonify(details), 200


@reports_api.route("/daily-agent-exceptions/export", methods=["GET"])
@reports_access(request, report="DailyAgentException")
def export_daily_agent_exceptions_report(access_token):
    query = request.args.to_dict()
    query["page"] = 1
    query["size"] = sys.maxsize
    response = reporting_service.daily_agent_exceptions_report(query)
    data_list = []
    for item in response["content"]:
        item["bookingDate"] = change_date_format(item["bookingDate"])
        item["dateOfTravel"] = change_date_format(item["dateOfTravel"])
        item["dateOfReturn"] = change_date_format(item["dateOfReturn"])
        data_list.append(item)
    header_dict = OrderedDict(
        [
            ("clientName", "Client Name"),
            ("bookingRef", "Booking Ref"),
            ("leadPax", "Lead Pax"),
            ("leadTime", "Lead Time"),
            ("numberOfPax", "Number Of Pax"),
            ("bookingDate", "Booking Date"),
            ("dateOfTravel", "Date Of Travel"),
            ("dateOfReturn", "Date Of Return"),
            ("bondType", "Bond Type"),
            ("totalBookingValue", "Total Booking Value"),
            ("deposits", "Deposits"),
            ("totalInTrust", "Total In Trust"),
            ("totalClaimAmount", "Total Claim Amount"),
            ("totalAgentAmountInTrust", "Total Agent Amount In Trust"),
        ]
    )
    name = "DailyAgentExceptions"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/trust-balance/sftp-upload", methods=["GET"])
@ptt_user_required()
def tbr_sftp_upload(access_token):
    query = request.args
    if not all([query.get("type"), query.get("client"), query.get("fileId")]):
        raise ControllerException(message="Missing required details")
    reporting_service.tbr_sftp_upload(query)
    return Response(status=200)


@reports_api.route("/hsbc-bank-reconciliation/export", methods=["GET"])
@reports_access(request, report="HSBCBankReconciliationStatement")
def export_hsbc_bank_reconciliation_statement_report(access_token):
    client = request.args.get("client")
    currency = request.args.get("currency")
    from_date = request.args.get("fromDate")
    to_date = request.args.get("toDate")
    accounting_statement_no = request.args.get("accountingStatement")
    bank_name = request.args.get("bankName")

    hide_warnings = True if request.args.get("hideWarnings") == "true" else False
    bank_reconciliation_data = reporting_service.bank_reconciliation_statement_report(
        client, currency, from_date, to_date
    )

    if not (accounting_statement_no):
        accounting_statement_no = "1"
    accounting_statements = {
        "1": "Please note that the opening and closing balances above include balances held in the current, reserve and bonus accounts",
        "2": "Please note that the opening and closing balances above include balances held in the current and reserve accounts",
        "3": "Please note that the receipts include £   which relate to monies processed by  which were accounted on  and this amount is included in the Closing Balance. Also note that the receipts exclude £, which relate to monies processed by   which were received into the Trust bank account on    and are included in the opening balance as at ",
        "4": "",
    }
    bank_reconciliation_data["accountingStatement"] = accounting_statements.get(accounting_statement_no)
    client_name = client_service.get_client_friendly_name(client)
    from_date = date_format(from_date)
    to_date = date_format(to_date)
    if not all([client, from_date, to_date, currency]):
        raise ControllerException(message="missing required fields")
    name = "HSBC Bank Reconciliation Statement Of Trust Account"

    headers = {"Authorization": f"Bearer {access_token}"}
    bank_accounts = lookup_service.get_bank_accounts(client, currency, bank_name)
    data = {"bank_accounts": bank_accounts, "from_date": from_date, "to_date": to_date, "client_name": client_name}
    open_banking_response = requests.post(
        f'{current_app.config["OPEN_BANKING_URI"]}/api/data/banking-summary', json=data, headers=headers
    )
    if open_banking_response.status_code == 400:
        error_response = make_response(json.loads(open_banking_response.content))
        error_message = json.loads(open_banking_response.content)
        if error_message.get("error") == "invalid_grant":
            error_response.status_code = open_banking_response.status_code
            abort(error_response)
    from_date = change_date_format(from_date)
    to_date = change_date_format(to_date)
    if open_banking_response.status_code != 200:
        logging.info(json.loads(open_banking_response.content))
        open_banking_data = None
        reporting_service.export_hsbc_bank_reconciliation_statement_report(
            bank_reconciliation_data, open_banking_data, hide_warnings, name, client, from_date, to_date, currency
        )
        response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
        response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        return response

    open_banking_data = open_banking_response.json()

    reporting_service.export_hsbc_bank_reconciliation_statement_report(
        bank_reconciliation_data, open_banking_data, hide_warnings, name, client, from_date, to_date, currency
    )
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/non-trust-bookings", methods=["GET"])
@reports_access(request, report="NonTrustBookingReport")
def non_trust_booking_report(access_token):
    client = request.args.get("client")
    to_date = request.args.get("toDate")
    from_date = request.args.get("fromDate")
    currency = request.args.get("currency")
    page = int(request.args.get("page"))
    size = int(request.args.get("size"))

    if not client:
        return {"error": "client_id is required"}, 400

    response = reporting_service.fetch_non_trust_bookings_data(client, currency, from_date, to_date, page, size)
    return jsonify(response), 200


@reports_api.route("/non-trust-bookings/export", methods=["GET"])
@reports_access(request, report="NonTrustBookingReport")
def export_non_trust_booking_report(access_token):
    client = request.args.get("client")
    to_date = request.args.get("toDate")
    from_date = request.args.get("fromDate")
    currency = request.args.get("currency")

    if not client:
        return {"error": "client_id is required"}, 400

    response = reporting_service.fetch_non_trust_bookings(client, currency, from_date, to_date)

    data_list = []
    for index, data in enumerate(response, start=1):
        data["value"] = str(index)
        data["booking_date"] = change_date_format(data.get("booking_date"))
        data["departure_date"] = change_date_format(data.get("departure_date"))
        data["return_date"] = change_date_format(data.get("return_date"))
        data["payment_date"] = change_date_format(data.get("payment_date"))
        data_list.append(data)

    header_dict = OrderedDict(
        [
            ("value", "EntryNo"),
            ("entry_date", "EntryDate"),
            ("booking_ref", "BookingRef"),
            ("lead_pax", "LeadPax"),
            ("pax_count", "PaxCount"),
            ("departure_date", "DepartureDate"),
            ("return_date", "ReturnDate"),
            ("type", "Type"),
            ("bonding", "Bonding"),
            ("days_to_process", "DaysToProcess"),
            ("supplier_ref", "SupplierRef"),
            ("supplier_names", "SupplierNames"),
            ("booking_date", "BookingDate"),
            ("booking_status", "BookingStatus"),
            ("current_reservation", "CurrentReservationStatus"),
            ("last_status_update", "LastStatusUpdate"),
            ("total_bonded_value", "TotalBondedValue"),
            ("non_bonded_amount", "NonBondedAmount"),
            ("entry_type", "EntryType"),
            ("currency_code", "CurrencyCode"),
            ("payment_type", "PaymentType"),
            ("payment_date", "PaymentDate"),
            ("journal_id", "JournalID"),
            ("alloc_rec_id", "AllocRecID"),
            ("alloc_date", "AllocDate"),
            ("allocated_amount", "AllocatedAmount"),
            ("remaining_amount", "RemainingAmount"),
            ("bond_proportion", "BondProportion"),
            ("date_banking_file", "DateBankingFile"),
            ("ref_banking_file", "RefBankingFile"),
            ("bank_file_amount", "BankFileAmount"),
        ]
    )

    name = "NonTrustBookingReport"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/forty-percent", methods=["GET"])
@reports_access(request, report="FortyPercentReport")
def forty_percent_report(access_token):
    client_id = request.args.get("client")
    to_date = request.args.get("toDate")
    from_date = request.args.get("fromDate")

    if not client_id:
        return {"error": "client_id is required"}, 400

    response = reporting_service.fetch_forty_percent_data(client_id, from_date, to_date)
    return response, 200


@reports_api.route("/forty-percent/export", methods=["GET"])
@reports_access(request, report="FortyPercentReport")
def export_forty_percent_report(access_token):
    client_id = request.args.get("client")
    to_date = request.args.get("toDate")
    from_date = request.args.get("fromDate")
    balance = request.args.get("balance")

    if not client_id:
        return {"error": "client_id is required"}, 400
    if balance is not None:
        try:
            balance = int(balance)
        except ValueError:
            return {"error": "balance must be an integer"}, 400

    data = (reporting_service.fetch_forty_percent_data(client_id, from_date, to_date)).json
    file_name = "Rebalancing Report.xlsx"
    reporting_service.export_forty_percent_data(data, from_date, to_date, file_name, balance)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{file_name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/movement_of_funds/export", methods=["POST"])
@reports_access(request, report="BlueStyleReport")
def export_movement_of_funds(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.json
    if not data.get("currency"):
        raise ControllerException(message="missing required fields")
    currency = data.get("currency")
    from_date = data.get("fromDate")
    to_date = data.get("toDate")
    client_id = data.get("clientId")
    file_name = f"Movement of Funds {to_date}.xlsx"
    reporting_service.movement_of_funds_excel_file(client_id, currency, from_date, to_date, user_id, file_name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{file_name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/non_trust_bookings/export", methods=["POST"])
@reports_access(request, report="BlueStyleReport")
def export_non_trust_bookings(access_token):
    data = request.json
    client_id = data.get("clientId")
    from_date = data.get("fromDate")
    to_date = data.get("toDate")
    currency = data.get("currency")
    if not data.get("clientId"):
        raise ControllerException(message="missing required fields")
    file_name = f"Non Trust Bookings {to_date}.xlsx"
    reporting_service.non_trust_bookings_excel_file(client_id, currency, from_date, to_date, file_name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{file_name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/risk_exposure_breakdown/export", methods=["POST"])
@reports_access(request, report="BlueStyleReport")
def export_risk_exposure_breakdown(access_token):

    data = request.json
    client_id = data.get("clientId")
    from_date = data.get("fromDate")
    to_date = data.get("toDate")
    currency = data.get("currency")
    if not data.get("clientId") or not data.get("currency"):
        raise ControllerException(message="missing required fields")

    file_name = f"Risk Exposure Breakdown {to_date}.xlsx"
    reporting_service.exposure_breakdown_excel_file(client_id, currency, from_date, to_date, file_name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{file_name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@reports_api.route("/delayed_trust_funds/export", methods=["POST"])
@reports_access(request, report="BlueStyleReport")
def export_risk_exposure_graph(access_token):

    data = request.json
    client_id = data.get("clientId")
    from_date = data.get("fromDate")
    to_date = data.get("toDate")
    currency = data.get("currency")

    file_name = f"Delayed Trust Funds {to_date}.xlsx"
    reporting_service.delayed_trust_funds_excel_file(currency, from_date, to_date, client_id, file_name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{file_name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response
