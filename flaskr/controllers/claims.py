import sys
from collections import OrderedDict
from jose import jwt
from flaskr.helpers.date_util import change_date_format, find_day, find_time
from flask import Blueprint, current_app, jsonify, request, Response, send_file, make_response
from flaskr.controllers.exceptions import ControllerException
from flaskr.services.claims_service import claim_service
from flaskr.helpers.auth import ptt_sftp_user_required, ptt_user_required, user_required, client_access
from flaskr.services.auth_service import auth_service
from flaskr.services.reporting_service import reporting_service

claims_api = Blueprint("claims_api", __name__)


@claims_api.route("/upload/presigned-url", methods=["GET"])
@ptt_sftp_user_required()
def claim_create_presigned_url(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.args
    if not request.args.get("clientId") or not request.args.get("fileName"):
        raise ControllerException(message="Missing required details")
    if data.get("clientId"):
        client_access(user_id, data.get("clientId"))
    client_id = request.args["clientId"]
    file_name = request.args["fileName"]
    claim_from_tbr = True if request.args.get("claimFromTBR") == "true" else False
    response = claim_service.claim_create_presigned_url(client_id, file_name, claim_from_tbr, user_id)
    return response


@claims_api.route("/upload", methods=["POST"])
@ptt_sftp_user_required()
def claim_create(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.args
    if data.get("clientId"):
        client_access(user_id, data.get("client"))
    if not all(key in request.json for key in ["clientId", "fileName", "fileId", "claimFromTBR"]):
        raise ControllerException(message="Missing required details")
    client_id = request.json["clientId"]
    file_name = request.json["fileName"]
    file_id = request.json["fileId"]
    claim_from_tbr = request.json["claimFromTBR"]
    sftp = request.json["sftp"] if request.json.get("sftp") else False
    sftp_key = request.json["sftpKey"] if request.json.get("sftpKey") else ""
    claim_service.claim_create(client_id, file_name, file_id, claim_from_tbr, sftp, user_id, sftp_key)
    return Response(status=201)


@claims_api.route("/details/<claims_id>", methods=["GET"])
@user_required()
def claim_details(access_token, claims_id):
    details = claim_service.get_claim_details(claims_id)
    return jsonify(details), 200


@claims_api.route("/transaction/<claims_id>", methods=["GET"])
@user_required()
def claim_transaction(access_token, claims_id):
    query = request.args.get("query") or ""
    page = request.args.get("page")
    size = request.args.get("size")
    sort_key = request.args.get("sortKey") or "_id"
    sort_order = int(request.args.get("sortOrder") or 1)
    converted_sort_key_list = {
        "_id": "_id",
        "bookingRef": "booking_ref",
        "element": "element",
        "currencyCode": "currency_code",
        "status": "status",
        "amount": "amount",
    }
    if sort_key not in converted_sort_key_list.keys():
        raise ControllerException(message="invalid sort key")
    if sort_order not in [1, -1]:
        raise ControllerException(message="invalid sort order")
    converted_sort_key = converted_sort_key_list[sort_key]
    claim_transaction_list = claim_service.get_claim_transaction(
        claims_id, query, page, size, converted_sort_key, sort_order
    )
    return jsonify(claim_transaction_list)


@claims_api.route("/transaction/<claims_id>/export", methods=["GET"])
@ptt_user_required()
def export_claim_transaction(access_token, claims_id):
    query = request.args.get("query") or ""
    page = 1
    size = sys.maxsize
    sort_key = request.args.get("sortKey") or "_id"
    sort_order = int(request.args.get("sortOrder") or 1)
    converted_sort_key_list = {
        "_id": "_id",
        "bookingRef": "booking_ref",
        "element": "element",
        "currencyCode": "currency_code",
        "status": "status",
        "amount": "amount",
    }
    if sort_key not in converted_sort_key_list.keys():
        raise ControllerException(message="invalid sort key")
    if sort_order not in [1, -1]:
        raise ControllerException(message="invalid sort order")
    converted_sort_key = converted_sort_key_list[sort_key]
    claim_transaction = claim_service.get_claim_transaction(
        claims_id, query, page, size, converted_sort_key, sort_order
    )
    header_dict = OrderedDict(
        [
            ("bookingRef", "Booking Ref."),
            ("element", "Element"),
            ("count", "Claim Count"),
            ("duplicates", "Duplicates"),
            ("currencyCode", "Currency"),
            ("amount", "Amount"),
        ]
    )
    name = "Transactions"
    reporting_service.generate_excel_report(header_dict, claim_transaction["content"], name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@claims_api.route("/<check_type>/<claimId>", methods=["GET"])
@ptt_user_required()
def quick_check(access_token, check_type, claimId):
    query = request.args.get("query") or ""
    output_list = claim_service.get_check_type(check_type, claimId, query)

    return jsonify(output_list)


@claims_api.route("/<check_type>/<claimId>/export", methods=["GET"])
@ptt_user_required()
def export_quick_check(access_token, check_type, claimId):
    query = request.args.get("query") or ""
    data_list = claim_service.get_check_type(check_type, claimId, query)

    header_dict = OrderedDict(
        [
            ("bookingRef", "Booking Ref."),
            ("element", "Element Name"),
            ("count", "Claim Count"),
            ("duplicates", "Duplicate Element"),
            ("currencyCode", "Currency"),
            ("amount", "Amount"),
        ]
    )
    if check_type == "full-check":
        name = "FullChecks"
    elif check_type == "quick-check":
        name = "QuickChecks"
    else:
        name = "Checks"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@claims_api.route("/summary/<claims_id>", methods=["GET"])
@ptt_user_required()
def claim_summary(access_token, claims_id):
    query = request.args.get("query") or ""
    sort_key = request.args.get("sortKey") or "_id"
    sort_order = int(request.args.get("sortOrder") or 1)
    if sort_key not in ["_id", "element", "count", "minAmount", "maxAmount", "currency", "total"]:
        raise ControllerException(message="invalid sort key")
    if sort_order not in [1, -1]:
        raise ControllerException(message="invalid sort order")
    valid_claim_summary_list = claim_service.claim_summary(claims_id, query, sort_key, sort_order, is_deleted=False)
    void_claim_summary_list = claim_service.claim_summary(claims_id, query, sort_key, sort_order, is_deleted=True)
    return jsonify({"valid": valid_claim_summary_list, "void": void_claim_summary_list})


@claims_api.route("/summary/<claims_id>/export", methods=["GET"])
@ptt_user_required()
def export_claim_summary(access_token, claims_id):
    query = request.args.get("query") or ""
    sort_key = request.args.get("sortKey") or "_id"
    sort_order = int(request.args.get("sortOrder") or 1)
    if sort_key not in ["_id", "element", "count", "minAmount", "maxAmount", "currency", "total"]:
        raise ControllerException(message="invalid sort key")
    if sort_order not in [1, -1]:
        raise ControllerException(message="invalid sort order")

    claim_summary_list = claim_service.claim_summary(claims_id, query, sort_key, sort_order)
    claim_data = []
    for claim in claim_summary_list:
        x = {}
        x["element"] = f"Total for:{claim['currency']}"
        x["count"] = claim["totalCount"]
        x["total"] = claim["totalAmount"]
        claim["content"].append(x)
        for item in claim["content"]:
            claim_data.append(item)
    header_dict = OrderedDict(
        [
            ("element", "Element"),
            ("count", "Items"),
            ("minAmount", "Min Amount"),
            ("maxAmount", "Max Amount"),
            ("currency", "Currency"),
            ("total", "Total"),
        ]
    )
    name = "ClaimSummary"
    reporting_service.generate_excel_report(header_dict, claim_data, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@claims_api.route("/search/latest", methods=["POST"])
@user_required()
def claim_search_latest(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    data = request.args
    if data.get("client"):
        client_access(user_id, data.get("client"))
    claims = jwt.get_unverified_claims(access_token)
    body = request.json
    response = claim_service.claim_search_latest(user_id, body)
    return jsonify(response)


@claims_api.route("/search/latest/export", methods=["POST"])
@user_required()
def export_claim_search_latest(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    body = request.json
    body["page"] = 1
    body["size"] = int(sys.maxsize)
    response = claim_service.claim_search_latest(user_id, body)
    user_data = auth_service.ptt_users_list()
    data_list = []
    for data in response["content"]:
        data["submittedDate"] = change_date_format(data["submittedDate"][:10])
        data["claimDate"] = change_date_format(data["claimDate"])
        for i in data["items"]:
            data["currency"] = i["currency"]
            data["count"] = i["count"]
            data["amount"] = i["amount"]
            data["checks"] = i["checks"]
            data["%checks"] = i["percentageCheck"]
            data["%tests"] = i["percentageTotal"]
            data_list.append(data.copy())
    for item in data_list:
        user = next(filter(lambda x: x["userId"] == item["assignedTo"], user_data), {"name": ""})
        item["assignedTo"] = user["name"]
    header_dict = OrderedDict(
        [
            ("clientId", "Client Id"),
            ("clientName", "Client Name"),
            ("friendlyName", "Friendly Name"),
            ("claimDate", "Claim Date"),
            ("assignedTo", "Assigned To"),
            ("status", "Status"),
            ("count", "Count"),
            ("currency", "Currency"),
            ("amount", "Amount"),
            ("anomalies", "Anomalies"),
            ("checks", "Checks"),
            ("%checks", "% Checks"),
            ("%tests", "% Tests"),
            ("submittedDate", "Submitted"),
            ("notes", "Notes"),
        ]
    )
    name = "ClaimSearchLatest"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@claims_api.route("/search/summary", methods=["POST"])
@ptt_user_required()
def claim_search_summary(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    body = request.json
    response = claim_service.claim_search_summary(user_id, body)
    return jsonify(response)


def filter_response_by_trust_account(response):
    atol_standard = []
    non_travel_clients = []
    other_accounts = []

    for item in response:
        trust_account = item.get("trustAccount")
        if trust_account == "ATOL Escrow" or trust_account == "Escrow Trigger":
            atol_standard.append(item)
        elif trust_account == "Non-Flight PTR 2018":
            non_travel_clients.append(item)
        elif (
            trust_account != "ATOL Escrow"
            or trust_account != "Non-Flight PTR 2018"
            or trust_account != "Escrow Trigger"
        ):
            other_accounts.append(item)

    return atol_standard, non_travel_clients, other_accounts


@claims_api.route("/search/summary/export", methods=["POST"])
@ptt_user_required()
def export_claim_search_summary(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]

    body = request.json
    body["page"] = 1
    body["size"] = int(sys.maxsize)

    response = claim_service.claim_search_summary(user_id, body)
    data_list = []
    for data in response["content"]:
        data["claimindate"] = change_date_format(data["submittedDate"][:10])
        data["claiminday"] = find_day(data["submittedDate"])
        data["claimintime"] = find_time(data["submittedDate"])

        for i in data["items"]:
            data["currency"] = i["currency"]
            data["amount"] = i["amount"]
            data_list.append(data.copy())

    atol_standard_data_list, non_travel_clients_data_list, other_accounts_data_list = filter_response_by_trust_account(
        data_list
    )
    name = "ClaimSearchSummary"
    reporting_service.generate_excel_report_claim_search_summary(
        atol_standard_data_list, non_travel_clients_data_list, other_accounts_data_list, name
    )
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@claims_api.route("/update", methods=["PUT"])
@ptt_user_required()
def update_claims_metadata(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    claims_id = request.json.get("claimId")
    notes = request.json.get("notes")
    status = request.json.get("status")
    assigned_to = request.json.get("assignedTo")
    frequency = request.json.get("frequency")
    if claims_id and (notes or status or assigned_to or frequency):
        claim_service.update_claims_metadata(request.json, user_id)
    else:
        raise ControllerException(message="Missing required details")
    return Response(status=200)


@claims_api.route("/automated-transaction/<claims_id>", methods=["GET"])
@ptt_user_required()
def claim_automated_transaction(access_token, claims_id):
    query = request.args.get("query") or ""
    claim_automated_transaction_list = claim_service.get_claim_automated_transaction(claims_id, query)
    return jsonify(claim_automated_transaction_list)


@claims_api.route("/automated-transaction/<claims_id>/export", methods=["GET"])
@ptt_user_required()
def export_claim_automated_transaction(access_token, claims_id):
    query = request.args.get("query") or ""
    claim_automated_transaction_list = claim_service.get_claim_automated_transaction(claims_id, query)
    data_list = claim_automated_transaction_list["transactions"]

    header_dict = OrderedDict(
        [
            ("bookingRef", "Booking Ref."),
            ("element", "Element"),
            ("count", "Claim Count"),
            ("duplicates", "Duplicates"),
            ("currencyCode", "Currency"),
            ("amount", "Amount"),
            ("description", "Description"),
        ]
    )
    name = "AutomatedTransactions"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@claims_api.route("/transaction/<transaction_id>", methods=["PUT"])
@ptt_user_required()
def update_claim_transaction(access_token, transaction_id):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    if (
        "bookingRef" in request.json.keys()
        or {"check", "status"} <= request.json.keys()
        or {"check", "amount"} <= request.json.keys()
    ):
        raise ControllerException(message="Booking Ref is not editable")
    claim_service.update_claim_transaction(transaction_id, request.json, user_id)
    return Response(status=200)


@claims_api.route("/anomalies/<claims_id>", methods=["GET"])
@ptt_user_required()
def claim_anomalies(access_token, claims_id):
    query = request.args.get("query") or ""
    page = request.args.get("page")
    size = request.args.get("size")
    sort_key = request.args.get("sortKey") or "createdAt"
    sort_order = int(request.args.get("sortOrder") or -1)
    converted_sort_key_list = {
        "createdAt": "created_at",
        "bookingRef": "_id.booking_ref",
        "bookingDate": "trust.booking_date",
        "dateOfTravel": "trust.departure_date",
        "dateOfReturn": "trust.return_date",
        "count": "count",
        "balanceInTrust": "trust.balance",
        "anomalyType": "_id.anomaly_type",
        "status": "status",
        "totalClaim": "amount",
        "leadPassenger": "trust.lead_pax",
    }
    if sort_key not in converted_sort_key_list.keys():
        raise ControllerException(message="invalid sort key")
    if sort_order not in [1, -1]:
        raise ControllerException(message="invalid sort order")
    converted_sort_key = converted_sort_key_list[sort_key]
    claim_anomaly = claim_service.get_claim_anomalies(claims_id, query, page, size, converted_sort_key, sort_order)
    return jsonify(claim_anomaly)


@claims_api.route("/anomalies/<claims_id>/export", methods=["GET"])
@ptt_user_required()
def export_claim_anomalies(access_token, claims_id):
    query = request.args.get("query") or ""
    page = 1
    size = sys.maxsize
    sort_key = request.args.get("sortKey") or "createdAt"
    sort_order = int(request.args.get("sortOrder") or -1)
    converted_sort_key_list = {
        "createdAt": "created_at",
        "bookingRef": "_id.booking_ref",
        "bookingDate": "trust.booking_date",
        "dateOfTravel": "trust.departure_date",
        "dateOfReturn": "trust.return_date",
        "count": "count",
        "balanceInTrust": "trust.balance",
        "anomalyType": "_id.anomaly_type",
        "status": "status",
        "totalClaim": "amount",
        "leadPassenger": "trust.lead_pax",
    }
    if sort_key not in converted_sort_key_list.keys():
        raise ControllerException(message="invalid sort key")
    if sort_order not in [1, -1]:
        raise ControllerException(message="invalid sort order")
    converted_sort_key = converted_sort_key_list[sort_key]
    claim_anomaly = claim_service.get_claim_anomalies(claims_id, query, page, size, converted_sort_key, sort_order)
    data_list = []
    for data in claim_anomaly["content"]:
        data["bookingDate"] = change_date_format(data["bookingDate"])
        data["dateOfReturn"] = change_date_format(data["dateOfReturn"])
        data["dateOfTravel"] = change_date_format(data["dateOfTravel"])
        data["currency_code"] = ", ".join(data["currency_code"])
        data_list.append(data)
    header_dict = OrderedDict(
        [
            ("bookingRef", "Booking Ref."),
            ("bookingDate", "Booking Date"),
            ("dateOfTravel", "Date of Travel"),
            ("dateOfReturn", "Return Date"),
            ("currency_code", "Currency"),
            ("balanceInTrust", "Balance in Trust"),
            ("anomalyType", "Anomaly Type"),
            ("status", "Status"),
            ("count", "Count"),
            ("totalClaim", "Total Claim"),
        ]
    )
    name = "ClaimAnomalies"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@claims_api.route("/get-transaction/<transaction_id>", methods=["GET"])
@user_required()
def claim_get_transaction(access_token, transaction_id):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    claim_get_transaction_list = claim_service.claim_get_transaction(transaction_id, user_id)
    return jsonify(claim_get_transaction_list)


@claims_api.route("/claim-testing", methods=["POST"])
@ptt_user_required()
def claim_testing(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    body = request.json
    response = claim_service.claim_testing(user_id, body)
    return jsonify(response)


@claims_api.route("/claim-testing/export", methods=["POST"])
@ptt_user_required()
def export_claim_testing(access_token):
    claims = jwt.get_unverified_claims(access_token)
    user_id = claims["sub"]
    body = request.json
    body["page"] = 1
    body["size"] = sys.maxsize
    response = claim_service.claim_testing(user_id, body)
    data_list = []
    for data in response["content"]:
        for item in data["items"]:
            data["currency"] = item["currency"]
            data["revisedClaim"] = item["revisedClaim"]
            data["originalClaim"] = item["originalClaim"]
            data_list.append(data.copy())
    header_dict = OrderedDict(
        [
            ("clientId", "Client ID"),
            ("clientName", "Client Name"),
            ("fileDate", "File Date"),
            ("currency", "currency"),
            ("originalClaim", "Original Claim"),
            ("revisedClaim", "Revised Claim"),
            ("reasons", "Reasons"),
        ]
    )
    name = "ClaimTesting"
    reporting_service.generate_excel_report(header_dict, data_list, name)
    response = make_response(send_file(f"{current_app.config['TEMP_DIR']}/{name}"))
    response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    return response


@claims_api.route("/claim-testing/<claims_id>", methods=["PUT"])
@ptt_user_required()
def update_claim_testing(access_token, claims_id):
    if claims_id:
        claim_service.update_claim_testing(claims_id, request.json)
    else:
        raise ControllerException(message="Missing required details")
    return Response(status=200)


@claims_api.route("/search/summary_escrow", methods=["POST"])
@ptt_user_required()
def claim_escrow_search_summary(access_token):
    body = request.json
    response = claim_service.claim_escrow_search_summary(body)
    return jsonify(response)


@claims_api.route("/edit_notes/<claims_id>/<file_id>", methods=["PUT"])
def edit_notes(claims_id, file_id):
    new_notes = request.json.get("notes")
    response = claim_service.update_notes(claims_id, file_id, new_notes)
    return response
