from flask import Blueprint, jsonify, current_app, request
from flaskr.helpers.auth import _verify_claims, get_token, ptt_powerbi_admin_required, user_required
from flaskr.services.powerbi_service import powerbi_service
from jose import jwt
import traceback

powerbi_api = Blueprint("powerbi_api", __name__)


@powerbi_api.route("/dashboard/embed-info", methods=["GET"])
def get_dashboard_embed_info():
    """
    Get PowerBI dashboard embed information
    """
    try:
        try:
            access_token = get_token(request.headers.get("Authorization"))
            claims = jwt.get_unverified_claims(access_token)
            user_id = claims["sub"]
        except Exception as e:
            return jsonify({"error": f"Authentication error: {str(e)}"}), 500
        # Check if ERV-specific report is requested
        report_type = request.args.get("report_type")
        if report_type == "erv":
            try:
                has_erv_access = _verify_claims(access_token, ["ptt-erv-dashboard"])
                has_admin_access = _verify_claims(access_token, ["ptt-admin"])
                has_powerbi_admin_access = _verify_claims(access_token, ["ptt-powerbi-admin"])
                if not (has_erv_access or has_admin_access or has_powerbi_admin_access):
                    return jsonify(msg="ERV dashboard access restricted to ERV users and admins"), 403

                workspace_id = current_app.config.get("POWERBI_WORKSPACE_ID")
                # Get ERV dashboard configuration from database
                erv_config = powerbi_service.get_erv_powerbi_dashboard_config()
                if erv_config:
                    report_id = erv_config["report_id"]
                else:
                    # Fallback to environment config if database config not found
                    report_id = current_app.config.get("POWERBI_ERV_REPORT_ID")
            except Exception as e:
                return jsonify({"error": f"Authorization error: {str(e)}"}), 500
        else:
            try:
                # has_powerbi_admin = _verify_claims(access_token, ["ptt-powerbi-admin"])
                # if not has_powerbi_admin:
                #     return jsonify(msg="PowerBI dashboard access restricted to PowerBI admins"), 403

                workspace_id = current_app.config.get("POWERBI_WORKSPACE_ID")
                report_id = request.args.get("reportId")
                if report_id is None:
                    report_id = current_app.config.get("POWERBI_REPORT_ID")
            except Exception as e:
                return jsonify({"error": f"Authorization error: {str(e)}"}), 500

        current_app.logger.info(f"Using workspace_id: {workspace_id}, report_id: {report_id}")

        if not workspace_id or not report_id:
            error_msg = "PowerBI configuration not found"
            current_app.logger.error(error_msg)
            return jsonify({"error": error_msg}), 500

        # Get report details and embed token
        try:
            current_app.logger.info("Getting report embed info...")
            embed_info = powerbi_service.get_report_embed_info(report_id, workspace_id, user_id)

            return jsonify(
                {
                    "embedUrl": embed_info.get("embedUrl"),
                    "embedToken": embed_info.get("token"),
                    "reportId": report_id,
                    "workspaceId": workspace_id,
                }
            )
        except Exception as e:
            current_app.logger.error(f"Error getting embed info: {str(e)}")
            return jsonify({"error": f"Error getting PowerBI embed info: {str(e)}"}), 500

    except Exception as e:
        error_msg = f"Error getting PowerBI dashboard info: {str(e)}\n{traceback.format_exc()}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/reports", methods=["GET"])
@ptt_powerbi_admin_required()
def get_workspace_reports(access_token):
    """
    Get all reports available in the PowerBI workspace
    """
    try:
        workspace_id = request.args.get("workspace_id") or current_app.config.get("POWERBI_WORKSPACE_ID")
        if not workspace_id:
            error_msg = "PowerBI workspace ID not provided and not configured"
            return jsonify({"error": error_msg}), 400

        # Get all reports in the workspace
        reports = powerbi_service.get_workspace_reports(workspace_id)
        return jsonify({"reports": reports})
    except Exception as e:
        error_msg = f"Error getting PowerBI reports: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/assign-powerbi-reports", methods=["PUT"])
@ptt_powerbi_admin_required()
def update_user_powerbi_reports(access_token):
    """
    Assign or remove PowerBI reports for a user
    """
    try:
        data = request.json
        if not data.get("userId") or not isinstance(data.get("pbiReports"), list):
            return jsonify({"error": "Missing userId or invalid pbiReports format"}), 400
            
        user_id = data["userId"]
        pbi_reports = data["pbiReports"]
        
        # Update user's PowerBI reports in database
        result = powerbi_service.update_user_powerbi_reports(user_id, pbi_reports)
        return jsonify({"success": True, "message": "User PowerBI reports updated successfully"}), 200
    except Exception as e:
        error_msg = f"Error updating user PowerBI reports: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/assigned-powerbi-reports/<user_id>", methods=["GET"])
@ptt_powerbi_admin_required()
def get_user_powerbi_reports(access_token, user_id):
    """
    Get PowerBI reports assigned to a user
    """
    try:
        if not user_id:
            return jsonify({"error": "User ID is required"}), 400
            
        # Get user's PowerBI reports from database
        reports = powerbi_service.get_user_powerbi_reports(user_id)
        return jsonify({"reports": reports}), 200
    except Exception as e:
        error_msg = f"Error retrieving user PowerBI reports: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/assigned-powerbi-reports", methods=["GET"])
@user_required()
def get_my_powerbi_reports(access_token):
    """
    Get PowerBI reports assigned to the logged-in user
    """
    try:
        claims = jwt.get_unverified_claims(access_token)
        user_id = claims["sub"]
            
        # Get user's PowerBI reports from database
        reports = powerbi_service.get_user_powerbi_reports(user_id)
        return jsonify({"reports": reports}), 200
    except Exception as e:
        error_msg = f"Error retrieving user PowerBI reports: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500

@powerbi_api.route("/erv-powerbi-dashboard-config", methods=["PUT"])
@ptt_powerbi_admin_required()
def update_erv_dashboard_config(access_token):
    """
    Update ERV Powerbi dashboard configuration
    """
    try:
        data = request.json
        if not data.get("reportId") or not data.get("reportName"):
            return jsonify({"error": "Missing reportId or reportName"}), 400
            
        report_id = data["reportId"]
        report_name = data["reportName"]
        
        # Update ERV dashboard configuration in database
        powerbi_service.update_erv_dashboard_config(report_id, report_name)
        return jsonify({"success": True, "message": "ERV Powerbi dashboard configuration updated successfully"}), 200
    except Exception as e:
        error_msg = f"Error updating ERV dashboard configuration: {str(e)}"
        return jsonify({"error": error_msg}), 500

@powerbi_api.route("/erv-powerbi-dashboard-config", methods=["GET"])
@ptt_powerbi_admin_required()
def get_erv_powerbi_dashboard_config(access_token):
    try:
        # Get ERV dashboard configuration from database
        config = powerbi_service.get_erv_powerbi_dashboard_config()
        return jsonify({"config": config}), 200
    except Exception as e:
        error_msg = f"Error retrieving ERV dashboard configuration: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500
