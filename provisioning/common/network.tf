data "aws_availability_zones" "azs" {
}

resource "aws_vpc" "ptt-vpc" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = merge(local.tags, {
    Name = local.vpc_name
  })

  lifecycle {
    create_before_destroy = true
  }
}

data "aws_vpc_peering_connection" "pc" {
  peer_vpc_id = aws_vpc.ptt-vpc.id
}

resource "aws_internet_gateway" "ptt-igw" {
  vpc_id = aws_vpc.ptt-vpc.id

  tags = merge(local.tags, {
    Name = local.igw_name
  })
}

resource "aws_route_table" "ptt-rt-public" {
  vpc_id = aws_vpc.ptt-vpc.id

  route {
    cidr_block = var.rt_wide_route
    gateway_id = aws_internet_gateway.ptt-igw.id
  }

  route {
    cidr_block                = data.aws_vpc_peering_connection.pc.cidr_block
    vpc_peering_connection_id = data.aws_vpc_peering_connection.pc.id
  }

  tags = merge(local.tags, {
    Name = local.public_rt_name
  })
}

resource "aws_eip" "ptt-eip" {
  count = 2
  vpc   = true
}

resource "aws_nat_gateway" "ptt-nat-gateway" {
  count         = 2
  allocation_id = aws_eip.ptt-eip[count.index].id
  subnet_id     = aws_subnet.ptt-public-subnets.*.id[count.index]
  tags = {
    "Name" = "DummyNatGateway"
  }
}

resource "aws_route_table" "ptt-rt-private" {
  count  = 2
  vpc_id = aws_vpc.ptt-vpc.id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.ptt-nat-gateway[count.index].id
  }

  route {
    cidr_block                = data.aws_vpc_peering_connection.pc.cidr_block
    vpc_peering_connection_id = data.aws_vpc_peering_connection.pc.id
  }

  tags = merge(local.tags, {
    Name = local.private_rt_name
  })
}

resource "aws_subnet" "ptt-public-subnets" {
  count                   = 2
  cidr_block              = var.public_cidrs[count.index]
  vpc_id                  = aws_vpc.ptt-vpc.id
  map_public_ip_on_launch = true
  availability_zone       = data.aws_availability_zones.azs.names[count.index]

  tags = merge(local.tags, {
    Name = "${local.public_subnet_name}-${count.index + 1}"
  })
}

resource "aws_subnet" "ptt-private-subnets" {
  count             = 2
  cidr_block        = var.private_cidrs[count.index]
  availability_zone = data.aws_availability_zones.azs.names[count.index]
  vpc_id            = aws_vpc.ptt-vpc.id

  tags = merge(local.tags, {
    Name = "${local.private_subnet_name}-${count.index + 1}"
  })
}

resource "aws_route_table_association" "ptt-public-rt-assc" {
  count          = 2
  route_table_id = aws_route_table.ptt-rt-public.id
  subnet_id      = aws_subnet.ptt-public-subnets.*.id[count.index]
}

resource "aws_route_table_association" "ptt-private-rt-assc" {
  count          = 2
  route_table_id = aws_route_table.ptt-rt-private.*.id[count.index]
  subnet_id      = aws_subnet.ptt-private-subnets.*.id[count.index]
}

resource "aws_security_group" "ptt-public-sg" {
  name        = local.sg_name
  description = "access to public instances"
  vpc_id      = aws_vpc.ptt-vpc.id
}

resource "aws_security_group" "ptt-vpc-endpoint-sg" {
  name        = "ptt-vpc-endpoint-sg-${var.env}"
  description = "Access to vpc endpoints"
  vpc_id      = aws_vpc.ptt-vpc.id

  ingress {
    description = "TLS from VPC"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [aws_vpc.ptt-vpc.cidr_block]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [aws_vpc.ptt-vpc.cidr_block]
  }
}

resource "aws_vpc_endpoint" "vpc-endpoint-s3" {
  vpc_id       = aws_vpc.ptt-vpc.id
  service_name = "com.amazonaws.${var.aws_region}.s3"
  tags = merge(local.tags, {
    "Name" : "s3-endpoint-${var.env}"
  })
}

resource "aws_vpc_endpoint_route_table_association" "ptt-public-vpc-endpoint-s3-rt-assc" {
  route_table_id  = aws_route_table.ptt-rt-public.id
  vpc_endpoint_id = aws_vpc_endpoint.vpc-endpoint-s3.id
}

resource "aws_vpc_endpoint_route_table_association" "ptt-private-vpc-endpoint-s3-rt-assc" {
  count           = 2
  route_table_id  = aws_route_table.ptt-rt-private.*.id[count.index]
  vpc_endpoint_id = aws_vpc_endpoint.vpc-endpoint-s3.id
}

resource "aws_vpc_endpoint" "vpc-endpoint-secretsmanager" {
  vpc_id            = aws_vpc.ptt-vpc.id
  service_name      = "com.amazonaws.${var.aws_region}.secretsmanager"
  vpc_endpoint_type = "Interface"

  subnet_ids = aws_subnet.ptt-private-subnets[*].id

  security_group_ids = [aws_security_group.ptt-vpc-endpoint-sg.id]

  private_dns_enabled = true

  tags = merge(local.tags, {
    "Name" : "secretsmanager-endpoint-${var.env}"
  })
}

resource "aws_vpc_endpoint" "vpc-endpoint-states" {
  vpc_id            = aws_vpc.ptt-vpc.id
  service_name      = "com.amazonaws.${var.aws_region}.states"
  vpc_endpoint_type = "Interface"

  subnet_ids = aws_subnet.ptt-private-subnets[*].id

  security_group_ids = [aws_security_group.ptt-vpc-endpoint-sg.id]

  private_dns_enabled = true

  tags = merge(local.tags, {
    "Name" : "states-endpoint-${var.env}"
  })
}

resource "aws_vpc_endpoint" "vpc-endpoint-ecr-api" {
  vpc_id            = aws_vpc.ptt-vpc.id
  service_name      = "com.amazonaws.${var.aws_region}.ecr.api"
  vpc_endpoint_type = "Interface"

  subnet_ids = aws_subnet.ptt-private-subnets[*].id

  security_group_ids = [aws_security_group.ptt-vpc-endpoint-sg.id]

  private_dns_enabled = true

  tags = merge(local.tags, {
    "Name" : "ecr-api-endpoint-${var.env}"
  })
}

resource "aws_vpc_endpoint" "vpc-endpoint-ecr-dkr" {
  vpc_id            = aws_vpc.ptt-vpc.id
  service_name      = "com.amazonaws.${var.aws_region}.ecr.dkr"
  vpc_endpoint_type = "Interface"

  subnet_ids = aws_subnet.ptt-private-subnets[*].id

  security_group_ids = [aws_security_group.ptt-vpc-endpoint-sg.id]

  private_dns_enabled = true

  tags = merge(local.tags, {
    "Name" : "ecr-dkr-endpoint-${var.env}"
  })
}

resource "aws_vpc_endpoint" "vpc-endpoint-logs" {
  vpc_id            = aws_vpc.ptt-vpc.id
  service_name      = "com.amazonaws.${var.aws_region}.logs"
  vpc_endpoint_type = "Interface"

  subnet_ids = aws_subnet.ptt-private-subnets[*].id

  security_group_ids = [aws_security_group.ptt-vpc-endpoint-sg.id]

  private_dns_enabled = true

  tags = merge(local.tags, {
    "Name" : "logs-endpoint-${var.env}"
  })
}

resource "aws_vpc_endpoint" "vpc-endpoint-sqs" {
  vpc_id            = aws_vpc.ptt-vpc.id
  service_name      = "com.amazonaws.${var.aws_region}.sqs"
  vpc_endpoint_type = "Interface"

  subnet_ids = aws_subnet.ptt-private-subnets[*].id

  security_group_ids = [aws_security_group.ptt-vpc-endpoint-sg.id]

  private_dns_enabled = true

  tags = merge(local.tags, {
    "Name" : "sqs-endpoint-${var.env}"
  })
}

resource "aws_apigatewayv2_vpc_link" "ptt-api-vpc-link" {
  name               = "vpc-link-${var.env}"
  security_group_ids = []
  subnet_ids         = aws_subnet.ptt-private-subnets.*.id

  tags = local.tags
}
