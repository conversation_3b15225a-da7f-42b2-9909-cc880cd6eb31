resource "aws_security_group" "ptt-alb-sg" {
  name        = local.alb_sg_name
  description = "control access to the application load balancer"
  vpc_id      = aws_vpc.ptt-vpc.id

  ingress {
    from_port   = 443
    protocol    = "TCP"
    to_port     = 443
    cidr_blocks = var.private_cidrs
  }

  egress {
    from_port = 0
    protocol  = "-1"
    to_port   = 0
    cidr_blocks = [
    "0.0.0.0/0"]
  }

  tags = merge(local.tags, {
    Name = local.alb_sg_name
  })
}

resource "aws_security_group" "ptt-ecs-sg" {
  name        = local.sg_ecs_name
  description = "control access to the ecs cluster"
  vpc_id      = aws_vpc.ptt-vpc.id

  ingress {
    from_port = var.app_port
    protocol  = "TCP"
    to_port   = var.app_port
    security_groups = [
    aws_security_group.ptt-alb-sg.id]
  }

  egress {
    protocol  = "-1"
    from_port = 0
    to_port   = 0
    cidr_blocks = [
    "0.0.0.0/0"]
  }

  tags = merge(local.tags, {
    Name = local.sg_ecs_name
  })
}

resource "aws_alb" "ptt-alb" {
  name               = local.alb_name
  internal           = true
  load_balancer_type = "application"
  subnets            = aws_subnet.ptt-private-subnets.*.id
  security_groups = [
  aws_security_group.ptt-alb-sg.id]

  tags = merge(local.tags, {
    Name = local.alb_name
  })
}

resource "aws_alb_target_group" "ptt-alb-target-group" {
  name        = local.alb_target_group_name
  port        = 80
  protocol    = "HTTP"
  vpc_id      = aws_vpc.ptt-vpc.id
  target_type = "ip"

  health_check {
    path                = "/"
    healthy_threshold   = 5
    unhealthy_threshold = 2
    timeout             = 20
    interval            = 45
    matcher             = "200"
  }

  tags = merge(local.tags, {
    Name = local.alb_target_group_name
  })
}

resource "aws_alb_listener" "ptt-alb-listener" {
  load_balancer_arn = aws_alb.ptt-alb.arn
  port              = 443
  protocol          = "HTTPS"
  certificate_arn   = var.certificate_arn

  default_action {
    target_group_arn = aws_alb_target_group.ptt-alb-target-group.arn
    type             = "forward"
  }

  tags = local.tags

}
