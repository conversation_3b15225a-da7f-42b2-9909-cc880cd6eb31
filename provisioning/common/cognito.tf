data "aws_lambda_function" "cognito_custom_email_lambda" {
  function_name = local.cognito_custom_email_lambda
}



resource "aws_cognito_user_pool" "pool" {
  name = local.cognito_user_pool

  tags = merge(local.tags, {
    Name = local.cognito_user_pool
  })

  mfa_configuration = "OPTIONAL"

  schema {
    attribute_data_type      = "String"
    developer_only_attribute = false
    mutable                  = true
    name                     = "designation"
    required                 = false

    string_attribute_constraints {}
  }

  software_token_mfa_configuration {
    enabled = true
  }

  account_recovery_setting {
    recovery_mechanism {
      name     = "verified_email"
      priority = 1
    }

    recovery_mechanism {
      name     = "verified_phone_number"
      priority = 2
    }
  }

  lambda_config {
    custom_message = data.aws_lambda_function.cognito_custom_email_lambda.arn
  }
}

resource "aws_lambda_permission" "allow_execution_from_user_pool" {
  statement_id  = "AllowExecutionFromUserPool"
  action        = "lambda:InvokeFunction"
  function_name = local.cognito_custom_email_lambda
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.pool.arn
}


resource "aws_cognito_user_pool_client" "client" {
  name                   = "app-client"
  user_pool_id           = aws_cognito_user_pool.pool.id
  explicit_auth_flows    = ["ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_PASSWORD_AUTH"]
  access_token_validity  = 1
  id_token_validity      = 1
  refresh_token_validity = 30

  token_validity_units {
    access_token  = "hours"
    id_token      = "hours"
    refresh_token = "days"
  }
}
