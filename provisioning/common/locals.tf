locals {
  vpc_name            = "ptt-vpc-${var.env}"
  igw_name            = "ptt-igw-${var.env}"
  public_rt_name      = "ptt-public-rt-${var.env}"
  private_rt_name     = "ptt-private-rt-${var.env}"
  public_subnet_name  = "ptt-public-subnet-${var.env}"
  private_subnet_name = "ptt-private-subnet-${var.env}"
  db_subnet_name      = "ptt-documentdb-subnet-${var.env}"
  sg_name             = "ptt-public-sg-${var.env}"

  nlb_name              = "ptt-nlb-${var.env}"
  alb_name              = "ptt-alb-${var.env}"
  alb_sg_name           = "ptt-alb-sg-${var.env}"
  sg_ecs_name           = "ptt-app-ecs-from-alb-${var.env}"
  alb_target_group_name = "ptt-alb-to-ecs-tg-${var.env}"
  ecs_cluster_name      = "ptt-backend-service-cluster-${var.env}"
  ecs_service_name      = "ptt-backend-service-${var.env}"
  cloudwatch_group      = "ptt-backend-${var.env}"
  api_gateway_name      = "ptt-api-gateway-${var.env}"

  secret_name = var.secret_name

  cognito_user_pool                    = "ptt-user-pool-${var.env}"
  banking_files_bucket                 = "ptt-banking-files-${var.env}"
  insurance_files_bucket               = "ptt-client-insurance-${var.env}"
  atol_files_bucket                    = "ptt-client-atol-${var.env}"
  bond_files_bucket                    = "ptt-client-bond-${var.env}"
  claim_files_bucket                   = "ptt-claim-files-${var.env}"
  client_insurance_suppliers_bucket    = "ptt-client-insurance-suppliers-${var.env}"
  file_template_bucket                 = "ptt-file-template-${var.env}"
  user_profile_bucket                  = "ptt-user-profile-pics-${var.env}"
  reports_bucket                       = "ptt-reports-${var.env}"
  sftp_bucket                          = "ptt-sftp-${var.env}"
  sftp_to_ptt_s3_bucket                = "sftp-to-ptt-${var.env}"
  cognito_custom_email_lambda          = "cognito-custom-email-lambda-${var.env}"
  reports_queue                        = "ptt-reports-queue-${var.env}"
  wafv2_rule_group                     = "ptt-wafv2-rule-group-${var.env}"
  wafv2_rule                           = "ptt-geo-restriction-rule-${var.env}"
  banking_and_claim_summary_bucket     = "ptt-banking-and-claim-summary-bucket-${var.env}"
  internal_audit_bucket                = "ptt-internal-audit-${var.env}"
  authorized_signatories_bucket        = "ptt-authorized-signatories-pics-${var.env}"
  manuals_bucket                       = "ptt-client-manuals-${var.env}"
  erv_dashboard_graphs_snapshot_bucket = "ptt-erv-dashboard-graphs-snapshot-${var.env}"

  banking_template_path = "../../../docs/assets/templates/banking.xlsx"
  claim_template_path   = "../../../docs/assets/templates/claim.xlsx"
  default_picture_path  = "../../../docs/assets/profile/default.jfif"

  web_app_url  = "https://${var.env}.pttapp.com"
  cors_origins = var.env == "dev" ? ["*"] : [local.web_app_url]

  tags = {
    env         = var.env,
    Application = "ptt-backend-service"
  }
}
