variable "env" {
}

variable "ecs_task_cpu" {
  default = 512
}

variable "ecs_task_memory" {
  default = 2048
}

variable "ecs_task_desired_count" {
}

variable "aws_region" {
  default = "eu-west-2"
}

variable "vpc_cidr" {
}

variable "rt_wide_route" {
  default = "0.0.0.0/0"
}

variable "public_cidrs" {
}

variable "private_cidrs" {
}

variable "app_port" {
  default = 5000
}

variable "app_image" {
}

variable "logs_retention_in_days" {
  default = 30
}

variable "certificate_arn" {
}

variable "app_version" {}
variable "secret_name" {}

variable "major_travel" {}
variable "anglia_tours" {}
variable "wst_travel" {}
variable "we_love_holidays" {}
variable "swoop" {}
variable "swoop_travel" {}
variable "nas" {}
variable "ts" {}
variable "itgp" {}
variable "inte" {}
variable "gtl" {}
variable "turq" {}
variable "hobe" {}
variable "flypop" {}
variable "barrhead" {}
variable "hays" {}
variable "sunshine" {}
variable "broadway" {}
variable "iglu_escrow" {}
variable "caledonian" {}
variable "travel_republic" {}
variable "tct" {}
variable "tdc" {}
variable "est" {}
variable "nst" {}
variable "pgl" {}
variable "wlh_new" {}
variable "pennywood" {}
variable "bluestyle" {}
variable "kafka_bootstrap_servers" {}
variable "powerbi_client_id" {}
variable "powerbi_client_secret" {}
variable "powerbi_tenant_id" {}
variable "powerbi_workspace_id" {}
variable "powerbi_report_id" {}
variable "powerbi_erv_report_id" {}
variable "ecs_service_max_capacity" {
  default = 12
}
variable "ecs_service_min_capacity" {
  default = 4
}
