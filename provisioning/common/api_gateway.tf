resource "aws_apigatewayv2_api" "ptt-api" {
  name                         = local.api_gateway_name
  protocol_type                = "HTTP"
  disable_execute_api_endpoint = true

  cors_configuration {
    allow_origins = local.cors_origins
    allow_headers = ["*"]
    allow_methods = ["*"]
  }

  tags = local.tags
}

resource "aws_apigatewayv2_integration" "ptt-api-integration" {
  api_id             = aws_apigatewayv2_api.ptt-api.id
  integration_type   = "HTTP_PROXY"
  connection_id      = aws_apigatewayv2_vpc_link.ptt-api-vpc-link.id
  connection_type    = "VPC_LINK"
  description        = "VPC integration"
  integration_method = "ANY"
  integration_uri    = aws_alb_listener.ptt-alb-listener.arn
  request_parameters = {
    "append:header.clientIp" = "$context.identity.sourceIp"
  }

  tls_config {
    server_name_to_verify = aws_apigatewayv2_domain_name.ptt-api-domain-name.domain_name
  }
}

resource "aws_apigatewayv2_authorizer" "ptt-api-authorizer" {
  api_id           = aws_apigatewayv2_api.ptt-api.id
  authorizer_type  = "JWT"
  identity_sources = ["$request.header.Authorization"]
  name             = "cognito-authorizer"

  jwt_configuration {
    audience = [aws_cognito_user_pool_client.client.id]
    issuer   = "https://${aws_cognito_user_pool.pool.endpoint}"
  }
}

resource "aws_apigatewayv2_route" "route_without_authorizer" {
  for_each  = toset(["OPTIONS /{proxy+}", "GET /", "POST /api/login", "POST /api/forgot-password", "POST /api/confirm-forgot-password", "POST /api/respond-to-auth-challenge", "POST /api/respond-to-software-token-mfa-challenge", "POST /api/respond-to-sms-mfa-challenge", "POST /api/refresh"])
  api_id    = aws_apigatewayv2_api.ptt-api.id
  route_key = each.value
  target    = "integrations/${aws_apigatewayv2_integration.ptt-api-integration.id}"
}

resource "aws_apigatewayv2_route" "default_route" {
  api_id             = aws_apigatewayv2_api.ptt-api.id
  route_key          = "$default"
  target             = "integrations/${aws_apigatewayv2_integration.ptt-api-integration.id}"
  authorization_type = "JWT"
  authorizer_id      = aws_apigatewayv2_authorizer.ptt-api-authorizer.id
}

resource "aws_apigatewayv2_stage" "default_stage" {
  api_id      = aws_apigatewayv2_api.ptt-api.id
  name        = "$default"
  auto_deploy = true

  tags = local.tags
}

resource "aws_apigatewayv2_domain_name" "ptt-api-domain-name" {
  domain_name = "${var.env}-api.pttapp.com"

  domain_name_configuration {
    certificate_arn = var.certificate_arn
    endpoint_type   = "REGIONAL"
    security_policy = "TLS_1_2"
  }
}

resource "aws_apigatewayv2_api_mapping" "ptt-api-mapping" {
  api_id      = aws_apigatewayv2_api.ptt-api.id
  domain_name = aws_apigatewayv2_domain_name.ptt-api-domain-name.id
  stage       = aws_apigatewayv2_stage.default_stage.id
}
