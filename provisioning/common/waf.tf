
resource "aws_wafv2_rule_group" "geo_restriction_rule" {
  capacity = 10
  name     = local.wafv2_rule_group
  scope    = "REGIONAL"

  rule {
    name     = "geo_restriction_rule"
    priority = 1

    action {
      allow {}
    }

    statement {
      geo_match_statement {
        country_codes = ["MU", "GB", "IN", "CZ", "PL"]
        forwarded_ip_config {
          header_name       = "clientIp"
          fallback_behavior = "NO_MATCH"
        }
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = false
      metric_name                = "geo_restriction_rule"
      sampled_requests_enabled   = false
    }
  }
  visibility_config {
    cloudwatch_metrics_enabled = false
    metric_name                = "geo_restriction_rule"
    sampled_requests_enabled   = false
  }
}



resource "aws_wafv2_web_acl" "aws_wafv2_rule" {
  name        = local.wafv2_rule
  description = "ptt-app-waf-${var.env}"
  scope       = "REGIONAL"

  default_action {
    block {}
  }

  rule {
    name     = "geo_restriction_rule"
    priority = 1
    statement {
      rule_group_reference_statement {
        arn = aws_wafv2_rule_group.geo_restriction_rule.arn

      }

    }
    override_action {
      none {}
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "geo_restriction_rule"
      sampled_requests_enabled   = true
    }
  }

  tags = merge(local.tags, {
    Name = local.wafv2_rule
  })

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "awswafv2_rule"
    sampled_requests_enabled   = true
  }
}


resource "aws_wafv2_web_acl_association" "web_acl_association_lb" {
  resource_arn = aws_alb.ptt-alb.arn
  web_acl_arn  = aws_wafv2_web_acl.aws_wafv2_rule.arn
}
