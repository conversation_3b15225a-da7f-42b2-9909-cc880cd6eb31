resource "aws_ecs_cluster" "ptt-ecs-cluster" {
  name = local.ecs_cluster_name

  service_connect_defaults {
    namespace = aws_service_discovery_http_namespace.backend-namespace.arn
  }

  tags = merge(local.tags, {
    Name = local.ecs_cluster_name
  })
}

resource "aws_ecs_task_definition" "ptt-ecs-task" {
  family = "flask-app"
  requires_compatibilities = [
  "FARGATE"]
  network_mode          = "awsvpc"
  cpu                   = var.ecs_task_cpu
  memory                = var.ecs_task_memory
  tags                  = local.tags
  execution_role_arn    = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn         = aws_iam_role.ecs_task_role.arn
  container_definitions = <<DEFINITION
[
   {
      "name":"flask-app",
      "image":"${var.app_image}:${var.app_version}",
      "essential":true,
      "portMappings":[
         {
            "containerPort":${var.app_port},
            "hostPort":${var.app_port},
            "protocol":"tcp"
         }
      ],
      "logConfiguration": {
          "logDriver": "awslogs",
          "options": {
            "awslogs-group": "${aws_cloudwatch_log_group.logs.name}",
            "awslogs-region": "${var.aws_region}",
            "awslogs-stream-prefix": "ecs"
          }
        },
      "environment":[
         {
            "name":"ENVIRONMENT",
            "value":"${var.env}"
         },
         {
            "name":"SECRET_NAME",
            "value":"${local.secret_name}"
         },
         {
            "name":"USER_POOL_ID",
            "value":"${aws_cognito_user_pool.pool.id}"
         },
         {
            "name":"APP_CLIENT_ID",
            "value":"${aws_cognito_user_pool_client.client.id}"
         },
         {
            "name":"BANKING_FILE_BUCKET",
            "value":"${local.banking_files_bucket}"
         },
         {
            "name":"CLAIM_FILE_BUCKET",
            "value":"${local.claim_files_bucket}"
         },
         {
            "name":"INSURANCE_FILE_BUCKET",
            "value":"${local.insurance_files_bucket}"
         },
         {
            "name":"BOND_FILE_BUCKET",
            "value":"${local.bond_files_bucket}"
         },
         {
            "name":"ATOL_FILE_BUCKET",
            "value":"${local.atol_files_bucket}"
         },
         {
            "name":"SUPPLIER_LIST_FILE_BUCKET",
            "value":"${local.client_insurance_suppliers_bucket}"
         },
         {
            "name":"FILE_TEMPLATE_BUCKET",
            "value":"${local.file_template_bucket}"
         },
         {
            "name":"USER_PROFILE_BUCKET",
            "value":"${local.user_profile_bucket}"
         },
         {
            "name":"REPORTS_BUCKET",
            "value":"${local.reports_bucket}"
         },
         {
            "name":"SFTP_BUCKET",
            "value":"${local.sftp_bucket}"
         },
         {
            "name":"MAJOR_TRAVEL",
            "value":"${var.major_travel}"
         },
         {
            "name":"ANGLIA_TOURS",
            "value":"${var.anglia_tours}"
         },
         {
            "name":"WST_TRAVEL",
            "value":"${var.wst_travel}"
         },
         {
            "name":"WE_LOVE_HOLIDAYS",
            "value":"${var.we_love_holidays}"
         },
         {
            "name":"SWOOP",
            "value":"${var.swoop}"
         },
         {
            "name":"SWOOP_TRAVEL",
            "value":"${var.swoop_travel}"
         },
         {
            "name":"NAS",
            "value":"${var.nas}"
         },
         {
            "name":"TS",
            "value":"${var.ts}"
         },
         {
            "name":"ITGP",
            "value":"${var.itgp}"
         },
         {
            "name":"INTE",
            "value":"${var.inte}"
         },
         {
            "name":"GTL",
            "value":"${var.gtl}"
         },
         {
            "name":"TURQ",
            "value":"${var.turq}"
         },
         {
            "name":"HOBE",
            "value":"${var.hobe}"
         },
         {
            "name":"TCT",
            "value":"${var.tct}"
         },
          {
            "name":"TDC",
            "value":"${var.tdc}"
         },
           {
            "name":"NST",
            "value":"${var.nst}"
         },
         {
            "name":"EST",
            "value":"${var.est}"
         },
           {
            "name":"PGL",
            "value":"${var.pgl}"
         },
         {
            "name":"FLYPOP",
            "value":"${var.flypop}"
         },
         {
            "name":"BARRHEAD",
            "value":"${var.barrhead}"
         },
         {
            "name":"HAYS",
            "value":"${var.hays}"
         },
         {
            "name":"SUNSHINE",
            "value":"${var.sunshine}"
         },
         {
            "name":"BROADWAY",
            "value":"${var.broadway}"
         },
         {
            "name":"IGLU_ESCROW",
            "value":"${var.iglu_escrow}"
         },
         {
            "name":"CALEDONIAN",
            "value":"${var.caledonian}"
         },
         {
            "name":"BANKING_AND_CLAIM_SUMMARY_BUCKET",
            "value":"${local.banking_and_claim_summary_bucket}"
         },
         {
            "name":"IA_BUCKET",
            "value":"${local.internal_audit_bucket}"
         },
         {
            "name":"TCT",
            "value":"${var.tct}"
         },
         {
            "name":"TDC",
            "value":"${var.tdc}"
         },
         {
            "name":"NST",
            "value":"${var.nst}"
         },
         {
            "name":"EST",
            "value":"${var.est}"
         },
         {
            "name":"PGL",
            "value":"${var.pgl}"
         },
         {
            "name":"TRAVEL_REPUBLIC",
            "value":"${var.travel_republic}"
         },
         {
            "name":"PTT_BUCKET",
            "value":"${local.sftp_to_ptt_s3_bucket}"
         },
         {
            "name":"AUTHORIZED_SIGNATORIES_BUCKET",
            "value":"${local.authorized_signatories_bucket}"
         },
         {
            "name":"WLH_NEW",
            "value":"${var.wlh_new}"
         },
         {
            "name":"PENNYWOOD",
            "value":"${var.pennywood}"
         },
         {
            "name":"BLUESTYLE",
            "value":"${var.bluestyle}"
         },
         {
            "name":"POWERBI_CLIENT_ID",
            "value":"${var.powerbi_client_id}"
         },
         {
            "name":"POWERBI_CLIENT_SECRET",
            "value":"${var.powerbi_client_secret}"
         },
         {
            "name":"POWERBI_TENANT_ID",
            "value":"${var.powerbi_tenant_id}"
         },
         {
            "name":"POWERBI_WORKSPACE_ID",
            "value":"${var.powerbi_workspace_id}"
         },
         {
            "name":"POWERBI_REPORT_ID",
            "value":"${var.powerbi_report_id}"
         },
         {
            "name":"POWERBI_ERV_REPORT_ID",
            "value":"${var.powerbi_erv_report_id}"
         },
         {
            "name":"MANUALS_BUCKET",
            "value":"${local.manuals_bucket}"
         },
         {
            "name":"ERV_DASHBOARD_GRAPHS_SNAPSHOT_BUCKET",
            "value":"${local.erv_dashboard_graphs_snapshot_bucket}"
         },
         {
            "name":"KAFKA_BOOTSTRAP_SERVERS",
            "value":"${var.kafka_bootstrap_servers}"
         } 
      ]
   },
   {
      "name":"celery-worker",
      "image":"${var.app_image}:${var.app_version}",
      "entrypoint":["/bin/bash", "-c", "/opt/app/worker-entrypoint.sh"],
      "essential":false,
      "portMappings":[
      ],
      "user":"1000",
      "logConfiguration": {
          "logDriver": "awslogs",
          "options": {
            "awslogs-group": "${aws_cloudwatch_log_group.logs.name}",
            "awslogs-region": "${var.aws_region}",
            "awslogs-stream-prefix": "ecs"
          }
        },
      "environment":[
         {
            "name":"ENVIRONMENT",
            "value":"${var.env}"
         },
         {
            "name":"SECRET_NAME",
            "value":"${local.secret_name}"
         },
         {
            "name":"USER_POOL_ID",
            "value":"${aws_cognito_user_pool.pool.id}"
         },
         {
            "name":"APP_CLIENT_ID",
            "value":"${aws_cognito_user_pool_client.client.id}"
         },
         {
            "name":"BANKING_FILE_BUCKET",
            "value":"${local.banking_files_bucket}"
         },
         {
            "name":"CLAIM_FILE_BUCKET",
            "value":"${local.claim_files_bucket}"
         },
         {
            "name":"INSURANCE_FILE_BUCKET",
            "value":"${local.insurance_files_bucket}"
         },
         {
            "name":"BOND_FILE_BUCKET",
            "value":"${local.bond_files_bucket}"
         },
         {
            "name":"ATOL_FILE_BUCKET",
            "value":"${local.atol_files_bucket}"
         },
         {
            "name":"SUPPLIER_LIST_FILE_BUCKET",
            "value":"${local.client_insurance_suppliers_bucket}"
         },
         {
            "name":"FILE_TEMPLATE_BUCKET",
            "value":"${local.file_template_bucket}"
         },
         {
            "name":"USER_PROFILE_BUCKET",
            "value":"${local.user_profile_bucket}"
         },
         {
            "name":"REPORTS_BUCKET",
            "value":"${local.reports_bucket}"
         },
         {
            "name":"MAJOR_TRAVEL",
            "value":"${var.major_travel}"
         },
         {
            "name":"ANGLIA_TOURS",
            "value":"${var.anglia_tours}"
         },
         {
            "name":"WST_TRAVEL",
            "value":"${var.wst_travel}"
         },
         {
            "name":"WE_LOVE_HOLIDAYS",
            "value":"${var.we_love_holidays}"
         },
         {
            "name":"SWOOP",
            "value":"${var.swoop}"
         },
         {
            "name":"SWOOP_TRAVEL",
            "value":"${var.swoop_travel}"
         },
         {
            "name":"NAS",
            "value":"${var.nas}"
         },
         {
            "name":"TS",
            "value":"${var.ts}"
         },
         {
            "name":"ITGP",
            "value":"${var.itgp}"
         },
         {
            "name":"INTE",
            "value":"${var.inte}"
         },
         {
            "name":"GTL",
            "value":"${var.gtl}"
         },
         {
            "name":"TURQ",
            "value":"${var.turq}"
         },
         {
            "name":"HOBE",
            "value":"${var.hobe}"
         },
         {
            "name":"FLYPOP",
            "value":"${var.flypop}"
         },
         {
            "name":"BARRHEAD",
            "value":"${var.barrhead}"
         },
         {
            "name":"HAYS",
            "value":"${var.hays}"
         },
         {
            "name":"SUNSHINE",
            "value":"${var.sunshine}"
         },
         {
            "name":"BROADWAY",
            "value":"${var.broadway}"
         },
         {
            "name":"IGLU_ESCROW",
            "value":"${var.iglu_escrow}"
         },
         {
            "name":"CALEDONIAN",
            "value":"${var.caledonian}"
         },
         {
            "name":"BANKING_AND_CLAIM_SUMMARY_BUCKET",
            "value":"${local.banking_and_claim_summary_bucket}"
         },
         {
            "name":"IA_BUCKET",
            "value":"${local.internal_audit_bucket}"
         },
         {
            "name":"TRAVEL_REPUBLIC",
            "value":"${var.travel_republic}"
         },
         {
            "name":"AUTHORIZED_SIGNATORIES_BUCKET",
            "value":"${local.authorized_signatories_bucket}"
         },
         {
            "name":"WLH_NEW",
            "value":"${var.wlh_new}"
         },
         {
            "name":"PENNYWOOD",
            "value":"${var.pennywood}"
         },
         {
            "name":"BLUESTYLE",
            "value":"${var.bluestyle}"
         },
         {
            "name":"POWERBI_CLIENT_ID",
            "value":"${var.powerbi_client_id}"
         },
         {
            "name":"POWERBI_CLIENT_SECRET",
            "value":"${var.powerbi_client_secret}"
         },
         {
            "name":"POWERBI_TENANT_ID",
            "value":"${var.powerbi_tenant_id}"
         },
         {
            "name":"POWERBI_WORKSPACE_ID",
            "value":"${var.powerbi_workspace_id}"
         },
         {
            "name":"POWERBI_REPORT_ID",
            "value":"${var.powerbi_report_id}"
         },
         {
            "name":"POWERBI_ERV_REPORT_ID",
            "value":"${var.powerbi_erv_report_id}"
         },
         {
            "name":"MANUALS_BUCKET",
            "value":"${local.manuals_bucket}"
         },
         {
            "name":"ERV_DASHBOARD_GRAPHS_SNAPSHOT_BUCKET",
            "value":"${local.erv_dashboard_graphs_snapshot_bucket}"
         }
      ]
   }
]
DEFINITION
}
resource "aws_ecs_service" "backend-service" {
  name                 = local.ecs_service_name
  cluster              = aws_ecs_cluster.ptt-ecs-cluster.id
  task_definition      = aws_ecs_task_definition.ptt-ecs-task.arn
  desired_count        = var.ecs_task_desired_count
  launch_type          = "FARGATE"
  force_new_deployment = true

  network_configuration {
    security_groups  = [aws_security_group.ptt-ecs-sg.id]
    subnets          = aws_subnet.ptt-private-subnets.*.id
    assign_public_ip = false
  }

  load_balancer {
    container_name   = "flask-app"
    container_port   = var.app_port
    target_group_arn = aws_alb_target_group.ptt-alb-target-group.id
  }

  service_connect_configuration {
    enabled = true
  }

  depends_on = [
    aws_alb_listener.ptt-alb-listener
  ]

  tags = merge(local.tags, {
    Name = local.ecs_service_name
  })
}

resource "aws_appautoscaling_target" "backend_service_autoscaling" {
  count              = var.env == "prod" ? 1 : 0
  max_capacity       = var.ecs_service_max_capacity
  min_capacity       = var.ecs_service_min_capacity
  resource_id        = "service/${aws_ecs_cluster.ptt-ecs-cluster.name}/${aws_ecs_service.backend-service.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}

resource "aws_appautoscaling_policy" "cpu_scaling_policy" {
  count              = var.env == "prod" ? 1 : 0
  name               = "backend-cpu-autoscaling-policy"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.backend_service_autoscaling[0].resource_id
  scalable_dimension = aws_appautoscaling_target.backend_service_autoscaling[0].scalable_dimension
  service_namespace  = aws_appautoscaling_target.backend_service_autoscaling[0].service_namespace

  target_tracking_scaling_policy_configuration {
    target_value = 70.0
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    scale_in_cooldown  = 300
    scale_out_cooldown = 300
  }
}

resource "aws_appautoscaling_policy" "memory_scaling_policy" {
  count              = var.env == "prod" ? 1 : 0
  name               = "backend-memory-autoscaling-policy"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.backend_service_autoscaling[0].resource_id
  scalable_dimension = aws_appautoscaling_target.backend_service_autoscaling[0].scalable_dimension
  service_namespace  = aws_appautoscaling_target.backend_service_autoscaling[0].service_namespace

  target_tracking_scaling_policy_configuration {
    target_value = 70.0
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    scale_in_cooldown  = 300
    scale_out_cooldown = 300
  }
}
