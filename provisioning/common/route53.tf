data "aws_route53_zone" "ptt_public_zone" {
  name         = "pttapp.com"
  private_zone = false
}

resource "aws_route53_record" "ptt_api_r53" {
  name    = aws_apigatewayv2_domain_name.ptt-api-domain-name.domain_name
  type    = "A"
  zone_id = data.aws_route53_zone.ptt_public_zone.id


  alias {
    name                   = aws_apigatewayv2_domain_name.ptt-api-domain-name.domain_name_configuration[0].target_domain_name
    zone_id                = aws_apigatewayv2_domain_name.ptt-api-domain-name.domain_name_configuration[0].hosted_zone_id
    evaluate_target_health = false
  }
}
