resource "aws_s3_bucket" "banking-files-bucket" {
  bucket = local.banking_files_bucket
  tags = merge(local.tags, {
    Name = local.banking_files_bucket
  })
}
resource "aws_s3_bucket_acl" "banking-files-bucket-acl" {
  bucket = aws_s3_bucket.banking-files-bucket.id
  acl    = "private"
}
resource "aws_s3_bucket_public_access_block" "block-public-access-for-banking-files-bucket" {
  bucket = aws_s3_bucket.banking-files-bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_cors_configuration" "cors-banking-files-bucket" {
  bucket = aws_s3_bucket.banking-files-bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST"]
    allowed_origins = local.cors_origins
    expose_headers  = []
  }
}

resource "aws_s3_bucket" "insurance-files-bucket" {
  bucket = local.insurance_files_bucket
  tags = merge(local.tags, {
    Name = local.insurance_files_bucket
  })
}
resource "aws_s3_bucket_acl" "insurance-files-bucket-acl" {
  bucket = aws_s3_bucket.insurance-files-bucket.id
  acl    = "private"
}
resource "aws_s3_bucket_public_access_block" "block-public-access-for-insurance-files-bucket" {
  bucket = aws_s3_bucket.insurance-files-bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket" "atol-files-bucket" {
  bucket = local.atol_files_bucket
  tags = merge(local.tags, {
    Name = local.atol_files_bucket
  })
}
resource "aws_s3_bucket_acl" "atol-files-bucket-acl" {
  bucket = aws_s3_bucket.atol-files-bucket.id
  acl    = "private"
}
resource "aws_s3_bucket_public_access_block" "block-public-access-for-atol-files-bucket" {
  bucket = aws_s3_bucket.atol-files-bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket" "claim-files-bucket" {
  bucket = local.claim_files_bucket
  tags = merge(local.tags, {
    Name = local.claim_files_bucket
  })
}
resource "aws_s3_bucket_acl" "claim-files-bucket-acl" {
  bucket = aws_s3_bucket.claim-files-bucket.id
  acl    = "private"
}
resource "aws_s3_bucket_public_access_block" "block-public-access-for-claim-files-bucket" {
  bucket = aws_s3_bucket.claim-files-bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_cors_configuration" "cors-claim-files-bucket" {
  bucket = aws_s3_bucket.claim-files-bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST"]
    allowed_origins = local.cors_origins
    expose_headers  = []
  }
}

resource "aws_s3_bucket" "client-insurance-suppliers-bucket" {
  bucket = local.client_insurance_suppliers_bucket
  tags = merge(local.tags, {
    Name = local.client_insurance_suppliers_bucket
  })
}
resource "aws_s3_bucket_acl" "client-insurance-suppliers-bucket-acl" {
  bucket = aws_s3_bucket.client-insurance-suppliers-bucket.id
  acl    = "private"
}
resource "aws_s3_bucket_public_access_block" "block-public-access-for-client-insurance-suppliers-bucket" {
  bucket = aws_s3_bucket.client-insurance-suppliers-bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket" "file-template-bucket" {
  bucket = local.file_template_bucket
  tags = merge(local.tags, {
    Name = local.file_template_bucket
  })
}
resource "aws_s3_bucket_acl" "file-template-bucket-acl" {
  bucket = aws_s3_bucket.file-template-bucket.id
  acl    = "private"
}
resource "aws_s3_bucket_public_access_block" "block-public-access-for-file-template-bucket" {
  bucket = aws_s3_bucket.file-template-bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}


resource "aws_s3_object" "banking_template" {
  bucket = aws_s3_bucket.file-template-bucket.id
  key    = "banking"
  acl    = "private"
  source = local.banking_template_path
  etag   = md5(local.banking_template_path)
}

resource "aws_s3_object" "claim_template" {
  bucket = aws_s3_bucket.file-template-bucket.id
  key    = "claim"
  acl    = "private"
  source = local.claim_template_path
  etag   = md5(local.claim_template_path)
}

resource "aws_s3_bucket" "user-profile-bucket" {
  bucket = local.user_profile_bucket
  tags = merge(local.tags, {
    Name = local.user_profile_bucket
  })
}
resource "aws_s3_bucket_acl" "user-profile-bucket-acl" {
  bucket = aws_s3_bucket.user-profile-bucket.id
  acl    = "private"
}
resource "aws_s3_bucket_public_access_block" "block-public-access-for-user-profile-bucket" {
  bucket = aws_s3_bucket.user-profile-bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket" "reports-bucket" {
  bucket = local.reports_bucket
  tags = merge(local.tags, {
    Name = local.reports_bucket
  })
}
resource "aws_s3_bucket_acl" "reports-bucket-acl" {
  bucket = aws_s3_bucket.reports-bucket.id
  acl    = "private"
}
resource "aws_s3_bucket_public_access_block" "block-public-access-for-reports-bucket" {
  bucket = aws_s3_bucket.reports-bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_cors_configuration" "cors-reports-bucket" {
  bucket = aws_s3_bucket.reports-bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST"]
    allowed_origins = local.cors_origins
    expose_headers  = []
  }
}

resource "aws_s3_object" "default_picture" {
  bucket = aws_s3_bucket.user-profile-bucket.id
  key    = "default"
  acl    = "private"
  source = local.default_picture_path
  etag   = md5(local.default_picture_path)
}

resource "aws_s3_bucket" "banking-and-claim-summary-bucket" {
  bucket = local.banking_and_claim_summary_bucket
  tags = merge(local.tags, {
    Name = local.banking_and_claim_summary_bucket
  })
}
resource "aws_s3_bucket_public_access_block" "block-public-access-for-banking-and-claim-summary-bucket" {
  bucket = aws_s3_bucket.banking-and-claim-summary-bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_cors_configuration" "cors-banking-and-claim-summary-bucket" {
  bucket = aws_s3_bucket.banking-and-claim-summary-bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST"]
    allowed_origins = local.cors_origins
    expose_headers  = []
  }
}

resource "aws_s3_bucket_policy" "banking-and-claim-summary-bucket-policy" {
  bucket = aws_s3_bucket.banking-and-claim-summary-bucket.id

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Sid" : "AllowAccess",
        "Effect" : "Allow",
        "Principal" : {
          "AWS" : aws_iam_role.ecs_task_execution_role.arn
        },
        "Action" : [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:DeleteObject"
        ],
        "Resource" : [
          "${aws_s3_bucket.banking-and-claim-summary-bucket.arn}",
          "${aws_s3_bucket.banking-and-claim-summary-bucket.arn}/*"
        ]
      }
    ]
  })
}


resource "aws_s3_bucket" "internal-audit-bucket" {
  bucket = local.internal_audit_bucket
  tags = merge(local.tags, {
    Name = local.internal_audit_bucket
  })
}
resource "aws_s3_bucket_public_access_block" "block-public-access-for-internal-audit-bucket" {
  bucket = aws_s3_bucket.internal-audit-bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_cors_configuration" "cors-internal-audit-bucket" {
  bucket = aws_s3_bucket.internal-audit-bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST"]
    allowed_origins = local.cors_origins
    expose_headers  = []
  }
}

resource "aws_s3_bucket_policy" "internal-audit-bucket-policy" {
  bucket = aws_s3_bucket.internal-audit-bucket.id

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Sid" : "AllowAccess",
        "Effect" : "Allow",
        "Principal" : {
          "AWS" : aws_iam_role.ecs_task_execution_role.arn
        },
        "Action" : [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:DeleteObject"
        ],
        "Resource" : [
          "${aws_s3_bucket.internal-audit-bucket.arn}",
          "${aws_s3_bucket.internal-audit-bucket.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_s3_bucket" "authorized-signatories-bucket" {
  bucket = local.authorized_signatories_bucket
  tags = merge(local.tags, {
    Name = local.authorized_signatories_bucket
  })
}

resource "aws_s3_bucket" "manuals_bucket" {
  bucket = local.manuals_bucket
  tags = merge(local.tags, {
    Name = local.manuals_bucket
  })
}

resource "aws_s3_bucket" "erv_dashboard_graphs_snapshot_bucket" {
  bucket = local.erv_dashboard_graphs_snapshot_bucket
  tags = merge(local.tags, {
    Name = local.erv_dashboard_graphs_snapshot_bucket
  })
}
