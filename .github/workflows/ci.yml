name: Continuous Integration

on:
  pull_request:
    branches: [dev, release, main]

jobs:

  quality:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Check PR title
        run: |
          title="${{ github.event.pull_request.title }}"
          regex="^IMP-.*: .*"
          if [[ "$title" =~ $regex ]]; then
            echo "PR Title is valid"
          else
            echo "PR Title is invalid: '$title'"
            exit 1
          fi

      - name: Check version bump and CHANGELOG entry
        run: |
          chmod +x ./check_version.sh
          ./check_version.sh
          

  terraform_plan:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ fromJson('{dev:"./provisioning/nonprod/dev",release:"./provisioning/nonprod/pre-prod",main:"./provisioning/prod/prod" }')[github.base_ref] }}
    steps:
      - uses: actions/checkout@v4

      - name: AWS Configuration for prod
        if: ${{ github.base_ref == 'main' }}
        run: |
          echo "AWS_ACCESS_KEY_ID=${{ secrets.PROD_AWS_ACCESS_KEY_ID }}" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}" >> $GITHUB_ENV
          echo "TF_VAR_secret_name=${{ secrets.PROD_SECRET_NAME }}" >> $GITHUB_ENV

      - name: AWS Configuration for release
        if: ${{ github.base_ref == 'release' }}
        run: |
          echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> $GITHUB_ENV
          echo "TF_VAR_secret_name=${{ secrets.PRE_PROD_SECRET_NAME }}" >> $GITHUB_ENV

      - name: AWS Configuration for dev
        if: ${{ github.base_ref == 'dev' }}
        run: |
          echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> $GITHUB_ENV
          echo "TF_VAR_secret_name=${{ secrets.DEV_SECRET_NAME }}" >> $GITHUB_ENV

      - name: Install Terraform
        env:
          TERRAFORM_VERSION: "1.1.3"
        run: |
          tf_version=$TERRAFORM_VERSION
          wget https://releases.hashicorp.com/terraform/"$tf_version"/terraform_"$tf_version"_linux_amd64.zip
          unzip terraform_"$tf_version"_linux_amd64.zip
          sudo mv terraform /usr/local/bin/
      - name: Verify Terraform version
        run: terraform --version

      - name: Terraform init
        run: terraform init -input=false

      - name: Terraform plan
        env:
          TF_VAR_app_version: ${{ github.sha }}
        run: terraform plan

  pytest:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up python
        id: setup-python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      #----------------------------------------------
      #  -----  install & configure poetry  -----
      #----------------------------------------------
      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          virtualenvs-create: true
          virtualenvs-in-project: true
          installer-parallel: true

      #----------------------------------------------
      #       load cached venv if cache exists
      #----------------------------------------------
      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v4
        with:
          path: .venv
          key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}
      #----------------------------------------------
      # install dependencies if cache does not exist
      #----------------------------------------------
      - name: Install dependencies
        if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
        run: poetry install --no-interaction --no-root

      - name: Run pytest
        run: PYTHONPATH=$PWD poetry run python -m pytest -v --cov=flaskr/
