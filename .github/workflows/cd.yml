name: Continuous Deployment

on:
  push:
    branches: [dev, release, main]

jobs:
  docker_publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1

      - name: Configure AWS credentials (prod)
        if: ${{ github.ref_name == 'main' }}
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-2

      - name: Configure AWS credentials (nonprod)
        if: ${{ github.ref_name != 'main' }}
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ptt-backend-service
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
  terraform_apply:
    runs-on: ubuntu-latest
    needs: [docker_publish]
    defaults:
      run:
        working-directory: ${{ fromJson('{dev:"./provisioning/nonprod/dev",release:"./provisioning/nonprod/pre-prod",main:"./provisioning/prod/prod" }')[github.ref_name] }}
    steps:
      - uses: actions/checkout@v1

      - name: AWS Configuration for prod
        if: ${{ github.ref_name == 'main' }}
        run: |
          echo "AWS_ACCESS_KEY_ID=${{ secrets.PROD_AWS_ACCESS_KEY_ID }}" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}" >> $GITHUB_ENV
          echo "TF_VAR_secret_name=${{ secrets.PROD_SECRET_NAME }}" >> $GITHUB_ENV

      - name: AWS Configuration for release
        if: ${{ github.ref_name == 'release' }}
        run: |
          echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> $GITHUB_ENV
          echo "TF_VAR_secret_name=${{ secrets.PRE_PROD_SECRET_NAME }}" >> $GITHUB_ENV

      - name: AWS Configuration for dev
        if: ${{ github.ref_name == 'dev' }}
        run: |
          echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> $GITHUB_ENV
          echo "TF_VAR_secret_name=${{ secrets.DEV_SECRET_NAME }}" >> $GITHUB_ENV

      - name: Install Terraform
        env:
          TERRAFORM_VERSION: "1.1.3"
        run: |
          tf_version=$TERRAFORM_VERSION
          wget https://releases.hashicorp.com/terraform/"$tf_version"/terraform_"$tf_version"_linux_amd64.zip
          unzip terraform_"$tf_version"_linux_amd64.zip
          sudo mv terraform /usr/local/bin/
      - name: Verify Terraform version
        run: terraform --version

      - name: Terraform init
        run: terraform init -input=false

      - name: Terraform apply
        env:
          TF_VAR_app_version: ${{ github.sha }}
        run: terraform apply -auto-approve -input=false

  push_tag:
    runs-on: ubuntu-latest
    needs: [terraform_apply]
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: "0"
      - name: Bump version and push tag
        uses: anothrNick/github-tag-action@1.36.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          WITH_V: true
