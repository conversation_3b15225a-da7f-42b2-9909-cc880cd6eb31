# ptt-backend-service

## Development Setup

1. Run `poetry install` to install the dependencies using poetry
2. Set the environment variables `FLASK_APP` and `FLASK_ENV` with the appropriate values based on the environment.
3. Also set the required environment variables that are required in the `config.py` file. Eg. `DATABASE_URI`
For example, in a *nix development environment following command can be used to set the environment variables.

```bash
export FLASK_APP=flaskr
export FLASK_ENV=development
export DATABASE_URI=<local-uri>
```

## Install pre-commit hooks
After cloning the repository, run the following command to install pre-commit hook. This automatically runs 
[black](https://pypi.org/project/black/) and [flake8](https://flake8.pycqa.org/en/latest/) tools, which
perform code formatting. Make sure to  add the formatted files to git again.

```
poetry run pre-commit install 
```

## Starting the server

Start the flask server by running `flask run` from the terminal.